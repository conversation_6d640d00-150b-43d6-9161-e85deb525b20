{"browser_upgrades": {"chrome": {"rules": []}, "firefox": {"rules": [{"id": "default_rule", "conditions": {"default": true}, "action": {"version": "133.0", "variant": "universal"}}]}, "webview": {"rules": []}, "trichrome": {"rules": []}}, "rule_condition_types": {"os_version": ["lt", "lte", "gt", "gte", "eq", "neq"], "device_model": ["in", "not_in", "regex"], "device_id": ["in", "not_in", "regex"], "is_dedicated_device": ["boolean"], "manufacturer": ["in", "not_in", "regex"], "custom_function": ["string"], "default": ["boolean"]}, "config_version": "1.0.0"}