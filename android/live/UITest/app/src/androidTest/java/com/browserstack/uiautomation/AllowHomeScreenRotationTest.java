package com.browserstack.uiautomation;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.uiautomator.By;
import androidx.test.uiautomator.UiObject;
import androidx.test.uiautomator.UiObjectNotFoundException;
import androidx.test.uiautomator.UiSelector;
import androidx.test.uiautomator.UiScrollable;
import androidx.test.uiautomator.Until;

import java.io.IOException;

import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(AndroidJUnit4.class)
public class AllowHomeScreenRotationTest extends CommonTest {
    @Test
    public void allowHomeScreenRotation() throws Exception {
        String[] allowHomeScreenRotationOptions = {"Allow Home screen rotation", "Allow home screen rotation"};
        try {
            mDevice.executeShellCommand("am start -n com.google.android.apps.nexuslauncher/com.android.launcher3.settings.SettingsActivity");
            boolean foundOption = false;
            UiScrollable appViews = new UiScrollable(new UiSelector().scrollable(true));

            for (String option : allowHomeScreenRotationOptions) {
                // Check if the option is already visible without scrolling
                if (mDevice.wait(Until.hasObject(By.text(option)), 600)) {
                    foundOption = true;
                    break;
                }
                // If not visible, try scrolling to find it
                try {
                    appViews.scrollTextIntoView(option);
                    if (mDevice.wait(Until.hasObject(By.text(option)), 600)) {
                        foundOption = true;
                        break;
                    }
                } catch (UiObjectNotFoundException e) {
                    // If scrolling fails, continue to the next option
                }
            }

            if (!foundOption) {
                throw new Exception("Allow Home Screen Rotation option not found");
            }

            UiObject switchButton = null;
            for (String option : allowHomeScreenRotationOptions) {
                switchButton = new UiObject(new UiSelector().textContains(option));
                if (switchButton.exists()) {
                    break;
                }
            }

            if (switchButton != null) {
                switchButton.click();
            } else {
                throw new Exception("Switch button for Allow Home Screen Rotation not found");
            }

            mDevice.pressHome();
        }catch (IOException e){
            out(e.getMessage(), 0);
            throw e;
        }
    }
}
