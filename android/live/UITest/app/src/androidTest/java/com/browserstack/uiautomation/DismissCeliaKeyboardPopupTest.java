package com.browserstack.uiautomation;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.uiautomator.By;
import androidx.test.uiautomator.UiObject;
import androidx.test.uiautomator.UiSelector;
import androidx.test.uiautomator.Until;

import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(AndroidJUnit4.class)
public class DismissCeliaKeyboardPopupTest extends CommonTest {

    @Test
    public void dismissCeliaKeyboardPopup() throws Exception {
        mDevice.executeShellCommand("am start -n com.huawei.ohos.inputmethod/com.appstore.view.activity.SetupWizardActivity");
        String cancel_btn_txt = "CANCEL";
        mDevice.wait(Until.hasObject(By.text(cancel_btn_txt)), 1000);
        UiObject cancel_btn = mDevice.findObject(new UiSelector().textMatches(cancel_btn_txt));
        if (uiObjectNotEmpty(cancel_btn)) {
            cancel_btn.click();
            log("Keyboard popup dismissed");
        } else {
            log("Cancel button not found");
            throw new Exception("Cancel button not found");
        }
    }

    private boolean uiObjectNotEmpty(UiObject element) {
        return element != null && element.exists();
    }
}
