package com.browserstack.uiautomation;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.browserstack.uiautomation.android.Android;
import com.browserstack.uiautomation.android.ManufacturerFactory;

import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(AndroidJUnit4.class)
public class EnableSimStateTest extends CommonTest {

    @Test
    public void enableSIMState() throws Exception {
        Android device = ManufacturerFactory.getDevice(mDeviceManufacturer, mDeviceMajorVersion);
        device.enableSim();
    }
}
