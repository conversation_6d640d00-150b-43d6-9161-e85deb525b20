## Intent Framework
We have started sending intents from the Platform instead of hard-coding them in the automation itself, using the ui_automation_rules_config.json file.

Example:

```
"disable_permission_monitoring": {  //automation name
    "rules": [  // Pay attention to the order in which rules are written, last will be evaluated first
        {
            // Rule #1
            "device_model": ["Pixel 5", "SM-G991B"],  // If this exists, then only device_model presence is checked in array
            "os_version": "^(1[1-9]|[2-9]\\d+)(\\.[0-9])?$",   // We are using regex here for os_version check
            "via": "intent_automation",   // This tells that we want to use an intent
            "intent": "-a android.settings.APPLICATION_DEVELOPMENT_SETTINGS"  // The intent which we will use
        },
        {
            // Rule #2
            "os_version": "^(1[5-9]|[2-9]\\d+)(\\.[0-9])?$",
            "via": "intent_automation",
            "intent": "-a android.settings.APPLICATION_DEVELOPMENT_SETTINGS.blah.blah"   // Some Other Intent
        },
        // New rules to be added keeping in mind the existing rules, so that we don't get any unwanted matches
    ],
    "default_via": "normal_automation",   // This tells we want to use automation without intent when no rule matches. Can be used for a global intent as well, but we rarey find such intents.
    "default_intent": "-a android.settings.APPLICATION_DEVELOPMENT_SETTINGS"   // Can be kept nil as well
},
```
