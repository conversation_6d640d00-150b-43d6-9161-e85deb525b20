require 'logger'
require 'json'

class BrowserUpgradeManager
  # Load the configuration
  def initialize(logger, logger_params = {})
    logger_params[:component] = self.class.to_s

    @logger = logger
    @logger_params = logger_params
    @config = JSON.parse(File.read("config/browser_upgrade.json"))
  end

  # Get Chrome version and variant using switch-case pattern
  def get_browser_version_and_variant(browser_name, device_obj)
    # Find the matching rule

    rule = find_matching_rule(browser_name, device_obj)
    rule["action"]
  rescue e
    # Handle the error
    raise "Error finding matching rule: #{e.message}"

  end

  # Find the first matching rule for a browser
  def find_matching_rule(browser_type, device_obj)
    rules = @config["browser_upgrades"][browser_type]["rules"]
    # Iterate through rules (already sorted by priority)
    rules.each do |rule|
      return rule if rule_matches?(rule, device_obj)
    end
    # Should never get here if there's a default rule
    raise "No matching rule found and no default rule configured"
  end

  # Check if a rule matches the device
  def rule_matches?(rule, device_obj)
    # Default rule always matches
    return true if rule["conditions"]["default"]

    # Custom function handling (switch case for custom functions)
    if rule["conditions"]["custom_function"]
      # case rule["conditions"]["custom_function"]
      # Add other custom functions as needed using when clause and unccoment above line
      return false
    end

    # Check OS version conditions (switch case for condition type)
    check_os_version_conditions(rule, device_obj)

    # Check device model conditions
    check_device_model_conditions(rule, device_obj)

    # Check device ID conditions
    check_device_id_conditions(rule, device_obj)

    # Check dedicated device condition
    dedicated_rule = rule["conditions"]["dedicated_device"]
    return false if dedicated_rule && (device_obj.dedicated_device? != dedicated_rule)

    # Check manufacturer conditions
    check_manufacturer_conditions(rule, device_obj)

    # All conditions passed
    true
  end

  def check_manufacturer_conditions(rule, device_obj)
    if rule["conditions"]["manufacturer"]
      manufacturer_conditions = rule["conditions"]["manufacturer"]
      manufacturer_conditions.each do |operator, value|
        case operator
        when "in"
          return false unless value.include?(device_obj.manufacturer)
        when "not_in"
          return false if value.include?(device_obj.manufacturer)
        when "regex"
          return false unless device_obj.manufacturer.match?(Regexp.new(value))
        end
      end
    end
  end

  def check_device_id_conditions(rule, device_obj)
    if rule["conditions"]["device_id"]
      device_id_conditions = rule["conditions"]["device_id"]
      device_id_conditions.each do |operator, value|
        case operator
        when "in"
          return false unless value.include?(device_obj.id)
        when "not_in"
          return false if value.include?(device_obj.id)
        when "regex"
          return false unless device_obj.id.match?(Regexp.new(value))
        end
      end
    end
  end

  def check_os_version_conditions(rule, device_obj)
    if rule["conditions"]["os_version"]
      os_version_conditions = rule["conditions"]["os_version"]
      os_version_conditions.each do |operator, value|
        case operator
        when "lt"
          return false if device_obj.os_version.to_i >= value
        when "lte"
          return false if device_obj.os_version.to_i > value
        when "gt"
          return false if device_obj.os_version.to_i <= value
        when "gte"
          return false if device_obj.os_version.to_i < value
        when "eq"
          return false if device_obj.os_version.to_i != value
        when "neq"
          return false if device_obj.os_version.to_i == value
        end
      end
    end
  end

  def check_device_model_conditions(rule, device_obj)
    if rule["conditions"]["device_model"]
      device_model_conditions = rule["conditions"]["device_model"]
      device_model_conditions.each do |operator, value|
        case operator
        when "in"
          return false unless value.include?(device_obj.model)
        when "not_in"
          return false if value.include?(device_obj.model)
        when "regex"
          return false unless device_obj.model.match?(Regexp.new(value))
        end
      end
    end
  end
end