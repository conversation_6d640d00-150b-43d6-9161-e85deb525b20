#!/usr/bin/ruby
require 'net/http'
require 'uri'
require 'fileutils'
require 'json'
require 'dotenv/load'
require '/usr/local/.browserstack/mobile/common/push_to_zombie'

SITESPEED_S3_UPLOAD = "/usr/local/.browserstack/mobile/android/freetools/sitespeed_script/s3_upload.rb".freeze
KILL_SITESPEED_CHROMEDRIVER = "/usr/local/.browserstack/mobile/android/freetools/sitespeed_script/"\
                              "kill_sitespeed_chromedriver.sh".freeze
require SITESPEED_S3_UPLOAD

def prepare_result_data(browser_time, browser_time_runs, page_xray, page_xray_runs)
  browser_time_runs.each do |run|
    tmp = JSON.parse(File.read(run))
    tmp["har"] = {}
    tmp["cpu"]["urls"] = {}  if tmp['cpu'].is_a?(Hash)
    tmp["coach"] = {}
    browser_time[run.split('.')[-2]] = tmp
  end

  page_xray_runs.each do |run|
    tmp = JSON.parse(File.read(run))
    tmp["assets"] = {}
    tmp["firstParty"] = {}
    tmp["thirdParty"] = {}
    tmp["domains"] = {}
    tmp["afterOnContentLoad"] = {}
    tmp["afterOnLoad"] = {}
    tmp["coach"] = {}
    tmp["cpu"]["urls"] = {} if tmp['cpu'].is_a?(Hash)
    page_xray[run.split('.')[-2]] = tmp
  end
end

def send_performance_result(folder_name, job_id, device_id, sitespeed_exit_code, device_name, host)
  sitespeed_exit_code = begin
    Integer(sitespeed_exit_code)
  rescue StandardError
    1
  end

  # Delete status file to indicate success to SiteSpeed#trigger_backup
  File.delete("/usr/local/.browserstack/free_tools/jobs/#{folder_name}/status.txt") if File.exist?("/usr/local/"\
                                                              ".browserstack/free_tools/jobs/#{folder_name}/status.txt")
  env = File.read("/usr/local/.browserstack/free_tools/jobs/#{folder_name}/env.txt")
  puts env
  puts "####### #{job_id}  ####### #{device_id} ####### Sending response #{sitespeed_exit_code}"

  speedlab_url = "#{host}/sitespeed_result"
  log_file = "/var/log/browserstack/freetools_sitespeed.log"

  unless Dir.glob("/usr/local/.browserstack/free_tools/jobs/#{folder_name}"\
                  "/result/pages/**/data/browsertime.run*.json").empty?
    sitespeed_exit_code = 0
    puts 'Switching from fail to success'
  end

  if sitespeed_exit_code == 0
    multi_har_upload_exit_code = sitespeed_har_upload(folder_name, job_id, device_name)
    upload_coach_code = upload_coach(job_id, device_name, folder_name, "sitespeed")
    check_exit_code = filmstrip_check(folder_name)
    if check_exit_code == 0
      multi_filmstrip_upload_exit_code = sitespeed_filmstrip_upload(
        folder_name, job_id, device_name
      )
    end
    browser_time = {}
    page_xray = {}
    browser_time_runs = Dir.glob("/usr/local/.browserstack/free_tools/jobs/#{folder_name}"\
                                 "/result/pages/**/data/browsertime.run*.json")
    page_xray_runs = Dir.glob("/usr/local/.browserstack/free_tools/jobs/#{folder_name}"\
                              "/result/pages/**/data/pagexray.run*.json")

    prepare_result_data(browser_time, browser_time_runs, page_xray, page_xray_runs)
    form_data = {
      "device_id" => device_id,
      "exit_code" => sitespeed_exit_code,
      "job_id" => job_id,
      "multi_upload_status" => multi_har_upload_exit_code.to_json,
      "browser_time" => browser_time.to_json,
      "page_xray" => page_xray.to_json,
      "filmstrip_check_exit_code" => check_exit_code,
      "multi_filmstrip_upload_exit_code" => multi_filmstrip_upload_exit_code.to_json,
      "upload_coach_code" => upload_coach_code.to_json
    }
  else
    error = File.read("/usr/local/.browserstack/free_tools/jobs/#{folder_name}/log.txt")
    open(log_file, 'a') do |f| # rubocop:todo Security/Open
      f.puts error
    end
    #alerts
    zombie_push("android", "failed-sitespeed-job", "exitcode : #{sitespeed_exit_code}", "",
                "sitespeed failed job_id: #{job_id}, error: #{error}", device_id.to_s)
    form_data = {
      "job_id" => job_id,
      "device_id" => device_id,
      "exit_code" => sitespeed_exit_code,
      "error" => error.to_s
    }
  end
  send_request(speedlab_url, form_data)
  puts "Deleting folder"
  FileUtils.rm_rf("/usr/local/.browserstack/free_tools/jobs/#{folder_name}")

  # kill speedlab session specific chromedriver

  output = `bash "#{KILL_SITESPEED_CHROMEDRIVER}" "#{folder_name}"`
  puts "Sitespeed chromedriver killed, output: #{output}"
  check_for_running_chromedriver
end

def send_request(url, form_data)

  puts "send request, using net/http"
  uri = URI.parse(url)
  request = Net::HTTP::Post.new(uri)
  request.set_form_data(form_data)
  req_options = {
    use_ssl: uri.scheme == "https"
  }
  response = Net::HTTP.start(uri.hostname, uri.port, req_options) do |http|
    http.request(request)
  end
  response_code = response.code
  puts response_code
  if response_code != '200'
    puts "response body for non-200: #{response.body}"
    raise "invalid response code, #{response_code}"
  end
rescue StandardError => e
  puts e.message
  zombie_push("android", "sitespeed-webhook-hit-failed",
              "Exception in hitting #{url}, job_id: #{form_data['job_id']} exception: #{e.message}",
              "", "", (form_data["device_id"]).to_s)

end

def check_for_running_chromedriver
  output =
    `ps -e -o "pid,etimes,command" | grep "/usr/local/.browserstack/free_tools/jobs/" \
| grep "chromedriver/chromedriver" | grep "port="| awk '{if($2>7200) print $0}'`

  unless output.empty?
    puts "chromedriver check result: #{output}"
    zombie_push("android", "freetools-running-chromedriver", output.to_s, "", "", "")
  end
end

if __FILE__ == $PROGRAM_NAME
  folder_name = ARGV[0]
  job_id = ARGV[1]
  device_id = ARGV[2]
  sitespeed_exit_code = ARGV[3]
  device_name = ARGV[4]
  host = ARGV[5]
  send_performance_result(folder_name, job_id, device_id, sitespeed_exit_code, device_name, host)
end
