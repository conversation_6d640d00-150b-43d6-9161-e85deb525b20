require 'aws-sdk-s3'
require 'dotenv/load'
require_relative '../../constants'
require 'fileutils'

include BrowserStack # rubocop:todo Style/MixinUsage

require "#{BS_DIR}/mobile/common/push_to_zombie.rb"

FREETOOLS_JOBS_FOLDER = "#{BS_DIR}/free_tools/jobs".freeze
FILMSTRIP_JOB_RELATIVE_PATH = "result/pages/**/data/filmstrip".freeze
COACH_RUN_JOB_RELATIVE_PATH = "result/pages/**/data/coach.run".freeze
BROWSERTIME_RUN_JOB_RELATIVE_PATH = "result/pages/**/data/browsertime.run".freeze

EVENT_NAMES = ["fcp", "vc_85", "vc_95", "vc_99", "last_vc", "page_load_time", "dom_content_load_time"].freeze

LIGHTHOUSE_LOG_PATH = "/var/log/browserstack/freetools_lighthouse.log".freeze
LIGHTHOUSE_LOGGER = Logger.new(LIGHTHOUSE_LOG_PATH.to_s)

def sitespeed_har_upload(folder_name, job_id, device_name)
  har_file_path = "#{FREETOOLS_JOBS_FOLDER}/#{folder_name}/result/pages/**/browsertime.har"
  status, absolute_har_file_path = validate_har_file(har_file_path)
  return {} unless status

  begin
    hars = []
    har_data = JSON.parse(File.read(absolute_har_file_path))
    hars_exit_code = {}
    run_datas = har_data['log']['pages'].dup
    har_data['log']['pages'] = []
    puts "Splitting and Uploading #{run_datas.length} runs"
    run_datas.length.times do |i|
      if !run_datas[i]['id'].include?('failing_page')
        hars[i] =   Marshal.load(Marshal.dump(har_data))
        hars[i]['log']['pages'].push(run_datas[i])
        hars_exit_code["run-#{i + 1}"] = 0
      else
        hars_exit_code["run-#{i}"] = 1
        next
      end
    end

    hars.each_with_index do |har_run_data, i|
      s3_object_path = "data/har/#{job_id}/#{device_name}/run-#{i + 1}.har"
      hars_exit_code["run-#{i}"] = upload_file_to_s3(s3_object_path, nil, folder_name, "har", har_run_data.to_json)
    end
  rescue StandardError => e
    puts "Run wise har upload failed with#{e.message}"
  end
  hars_exit_code
end

def validate_har_file(har_file_path)
  max_har_size_allowed = 5 * 1024 * 1024
  possible_paths = Dir.glob(har_file_path)
  if possible_paths.empty?
    puts "HAR file not present"
    zombie_push('android', 'har-file-error', "HAR files doesn't exist, folder: #{har_file_path}", '', '')
    [false, '']
  elsif File.size(possible_paths[0]) > max_har_size_allowed
    puts "HAR file too large"
    zombie_push('android', 'har-file-error', "HAR files size too big, folder:  #{har_file_path}", '', '')
    [false, '']
  else
    [true, possible_paths[0]]
  end
end

def get_uploading_params(folder_name)
  params = File.read("#{FREETOOLS_JOBS_FOLDER}/#{folder_name}/params.txt")
  params_hash = JSON.parse(params.gsub("=>", ":").gsub(":nil,", ":null,"))
  if valid_aws_params?(params_hash)
    [false, params_hash["region"], params_hash["key"], params_hash["secret"], params_hash["bucket"]]
  else
    puts "can't fetch required params"
    [true]
  end
rescue StandardError
  zombie_push('android', 'har-file-error', "getting key from params failed, folder:  #{folder_name}", '', '')
  [true]
end

def valid_aws_params?(params_hash)
  params_hash.key?("region") && params_hash.key?("key") &&
         params_hash.key?("secret") && params_hash.key?("bucket")
end

def filmstrip_check(folder_name)
  params = File.read("#{FREETOOLS_JOBS_FOLDER}/#{folder_name}/params.txt")
  params_hash = JSON.parse(params.gsub("=>", ":").gsub(":nil,", ":null,"))
  if filmstrip_upload_allowed?(params_hash)
    puts "filmstrip upload not allowed"
    return 1
  end
  filmstrip_folders = Dir.glob("#{FREETOOLS_JOBS_FOLDER}/#{folder_name}/#{FILMSTRIP_JOB_RELATIVE_PATH}/*")
  if filmstrip_folders.empty?
    puts "Filmstrip folders not found"
    return 1
  end
  browser_time_runs_files = Dir.glob("#{FREETOOLS_JOBS_FOLDER}/#{folder_name}"\
                                     "/#{BROWSERTIME_RUN_JOB_RELATIVE_PATH}*.json")
  if browser_time_runs_files.empty?
    puts "browsertime files not found"
    return 1
  end
  puts "filmstrip-check passed"
  0
end

def filmstrip_upload_allowed?(params_hash)
  !params_hash.key?("filmstrip_upload") || (params_hash.key?("filmstrip_upload") &&
                                                   params_hash["filmstrip_upload"] == false)
end

def sitespeed_filmstrip_upload(folder_name, job_id, device_name)
  browser_time_runs_files = Dir.glob("#{FREETOOLS_JOBS_FOLDER}/#{folder_name}"\
                                     "/#{BROWSERTIME_RUN_JOB_RELATIVE_PATH}*.json")
  error, region, key, secret, bucket_name = get_uploading_params(folder_name)
  if error == true
    puts "error in getting s3 creds"
    return {}
  end
  film_exit_codes = {}
  browser_time_runs_files.each do |run_file|
    run_name = run_file.split('.')[-2]
    film_exit_codes[run_name] = 1
    filmstrip_regex_path = "#{FREETOOLS_JOBS_FOLDER}/#{folder_name}"\
                           "/#{FILMSTRIP_JOB_RELATIVE_PATH}/#{run_name.split('-')[1]}"
    filmstrip_folders = Dir.glob(filmstrip_regex_path)
    if filmstrip_folders.empty?
      puts "filmstrip folder not found, for #{filmstrip_regex_path}"
      next
    end
    filmstrip_files = Dir.entries(filmstrip_folders[0]) - [".", ".."]
    filmstrip_files.map! { |filmstrip_file| (filmstrip_file.scan(/\d+/)[0]).to_i }
    browsertime_metrics = JSON.parse(File.read(run_file))
    EVENT_NAMES.each do |event_name|
      success, value = send("get_#{event_name}_value", browsertime_metrics)
      if success == true
        find_and_upload_filmstrip(folder_name, job_id, device_name, value, run_name, event_name, filmstrip_files)
      else
        puts "#{event_name} value not found, for: #{run_file}"
      end
      film_exit_codes[run_name] = 0 if success
    end
  end
  puts "Uploaded all the required filmstrips"
  film_exit_codes
end

def find_and_upload_filmstrip(folder_name, job_id, device_name, metric_value, run_name, event_name, filmstrip_files)
  closest = get_closest(filmstrip_files, metric_value)
  puts "metric_value: #{metric_value}, event_name: #{event_name}, closest: #{closest}"
  closest_file_path = Dir.glob("#{FREETOOLS_JOBS_FOLDER}/#{folder_name}"\
                              "/#{FILMSTRIP_JOB_RELATIVE_PATH}/#{run_name.split('-')[1]}/*#{closest}*")
  upload_s3_path = "data/filmstrip/#{job_id}/#{device_name}/#{run_name}/#{event_name}"
  upload_file_to_s3(upload_s3_path, closest_file_path[0], folder_name, "filmstrip")
rescue StandardError => e
  puts "Exception in uploading filmstrip: #{e.message}"
  zombie_push('android', 'filmstrip-upload-error', "Exception in uploading #{folder_name}, #{run_name}, #{event_name}",
              '', '')
end

def upload_coach(job_id, device_name, folder_name, mode)
  coach_runs_files = (
    if mode == "sitespeed"
      Dir.glob("#{FREETOOLS_JOBS_FOLDER}/#{folder_name}/#{COACH_RUN_JOB_RELATIVE_PATH}*.json")
    else
      Dir.glob("#{FREETOOLS_JOBS_FOLDER}/#{folder_name}/run-*/sitespeedRecommendations.json")
    end
  )
  if coach_runs_files.empty?
    s3_log("Coach files not found", mode)
    return 1
  end
  s3_log("Found Coach files #{coach_runs_files}", mode)
  return_codes = {}
  coach_runs_files.each do |file_path|
    run_name = mode == "sitespeed" ? file_path.split('.')[-2] : file_path.split('.')[-2].split('/')[-2]
    begin
      coach_data = JSON.parse(File.read(file_path))
      best_practices = begin
        coach_data['advice']['bestpractice']['adviceList']
      rescue StandardError
        {}
      end
      relevant_best_practices = best_practices.reject { |_k, v| v['score'] == 100 }

      performance = begin
        coach_data['advice']['performance']['adviceList']
      rescue StandardError
        {}
      end
      relevant_performance = performance.reject { |_k, v| v['score'] == 100 }

      s3_log("Best practices Advice: #{relevant_best_practices.keys}", mode)
      s3_log("Performance Advice: #{performance.keys}", mode)

      body_data = {
        best_practices: relevant_best_practices,
        performance: relevant_performance
      }.to_json

      s3_object_path = "data/coach/#{job_id}/#{device_name}/#{run_name}/audit.json"
      s3_log("S3 Object Path: #{s3_object_path}", mode)
      upload_result = upload_file_to_s3(s3_object_path, nil, folder_name, "coach", body_data)
      s3_log("Upload Coach files failed for #{file_path}", mode) if upload_result == 1
      return_codes[run_name] = upload_result
    rescue StandardError => e
      s3_log("Exception in uploading coach for #{file_path}: #{e.message}", mode)
      return_codes[run_name] = 1
    end
  end
  return_codes
end

def upload_file_to_s3(s3_url, file_path, folder_name, content, body_data = nil)
  error, region, key, secret, bucket_name = get_uploading_params(folder_name)
  if error == true
    puts "error in getting s3 creds during #{content} upload"
    return 1
  end
  s3 = Aws::S3::Resource.new(access_key_id: key, secret_access_key: secret, region: region)
  bucket = s3.bucket(bucket_name)
  s3_object = bucket.object(s3_url)
  s3_object.upload_file(file_path) if file_path
  if body_data
    s3_object.put({
      body: body_data
    })
  end
  if s3_object.exists?
    puts "successful upload, #{s3_object.public_url} for: #{s3_url} "
    0
  else
    puts "Failed upload."
    zombie_push('android', "#{content}-upload-error", "#{content} upload failed, folder: #{s3_url}", '', '')
    1
  end
end

def get_closest(timestamps, value)
  closest = timestamps[0]
  timestamps.each do |timestamp|
    closest = timestamp if (value - closest).abs > (value - timestamp).abs
  end
  closest
end

def get_fcp_value(browsertime_metrics)
  [true, browsertime_metrics["timings"]["paintTiming"]["first-contentful-paint"]]
rescue StandardError => e
  puts "Exception in getting fcp: #{e.message}"
  [false, 0]
end

def get_vc_85_value(browsertime_metrics)
  [true, browsertime_metrics["visualMetrics"]["VisualComplete85"]]
rescue StandardError => e
  s3_log "Exception in getting vc_85: #{e.message}"
  [false, 0]
end

def get_vc_95_value(browsertime_metrics)
  [true, browsertime_metrics["visualMetrics"]["VisualComplete95"]]
rescue StandardError => e
  s3_log "Exception in getting vc_95: #{e.message}"
  [false, 0]
end

def get_vc_99_value(browsertime_metrics)
  [true, browsertime_metrics["visualMetrics"]["VisualComplete99"]]
rescue StandardError => e
  s3_log "Exception in getting vc_99: #{e.message}"
  [false, 0]
end

def get_last_vc_value(browsertime_metrics)
  [true, browsertime_metrics["visualMetrics"]["LastVisualChange"]]
rescue StandardError => e
  s3_log "Exception in getting last_vc: #{e.message}"
  [false, 0]
end

def get_page_load_time_value(browsertime_metrics)
  [true, browsertime_metrics["timings"]["pageTimings"]["pageLoadTime"]]
rescue StandardError => e
  puts "Exception in getting page load time: #{e.message}"
  [false, 0]
end

def get_dom_content_load_time_value(browsertime_metrics)
  [true, browsertime_metrics["timings"]["pageTimings"]["domContentLoadedTime"]]
rescue StandardError => e
  puts "Exception in getting dom content load time: #{e.message}"
  [false, 0]
end

def s3_log(log, mode="sitespeed")
  LIGHTHOUSE_LOGGER.info { log } if mode != "sitespeed"
end

if $PROGRAM_NAME == __FILE__
  case ARGV[0]
  when "har-upload"
    exit_code = sitespeed_har_upload(ARGV[1], ARGV[2], ARGV[3])
    exit(exit_code['run-1'].to_i)
  when "filmstrip-upload"
    exit_code = sitespeed_filmstrip_upload(ARGV[1], ARGV[2], ARGV[3])
    exit(exit_code['run-1'].to_i)
  when "filmstrip-check"
    exit_code = filmstrip_check(ARGV[1])
  end
  exit(exit_code.to_i)
end
