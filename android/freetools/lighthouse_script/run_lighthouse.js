const fs = require('fs');
const path = require('path');
const { spawnSync } = require('child_process');
const args = process.argv.slice(2);

(async () => {
  let nodeErrors = [];
  let startTime = new Date();
  const lighthousePath = args[0];
  const outputPath = args[1];

  process.on('exit', (code = 1) => {
    console.log("Exiting Lighthouse process");
    if (nodeErrors.length !== 0) {
      fs.writeFileSync(`${outputPath}/log.json`, JSON.stringify({nodeErrors}));
    }
    clearUsedPorts();
    process.exit(code)
  });
  process.on('SIGTERM', () => {
    nodeErrors.push({
      'success': false,
      'Run: ': '-1',
      error: "EXTERNAL_TIMEOUT"
    });
    console.log('SIGTERM called, exiting')
    process.exit(3);
  });

  const confPath = `${outputPath}/node_params.json`;
  let url, parsedOptions;

  try{
    const confData = fs.readFileSync(confPath, {encoding:'utf8', flag:'r'});
    url = JSON.parse(confData)["job_config"]["url"];
  } catch(err) {
    nodeErrors.push(err);
    process.exit(2);
  }
  const numberOfRuns = args[2];
  const jobId = args[3];
  const deviceId = args[4];
  const providedOptions = args[5] || '{}';
  const MAX_INVALID_RUNS = args[6] || 3;
  let inValidMetricRuns = 0;

  try {
    parsedOptions = JSON.parse(providedOptions);
    const lighthouse = require(lighthousePath + "/node_modules/lighthouse/lighthouse-core/index.js");
    const {fromLog} = require(lighthousePath + "/node_modules/chrome-har-capturer/index.js");
    console.log(`[JOBID: ${jobId}] Starting lighthouse with: ${JSON.stringify(args)}`);
    let successfulRuns = 0;
    while (successfulRuns < numberOfRuns && inValidMetricRuns <= MAX_INVALID_RUNS) {
      fs.mkdirSync(outputPath + `/run-${successfulRuns + 1}`, {recursive: true});
      let isSuccess = await runHandler(successfulRuns + 1, lighthouse, fromLog, parsedOptions);
      if (isSuccess) {
        successfulRuns++;
      }
    }

    let endTime = new Date();
    let diff = (endTime - startTime) / 1000;
    fs.writeFileSync(outputPath + "/.time", diff + "");
    console.log(`[JOBID: ${jobId}] Finished`);
    console.log(`Script took ${diff} seconds`);
    if(inValidMetricRuns >= MAX_INVALID_RUNS){
      console.log("Exiting due to error in URL ", url);
      nodeErrors.push({
        'success': false,
        'Run: ': '-1',
        error: "URL_FAILURE"
      });
      process.exit(1);
    }
    process.exit(0);
  } catch (err) {
    console.log(`[JOBID: ${jobId}] Failed. Error: ${err.stack || err.message}`);
    nodeErrors.push({
      'success': false,
      'Run: ': 'Process failure',
      error: err.stack || err.message
    })
    process.exit(1);
  }

  function logAndRun(command, args) {
    let result = spawnSync(command, args);
    console.log(result.output?.toString() || result);
  }

  function prepareDeviceForLighthouse(deviceId, port) {
    logAndRun('adb', ["-s", `${deviceId}`, `forward`, `tcp:${port}`, "localabstract:chrome_devtools_remote"]);
    logAndRun('bash', ["-c", `/usr/local/.browserstack/mobile/android/driver_actions.sh device_screen ${deviceId} > /dev/null`]);
    logAndRun('adb', ["-s", `${deviceId}`, "shell", `am force-stop`, `com.android.chrome`]);
    logAndRun('adb', ["-s", `${deviceId}`, "shell", "am start", "-n", `com.android.chrome/com.google.android.apps.chrome.Main`]);
    logAndRun('sleep', [5]);
  }

  function postLighthouseRun(deviceId, port) {
    logAndRun('adb', ["-s", `${deviceId}`, `forward`, "--remove", `tcp:${port}`]);
  }

  async function runHandler(i, lighthouse, fromLog, parsedOptions) {
    let port;
    port = getFreePort();
    try {
      let runStartTime = new Date();

      console.log(`[JOBID: ${jobId}] Run ${i} started`);

      const options = {
        port: port,
        output: ['html', 'json'],
        "screenEmulation": {
          "disabled": true
        },
        "emulatedUserAgent": "false",
        "formFactor": "mobile",
        "onlyCategories": [
          "best-practices",
          "performance",
          "navigationTiming"
        ],
        ...parsedOptions
      };
      const settings = {
        "extends": "lighthouse:default",
        passes: [
          {
            passName: 'defaultPass', // Needed to run custom Gatherers/Audits in the same pass
            useThrottling: false,
            gatherers: [path.join(__dirname, 'performance_gather.js'), path.join(__dirname, 'recommendation_gather.js')]
          }
        ],
        audits: [path.join(__dirname, 'performance_audit.js'), path.join(__dirname, 'recommendation_audit.js')],
        categories: {
          navigationTiming: {
            title: 'Navigation metrics & Sitespeed Recommendations',
            description: 'Performance metrics Navigation API & SiteSpeed DOM Advices',
            auditRefs: [
              // When we add more custom audits, `weight` controls how they're averaged together.
              {id: 'performance_audit', weight: 0},
              {id: 'recommendation_audit', weight: 0}
            ]
          }
        }
      };

      prepareDeviceForLighthouse(deviceId, port);

      const result = await lighthouse(url, options, settings);
      let score = result.lhr.categories.performance.score;
      if(!score) {
        inValidMetricRuns++;
        postLighthouseRun(deviceId, port);
        return false
      }

      try{
        let har = await fromLog(url, result.artifacts?.devtoolsLogs.defaultPass);
        if(har) fs.writeFileSync(`${outputPath}/run-${i}/report.har`, JSON.stringify(har));
      } catch(err) {
        console.log('Error Capturing HAR: ', err);
      }
      const navMetrics = result.artifacts?.Performance;
      const sitespeedRecommendations = result.artifacts?.Recommendation;
      fs.writeFileSync(`${outputPath}/run-${i}/report.json`, JSON.stringify(result.lhr));
      if (navMetrics) fs.writeFileSync(`${outputPath}/run-${i}/navMetrics.json`, navMetrics);
      if (sitespeedRecommendations) fs.writeFileSync(`${outputPath}/run-${i}/sitespeedRecommendations.json`, sitespeedRecommendations);
      let filmstrip = result.lhr?.audits["screenshot-thumbnails"]?.details.items;
      if(filmstrip){
        fs.mkdirSync(`${outputPath}/run-${i}/filmstrip`, {recursive: true});
        for (let j = 0; j < filmstrip.length; j++) {
          let base64Data = filmstrip[j].data;
          base64Data = base64Data.split(",")[1];
          fs.writeFileSync(`${outputPath}/run-${i}/filmstrip/${j}_${filmstrip[j].timing}.jpeg`, base64Data, 'base64');
        }
      }
      if(result.report[0]){
        fs.writeFileSync(`${outputPath}/run-${i}/result.html`, result.report[0]);
      }

      fs.writeFileSync(`${outputPath}/run-${i}/result.json`, JSON.stringify({'success': true}));
      let runEndTime = new Date();
      let diff = (runEndTime - runStartTime) / 1000;
      console.log(`[JOBID: ${jobId}] Run ${i} took ${diff} seconds`);
      fs.writeFileSync(`${outputPath}/run-${i}/.time`, diff + "");
      console.log(`[JOBID: ${jobId}] Run ${i} finished`);
      postLighthouseRun(deviceId, port);
      return true;
    } catch (err) {
      postLighthouseRun(deviceId, port);
      let filmstripDirectory = `${outputPath}/run-${i}/filmstrip`
      try {
        let files = fs.readdirSync(filmstripDirectory);
        const promises = files.map(file => fs.promises.unlink(path.join(filmstripDirectory, file)));
        await Promise.all(promises);
      } catch (error) {
        console.log(`Deletion failed. Error: ${error.message}`);
        nodeErrors.push(error.message);
      }
      console.log(`[JOBID: ${jobId}] Run ${i} failed. Error: ${err.stack || err.message}`);
      nodeErrors.push({
        'success': false,
        'Run: ': i,
        error: err.stack || err.message
      })
      return false;
    }
  }

  function clearUsedPorts(device = args[5]){
    let connList = spawnSync('adb', [`forward`, "--list"]);
    //gives list like: 'RFCR109EMVE tcp:9224 localabstract:chrome_devtools_remote\n'
    let ports = connList.output.toString().split('\n').filter(p => p.match(device) && p.match('localabstract:chrome_devtools_remote')).map(x => x.split(' ')[1].split(':')[1])
    ports.forEach(port => postLighthouseRun(device, port));
  }

})();

function getFreePort(retry = true) {
  try {
    let process = spawnSync("bash", ["-c", "comm -23 <(seq 9222 9300 | sort) <(ss -tan | awk '{print $4}' | cut -d':' -f2 | sort -u) |  head -n 1"]);
    let output = process.stdout?.toString(); // output is like 9222\n
    let port = parseInt(output);
    // double check the port
    if (spawnSync('lsof', [`-i:${port}`]).stdout?.toString() === '') {
      console.log(`Using port ${port}`);
      return port;
    } else {
      if (retry) {
        port = getFreePort(false);
        return port;
      }
      return false;
    }
  } catch (e) {
    console.log(`Error ${e.stackTrace || e.message} on finding port`);
    if (retry) {
      port = getFreePort(false);
      return port;
    }
    return false;
  }
}



