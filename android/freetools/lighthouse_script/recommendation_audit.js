'use strict';

const lighthousePath = process.env.LIGHTHOUSE_V9
const { Audit } = require(`${lighthousePath}/node_modules/lighthouse/lighthouse-core/index.js`);

class RecommendationAudit extends Audit {
  static get meta() {
    return {
      id: 'recommendation_audit',
      title: 'Sitespeed Dom Advices',
      failureTitle: 'Sitespeed DOM Advices',
      description: 'Sitespeed DOM Advices',
      requiredArtifacts: ['Recommendation'],
    };
  }

  static audit(artifacts) {
    const recommendation = artifacts.Recommendation;
    console.log(recommendation)
    return {
      score: 1,
      details: {
        type: 'recommendation',
        recommendation: artifacts.Recommendation,
      },
    };
  }
}
module.exports = RecommendationAudit;
