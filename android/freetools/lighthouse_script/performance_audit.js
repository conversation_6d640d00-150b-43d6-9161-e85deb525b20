'use strict';

const lighthousePath = process.env.LIGHTHOUSE_V9
const { Audit } = require(`${lighthousePath}/node_modules/lighthouse/lighthouse-core/index.js`);

class NavAPIAudit extends Audit {
  static get meta() {
    return {
      id: 'performance_audit',
      title: 'Performance metrics Navigation API',
      failureTitle: 'Performance metrics Navigation API',
      description: 'Performance metrics Navigation API',
      requiredArtifacts: ['Performance'],
    };
  }

  static audit(artifacts) {
    const measure = artifacts.Performance;
    console.log(measure)
    return {
      score: 1,
      details: {
        type: 'performance',
        performance: artifacts.Performance,
      },
    };
  }
}
module.exports = NavAPIAudit;
