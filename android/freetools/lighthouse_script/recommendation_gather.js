'use strict';

const lighthousePath = process.env.LIGHTHOUSE_V9
const { Gatherer } = require(`${lighthousePath}/node_modules/lighthouse/lighthouse-core/index.js`);
const sitespeedCoach = require('./coach.js');

class Recommendation extends Gatherer {
  afterPass(options) {
    const driver = options.driver;
    return driver.evaluateAsync(`JSON.stringify((${sitespeedCoach.fn.toString()})())`);
  }
}

module.exports = Recommendation;
