# This script serves as the main entry point for running Maestro sessions.
# It provides methods to initialize, execute, and manage test sessions using the Maestro framework.

require "browserstack_logger" # Logger for BrowserStack-specific logs.
require "json"               # JSON parsing and generation.
require "fileutils"          # File utilities for file operations.

# Load helper modules and classes.
require_relative "../../helpers/utils"
require_relative '../../../common/helpers'
require_relative "../../exit_file"
require_relative "../../lib/os_utils"
require_relative "../helpers/maestro_helper_runner"
require_relative "../../../common/push_to_zombie"
require_relative "../helpers/maestro_helper"
require_relative "../../constants"

# Constants for paths and configurations
BS_HOME = "/usr/local/.browserstack".freeze
MOBILE_COMMON_HOME = "#{BS_HOME}/mobile-common".freeze
MAESTRO_TIMEOUT_MANAGER_FILE = \
  "#{MOBILE_COMMON_HOME}/frameworks_timeout_manager/app_automate_frameworks/maestro_time_out_manager.rb".freeze

SERVER_LOG = "/var/log/browserstack/server.log".freeze

# Conditionally require the MaestroTimeoutManager based on a specific condition.
require(MAESTRO_TIMEOUT_MANAGER_FILE) if File.exist?(MAESTRO_TIMEOUT_MANAGER_FILE)

# Namespace for Maestro-related functionality.
module Maestro
  # Class to manage and execute Maestro test sessions.
  class SessionRunner # rubocop:disable Metrics/ClassLength
    # Entry point for running the session from a bash script.
    #
    # This method parses command-line arguments, initializes the session runner,
    # and invokes the specified function.
    def self.run_from_bash
      # Ensure there are enough arguments provided.
      raise StandardError, "Not enough arguments" if ARGV.size < 9

      # Parse command-line arguments.
      function_to_call = ARGV[0].to_s.strip
      device_id = ARGV[1].to_s.strip
      device_model = ARGV[2].to_s.strip
      device_name = ARGV[3].to_s.strip
      device_version = ARGV[4].to_s.strip
      test_framework = ARGV[5].to_s.strip || "maestro"
      test_suite_download_path = ARGV[6].to_s.strip
      instrumentations_logs_file_path = ARGV[7].to_s.strip
      session_summary_file_path = ARGV[8].to_s.strip
      callback_file_path = ARGV[9].to_s.strip
      args = ARGV[10..] # Additional arguments.

      # Initialize the session runner with the parsed arguments.
      session_runner = Maestro::SessionRunner.new(
        device_id, device_model, device_name, device_version, test_framework, test_suite_download_path,
        instrumentations_logs_file_path, session_summary_file_path, callback_file_path
      )

      # Dynamically call the specified function with the additional arguments.
      session_runner.send(function_to_call, *args)
    rescue StandardError => e
      log("Error in  run_from_bash: #{e.message}/#{e.backtrace}", severity: "ERROR")
      # Write the error message to the exit file and re-raise the exception.
      ExitFile.write(e.message[0..200])
      raise e
    end

    # Initializes the session runner with the required parameters.
    #
    # @param device [String] ID of the device.
    # @param device_model [String] Model of the device.
    # @param device_name [String] Name of the device.
    # @param device_version [String] Version of the device.
    # @param test_framework [String] Name of the test framework (e.g., 'maestro').
    # @param test_suite_download_path [String] Path to the downloaded test suite.
    # @param instrumentations_logs_file_path [String] Path to the instrumentation logs file.
    # @param session_summary_file_path [String] Path to the session summary file.
    # @param callback_file_path [String] Path to the callback file.
    def initialize(device, device_model, device_name, device_version, test_framework, test_suite_download_path,
                   instrumentations_logs_file_path, session_summary_file_path, callback_file_path)
      # Ensure the device ID is not empty.
      raise "Device cannot be empty" if device.nil? || device == ""

      BrowserStack.init_logger(SERVER_LOG)
      # Initialize instance variables with the provided parameters.
      @session_summary_file_path = session_summary_file_path
      @instrumentation_logs_file_path = instrumentations_logs_file_path
      @callback_file_path = callback_file_path
      @device = device
      @device_name = device_name
      @device_model = device_model
      @device_version = device_version
      @test_framework = test_framework
      @test_suite_download_path = test_suite_download_path

      # Parse the session summary file.
      @session_summary_file = begin
        JSON.parse(File.read(@session_summary_file_path))
      rescue StandardError
        {}
      end

      # Extract the session ID from the summary file.
      @session_id = @session_summary_file["session_id"]
    end

    # Executes a dry run of the test suite.
    #
    # @param args [Array<String>] Additional arguments for the dry run.
    def dry_run(*args)
      test_suite_filename = args[0].to_s.strip
      test_suite_file_path = "/tmp/#{@test_framework}_testsuite_#{@device}"
      log("Executing Dry Run")

      # Unzip the folder in test_suite_download_path.
      unzip_test_suite_folder(@test_suite_download_path)

      # Create necessary files for the dry run.
      create_files(test_suite_file_path)

      # Execute the dry run and check for errors.
      execute_dry_run
      mh = MaestroHelper.new(@session_summary_file_path)
      mh.check_for_testsuite_parse_failure(@instrumentation_logs_file_path, callback_data,
                                           @device_model, @device, @device_name,
                                           @test_framework)
      update_summary_and_callback_file
      mh.parse_execution_plan(debug_output_path, test_suite_filename)
      check_if_testsuite_parse_empty(test_suite_filename)
    rescue StandardError => e
      # Log and handle errors during the dry run.
      BrowserStack.logger.error "Error in dry run script: #{e.message} #{e.backtrace}"
      ExitFile.write(e.message[0..200])
      raise e
    end

    # Retrieves the callback data from the callback file.
    #
    # This method reads and parses the callback file to extract the callback data.
    # If the file cannot be read or parsed, it returns an empty hash.
    #
    # @return [Hash] The parsed callback data or an empty hash if an error occurs.
    def callback_data
      @callback_data ||= begin
        # Read and parse the callback file.
        JSON.parse(File.read(@callback_file_path))
      rescue StandardError
        # Return an empty hash if an error occurs during file reading or parsing.
        {}
      end
    end

    # Checks if the test suite parse result is empty.
    #
    # This method verifies whether the test suite file exists and is not empty.
    # If the file is empty or missing, it updates the session summary and callback file
    # with an error reason and pushes the error to Zombie.
    #
    # @param test_suite_file_path [String] Path to the test suite file.
    def check_if_testsuite_parse_empty(test_suite_file_path)
      # Return early if there is already an error reason in the callback data.
      return unless callback_data && callback_data["error_reason"].to_s == ""

      # Check if the test suite file is missing or empty.
      if !File.file?(test_suite_file_path) || File.empty?(test_suite_file_path)
        zombie_error_message = 'testsuite-parse-empty'

        # Push the error to Zombie with relevant details.
        zombie_push("android", zombie_error_message, "Testsuite file empty", @device_model, "", @device, @session_id)

        # Update the session summary and callback file with the error reason.
        @session_summary_file["error_reason"] = "testsuite-parse-empty"
        callback_data["error_reason"] = "testsuite-parse-empty"
        update_summary_and_callback_file
      end
    end

    # Updates the session summary and callback file with the latest data.
    #
    # This method writes the updated session summary and callback data to their respective files.
    def update_summary_and_callback_file
      # Write the updated session summary to the session summary file.
      File.open(@session_summary_file_path, "w+") do |f|
        f.write(@session_summary_file.to_json)
      end

      # Write the updated callback data to the callback file.
      File.open(@callback_file_path, "w+") do |f|
        f.write(callback_data.to_json)
      end
    end

    # Creates necessary files for the test suite.
    #
    # @param test_suite_file_path [String] Path to the test suite file.
    def create_files(test_suite_file_path)
      FileUtils.touch test_suite_file_path
      FileUtils.touch @instrumentation_logs_file_path
    end

    # Executes the dry run command for the test suite.
    def execute_dry_run
      log("running dry run #{maestro_params}")
      # call session details
      session_details
       # Parse Maestro parameters from the session summary file
      maestro_params

      flows = begin
        @maestro_params[:execute]
      rescue StandardError
        []
      end
      js_engine = @maestro_params[:js_engine]
      maestro_version = @maestro_params[:maestro_version] || default_maestro_version
      driver_host_port = @maestro_params[:driver_host_port]

      # Construct the command for the dry run.
      zip_path = Dir.glob("#{test_suite_unzip_path}/*")[0]
      flows_to_execute = ""
      if flows.empty?
        flows_to_execute = zip_path
      else
        flows.each do |flow|
          flows_to_execute += \
            " #{zip_path}/#{flow}"
        end
      end

      cmd = "timeout 120s"
      cmd += " env MAESTRO_CLI_NO_ANALYTICS=true"
      cmd += " MAESTRO_USE_GRAALJS=true" if js_engine == "graaljs"
      cmd += " #{get_maestro_build_path(maestro_version)} \
            --device #{@device} --driver-host-port #{driver_host_port} \
            test --debug-output #{debug_output_path} --flatten-debug-output --dry-run \
            #{flows_to_execute} > #{@instrumentation_logs_file_path} 2>&1"

      log("cmd =  #{cmd}")
      # Retry logic for the dry run command.
      delay = 3
      try = 1
      max_retries = 3
      dry_run_output = ""
      while try <= max_retries
        OSUtils.execute(cmd, true)
        dry_run_output = File.read(@instrumentation_logs_file_path)
        log("dry_run_output : #{dry_run_output}")
        # Check for retry conditions.
        dry_run_retry_reason = if dry_run_output.strip.empty?
                                 "dry-run-empty"
                               elsif !dry_run_output.include?("Execution Plan")
                                 "dry-run-crashed"
                               end

        # Retry if the process crashed or the output is empty.
        break unless dry_run_retry_reason

        kind = "maestro-#{dry_run_retry_reason}"
        data = { attempt: try, reason: dry_run_retry_reason }.to_json
        zombie_push("android", kind, "", "", data, @device, @session_id)

        try += 1
        log("Command failed. Attempt #{try}/#{max_retries} with #{dry_run_retry_reason}")
        sleep delay
      end
    rescue StandardError => e
      # Handle errors during the dry run execution.
      log("Error: #{e.message} #{e.backtrace}", severity: "ERROR")
    end

    # Invokes a test execution with the provided parameters.
    #
    # This method prepares the test environment, sets up retry logic, and executes the test.
    #
    # @param args [Array<String>] Command-line arguments for the test execution.
    #   - args[0]: Test name.
    #   - args[1]: Test bundle ID.
    #   - args[2]: Unique test ID.
    #   - args[3]: Expire time for the test execution.
    #   - args[4]: Retry attempt count (optional).
    #   - args[5]: Maximum retry count (optional).
    #   - args[6]: Device orientation (optional).
    def invoke_test(*args)
      # Parse the arguments.
      test_name = args[0].to_s.strip # Name of the test.
      @unique_test_id = args[1].to_s.strip # Unique identifier for the test.
      expire_time = args[2].to_s.strip # Expiration time for the test execution.

      # Parse optional arguments for retry logic.
      retry_attempt = begin
        args[3].to_i
      rescue StandardError
        nil
      end

      max_test_retry_count = begin
        args[4].to_i
      rescue StandardError
        nil
      end

      # # Parse optional argument for device orientation.
      # device_orientation = args[6].to_s.strip

      # Execute the test with the provided parameters.
      run_test(
        expire_time,
        test_name,
        retry_attempt: retry_attempt,
        max_test_retry_count: max_test_retry_count
      )
    rescue StandardError => e
      # Log the error and write it to the exit file.
      BrowserStack.logger.error "Error initializing test: #{test_name} #{e.message} #{e.backtrace}"
      ExitFile.write(e.message[0..200])
      raise e
    end

    private

    # Retrieves the default version of the Maestro framework.
    #
    # This method fetches the default Maestro version from the configuration constants.
    #
    # @return [String] The default version of the Maestro framework.
    def default_maestro_version
      # Fetch the default Appium version from the MAESTRO_CONFIG constant.
      MAESTRO_CONFIG[:default_version]
    end

        # Checks for instrumentation errors in the logs.
    #
    # This method uses the `MaestroHelper` class to analyze the instrumentation logs
    # and identify any errors that occurred during the test execution.
    #
    # @return [void]
    def check_for_instrumentation_error
      # Initialize the MaestroHelper instance with the session summary file path.
      maestro_helper = MaestroHelper.new(@session_summary_file_path)

      # Check the instrumentation logs for errors.
      maestro_helper.check_instrumentation_error(
        @instrumentation_logs_file_path, # Path to the instrumentation logs file.
        @callback_file_path,             # Path to the callback file.
        @device_model,                   # Model of the device.
        @device,                         # ID of the device.
        @device_name,                    # Name of the device.
        @device_version,                 # Version of the device.
        @test_framework                  # Name of the test framework (e.g., 'maestro').
      )
    end

    # Retrieves the session details from a temporary file.
    #
    # This method reads and parses the session details file located in the `/tmp` directory.
    # If the file cannot be read or parsed, it returns an empty hash.
    #\
    # @return [Hash] The parsed session details or an empty hash if an error occurs.
    def session_details
      @session_details ||= begin
        # Read and parse the session details file.
        JSON.parse(File.read("/tmp/duplicate_session_#{@device}"))
      rescue StandardError
        # Return an empty hash if an error occurs during file reading or parsing.
        {}
      end
    end

    # Checks if a specific parameter exists in the session details.
    #
    # This method verifies whether the given parameter exists and is not empty
    # in the session details hash.
    #
    # @param param [String] The parameter key to check in the session details.
    # @return [Boolean] True if the parameter exists and is not empty, false otherwise.
    def session_details_param_exists?(param)
      # Check if the parameter exists and is not empty in the session details.
      !(session_details[param].nil? || session_details[param] == '{}')
    end

    # Retrieves the test parameters from the session details.
    #
    # This method fetches the "test_params" key from the session details if it exists.
    # If the key does not exist, it returns an empty hash.
    #
    # @return [Hash] The test parameters or an empty hash if not found.
    def test_params
      # Fetch the "test_params" key from the session details if it exists.
      @test_params ||= (session_details_param_exists?("test_params") ? session_details["test_params"] : {})
    end

    def maestro_params
      # Fetch the "test_params" key from the session details if it exists.
      params = begin
        JSON.parse(test_params)
      rescue StandardError
        test_params
      end
      @maestro_params  = {
        'execute': params["execute"] || [],
        'driver_host_port': params["maestro_host_port"] || get_device_port(@device).to_i + 300,
        'js_engine': params["js_engine"] || ""
      }
    end

    # Retrieves the unique test ID or session ID.
    #
    # This method returns the unique test ID if it exists; otherwise, it falls back
    # to the session ID.
    #
    # @return [String] The unique test ID or session ID.
    def test_or_session_id
      # Return the unique test ID if it exists; otherwise, return the session ID.
      @unique_test_id || @session_id
    end

    # Parses the test parameters from a JSON string.
    #
    # This method parses the provided JSON string to extract test parameters.
    # If the JSON string is empty or invalid, it logs an error and raises an exception.
    #
    # @param test_params_json [String] The JSON string containing test parameters.
    # @return [Hash] The parsed test parameters.
    def get_test_params(test_params_json)
      test_params_parsed = ""

      # Parse the JSON string if it is not empty.
      test_params_parsed = JSON.parse(test_params_json) unless test_params_json.empty?

      # Log the parsed test parameters.
      log("test_params_parsed: #{test_params_parsed}")
      test_params_parsed
    rescue StandardError => e
      # Log the error and raise it for further handling.
      log("Error parsing test_params: #{e.message} #{e.backtrace}", severity: "ERROR")
      raise e
    end

    # Executes a test with the provided parameters.
    #
    # This method prepares the test environment, executes the Maestro command,
    # and handles retries if the test fails or the instrumentation logs are empty.
    #
    # @param expire_time [String] The timeout duration for the test execution.
    # @param flow_file_path [String] The path to the test flow file.
    # @param retry_attempt [Integer] The current retry attempt (default: 0).
    # @param max_test_retry_count [Integer] The maximum number of retry attempts (default: 0).
    # @return [String, nil] The status of the test execution ("success" or "failed"), or nil if an error occurs.
    def run_test(expire_time, flow_file_path, retry_attempt: 0, max_test_retry_count: 0)
      # Log the start of the test execution.
      log("Started run test #{@session_id}  #{expire_time} "\
                               "#{retry_attempt} #{max_test_retry_count}")

      maestro_command_status = "failed"

      # Start the Maestro monitor script for the test.
      start_maestro_monitor_script(@unique_test_id)

      # Execute the Maestro command for the test.
      maestro_command_status = execute_maestro_command(
        expire_time,
        flow_file_path
      )

      # Stop the Maestro monitor script after execution.
      stop_maestro_monitor_script(@unique_test_id)

      # Retry the test if it failed and the instrumentation logs are empty.
      if maestro_command_status == "failed" &&
         File.empty?(@instrumentation_logs_file_path) && retry_attempt < max_test_retry_count

        log("Instrumentation file empty, retrying.")
        retry_attempt += 1
        run_test(
          expire_time,
          flow_file_path,
          retry_attempt: retry_attempt,
          max_test_retry_count: max_test_retry_count
        )
      end

      # Log the completion of the test execution.
      log("Finished run_test for #{flow_file_path} with status #{maestro_command_status}")
      maestro_command_status
    rescue StandardError => e
      # Log the error and write it to the exit file.
      log("Error starting run test: #{e.message} #{e.backtrace}", severity: "ERROR")
      ExitFile.write(e.message[0..200])
      nil
    end

    # Retrieves the path where the test suite is unzipped.
    #
    # This method constructs the path to the directory where the test suite is unzipped
    # based on the session ID.
    #
    # @return [String] The path to the unzipped test suite directory.
    def test_suite_unzip_path
      # Construct the path using the session ID.
      "/tmp/#{@session_id}_test_suite"
    end

    # Retrieves the path for debug output logs.
    #
    # This method constructs the path to the directory where debug logs are stored
    # within the unzipped test suite directory.
    #
    # @return [String] The path to the debug output logs directory.
    def debug_output_path
      # Construct the path using the test suite unzip path.
      "#{test_suite_unzip_path}/logs"
    end

    # Retrieves the path to the Maestro build directory.
    #
    # This method constructs the path to the Maestro build directory based on the
    # specified Maestro version.
    #
    # @param maestro_version [String] The version of the Maestro framework.
    # @return [String] The path to the Maestro build directory.
    def get_maestro_build_path(maestro_version)
      # Construct the path using the specified Maestro version.
      "/usr/local/.browserstack/deps/maestro/maestro_#{maestro_version}/bin/maestro"
    end

    # Unzips the test suite folder to the specified destination.
    #
    # This method extracts the contents of the test suite ZIP file into the directory
    # specified by `test_suite_unzip_path`.
    #
    # @param folder_path [String] The path to the ZIP file containing the test suite.
    # @raise [StandardError] If an error occurs during the extraction process.
    def unzip_test_suite_folder(folder_path)
      # Define the destination directory for the extracted files.
      destination = test_suite_unzip_path
      cmd = "unzip #{folder_path} -d #{destination}"
      # Open the ZIP file and extract its contents.
      # Execute the command and handle the output and status.
      output, status = OSUtils.execute(cmd, true)

      unless status.success?
        log("Unziping test suite failed #{folder_path} with #{output}:#{status}")
        zombie_push('android', "maestro-unzip-test-failed", output, nil, status.to_s, nil)
      end

    rescue StandardError => e
      # Log the error and re-raise the exception.
      log("Error unzipping test suite folder: #{e.message} #{e.backtrace}", severity: "ERROR")
      raise e
    end

    # Starts the Maestro monitor script for a specific test.
    #
    # This method initializes the MaestroTimeoutManager and starts the timeout monitor
    # for the specified test.
    #
    # @param unique_test_id [String] The unique identifier for the test.
    # @raise [StandardError] If an error occurs while starting the monitor script.
    def start_maestro_monitor_script(unique_test_id)
      # Retrieve the idle timeout value from the session summary file.
      timeout = @session_summary_file["idle_timeout"]
      if defined?(AppAutomateFrameworks::MaestroTimeoutManager)
        # Initialize the MaestroTimeoutManager and start the monitor script.
        maestro_test = AppAutomateFrameworks::MaestroTimeoutManager.new(
          @device,
          @instrumentation_logs_file_path,
          framework: 'maestro',
          test_id: unique_test_id,
          logger: Logger.new($stdout),
          pattern: ["bin/maestro", "test"],
          script_identifier: [@device, "espresso_actions.sh"]
        )
        maestro_test.start(timeout)
      end
    rescue ArgumentError => e
      # Handle argument errors and retry with fewer arguments.
      log("Arg error TimeoutManager - retrying by removing extra args: #{e.message}", severity: "ERROR")
      timeout = @session_summary_file["idle_timeout"]
      if defined?(AppAutomateFrameworks::MaestroTimeoutManager)
        maestro_test = AppAutomateFrameworks::MaestroTimeoutManager.new(
          @device,
          @instrumentation_logs_file_path,
          test_id: test_or_session_id,
          logger: Logger.new($stdout),
          pattern: ["bin/maestro", "test"],
          script_identifier: [@device, "espresso_actions.sh"]
        )
        maestro_test.start(timeout)
      end
    rescue StandardError => e
      # Log the error and write it to the exit file.
      log("Error starting timeout monitor script: #{e.message} #{e.backtrace}", severity: "ERROR")
      ExitFile.write(e.message[0..200])
      nil
    end

    # Executes the Maestro command for a test.
    #
    # This method constructs and executes the Maestro CLI command to run a test flow.
    # It handles errors, logs the execution status, and pushes failure details to Zombie if needed.
    #
    # @param expire_time [String] The timeout duration for the command execution.
    # @param flow_file_path [String] The path to the test flow file.
    # @return [String] "success" if the command executes successfully, "failed" otherwise.
    def execute_maestro_command(expire_time, flow_file_path)
      # Parse Maestro parameters from the session summary file.
      maestro_params
      js_engine = @maestro_params[:js_engine]
      maestro_version = @maestro_params[:maestro_version] || default_maestro_version
      driver_host_port = @maestro_params[:driver_host_port]
      # main_app = @maestro_params["app_details"]["bundle_id"]

      # Log the execution details.
      log("Executing maestro command for #{@session_id} with params: "\
          "#{maestro_version} #{js_engine} #{driver_host_port} #{flow_file_path}")

      # Construct the Maestro CLI command.
      cmd = "timeout #{expire_time} env MAESTRO_CLI_NO_ANALYTICS=true"
      cmd += " MAESTRO_USE_GRAALJS=true" if js_engine == "graaljs"
      cmd += " #{get_maestro_build_path(maestro_version)} \
            --device #{@device} \
            --driver-host-port #{driver_host_port} \
            test \
            --debug-output  #{debug_output_path}/#{@unique_test_id} \
            --flatten-debug-output \
            --format junit \
            #{flow_file_path} > #{@instrumentation_logs_file_path} 2>&1"

      # Execute the command and capture the output and status.
      output, status = OSUtils.execute(cmd, true)

      # Log the execution status.
      log("Maestro command executed for #{@session_id} with status: #{status} : #{output}")
      post_execute_maestro_command

      # Return "success" if the command executed successfully.
      return "success" if status.success?

      # Log the failure and push details to Zombie.
      log("#{@device} #{@session_id}  MAESTRO TEST command failed with status: #{status}")
      zombie_push(
        "android",
        "maestro-command-start-failed",
        "MAESTRO TEST Command Failure",
        @device_model,
        status,
        @device,
        @session_id
      )
      "failed"
    rescue StandardError => e
      # Write the error to the exit file and return nil.
      ExitFile.write(e.message[0..200])
      nil
    end

    # Handles post-execution tasks for the Maestro command.
    #
    # This method checks if the device is offline and creates a marker file if necessary.
    def post_execute_maestro_command
      # Determine the test or session ID.
      test_id = @unique_test_id || @session_id

      # Create a marker file if the device is offline.
      FileUtils.touch("#{ESPRESSO_DEVICE_OFF_ADB_FILE_PREFIX}_#{test_id}_#{@device}") if is_device_off_adb?(@device,
                                                                                                            test_id)
    end

    # Stops the Maestro monitor script for a specific test.
    #
    # This method initializes the MaestroTimeoutManager and stops the timeout monitor
    # for the specified test.
    #
    # @param unique_test_id [String] The unique identifier for the test.
    # @raise [StandardError] If an error occurs while stopping the monitor script.
    def stop_maestro_monitor_script(unique_test_id)
      # Initialize the MaestroTimeoutManager for the test.
      if defined?(AppAutomateFrameworks::MaestroTimeoutManager)
        maestro_test = AppAutomateFrameworks::MaestroTimeoutManager.new(
          @device,
          @instrumentation_logs_file_path,
          framework: 'maestro',
          test_id: unique_test_id,
          logger: Logger.new($stdout)
        )

      # Stop the timeout monitor script.
        maestro_test.stop("true")
      end
    rescue ArgumentError => e
      # Handle argument errors and retry with fewer arguments.
      log("Arg error stop TimeoutManager - retrying by removing extra args: #{e.message} / #{e.backtrace}",
          severity: "ERROR")
      if defined?(AppAutomateFrameworks::MaestroTimeoutManager)
        maestro_test = AppAutomateFrameworks::MaestroTimeoutManager.new(
          @device,
          @instrumentation_logs_file_path,
          test_id: test_or_session_id,
          logger: Logger.new($stdout)
        )
        maestro_test.stop("true")
      end
    rescue StandardError => e
      # Log the error and write it to the exit file.
      log("stop_maestro_monitor_script #{e.message} / #{e.backtrace}", severity: "ERROR")
      nil
    end

    # Fetches screenshots for a test and uploads them to S3.
    #
    # This method retrieves screenshots from the debug output path, compresses them,
    # and uploads the resulting ZIP file to S3.
    #
    # @param args [Array<String>] Command-line arguments for the operation.
    #   - args[0]: Base S3 URL.
    #   - args[1]: AWS key for authentication.
    #   - args[2]: AWS secret for authentication.
    #   - args[3]: Unique test ID.
    # @raise [StandardError] If an error occurs during the process.
    def fetch_and_push_screenshots(*args)
      # Parse the arguments.
      baseurl = args[0].to_s.strip # Base S3 URL.
      aws_key = args[1].to_s.strip # AWS key for authentication.
      aws_secret = args[2].to_s.strip # AWS secret for authentication.
      @unique_test_id = args[3].to_s.strip # Unique identifier for the test.

      # Define the output folder path for screenshots.
      output_folder_path = "#{debug_output_path}/#{@unique_test_id}/screenshots"
      log("Fetching screenshots for #{@session_id}")

      # Initialize the MaestroHelper instance.
      maestro_helper = MaestroHelper.new(@session_summary_file_path)

      # Fetch and compress screenshots into a ZIP file.
      zip_file = maestro_helper.fetch_screenshots(output_folder_path)

      # Upload the ZIP file to S3 if it exists.
      maestro_helper.upload_screenshots_to_s3(@unique_test_id, zip_file, baseurl, aws_key, aws_secret) if zip_file
    rescue StandardError => e
      # Log the error and raise it for further handling.
      log("Error fetching screenshots: #{e.message} #{e.backtrace}", severity: "ERROR")
      raise e
    end

    def log(message, severity: "INFO")
      if severity == "ERROR"
        BrowserStack.logger.error "[ MAESTRO RUNNER ] [ERROR] #{message}"
        return
      end
      BrowserStack.logger.info "[ MAESTRO RUNNER ] #{message}"
    end

  end
end

Maestro::SessionRunner.run_from_bash if $PROGRAM_NAME == __FILE__
