# Required libraries and dependencies
require 'json'          # For parsing and generating JSON data
require 'digest'        # For generating MD5 hashes
require 'date'          # For working with dates and times
require 'fileutils'     # For file and directory operations
require 'dotenv/load'   # For loading environment variables from .env files
require 'open3'         # For capturing output from shell commands

# Constants for paths and configurations
BS_HOME = "/usr/local/.browserstack".freeze
MOBILE_COMMON_HOME = "#{BS_HOME}/mobile-common".freeze
DIR_HOME = "#{BS_HOME}/mobile".freeze
BUNDLE = '/home/<USER>/bin/bundle'.freeze
ZOMBIE_SCRIPT = '/usr/local/.browserstack/mobile/common/push_to_zombie.rb'.freeze

# Relative imports for project-specific utilities and constants
require_relative '../../constants'
require_relative "../../lib/os_utils"
require_relative '../../scripts/upload_to_s3'
require 'browserstack_logger' # Custom logger for BrowserStack
require "#{DIR_HOME}/common/push_to_zombie.rb"
require "#{DIR_HOME}/android/helpers/utils.rb"

MAESTRO_TIMEOUT_MANAGER_FILE = \
  "#{MOBILE_COMMON_HOME}/frameworks_timeout_manager/app_automate_frameworks/maestro_time_out_manager.rb".freeze

MAESTRO_COMMANDS_FORMATER_FILE = \
  "#{MOBILE_COMMON_HOME}/maestro/maestro_command_formater.rb".freeze

# Conditionally require the MaestroTimeoutManager based on a specific condition.
require(MAESTRO_TIMEOUT_MANAGER_FILE) if File.exist?(MAESTRO_TIMEOUT_MANAGER_FILE)

# Load the Maestro Commands Generator
require(MAESTRO_COMMANDS_FORMATER_FILE) if File.exist?(MAESTRO_COMMANDS_FORMATER_FILE)

# Include BrowserStack module for logging and utilities
include BrowserStack # rubocop:todo Style/MixinUsage

# Helper class for managing Maestro-related operations
#
# This class provides methods for handling screenshots, logs, test summaries,
# and other operations related to the Maestro framework.
class MaestroHelper # rubocop:todo Metrics/ClassLength
  # Constants specific to the MaestroHelper class
  SERVER_LOG = "/var/log/browserstack/server.log".freeze
  S3_ACL = "public-read".freeze
  DEFAULT_TEST_STATUS = 'QUEUED'.freeze

  # Error patterns for identifying specific issues in logs
  ERROR_KEYS = {
    "testsuite-parse-failure":
      /Failed\s+to\s+parse\s+file:\s+(?<file_name>.+)/,
    "testsuite-device-disonnected":
      /Device\s+(?<device_id>[a-zA-Z0-9]+)\s+was\s+requested,\s+but\s+it\s+is\s+not\s+connected\./,
    "testsuite-no-tests-found-directory":
      /Flow\sdirectories\s+do\s+not\s+contain\s+any\s+Flow\s+files:\s+(?<directory_path>.+)/,
    "testsuite-no-tests-found-flowfile":
      /Flow\spath\s+does\s+not\s+exist:\s+(?<file_path>.+)/
  }.freeze

  # BrowserStack-specific error patterns
  BSTACK_ERRORS = {
    "testsuite-device-disonnected":
      /Device\s+(?<device_id>[a-zA-Z0-9]+)\s+was\s+requested,\s+but\s+it\s+is\s+not\s+connected\./
  }.freeze

  # Keys to be removed from summary data
  UNNECESSARY_KEYS = ['video', 'screenshots', 'device', 'common_package_name', 'is_duplicate',
                      'is_cucumber_test_suite', 'use_rtc_app'].freeze

  # Flow statuses and their corresponding labels
  FLOWSTATUS = {
    'preparing': "RUNNING",
    'pending': "QUEUED",
    'installing': "RUNNING",
    'running': "RUNNING",
    'passed': "SUCCESS",
    'failed': "FAILED",
    'stopped': "FAILED",
    'skipped': "SKIPPED",
    'timedout': "TIMEDOUT",
    'canceled': "FAILED",
    'canceled by user': "FAILED",
    'run expired': "FAILED",
    'cancelled (unknown reason)': "FAILED"
  }.freeze

  # Regular expressions for parsing flow-related logs
  FLOW_REGEX = {
    flows_started: /Waiting\s+for\s+flows\s+to\s+complete\.{3}/,
    flow_status:
      /\[(?<flow_status>[^\]]+)\]\s+(?<flow_name>[^\d]+\d+)\s+\((?<duration>[^)]+)\)(?:\s+\((?<details>.+?)\))?/,
    canceled_flow: /(?<count>\d+)\s+Flow(?:s)?\s+Canceled/,
    all_flows_canceled: /All\s+flows\s+were\s+canceled/i,
    completed_flow:
      %r{(?<completed_flows>\d+)/(?<total_flows>\d+)\s+Flows?\s+(?<status>[A-Za-z]+)(?:\s+in\s+(?<duration>.+))?}
  }.freeze

  # Initializes the MaestroHelper with the given summary file
  #
  # @param summary_file [String] Path to the summary file
  # @raise [JSON::ParserError] If the summary file contains invalid JSON
  def initialize(summary_file)
    # Initialize the logger for BrowserStack
    BrowserStack.init_logger(SERVER_LOG)

    @summary_file = summary_file
    begin
      # Load the summary data from the file
      @summary_data = JSON.parse(File.read(summary_file))
    rescue StandardError => e
      # Handle errors gracefully by initializing an empty summary
      @summary_data = {}
      log("[#{summary_file}] Unable to read summary file v1. " \
        "Setting to {} : #{e.message} #{e.backtrace}" , severity: "ERROR")
    end

    # Session Summary File v2
    @summary_file_v2 = "#{@summary_file}_v2"
    # Ensure the v2 summary file exists
    File.open(@summary_file_v2, 'w') { |f| f.write('{}') } unless File.exist?(@summary_file_v2)
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))

    @session_id = @summary_data['session_id']
  end

    # Fetches screenshots from the specified path and compresses them into a zip file.
  #
  # @param path [String] Path to the directory containing screenshots.
  # @return [String, nil] Path to the zip file if successful, or nil if the directory doesn't exist.
  def fetch_screenshots(path)
    # Return nil if the directory does not exist.
    return nil unless File.directory?(path)

    # Define the path for the zip file.
    zip_file = File.join(path, "screenshots.zip")

    # Create a zip file containing all files in the directory.
    cmd = "zip -r #{zip_file} ."
    Dir.chdir(path) do
      output, status = OSUtils.execute(cmd, true)
      unless status.success?
        log("Zipping SS failed #{path} with #{output}:#{status}", severity: "ERROR")
        zombie_push('android', "maestro-zip-screenshots-failed", output, nil, status.to_s, nil)
        return nil
      end
    end

    # Return the path to the zip file.
    zip_file
  end

  # Uploads a zip file containing screenshots to an S3 bucket.
  #
  # @param session_id [String] The session ID for the upload.
  # @param screenshots_zip_file [String] Path to the zip file containing screenshots.
  # @param base_s3url [String] Base S3 URL for the upload.
  # @param aws_key [String] AWS key for authentication.
  # @param aws_secret [String] AWS secret for authentication.
  def upload_screenshots_to_s3(session_id, screenshots_zip_file, base_s3url, aws_key, aws_secret)
    # Record the start time of the upload.
    screenshots_upload_start_time = Time.now

    # Upload the zip file to S3.
    upload_log_to_s3(base_s3url, aws_key, aws_secret, screenshots_zip_file, session_id)

    # Record the end time of the upload.
    screenshots_upload_end_time = Time.now

    # Calculate the total upload time in milliseconds.
    screenshots_upload_total_time = ((screenshots_upload_end_time - screenshots_upload_start_time) * 1000).to_i

    # Log the upload time.
    log("#{session_id} Screenshots upload time: #{screenshots_upload_total_time} ms")
  end

  # Fetches screenshots from a specified path, compresses them into a zip file, and uploads them to S3.
  #
  # @param baseurl [String] Base URL for S3.
  # @param aws_key [String] AWS key for authentication.
  # @param aws_secret [String] AWS secret for authentication.
  # @param unique_test_id [String] Unique identifier for the test.
  # @return [String] S3 URL of the uploaded screenshots or an empty string if an error occurs.
  def fetch_and_push_screenshots(baseurl, aws_key, aws_secret, unique_test_id)

      # Store the unique test ID.
    @unique_test_id = unique_test_id

    # Define the output folder path for screenshots.
    output_folder_path = "#{debug_output_path(@session_id)}/#{@unique_test_id}/screenshots"
    log("Fetching screenshots for #{@session_id}")

    # Initialize MaestroHelper and fetch screenshots.
    zip_file = fetch_screenshots(output_folder_path)

    # Upload screenshots to S3 if the zip file exists.
    if zip_file
      return upload_screenshots_to_s3(
        "#{@session_id}#{@unique_test_id}", zip_file, baseurl, aws_key, aws_secret
      )
    end

    # Return an empty string if no zip file was created.
    ''
  rescue StandardError => e
    # Log the error and return an empty string.
    log("Error fetching screenshots: #{e.message} #{e.backtrace.join("\n")}", severity: "ERROR")
    ''
  end

  # Logs upload failure details and pushes data to the zombie service.
  #
  # @param session_id [String] The session ID for the upload.
  # @param error [String] Error message describing the failure.
  # @param file_name [String] Name of the file that failed to upload.
  # @param file_size [Integer] Size of the file in bytes.
  # @param retry_count [Integer] Number of retry attempts made.
  def log_upload_failure(session_id, error, file_name, file_size, retry_count)
    # Prepare the data to be logged and pushed to the zombie service.
    upload_data = {
      file_name: file_name,
      error: error,
      file_size_bytes: file_size,
      retry_count: retry_count
    }.to_json

    # Execute the zombie script to log the failure.
    OSUtils.execute("#{BUNDLE} exec ruby #{ZOMBIE_SCRIPT} \"android\" \"maestro-log-upload-error\" \
      \"log upload failed\" \"\" \"#{upload_data}\" \"\" \"#{session_id}\"")
  end

  # Logs retry attempts for screenshot uploads.
  #
  # @param session_id [String] The session ID for the upload.
  # @param file_name [String] Name of the file being retried.
  # @param file_size [Integer] Size of the file in bytes.
  # @param retry_count [Integer] Number of retry attempts made.
  def log_retry_attempt(session_id, file_name, file_size, retry_count)
    # Prepare the data to be logged for the retry attempt.
    upload_data = {
      file_name: file_name,
      file_size_bytes: file_size
    }.to_json

    # Execute the zombie script to log the retry attempt.
    OSUtils.execute("#{BUNDLE} exec ruby #{ZOMBIE_SCRIPT} \"android\" \"maestro-log-upload-retry\" \
      \"timeout-retry-#{retry_count}\" \"\" \"#{upload_data}\" \"\" \"#{session_id}\"")
  end

  # Updates the summary file and its v2 version with the current summary data.
  #
  # This method writes the current state of `@summary_data` and `@summary_data_v2`
  # to their respective files.
  def update_summary_file
    # Write the main summary data to the summary file.
    safe_file_write(@summary_file, @summary_data['device'], @summary_data['session_id'], __method__.to_s) do |f|
      f.write(@summary_data.to_json)
    end

    # Write the v2 summary data to the v2 summary file.
    safe_file_write(@summary_file_v2, @summary_data_v2['device'], @summary_data_v2['session_id'],
                    __method__.to_s) do |f|
      f.write(@summary_data_v2.to_json)
    end
  end

  # Checks if there are errors in the instrumentation file.
  #
  # @param instrumentation_file [String] Path to the instrumentation log file.
  # @param device_id [String] ID of the device used for testing.
  # @param callback_file_data [Hash] Data from the callback file.
  # @param device_model [String] Model of the device used for testing.
  # @param data [Hash] Additional data for error reporting.
  # @param test_framework [String] Name of the test framework (e.g., 'espresso', 'maestro').
  # @return [Boolean] Returns true if errors are found, false otherwise.
  def error_in_instrumentation_file?(
    instrumentation_file, device_id, callback_file_data, device_model, data, test_framework
  )
    # Check if the instrumentation file exists and is not empty.
    if !File.exist?(instrumentation_file) || File.zero?(instrumentation_file)
      zombie_push(
        'android',
        "#{test_framework}-testsuite-parse-empty",
        'instrumentation file does not exist or is empty',
        device_model,
        data,
        device_id,
        @summary_data_v2['session_id'],
        ''
      )
      callback_file_data["error_reason"] = "testsuite-parse-empty"
      @summary_data["error_reason"] = "testsuite-parse-empty"
      return true
    end

    # Check for specific parse failures in the instrumentation file.
    check_for_testsuite_parse_failure(
      instrumentation_file,
      callback_file_data,
      device_model,
      device_id,
      data,
      test_framework
    )
  end

  # Checks for specific test suite parse failures in the instrumentation file.
  #
  # @param instrumentation_file [String] Path to the instrumentation log file.
  # @param callback_file_data [Hash] Data from the callback file.
  # @param device_model [String] Model of the device used for testing.
  # @param device_id [String] ID of the device used for testing.
  # @param data [Hash] Additional data for error reporting.
  # @param test_framework [String] Name of the test framework (default: 'maestro').
  # @return [Boolean] Returns true if a parse failure is detected, false otherwise.
  def check_for_testsuite_parse_failure(
    instrumentation_file, callback_file_data, device_model,
    device_id, data, test_framework = 'maestro'
  )
    # Search for matching patterns in the instrumentation file.
    File.open(instrumentation_file) do |file|
      # Match the first pattern found to a known error key.
      file.each_line do |line|
        ERROR_KEYS.each do |error_key, error_pattern|
          next unless line.match(error_pattern)

          # Push the error to the zombie service and update callback data.
          zombie_push("android", "#{test_framework}-testsuite-parse-failed", line, device_model,
                      data, device_id, @summary_data_v2['session_id'], '')

          callback_file_data["error_reason"] = error_key
          @summary_data["error_reason"] = error_key
          return true
        end
      end
      false
    end
  end

  # Calculates the total count of tests for each status.
  #
  # @param classes_v2 [Hash] The v2 summary data containing test classes and their statuses.
  # @return [Hash] A hash containing the count of tests for each status.
  def get_count_for_each_status(classes_v2)
    # Initialize a hash to store the count of each status.
    total_tests_count = {
      "QUEUED" => 0,
      "RUNNING" => 0,
      "SUCCESS" => 0,
      "FAILED" => 0,
      "IGNORED" => 0,
      "TIMEDOUT" => 0,
      "ERROR" => 0
    }

    # Iterate through each class and update the counts.
    classes_v2.each_value do |class_details|
      total_tests_count["QUEUED"] += class_details[:tests_summary]['queued']
      total_tests_count["RUNNING"] += class_details[:tests_summary]['running']
      total_tests_count["SUCCESS"] += class_details[:tests_summary]['passed']
      total_tests_count["FAILED"] += class_details[:tests_summary]['failed']
      total_tests_count["IGNORED"] += class_details[:tests_summary]['skipped']
      total_tests_count["TIMEDOUT"] += class_details[:tests_summary]['timedout']
      total_tests_count["ERROR"] += class_details[:tests_summary]['error']
    end

    total_tests_count
  end

  # Updates the session details with the provided duration.
  #
  # @param duration [Integer] Duration of the session in seconds.
  def update_session_details(duration)
    # Update the duration in the summary data.
    @summary_data["duration"] = duration

    # Update the duration in the v2 summary data.
    @summary_data_v2["duration"] = duration

    # Flatten the session summary for v2.
    flatten_session_summary_v2

    # Save the updated summary data to the summary files.
    update_summary_file

    # Push the updated session details to the zombie service.
    zombie_push(
      'android', "total-tests-duration", '', '', duration,
      @summary_data_v2['device'], @summary_data_v2['session_id'], ''
    )
  end

  # Flattens the session summary for v2 by removing unnecessary keys and restructuring data.
  def flatten_session_summary_v2
    # Reload the v2 summary data from the file.
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))

    # Remove unnecessary keys from the summary data.
    UNNECESSARY_KEYS.each { |key| @summary_data_v2.delete(key) }

    # Flatten the class and test levels in the summary data.
    @summary_data_v2["classes"] = @summary_data_v2["classes"].values.map do |class_object|
      class_object['tests'] = class_object['tests'].values
      class_object
    end
  end

  # Processes and uploads logs for a specific session and test to S3.
  #
  # @param session_id [String] The session ID for the logs.
  # @param unique_test_id [String] Unique identifier for the test.
  # @param base_s3_url [String] Base URL for S3 uploads.
  # @param aws_key [String] AWS key for authentication.
  # @param aws_secret [String] AWS secret for authentication.
  def process_and_upload_logs(session_id, unique_test_id, base_s3_url, aws_key, aws_secret)
    # Define the debug output path for the session and test.
    debug_op_path = "#{debug_output_path(session_id)}/#{unique_test_id}"

    # Check if the debug output path exists and is not empty.
    if !Dir.exist?(debug_op_path) || Dir.empty?(debug_op_path)
      log("No logs found. Returning..")
      return
    end

    # Fetch all files from the debug output path.
    all_files = fetch_all_files(debug_op_path)

    # Upload each file to S3.
    all_files.each { |file| upload_log_to_s3(base_s3_url, aws_key, aws_secret, file, unique_test_id) }
  end

  # Fetches all files from the specified directory, excluding screenshots.
  #
  # @param directory_path [String] Path to the directory to fetch files from.
  # @return [Array<String>] List of file paths in the directory.
  def fetch_all_files(directory_path)
    # Use Dir.glob to fetch all files recursively, excluding screenshots.
    Dir.glob(File.join(directory_path, '**', '*')).select do |file|
      File.file?(file) && !file.include?("#{directory_path}/screenshots")
    end
  end

  # Generates an S3 URL for a given file.
  #
  # @param base_s3url [String] Base S3 URL.
  # @param session_id [String] The session ID for the file.
  # @param file_path [String] Path to the file.
  # @return [String] The generated S3 URL for the file.
  def generate_s3_url(base_s3url, session_id, file_path)
    if File.basename(file_path).match(/^commands-.*-nested\.json$/)
      return "#{base_s3url}/#{session_id}/#{session_id}-commands.json"
    end

    "#{base_s3url}/#{session_id}/#{session_id}-#{File.basename(file_path)}"
  end

 # Determines the content type (MIME type) based on the file extension.
  #
  # @param filename [String] The name or path of the file.
  # @return [String] The corresponding content type for the file extension.
  def get_content_type(filename)
    ext = File.extname(File.basename(filename)).delete_prefix('.')

    case ext
    when 'json'
      'text/json'
    when 'ec'
      'application/octet-stream'
    when 'zip'
      'applications/zip'
    when 'yml', 'yaml'
      'application/x-yaml'
    else
      'text/plain'
    end
  end

  # Uploads a log file to S3 with retry logic.
  #
  # @param base_s3_url [String] Base S3 URL for the upload.
  # @param aws_key [String] AWS key for authentication.
  # @param aws_secret [String] AWS secret for authentication.
  # @param file_path [String] Path to the file to be uploaded.
  # @param session_id [String] The session ID for the upload.
  # @param retry_count [Integer] Current retry attempt (default: 1).
  def upload_log_to_s3(base_s3_url, aws_key, aws_secret, file_path, session_id, retry_count = 1)
    return unless file_path

    max_upload_retry_count = 2

    log("Uploading Assets to #{base_s3_url}")

    content_type = get_content_type(file_path)
    # Attempt to upload the file to S3.
    status, error = UploadToS3.upload_file_to_s3(aws_key, aws_secret, content_type,
                                                 file_path, S3_ACL, generate_s3_url(base_s3_url, session_id, file_path),
                                                 nil, 10)
    if !status || error
      if retry_count <= max_upload_retry_count
        # Retry the upload if the maximum retry count has not been reached.
        return upload_log_to_s3(base_s3_url, aws_key, aws_secret, file_path, session_id, retry_count + 1)
      else
        # Log the error if the upload fails after retries.
        log("#{session_id} upload #{file_path} failed with error - #{error}", severity: "ERROR")
        log_upload_failure(session_id, error, file_path, File.size(file_path), retry_count)
      end
    end

    # Log retry attempts if applicable.
    log_retry_attempt(session_id, file_path, File.size(file_path), retry_count) if retry_count > 1
  end

  # Reads and parses a summary file.
  #
  # @param summary_file_path [String] Path to the summary file.
  # @return [Hash] Parsed JSON data from the summary file, or an empty hash if parsing fails.
  def read_summary_file(summary_file_path)
    return unless File.exist?(summary_file_path)

    # Read and parse the summary file.
    file_content = File.read(summary_file_path)
    JSON.parse(file_content)
  rescue JSON::ParserError => e
    # Log an error if the file cannot be parsed as JSON.
    log("Failed to parse summary file as JSON: #{e.message}", severity: "ERROR")
    {}
  end

  def test_suite_unzip_path(session_id)
    "/tmp/#{session_id}_test_suite"
  end

  def debug_output_path(session_id)
    "#{test_suite_unzip_path(session_id)}/logs"
  end

  # Cleans up the test suite directory for a given session.
  #
  # @param session_id [String] The session ID.
  def clean(session_id)
    # Remove the test suite directory.
    FileUtils.rm_rf(test_suite_unzip_path(session_id))
  rescue StandardError => e
    # Log an error if the cleanup fails.
    log("Failed to clean up test suite path for session #{session_id}: #{e.message}", severity: "ERROR")
  end

  # Generates a hash representing the initial state of test counts.
  #
  # This method initializes the test states with default values for various statuses
  # such as "passed", "failed", "skipped", etc.
  #
  # @param count [Integer] The total number of tests (default: 0).
  # @return [Hash] A hash containing the initial state of test counts.
  def test_states(count = 0)
    {
      "total" => count,
      'passed' => 0,
      'failed' => 0,
      'skipped' => 0,
      'timedout' => 0,
      'error' => 0,
      'running' => 0,
      'queued' => count
    }
  end

  # Generates a default test object for a given test ID.
  #
  # This method initializes a test object with default values for attributes such as
  # "start_time", "status", "duration", etc.
  #
  # @param id [String] The unique identifier for the test.
  # @return [Hash] A hash representing the default test object.
  def default_test(id)
    {
      start_time: '',
      status: DEFAULT_TEST_STATUS,
      test_id: id,
      duration: '',
      instrumentation_log: '',
      device_log: '',
      video: ''
    }
  end

  def generate_flow_name(flow, session_id)
    flowname = flow.split("#{test_suite_unzip_path(session_id)}/").last
    flowname = flowname.split('/')
    flowname = flowname.length < 2 ? flow : flowname[1..].join('/') # Return original if there's no directory to remov
    flowname = flowname.split(".#{File.basename(flowname).split('.').last}").first
  end

  # Generates a default test object (v2) for a given test ID and test name.
  #
  # This method initializes a test object with default values for attributes such as
  # "name", "start_time", "status", etc., and includes additional fields for v2.
  #
  # @param id [String] The unique identifier for the test.
  # @param testname [String] The name of the test.
  # @return [Hash] A hash representing the default test object (v2).
  def default_test_v2(id, testname)
    {
      name: testname,
      start_time: '',
      status: DEFAULT_TEST_STATUS.downcase,
      test_id: id,
      duration: '',
      class: "",
      video: ''
    }
  end

  # Parses the execution plan and updates the summary data.
  #
  # @param execution_plan_dir [String] Directory containing the execution plan file.
  # @param test_dir [String] Directory containing the test files.
  # @param outfile [String] Path to the output file for the execution plan.
  def parse_execution_plan(execution_plan_dir, outfile)
    # Initialize result containers for test details.
    result = {}
    result_v2 = {}

    # Retrieve the session ID from the summary data.
    session_id = @summary_data_v2['session_id']

    # Define the path to the execution plan file.
    execution_plan_path = "#{execution_plan_dir}/execution_plan.json"

    # Read and parse the execution plan file.
    content = begin
      File.read(execution_plan_path)
    rescue StandardError
      ""
    end
    execution_plan = begin
      JSON.parse(content)
    rescue StandardError
      []
    end

    # Iterate over each flow in the execution plan.
    execution_plan.each do |flow|
      # Extract the flow name relative to the test directory.
      flow_name = generate_flow_name(flow, session_id)

      # Initialize the result entry for the flow.
      result[flow_name] = {}

      # Initialize the result_v2 entry if it doesn't already exist.
      if result_v2[flow_name].nil? && !flow.empty?
        result_v2[flow_name] = {
          name: flow_name,         # Name of the flow.
          tests_summary: test_states, # Placeholder for test summary.
          tests: {}                # Placeholder for individual tests.
        }
      end

      # Generate a unique test ID for the flow.
      test_id = session_id + get_test_id(flow)

      # Create a default test object for the flow.
      test_obj = default_test(test_id)
      result[flow_name][flow_name] = test_obj

      # Add the test object to result_v2.
      result_v2[flow_name][:tests][flow_name] = default_test_v2(test_id, flow_name)
      result_v2[flow_name][:tests][flow_name][:class] = flow_name

      # Update the test summary counts in result_v2.
      result_v2[flow_name][:tests_summary]['total'] += 1
      result_v2[flow_name][:tests_summary]['queued'] += 1
    end

    # Write the execution plan to a file for debugging or reference.
    safe_file_write(outfile, @summary_data_v2['device'], session_id, __method__.to_s) do |f|
      f.puts(execution_plan)
    end

    # Remove empty entries from the result hash.
    result.each { |class_name, class_obj| result.delete(class_name) if class_obj.empty? }

    # Update the summary data with test counts and statuses.
    @summary_data['test_count'] = execution_plan.count
    @summary_data['test_status'] = {
      'SUCCESS' => 0,
      'FAILED' => 0,
      'IGNORED' => 0,
      'TIMEDOUT' => 0,
      'RUNNING' => 0,
      'QUEUED' => execution_plan.count,
      'ERROR' => 0
    }
    @summary_data['test_details'] = result

    # Remove empty entries from result_v2.
    result_v2.each { |class_name, class_obj| result_v2.delete(class_name) if class_obj[:tests].empty? }

    # Update the session summary file (v2) with test details.
    @summary_data_v2['test_summary'] = test_states(execution_plan.count)
    @summary_data_v2['classes'] = result_v2

    # Save the updated summary data to the summary file.
    update_summary_file
  end

  # Generates a unique test ID based on the input string.
  #
  # @param test_input [String] Input string to generate the test ID.
  # @return [String] A unique test ID.
  def get_test_id(test_input)
    #by default its sha256 (bitlen is 256)
    md5_id = Digest::SHA2.hexdigest test_input
    md5_id[0..7]
  end

  # Generates a build pusher message for summary v2.
  #
  # @param outfile [String] Path to the output file for the message.
  # @param testsuite_file [String] Path to the test suite file.
  def get_build_pusher_message(outfile, testsuite_file)
    data = JSON.parse(File.read(@summary_file_v2))
    testlist = begin
      File.readlines(testsuite_file).each { |e| e.delete!("\n") }
    rescue StandardError
      []
    end
    messages = []
    combined_test_list = []
    message = {
      'build_id' => data['build_id'],
      'device' => data['device'],
      'session_id' => data['session_id'],
      'testlist' => []
    }
    testlist.each do |testname|
      test_id = "#{data['session_id']}#{get_test_id(testname)}"
      flow_name = generate_flow_name(testname, data['session_id'])
      combined_test_list << {
        'test_id' => test_id,
        'name' => flow_name,
        'classname' => flow_name,
        'status' => DEFAULT_TEST_STATUS.downcase
      }
    end
    chunked_test_list = combined_test_list.each_slice(30).to_a
    chunked_test_list.each do |test_list|
      message['testlist'] = test_list
      messages << message.to_json
    end
    log(
      "GET BUILD MESSAGE #{messages}, #{testlist}"
    )
    File.open(outfile, "w+") { |f| f.write(messages.to_json) if defined? messages }
  end

  # Updates the status of tests to "RUNNING" in the summary data.
  #
  # @param testname [String] Name of the test being updated.
  # @param session_id [String] The session ID.
  def update_tests_status_to_running(testname, session_id)
    @summary_data = JSON.parse(File.read(@summary_file))
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))
    flowname = generate_flow_name(testname, session_id)
    test_object = begin
      @summary_data['test_details'][flowname][flowname]
    rescue StandardError
      nil
    end

    if !test_object.nil? && (test_object['status'] == DEFAULT_TEST_STATUS)
      test_object['status'] = "RUNNING"
      @summary_data['test_status']['RUNNING'] += 1
      @summary_data['test_status']['QUEUED'] -= 1
    end

    test_object_v2 = begin
      @summary_data_v2['classes'][flowname]['tests'][flowname]
    rescue StandardError
      nil
    end

    if !test_object_v2.nil? && (test_object_v2['status'] == DEFAULT_TEST_STATUS.downcase)
      test_object_v2['status'] = "running"
      update_tests_status(flowname, 'running', 'queued')
    end
    update_summary_file
  end

  # Updates the test status at both session and class levels.
  #
  # @param classname [String] Name of the class containing the test.
  # @param new_state [String] New state of the test.
  # @param old_state [String] Previous state of the test.
  def update_tests_status(classname, new_state, old_state)
    puts "Updating #{classname} from #{old_state} to #{new_state}"
    unless @summary_data_v2.nil?
      class_result = begin
        @summary_data_v2['classes'][classname]['tests_summary']
      rescue StandardError
        nil
      end
      unless class_result.nil?
        class_result[new_state] += 1
        class_result[old_state] -= 1
      end

      unless @summary_data_v2["test_summary"].nil?
        @summary_data_v2["test_summary"][new_state] += 1
        @summary_data_v2["test_summary"][old_state] -= 1
      end
    end
  end

  # Generates a test pusher message for a specific test and writes it to a file.
  #
  # This method is only applicable for summary v2.
  #
  # @param testname [String] Name of the test.
  # @param session_id [String] The session ID.
  # @param outfile [String] Path to the output file where the message will be written.
  def get_test_pusher_message(testname, session_id, outfile)
    # Load the v2 summary data from the summary file.

    log "Get test_pusher called: #{testname}, #{session_id} , #{outfile}"
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))

    # Extract the flow name from the test name and session path.
    flowname = generate_flow_name(testname, session_id)
    log "Get test_pusher called: #{testname}, #{session_id} , #{outfile} , #{flowname}"
    # Retrieve the test object from the v2 summary data.
    test_object_v2 = begin
      @summary_data_v2['classes'][flowname]['tests'][flowname]
    rescue StandardError
      nil
    end

    # If the test object exists, construct the pusher message.
    unless test_object_v2.nil?
      message = {
        'build_id' => @summary_data_v2['build_id'],       # Build ID of the test.
        'session_id' => @summary_data_v2['session_id'],   # Session ID of the test.
        'test_id' => test_object_v2['test_id'],           # Unique test ID.
        'device' => @summary_data_v2['device'],           # Device used for the test.
        'classname' => flowname,                         # Class name of the test.
        'testname' => flowname,                           # Name of the test.
        'name' => flowname,                               # Flow name of the test.
        'status' => test_object_v2['status'],             # Status of the test.
        'duration' => test_object_v2['duration'],         # Duration of the test.
        'video' => test_object_v2['video'].to_s           # Video URL of the test.
      }
    end

    # Write the message to the specified output file if it is defined.
    File.open(outfile, "w+") { |f| f.write(message.to_json) if defined? message }
  end

  def process_subflow(current_subflow, current_subflow_logs, subflows)
    current_subflow['logs'] = current_subflow_logs
    subflows << current_subflow
  end

  # Parses the instrumentation logs for a specific test and extracts relevant details.
  #
  # @param input [String] Path to the instrumentation log file.
  # @param testname [String] Name of the test being executed.
  # @param device_id [String] ID of the device on which the test is running.
  # @param callback_file [String] Path to the callback file for storing test results.
  # @param test_framework [String] Name of the test framework (e.g., 'espresso', 'maestro').
  #
  # @return [Hash] A hash containing the final status of the test, including:
  #   - :completed_flows [Integer] Number of completed flows.
  #   - :total_flows [Integer] Total number of flows.
  #   - :status [String] Final status of the test (e.g., 'SUCCESS', 'FAILED', 'ERROR').
  #   - :duration [String] Duration of the test execution.
  #   - :stacktrace [String] Stacktrace if an error occurred.
  #   - :subflows [Array<Hash>] Details of individual subflows, including:
  #       - :flow_status [String] Status of the subflow.
  #       - :flow_name [String] Name of the subflow.
  #       - :duration [String] Duration of the subflow.
  #       - :logs [String] Logs associated with the subflow.
  def parse_instrumentation_logs(input, testname, device_id, callback_file, test_framework) # rubocop:disable Metrics/MethodLength
    log "Parse Instrumentation Logs: #{input} #{testname} #{device_id} #{callback_file} #{test_framework}"

    # Initialize variables for test status and details.
    callback_file_data = JSON.parse(File.read(callback_file))
    test_id = (@summary_data_v2['session_id'] + get_test_id(testname)).to_s
    subflows = []
    current_subflow = {}
    tests_started = false
    current_subflow_name = ''
    current_subflow_logs = ''
    parent_flow_logs = ''
    check_for_canceled_flows = false
    final_status = {
      completed_flows: 0,
      total_flows: 0,
      status: "UNKNOWN",
      maestro_status: "UNKNOWN",
      duration: 0,
      stacktrace: ''
    }

    # Handle the case where the input file is empty.
    if File.zero?(input)
      log("Instrumentation file is empty: #{input}", severity: "ERROR")
      final_status[:status] = "ERROR"
      final_status[:stacktrace] += ESPRESSO_INSTRUMENT_COMMAND_STALLED
      zombie_data = {
        "line" => ESPRESSO_INSTRUMENT_COMMAND_STALLED,
        "test_id" => test_id
      }
      zombie_push('android', "#{test_framework}-test-marked-error", ESPRESSO_INSTRUMENT_COMMAND_STALLED, '',
                  zombie_data, device_id, @summary_data_v2['session_id'], '')
    else
      # Parse the instrumentation log file line by line.
      File.open(input) do |f|
        f.each_line do |line|
          # Check if the line indicates a BrowserStack error.
          error_reason = get_browserstack_error(line)
          if check_for_canceled_flows

            if (match = line.match(FLOW_REGEX[:canceled_flow]))
              final_status[:canceled_flows] = begin
                match[:count].to_i
              rescue StandardError
                match[:count].to_s
              end

              next
            end
            check_for_canceled_flows = false if line.to_s.strip.gsub("\n", "") != ""
          end

          if error_reason
            log("BrowserStack error detected: #{error_reason}", severity: "ERROR")
            zombie_data = {
              "line" => line.to_s[0, 200],
              "test_id" => test_id
            }

            final_status = {
              stacktrace: current_subflow_logs,
              completed_flows: 0,
              total_flows: 0,
              status: "ERROR",
              duration: 0
            }

            zombie_push('android', "#{test_framework}-test-marked-error", error_reason, '', zombie_data, device_id,
                        @summary_data_v2['session_id'], '')

            callback_file_data["error_reason"] = error_reason
            write_to_file(callback_file, callback_file_data)
          elsif line.match(FLOW_REGEX[:all_flows_canceled])
            final_status = {
              completed_flows: 0,
              total_flows: 0,
              canceled_flows: 0,
              status: FLOWSTATUS[:canceled] || "FAILED",
              maestro_status: FLOWSTATUS[:canceled],
              duration: 0,
              stacktrace: line.to_s
            }
          elsif line.match(FLOW_REGEX[:flows_started]) && !tests_started
            tests_started = true
          elsif tests_started && (match = line.match(FLOW_REGEX[:flow_status]))
            # Finalize the previous subflow if a new one starts.
            if current_subflow_name != '' && current_subflow_name != match[:flow_name]
              process_subflow(current_subflow, current_subflow_logs, subflows)
              current_subflow_logs = ''
            end
            # Update the current subflow details.
            current_subflow_name = match[:flow_name]
            current_subflow = {
              status: FLOWSTATUS[match[:flow_status].downcase.to_sym] || match[:flow_status].upcase,
              maestro_status: match[:flow_status],
              flow_name: match[:flow_name],
              duration: match[:duration],
              details: match[:details] || ""
            }
            log("Subflow status: #{current_subflow}")

          elsif tests_started && (match = line.match(FLOW_REGEX[:completed_flow]))
            # Finalize the last subflow if flows are completed.
            if current_subflow_name != ''
              process_subflow(current_subflow, current_subflow_logs, subflows)
              current_subflow_logs = ''
            end

            final_status = {
              completed_flows: match[:completed_flows].to_i,
              total_flows: match[:total_flows].to_i,
              canceled_flows: 0,
              status: FLOWSTATUS[match[:status].downcase.to_sym] || match[:status].upcase,
              maestro_status: match[:status],
              duration: match[:duration]
            }
            check_for_canceled_flows = true
          elsif tests_started && current_subflow_name != ''
            # Append logs to the current subflow.
            current_subflow_logs += "#{line}\n"
          elsif current_subflow_name == ''
            # Append logs to the parent flow if no subflow is active.
            parent_flow_logs += "#{line}\n"
          end
        end
      end
    end
    # Handle the case where the final status is unknown.
    if final_status[:status] == 'UNKNOWN' && tests_started
      log("Could not fetch final status", severity: "ERROR")
      final_status[:status] = "FAILED"
      zombie_push('android', "#{test_framework}-test-failed-default", 'Not able to detect test status.',
                  '',
                  {
                    "test_id" => test_id
                  }.to_json,
                  device_id,
                  @summary_data_v2['session_id'], '')
    elsif final_status[:status] == 'UNKNOWN' && !tests_started
      log("Could not fetch final status")
      final_status[:status] = "ERROR"
      final_status[:stacktrace] = parent_flow_logs

    end

    # Merge subflows into the final status and return it.
    final_status[:subflows] = subflows
    log("Instrumentation Logs Parsed: #{final_status}")
    final_status
  rescue StandardError => e
    log("Error occurred while parsing instrumentation logs: #{e.message} #{e.backtrace}")
    {}
  end

  # Parses a line from instrumentation logs and checks if it might indicate a BrowserStack error.
  #
  # @param line [String] A single line from the instrumentation logs file.
  # @return [String, nil] If a BrowserStack error is found, returns the corresponding message; otherwise, nil.
  def get_browserstack_error(line)
    BSTACK_ERRORS.each do |error_reason, error_message|
      return error_reason if line.match(error_message)
    end
    nil
  end

  # Updates the test summary with the results of a test execution.
  #
  # @param session_id [String] The session ID.
  # @param unique_test_id [String] Unique identifier for the test.
  # @param testname [String] Name of the test.
  # @param result [Hash] A hash containing the test results.
  # @param s3url [String] Base S3 URL for logs.
  # @param start_time [Integer] Start time of the test execution.
  # @param video_url [String] URL of the test video.
  # @param ss_url [String] URL for screenshots.
  def update_test_summary(session_id, unique_test_id, testname, result, s3url, start_time, video_url, ss_url)

    log("[update_test_summary] Update test summary called with #{session_id}
      #{unique_test_id} , #{testname} ,
      #{result} ,  #{s3url} ,
      #{start_time} ,  #{video_url} ,
      #{ss_url}")

    @unique_test_id = unique_test_id
    @summary_data = JSON.parse(File.read(@summary_file))
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))

    flowname = generate_flow_name(testname, session_id)
    test_object = begin
      @summary_data['test_details'][flowname][flowname]
    rescue StandardError
      nil
    end

    video_url = "" if @summary_data["video"].to_s == 'false'

    unless test_object.nil?
      test_id = test_object['test_id']
      test_object.merge!(
        'status' => result[:status].to_s.upcase,
        'duration' => result[:duration],
        'device_log' => @summary_data["deviceLogs"].to_s == 'false' ? "" : "#{s3url}#{test_id}/devicelogs",
        'network_log' => @summary_data["networkLogs"].to_s == 'false' ? "" : "#{s3url}#{test_id}/networklogs",
        'instrumentation_log' => "#{s3url}#{test_id}/instrumentationlogs",
        'commands_log' => "#{s3url}#{test_id}/commandslogs",
        'maestro_log' => "#{s3url}#{test_id}/maestrologs",
        'screenshots' => [generate_s3_url(ss_url.chomp("/"), unique_test_id, "screenshots.zip")],
        'video' => video_url,
        'start_time' => Time.at(start_time.to_i),
        'metadata' => result
      )

      @summary_data["test_status"][result[:status].to_s.upcase] += begin
        1
      rescue StandardError
        @summary_data["test_status"][result[:status].to_s.upcase] =
          1
      end
      @summary_data["test_status"]["RUNNING"] -= 1

    end

    if result[:status].to_s.upcase == "TIMEDOUT"
      cls_params = { genre: "app_automate", automate_session_id: @summary_data_v2['session_id'], user_id: nil }
      push_to_cls(cls_params, "test_idle_timeout", "", { "test_id" => test_id, "test_summary" => @summary_data_v2 })
    end

    test_object_v2 = begin
      @summary_data_v2['classes'][flowname]['tests'][flowname]
    rescue StandardError
      nil
    end
    video_url = @summary_data_v2["video"].to_s == 'false' ? "" : video_url.split("#t=").last.to_s
    new_state = case result[:status].downcase
                when "success"
                  "passed"
                when "ignored"
                  "skipped"
                else
                  result[:status].downcase
                end
    old_state = test_object_v2['status']
    test_object_v2&.merge!(
      'status' => new_state,
      'duration' => result[:duration],
      'video' => @summary_data_v2["video"].to_s == "false" ? "" : video_url,
      'commands_log' => "#{s3url}#{test_id}/commandslogs",
      'maestro_log' => "#{s3url}#{test_id}/maestrologs",
      'screenshots' => [generate_s3_url(ss_url.chomp("/"), unique_test_id, "screenshots.zip")],
      'start_time' => Time.at(start_time.to_i),
      "metadata": result
    )

    update_tests_status(flowname, new_state, old_state) unless flowname.nil?
    update_summary_file

    log("[update_test_summary] Test status v1: #{test_object} \n v2: #{test_object_v2}")
  rescue StandardError => e
    log("[update_test_summary]: #{e.message} / #{e.backtrace}", severity: "ERROR")
  end

  # Pushes Maestro logs to S3 for a specific test.
  #
  # @param base_s3url [String] Base S3 URL.
  # @param session_id [String] The session ID.
  # @param unique_test_id [String] Unique identifier for the test.
  def push_maestro_logs_to_s3(base_s3url, session_id, unique_test_id, aws_key, aws_secret)
    # Implementation pending.
    debug_op_path = "#{debug_output_path(session_id)}/#{unique_test_id}"
    all_files = fetch_all_files(debug_op_path)
    all_files.each do |file|
      upload_log_to_s3(base_s3url, aws_key, aws_secret, file, unique_test_id)
      begin
        if defined?(MaestroCommandGenerator) &&
          File.basename(file).match(/^commands-.*\.json$/)
          command_generator = MaestroCommandGenerator.new(
            file,
            logger_params: BrowserStack.logger.params,
            logger: BrowserStack.logger
          )
          nested_commands_path = command_generator.process
          if nested_commands_path
            upload_log_to_s3(base_s3url, aws_key, aws_secret, nested_commands_path,
                             unique_test_id)
          end

        end
      rescue StandardError => e
        log("[MaestroCommandGenerator]  error: #{e.message}/#{e.backtrace}", severity: "ERROR")
      end
    end
  end

    # Updates the status of tests to "TIMEDOUT" for tests that have exceeded their timeout limit.
  #
  # This method reads the test suite file, identifies tests that are still in the "QUEUED" state,
  # and updates their status to "TIMEDOUT" in the summary data.
  #
  # @param testsuite_file [String] Path to the test suite file containing the list of tests.
  def update_timeout_tests(testsuite_file)
    # Load the summary data from the summary files.
    @summary_data = JSON.parse(File.read(@summary_file))
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))

    # Read the list of tests from the test suite file and remove newline characters.
    testlist = File.readlines(testsuite_file).each { |e| e.delete!("\n") }

    # Iterate over each test in the test list.
    testlist.each do |testname|
      # Extract the class name from the test name.
      classname = generate_flow_name(testname, @summary_data_v2['session_id'])

      # Retrieve the test object from the summary data.
      test_object = begin
        @summary_data['test_details'][classname][classname]
      rescue StandardError
        nil
      end

      # If the test is in the default state, update its status to "TIMEDOUT".
      if !test_object.nil? && (test_object['status'] == DEFAULT_TEST_STATUS)
        test_object['status'] = "TIMEDOUT"

        # Update the test status counts in the summary data.
        @summary_data['test_status']['TIMEDOUT'] += 1
        @summary_data['test_status']['QUEUED'] -= 1
      end

      # Retrieve the test object from the v2 summary data.
      test_object_v2 = begin
        @summary_data_v2['classes'][classname]['tests'][classname]
      rescue StandardError
        nil
      end

      # If the test is in the default state, update its status to "skipped" in v2.
      next unless !test_object_v2.nil? && (test_object_v2['status'] == DEFAULT_TEST_STATUS.downcase)

      test_object_v2['status'] = "skipped"

      # Update the test status counts in the v2 summary data.
      update_tests_status(classname, 'skipped', 'queued')
    end

    # Save the updated summary data to the summary files.
    update_summary_file
  end

  # Forcefully stops the current running test and times out all remaining tests in the session.
  #
  # This method creates a timeout file to signal that the session should be terminated.
  # It also invokes the MaestroTimeoutManager to kill idle processes.
  #
  # @param device_id [String] ID of the device on which the session is running.
  # @param test_framework [String] Name of the test framework (e.g., 'espresso', 'maestro').
  # @param session_id [String] The session ID for the current session.
  def force_stop_session(device_id, test_framework, session_id)
    # Create a timeout file that will be checked before running each test to terminate the session.
    FileUtils.touch "/tmp/#{test_framework}_timeout_#{device_id}_#{session_id}"
    if defined?(AppAutomateFrameworks::MaestroTimeoutManager)
      # Initialize the MaestroTimeoutManager to handle idle processes.
      maestro_session = AppAutomateFrameworks::MaestroTimeoutManager.new(
        device_id,
        nil,
        logger: BrowserStack.logger,
        logger_params: BrowserStack.logger.params,
        pattern: ["bin/maestro", "test"],
        script_identifier: [device_id, "espresso_actions.sh"]
      )

      # Kill any idle processes associated with the session.
      maestro_session.kill_idle_process
    end
  rescue StandardError => e
    # Log the error message and backtrace if an exception occurs.
    log("Error message: #{e.message} Backtrace: #{e.backtrace}", severity: "ERROR")

    # Re-raise the exception for further handling.
    raise e
  end

  # Updates the video tags in the summary files for each test.
  #
  # This method adjusts the video start and end times for each test in the summary files.
  # It ensures that the video metadata reflects the actual video availability, accounting
  # for any video loss during the session.
  #
  # The method processes the tests in reverse order to handle overlapping video segments
  # and updates the video metadata in both summary v1 and v2 files.
  def update_video_tag
    # Retrieve test details from summary v1 and v2 files.
    summary_v1_details = @summary_data["test_details"]
    classes_data = @summary_data_v2["classes"] || []
    total_classes = classes_data.size
    last_element_start_tag = nil

    # Iterate through the classes in reverse order.
    classes_data.reverse_each.with_index do |data, class_index|
      tests = data["tests"] || []
      total_tests = tests.size
      v1_class_details = summary_v1_details[summary_v1_details.keys[total_classes - class_index - 1]]

      # Iterate through the tests in reverse order.
      tests.reverse_each.with_index do |test, test_index|
        v1_test_details = v1_class_details[v1_class_details.keys[total_tests - test_index - 1]]

        # Extract video start and end times.
        video_time = test["video"]
        video_start, video_end = video_time.split(",").map(&:to_i)
        next if video_start.nil? || video_end.nil?

        # Adjust the video end time for the last test or based on the previous test's start time.
        if last_element_start_tag.nil?
          video_end += MEDIA_PROJECTION_LAST_TEST_CONSTANT_INCREASE
        else
          video_end = last_element_start_tag
        end

        # Update the last element's start tag for the next iteration.
        last_element_start_tag = video_start

        # Update the video metadata with the adjusted start and end times.
        video_time_with_offset = "#{video_start},#{video_end}"
        test["video"] = video_time_with_offset
        v1_video_url, v1_video_time = v1_test_details["video"].split("#t=").first
        v1_test_details["video"] = "#{v1_video_url}#t=#{video_time_with_offset}"
      end
    end

    # Save the updated summary files.
    update_summary_file
  rescue StandardError => e
    # Log the error and push the failure to Zombie.
    BrowserStack.logger.error "Some error happened while updating video tags, error: #{e.message}"
    zombie_push('android', 'video-tag-update-failed', e.message, '', '', @summary_data_v2['device'],
                @summary_data_v2['session_id'])
  end

  # Checks if a specific timestamp is missing in the video.
  #
  # This method determines whether a given timestamp falls within a period of video loss.
  #
  # @param timestamp [Integer] The timestamp to check.
  # @param previous_video_end_time [Integer] The end time of the previous video segment.
  # @param next_available_video_starts_at [Integer] The start time of the next available video segment.
  # @return [Boolean] True if the timestamp is missing in the video, false otherwise.
  def time_missing_in_video?(timestamp, previous_video_end_time, next_available_video_starts_at)
    timestamp >= previous_video_end_time && timestamp <= next_available_video_starts_at
  end

  # Finds the appropriate offset index for a given timestamp.
  #
  # This method determines the index of the offset timestamp that corresponds to the given timestamp.
  #
  # @param offset_timestamp_array [Array<Integer>] Array of offset timestamps.
  # @param timestamp [Integer] The timestamp to find the offset index for.
  # @param start [Integer] The starting index for the search.
  # @param stop [Integer] The ending index for the search.
  # @return [Integer] The index of the offset timestamp that corresponds to the given timestamp.
  def get_offset_index_for_timestamp(offset_timestamp_array, timestamp, start, stop)
    # Increment the start index until the timestamp is less than or equal to the next offset timestamp.
    start += 1 while (start + 1 <= stop) && (timestamp > offset_timestamp_array[start + 1])
    start
  end

  # Updates the video tags in the summary files, accounting for video loss.
  #
  # This method adjusts the video start and end times for each test in the summary files
  # based on video offset data. It ensures that the video metadata reflects the actual
  # video availability, accounting for any video loss during the session.
  def update_video_tag_in_summary_files
    # Load the summary files and offset data.
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))
    @summary_data = JSON.parse(File.read(@summary_file))
    offset_data = begin
      JSON.parse(File.read("/tmp/video_offset_files/offset_file_session_"\
                               "#{@summary_data_v2['session_id']}"))
    rescue StandardError
      {}
    end

    # Check if MediaProjection is enabled and update video tags accordingly.
    is_mediaprojection_enabled = @summary_data["use_rtc_app"] == "v2" || @summary_data_v2["use_rtc_app"] == "v2"
    update_video_tag if is_mediaprojection_enabled && is_video_enabled?

    # Return early if there is no offset data.
    return if offset_data.empty?

    # Prepare offset data for processing.
    offset_timestamp_array = offset_data.keys.map(&:to_i)
    offset_index = 0
    stop = offset_timestamp_array.length - 1
    first_offset_time = offset_timestamp_array[0]

    # Retrieve test details from summary files.
    summary_v1_details = @summary_data["test_details"]
    classes_data = @summary_data_v2["classes"] || []

    # Iterate through each class and test to adjust video metadata.
    classes_data.each_with_index do |data, class_index|
      tests = data["tests"] || []
      v1_class_details = summary_v1_details[summary_v1_details.keys[class_index]]
      tests.each_with_index do |test, test_index|
        v1_test_details = v1_class_details[v1_class_details.keys[test_index]]
        video_time = test["video"]
        video_start, video_end = video_time.split(",").map(&:to_i)
        test_start_timestamp = Time.parse(test["start_time"]).to_i
        test_end_timestamp = test_start_timestamp + (video_end - video_start)

        # Adjust video start time if it falls within a video loss period.
        if test_start_timestamp >= first_offset_time
          should_apply_start_offset = true
          test_start_time_offset_index = get_offset_index_for_timestamp(offset_timestamp_array, test_start_timestamp,
                                                                        offset_index, stop)

          # Calculate the next available video start time.
          previous_video_end_time = offset_timestamp_array[test_start_time_offset_index]
          video_loss_time = offset_data[offset_timestamp_array[test_start_time_offset_index].to_s].to_i
          if test_start_time_offset_index > 0
            video_loss_time -= offset_data[offset_timestamp_array[test_start_time_offset_index - 1].to_s].to_i
          end
          next_available_video_starts_at = previous_video_end_time + video_loss_time

          # Check if the test start time is missing in the video.
          is_test_start_time_missing_in_video = time_missing_in_video?(test_start_timestamp, previous_video_end_time,
                                                                       next_available_video_starts_at)
          video_start += next_available_video_starts_at - test_start_timestamp if is_test_start_time_missing_in_video
        end

        # Adjust video end time if it falls within a video loss period.
        if test_end_timestamp >= first_offset_time
          should_apply_end_offset = true
          test_end_time_offset_index = get_offset_index_for_timestamp(offset_timestamp_array, test_end_timestamp,
                                                                      offset_index, stop)
          offset_index = test_end_time_offset_index
          previous_video_end_time = offset_timestamp_array[test_end_time_offset_index]
          video_loss_time = offset_data[offset_timestamp_array[test_end_time_offset_index].to_s].to_i
          if test_end_time_offset_index > 0
            video_loss_time -= offset_data[offset_timestamp_array[test_end_time_offset_index - 1].to_s].to_i
          end
          next_available_video_starts_at = previous_video_end_time + video_loss_time

          # Check if the test end time is missing in the video.
          is_test_end_time_missing_in_video = time_missing_in_video?(test_end_timestamp, previous_video_end_time,
                                                                     next_available_video_starts_at)
          video_end += next_available_video_starts_at - test_end_timestamp if is_test_end_time_missing_in_video
        end

        # Handle cases where both start and end times are missing in the video.
        if is_test_start_time_missing_in_video && is_test_end_time_missing_in_video &&
           (test_start_time_offset_index == test_end_time_offset_index)
          video_start = 0
          video_end = 0
        else
          if should_apply_start_offset
            video_start -= offset_data[offset_timestamp_array[test_start_time_offset_index].to_s].to_i
          end
          if should_apply_end_offset
            video_end -= offset_data[offset_timestamp_array[test_end_time_offset_index].to_s].to_i
          end
        end

        # Update the video metadata with the adjusted start and end times.
        video_time_with_offset = "#{video_start},#{video_end}"
        test["video"] = video_time_with_offset
        v1_video_url, v1_video_time = v1_test_details["video"].split("#t=")
        v1_test_details["video"] = "#{v1_video_url}#t=#{video_time_with_offset}"
      end
    end

    # Save the updated summary files.
    update_summary_file
  rescue StandardError => e
    # Log the error and print the backtrace.
    log(
      "error in update_video_tag_in_summary_files message #{e.message} backtrace: #{e.backtrace.join('\n')}",
      severity: "ERROR"
    )
  end

  def log(message, severity: "INFO")
    if severity == "ERROR"
      BrowserStack.logger.error "[ MAESTRO HELPER ] [ERROR] #{message}"
      return
    end
    BrowserStack.logger.info "[ MAESTRO HELPER ] #{message}"
  end

end
