#
# Script to interface between bash (espresso_action.sh) and <PERSON> (maestro_helper.rb).
#
# This script acts as a runner for the MaestroHelper class, providing an interface
# to execute various tasks such as parsing builds, updating session details, handling
# timeouts, generating summaries, and uploading logs to S3.
#

require 'dotenv/load' # Load environment variables from .env file.
require_relative '../helpers/maestro_helper' # Import the MaestroHelper class.

# Runner class for executing tasks using MaestroHelper.
#
# This class provides a command-line interface to execute various tasks
# by invoking methods from the MaestroHelper class.
class MaestroHelperRunner
  # Initializes the runner and logs the start of the process.
  def initialize
    log "Starting MaestroHelperRunner..."
  end

  # Executes the task specified by the first command-line argument.
  #
  # This method parses the command-line arguments, determines the task to execute,
  # and invokes the corresponding method from the MaestroHelper class.
  def execute
    # Parse command-line arguments.
    task = ARGV[0] # Task to execute.
    input_file = ARGV[1] # Input file for the task.
    session_file = ARGV[2] # Session file for the task.

    # Initialize the MaestroHelper instance with the session file.
    mh = MaestroHelper.new(session_file)

    # Determine the task to execute based on the first argument.
    case task
    when 'parse_build'
      # Parse a JUnit build and generate a test suite output file.
      testsuite_outfile = ARGV[3]
      mh.parse_junit_build(input_file, testsuite_outfile)

    when 'update_session_details'
      # Update session details after all test cases are executed.
      session_duration = ARGV[3]
      mh.update_session_details(session_duration)

    when 'timeout_session'
      # Update the status of tests that have timed out.
      testsuite_file = ARGV[3]
      mh.update_timeout_tests(testsuite_file)

    when 'check_for_instrumentation_error'
      # Check for instrumentation errors in the logs.
      callback_file = ARGV[3]
      device_model = ARGV[4]
      device_id = ARGV[5]
      device_name = ARGV[6]
      device_version = ARGV[7]
      test_framework = ARGV[8]
      mh.check_instrumentation_error(
        input_file, callback_file, device_model, device_id,
        device_name, device_version, test_framework
      )

    when 'process_and_upload_logs'
      # Process and upload logs to S3.
      device_id = ARGV[3].to_s.strip
      session_id = ARGV[4].to_s.strip
      unique_test_id = ARGV[5].to_s.strip
      base_s3_url = ARGV[7].to_s.strip
      aws_key = ARGV[8].to_s.strip
      aws_secret = ARGV[9].to_s.strip
      mh.process_and_upload_logs(device_id, session_id, base_s3_url, aws_key, aws_secret)

    when 'get_build_message'
      # Generate a build pusher message.
      pusher_outfile = ARGV[3]
      testsuite_file = ARGV[4]
      mh.get_build_pusher_message(pusher_outfile, testsuite_file)

    when 'update_test_to_running'
      # Update the status of a test to "RUNNING".
      testname = ARGV[3].to_s.strip
      session_id = ARGV[4].to_s.strip
      mh.update_tests_status_to_running(testname, session_id)

    when 'get_test_message'
      # Generate a test pusher message for a specific test.
      pusher_outfile = ARGV[3]
      testname = ARGV[4]
      session_id = ARGV[5].to_s.strip
      mh.get_test_pusher_message(testname, session_id, pusher_outfile)

    when 'update_test_details'
      # Update the details of a test after execution.
      testname = ARGV[3]
      base_s3url = ARGV[4]
      start_time = ARGV[5]
      video_url = ARGV[6].gsub(/^"+|"+$/, '') # Remove surrounding quotes from the video URL.
      device_id = ARGV[7]
      callback_file = ARGV[8]
      test_framework = ARGV[9]
      session_id = ARGV[10]
      unique_test_id = ARGV[11]
      ss_url = ARGV[12]
      result = mh.parse_instrumentation_logs(input_file, testname, device_id, callback_file, test_framework)
      mh.update_test_summary(session_id, unique_test_id, testname, result, base_s3url, start_time, video_url, ss_url)

    when 'push_maestro_logs_to_s3'
      # Push Maestro logs to S3.
      base_s3url = ARGV[3].to_s.strip
      session_id = ARGV[4].to_s.strip
      unique_test_id = ARGV[5].to_s.strip
      aws_key = ARGV[6].to_s.strip
      aws_secret = ARGV[7].to_s.strip
      mh.push_maestro_logs_to_s3(base_s3url, session_id, unique_test_id, aws_key, aws_secret)
    when 'process_video_offset'
      mh.update_video_tag_in_summary_files
    when 'cleanup'
      # Clean up the test suite directory for a session.
      session_id = ARGV[4].to_s.strip
      mh.clean(session_id)

    else
      # Handle invalid task input.
      puts "Wrong Helper Param"
    end
  end

  # Helper method for logging messages to the console.
  #
  # @param message [String] The message to log.
  def log(message)
    puts "#{self.class} ID: #{object_id} message: #{message}"
  end
end

# Execute the runner if the script is run directly.
MaestroHelperRunner.new.execute if __FILE__ == $PROGRAM_NAME
