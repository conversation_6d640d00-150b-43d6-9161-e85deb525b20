require 'fileutils'
require_relative "../lib/utils/utils"
require_relative "../constants"

class DeviceState

  include BrowserStack

  attr_reader :udid

  def initialize(udid)
    @udid = udid
  end

  def respond_to_missing?(method, *)
    method_name = method.to_s
    method_name.end_with?(
      '_file_present?',
      '_file_to_array',
      '_file_older_than_days?',
      '_file_older_than_minutes?',
      '_file_clean_on_weekend?'
    ) ||
      (method_name.start_with?('remove_') && method_name.end_with?('_file')) ||
      (method_name.start_with?('touch_') && method_name.end_with?('_file')) ||
      (method_name.start_with?('read_') && method_name.end_with?('_file')) ||
      (method_name.start_with?('write_array_to_') && method_name.end_with?('_file')) ||
      (method_name.start_with?('write_with_lock_to_') && method_name.end_with?('_file')) ||
      (method_name.start_with?('write_to_') && method_name.end_with?('_file')) ||
      super
  end

  def method_missing(method, *args)
    method_name = method.to_s
    if method_name.end_with?('_file_present?')
      File.exist?(send(method_name.sub('_present?', '').to_sym))
    elsif method_name.end_with?('_file_to_array')
      Utils.read_file_in_array(send(method_name.sub('_to_array', '').to_sym))
    elsif method_name.start_with?('write_array_to_') && method_name.end_with?('_file')
      array = args[0]
      Utils.write_array_to_file!(send(method_name.sub('write_array_to_', '').to_sym), array)
    elsif method_name.start_with?('remove_') && method_name.end_with?('_file')
      FileUtils.rm_f(send(method_name.sub('remove_', '').to_sym))
    elsif method_name.start_with?('touch_') && method_name.end_with?('_file')
      Utils.touch_file_and_create_parents(send(method_name.sub('touch_', '').to_sym))
    elsif method_name.start_with?('write_to_') && method_name.end_with?('_file')
      data = args[0]
      Utils.write_to_file(send(method_name.sub('write_to_', '').to_sym), data)
    elsif method_name.start_with?('write_with_lock_to_') && method_name.end_with?('_file')
      data = args[0]
      Utils.write_to_file_with_lock(send(method_name.sub('write_with_lock_to_', '').to_sym), data)
    elsif method_name.end_with?('_file_older_than_days?')
      days = args[0]
      file_path = send(method_name.sub('_older_than_days?', '').to_sym)
      !File.exist?(file_path) || (File.mtime(file_path) < (Time.now - (60 * 60 * 24 * days)))
    elsif method_name.end_with?('_file_older_than_minutes?')
      minutes = args[0]
      file_path = send(method_name.sub('_older_than_minutes?', '').to_sym)
      !File.exist?(file_path) || (File.mtime(file_path) < (Time.now - (60 * minutes)))
    elsif method_name.end_with?('_file_clean_on_weekend?')
      days = args[0]
      file_path = send(method_name.sub('_clean_on_weekend?', '').to_sym)
      !File.exist?(file_path) || check_weekend_or_file_older?(file_path)
    elsif method_name.end_with?('_created_at')
      file_path = send(method_name.sub('_created_at', '').to_sym)
      File.birthtime(file_path)
    elsif method_name.end_with?('_updated_at')
      file_path = send(method_name.sub('_updated_at', '').to_sym)
      File.mtime(file_path)
    elsif method_name.start_with?('read_') && method_name.end_with?('_file')
      File.read(send(method_name.sub('read_', '').to_sym))
    else
      super
    end
  end

  def device_logger_detected_check_sim_tray_file
    File.join(STATE_FILES_DIR, "check_sim_tray_#{udid}")
  end

  def dedicated_device_file
    File.join(STATE_FILES_DIR, "dedicated_device_#{udid}")
  end

  def dedicated_cleanup_file
    File.join(STATE_FILES_DIR, "dedicated_cleanup_#{udid}")
  end

  def first_device_thread_done_file
    File.join(STATE_FILES_DIR, "first_device_thread_done_#{udid}")
  end

  def sim_info_file
    File.join(STATE_FILES_DIR, "sim_info_#{udid}")
  end

  def sim_conflict_file
    File.join(STATE_FILES_DIR, "sim_conflict_#{udid}")
  end
end
