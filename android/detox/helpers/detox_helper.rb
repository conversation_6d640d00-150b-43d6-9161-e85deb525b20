require 'browserstack_logger'
require 'android_toolkit'
require 'json'
require 'base64'
require_relative '../../constants'
require_relative '../../../common/push_to_zombie'
require_relative "../../lib/os_utils"
require_relative "../../helpers/utils"
require_relative "../../models/android_device"

class DetoxHelper

  RESERVED_INSTRUMENTATION_ARGS = ['class', 'package', 'func', 'unit', 'size', 'perf', 'debug', 'log', 'emma',
                                   'coverageFile'].freeze
  attr_reader :detox_port

  def initialize(device, default_port)
    @device = device
    @detox_port = "3#{default_port.to_i + 1100}"
    @adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    @device_object = BrowserStack::AndroidDevice.new(device, "DetoxHelper", BrowserStack.logger)
    @duplicate_session_file = "/tmp/duplicate_session_#{device}"
  end

  def start_detox_server
    if ensure_detox_server_not_running
      BrowserStack.logger.info("Starting detox server on port: #{@detox_port} device: #{@device} ")
      command_args = [
        DETOX_CLI,
        "run-server",
        "--port",
        @detox_port,
        "--loglevel",
        "trace"
      ]

      command_to_run = "#{command_args.join(' ')} >> #{BrowserStack::LOGGING_DIR}/detox_#{@device}.log 2>&1"

      spawn_process(command_to_run)
      sleep 2
      unless running_session?  # Check if detox server started
        raise FireCMDException, "Some error occured. Detox server not running!"
      end

      @device_object.reverse_port("tcp:#{@detox_port}", "tcp:#{@detox_port}")
      result = @device_object.port_reversed?(@detox_port, @detox_port)
      unless result
        raise FireCMDException,
              "port reverse failed for driver port: #{@detox_port}, app port: #{@detox_port} "
      end
      detox_server_pid(command_args[1..].join(' '))
    else
      raise FireCMDException, "Detox server is already running!"
    end
  end

  def detox_app_client_installed?(instrumentation_name)
    instru_list = @adb.list("instrumentation")
    return true if instru_list.any? do |device_instru|
      device_instru.include?(instrumentation_name)
    end

    false
  end

  def reserved_instrumentation_arg?(arg)
    RESERVED_INSTRUMENTATION_ARGS.include?(arg)
  end

  def encode_base64(value)
    Base64.strict_encode64(value).strip
  end

  def prepare_instrumentation_args(args)
    args.each_with_object({}) do |(key, value), result|
      key = key.to_s
      value_as_string = value.is_a?(String) ? value : value.to_json.to_s

      if reserved_instrumentation_arg?(key)
        next
      elsif !key.start_with?('detox')
        value_encoded = encode_base64(value_as_string)
      else
        value_encoded = value_as_string
      end

      result[key] = value_encoded
    end

  end

  def create_launch_args(launch_args = {})
    launch_args = prepare_instrumentation_args(launch_args)
    result = ""
    launch_args.each do |key, value|
      result += " -e #{key} #{value}"
    end
    result
  end

  def running_session?
    is_port_open?('127.0.0.1', @detox_port)
  end

  def terminate_app(bundle_id, retries)
    stop_detox_process('detox_client_instrumentation_pid', BrowserStack::SIGINT)
    retries.times do |index|
      begin
        @adb.am("force-stop #{bundle_id}")
      rescue AndroidToolkit::ADB::ADBError => e
        BrowserStack.logger.error "ADBError occured while terminating app with bundle_id: #{bundle_id}\
         message: #{e.message} retry_attempt: #{index + 1}"
        sleep 1
        next
      end
      return true
    end
    raise StandardError, "Error: After #{retries} attempts, failed to verify the launch of detox instrumentation."
  end

  def start_detox_client_instrumentation(session_id, instrumentation_name, launch_args = {})
    launch_args.each do |key, _value|
      launch_args.delete(key) if DETOX_IGNORED_LAUNCH_ARGS.include?(key)
    end
    cmd = "adb -s #{@device} shell $'am instrument -w -r"\
    " -e detoxServer ws://127.0.0.1:#{@detox_port}"\
    " -e detoxSessionId #{session_id}"\
    "#{create_launch_args(launch_args)}"\
    " -e debug false"\
    " #{instrumentation_name}' 2>&1"

    pid = spawn_process(cmd)
    sess_details = JSON.parse(File.read(@duplicate_session_file))
    sess_details['detox_client_instrumentation_pid'] = pid
    File.open(@duplicate_session_file, 'w') { |f| f.write(sess_details.to_json) }
  end

  def stop_detox_process(key, signal)
    pid = parse_duplicate_session_file(key)
    if !pid.nil? && pid != ""
      BrowserStack.logger.info(
        "stopping #{key}: #{pid}"
      )
      system("kill #{signal} #{pid}")
    end
  end

  def remove_reverse_port
    @adb.device_execute("reverse",  "--remove", "tcp:#{@detox_port}")
  end

  def parse_duplicate_session_file(key)

    JSON.parse(File.read(@duplicate_session_file))[key]
  rescue StandardError
    BrowserStack.logger.error "Key: #{key} not found in #{@duplicate_session_file}"
    nil

  end

  # This method is created because detox run-server command internally launches one more child process
  # in which the detox server is actually running. spawn_process returns pid of parent process in detox's case
  def detox_server_pid(cmd)
    detox_pid = `lsof -t -i :#{@detox_port}`.strip
    detox_server_process = `ps aux | grep #{detox_pid} | grep -v grep | grep '#{cmd}'`.strip
    raise FireCMDException, "Cannot find detox server pid" if detox_pid.to_s == "" || detox_server_process.to_s == ""

    detox_pid
  end

  def verify_app_launch(aut_bundle_id, retries)
    retries.times do |_|
      begin
        app_pid = @adb.shell("pidof", aut_bundle_id)
      rescue AndroidToolkit::ADB::ADBError
        app_pid = nil
      end

      return true if !app_pid.nil? && app_pid != ''

      sleep 1
    end

    raise StandardError, "Error: After #{retries} attempts, failed to verify the launch of detox instrumentation."
  end

  def ensure_detox_server_not_running(max_retries = 2)
    retries = 0
    while running_session? && retries < max_retries
      kill_cmd = "ps -ef | grep 'detox run-server --port #{@detox_port}' | grep -v grep | awk '{print $2}' | xargs kill"
      OSUtils.execute(kill_cmd)
      retries += 1
    end
    !running_session?
  rescue StandardError => e
    BrowserStack.logger.error "Some error occured while stopping detox server #{e.message}"
    false
  end

  def force_stop_session
    stop_detox_process('detox_client_instrumentation_pid', BrowserStack::SIGINT)
    stop_detox_process('detox_server_pid', BrowserStack::SIGKILL)
    ensure_detox_server_not_running(1)
    remove_reverse_port
  rescue StandardError => e
    BrowserStack.logger.error "Some error occured while stopping detox session #{e.message}"
  end
end
