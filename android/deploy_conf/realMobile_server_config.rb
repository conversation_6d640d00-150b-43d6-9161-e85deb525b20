rackup '/usr/local/.browserstack/mobile/android/deploy_conf/realMobile_config.ru'

# https://www.rubydoc.info/gems/puma/Puma/DSL#prune_bundler-instance_method
# This allows us to install new gems with just a phased-restart. Otherwise you
# need to take the master process down each time.
prune_bundler

port 45671

workers 2

# Overriding worker_shutdown_timeout (default 30) to avoid BMs on puma phased-restarts during start session request
# Related doc: https://browserstack.atlassian.net/wiki/spaces/ENG/pages/339149034/Avoiding+BMs+when+puma+server+phased-restart+is+triggered
worker_shutdown_timeout 300

persistent_timeout 75

tag 'real_mobile'

path_to_cert = File.expand_path('/usr/local/.browserstack/ssl_cert/mobile_host_cert.crt')
path_to_key = File.expand_path('/usr/local/.browserstack/ssl_cert/mobile_host_key.key')

if File.exist?(path_to_cert) && File.exist?(path_to_key)
  puts "SSL certificate and key found, starting Puma with SSL on port 45672"
  ssl_bind '0.0.0.0', '45672', {
    cert: path_to_cert,
    key: path_to_key
  }
else
  puts "SSL certificate and key not found, unable starting Puma with SSL on port 45672"
end
