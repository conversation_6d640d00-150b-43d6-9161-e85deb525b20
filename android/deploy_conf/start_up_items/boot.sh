#!/bin/bash
exec 2>>/var/log/browserstack/rc-local.log
exec 1>&2
echo "---------Started at $(date '+%F %T')----------"
set -e
set -x
export ADB_TRACE=all
setenforce 0
sleep 5
iptables --flush
iptables-restore < /etc/sysconfig/iptables || result=$? && true
if [ "$result" != "0" ]; then
    echo "retrying"
    sleep 5
    iptables-restore < /etc/sysconfig/iptables || retry_result=$? && true
    if [ ! -z "$retry_result" ] && [ "$retry_result" != "0" ]; then
        my_hostname=`hostname`
        curl -d people=mobile -d subject="boot failed on $my_hostname" -d message="boot failed on $my_hostname" -d mobile=true -d deploy=false https://alert-external.browserstack.com/alert
        exit $retry_result
    fi
fi
sudo service sshd reload
nohup /usr/local/.browserstack/mobile/common/getip.rb &
svscan /service/ >> /var/log/browserstack/service.log 2>&1 &
exit 0
