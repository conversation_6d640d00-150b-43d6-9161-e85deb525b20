BS_HOME=/usr/local/.browserstack/mobile
BUNDLE=/home/<USER>/bin/bundle

# Device check runs every minute to check state of devices
*/1 * * * * cd $BS_HOME && $BUNDLE exec ruby android/device_check.rb >> /var/log/browserstack/mobile_poll_error.log 2>&1

# Clean up some trace files created by appium
*/5 * * * * /usr/bin/find /tmp/ -mmin +120 -type d \( -name "xvfb-run*" -o -name "201*" \) -exec /usr/bin/rm -rf {} \;

# Hourly machine check to check all machine dependencies
0 */1 * * * cd $BS_HOME && $BUNDLE exec ruby android/machine_checks/machine_check.rb >> /var/log/browserstack/machine_check.log 2>&1

# Machine version check - check version of all locally checked-out code:
0 4 * * * cd $BS_HOME && $BUNDLE exec ruby ../mobile-common/machine_version_check/lib/check_runner.rb >> /var/log/browserstack/deploy_version_check.log 2>&1

# Fetch blocked domains lists and combine with global blocked domains:
5 2 * * * sh /usr/local/.browserstack/block_domains.sh &> /var/log/browserstack/block_domains.log
