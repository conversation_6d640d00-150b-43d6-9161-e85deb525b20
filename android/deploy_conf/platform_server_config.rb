rackup '/usr/local/.browserstack/mobile/android/deploy_conf/platform_server.ru'

# https://www.rubydoc.info/gems/puma/Puma/DSL#prune_bundler-instance_method
# This allows us to install new gems with just a phased-restart. Otherwise you
# need to take the master process down each time.
prune_bundler

port 45672

workers 2

# Overriding worker_shutdown_timeout (default 30) to avoid BMs on puma phased-restarts during start session request
# Related doc: https://browserstack.atlassian.net/wiki/spaces/ENG/pages/339149034/Avoiding+BMs+when+puma+server+phased-restart+is+triggered
worker_shutdown_timeout 300

persistent_timeout 75

tag 'platform_server'

path_to_cert = File.expand_path('/usr/local/.browserstack/ssl_cert/bstack_cert.crt')
path_to_key = File.expand_path('/usr/local/.browserstack/ssl_cert/bstack_key.key')

ssl_bind '0.0.0.0', '45673', {
  cert: path_to_cert,
  key: path_to_key
  # verify_mode: verify_mode         # default 'none'
  # reuse: true                       # optional
}

