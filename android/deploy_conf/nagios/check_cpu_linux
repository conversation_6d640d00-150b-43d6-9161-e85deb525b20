#!/bin/bash

OK=0
WARNING=1
CRITICAL=2
UNKNOWN=3

usage()
{
cat <<EOF

Check system load for Linux, FreeBSD, OSX, and AIX.

     Options:

	-a		Autodetect OS and CPUs
	-c <int> 	Critical threshold
	-o <os>		OS type, "linux/osx/freebsd/aix"
	-w <int>	Warning threshold

Usage:$0 -a
EOF
}

argcheck() {
# if less than n argument
if [ $ARGC -lt $1 ]; then
	echo "Missing arguments! Use \`\`-h'' for help."
	exit 1
fi
}

auto_os_detect() {

UNAME=$(uname)

 if [[ "$UNAME" == Darwin ]]; then
        OS="osx"
 elif [[ "$UNAME" == Linux ]]; then
        OS="linux"
 else
        echo "Unsupported OS type!"
        exit 1
 fi

}

determine_command () {

if [[ "$OS" == osx ]]; then
	CPU=$(iostat -w 1 -c 2 | tail -1 | awk '{print $4 + $5}')
elif [[ "$OS" == linux ]]; then
        CPU=$(iostat -c 1 2 | sed -e 's/,/./g' | tr -s ' ' ';' | sed '/^$/d' | tail -1 | awk -F \; '{print $2 + $4}')
else
	echo "OS not supported"
	exit $UNKNOWN
fi
}

ARGC=$#
CRIT=0
WARN=0
THRESHOLD=0
CPUUSAGE=0
AUTO_DETECT=0
OS=null
CRIT_STATUS=0
WARN_STATUS=0
OK_STATUS=0
DEFAULT=50

argcheck 1

while getopts "hac:o:w:" OPTION
do
     case $OPTION in
         h)
             usage
	     exit 0
             ;;
	 a)
	     AUTO_DETECT=1
	     auto_os_detect
	     ;;
	 c)
	     CRIT="$OPTARG"
	     THRESHOLD=1
             ;;
	 o)
	     echo "Need to implement \`\`-o'' yet"
	     exit 1
	     ;;
	 w)
	     WARN="$OPTARG"
	     THRESHOLD=1
	     ;;
         \?)
             exit 1
             ;;
     esac
done

determine_command

if [ $THRESHOLD -eq 0 ]; then

	for load in $CPU
	do
		CPUUSAGE=$(/usr/bin/printf "%d\n" $load 2>/dev/null)

		if [ $(echo $CPUUSAGE '>' $DEFAULT | bc -l)  -eq 1 ]; then
			CRIT_STATUS=$((CRIT_STATUS+1))
		elif [ $(echo $CPUUSAGE '>' $DEFAULT | bc -l) -eq 1 ]; then
			WARN_STATUS=$((WARN_STATUS+1))
		else
			OK_STATUS=$((OK_STATUS+1))
		fi
	done

fi

if [ $THRESHOLD -eq 1 ]; then

	for load in $CPU
	do
		CPUUSAGE=$(/usr/bin/printf "%d\n" $load 2>/dev/null)

		if [ $(echo $CPUUSAGE '>' $CRIT | bc -l)  -eq 1 ]; then
			CRIT_STATUS=$((CRIT_STATUS+1))
		elif [ $(echo $CPUUSAGE '>' $WARN | bc -l) -eq 1 ]; then
			WARN_STATUS=$((WARN_STATUS+1))
		else
			OK_STATUS=$((OK_STATUS+1))
		fi
	done
fi

if [ $CRIT_STATUS -gt 0 ]; then
	echo "CPU Usage CRITICAL: $CPU"
	exit $CRITICAL
elif [ $WARN_STATUS -gt 0 ]; then
	echo "CPU Usage WARNING: $CPU"
	exit $WARNING
else
	echo "CPU Usage: $CPU"
	exit $OK
fi
