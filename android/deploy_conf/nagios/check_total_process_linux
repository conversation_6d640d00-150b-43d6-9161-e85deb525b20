#!/bin/bash

OK=0
WARNING=1
CRITICAL=2
UNKNOWN=3

usage()
{
cat <<EOF

Check system load for Linux, FreeBSD, OSX, and AIX.

     Options:

	-a		Autodetect OS and CPUs
	-c <int> 	Critical threshold
	-o <os>		OS type, "linux/osx/freebsd/aix"
	-w <int>	Warning threshold

Usage:$0 -a
EOF
}

argcheck() {
# if less than n argument
if [ $ARGC -lt $1 ]; then
	echo "Missing arguments! Use \`\`-h'' for help."
	exit 1
fi
}

auto_os_detect() {

UNAME=$(uname)

 if [[ "$UNAME" == Darwin ]]; then
        OS="osx"
 elif [[ "$UNAME" == Linux ]]; then
        OS="linux"
 else
        echo "Unsupported OS type!"
        exit 1
 fi

}

determine_command () {

if [[ "$OS" == osx ]]; then
	NOOFPROCESS=$(ps -A | wc -l)
elif [[ "$OS" == linux ]]; then
	NOOFPROCESS=$(ps -A | wc -l)
else
	echo "OS not supported"
	exit $UNKNOWN
fi
}

ARGC=$#
CRIT=0
WARN=0
THRESHOLD=0
NO=0
AUTO_DETECT=0
OS=null
CRIT_STATUS=0
WARN_STATUS=0
OK_STATUS=0
DEFAULT=300

argcheck 1

while getopts "hac:w:" OPTION
do
     case $OPTION in
         h)
             usage
	     exit 0
             ;;
	 a)
	     AUTO_DETECT=1
	     auto_os_detect
	     ;;
	 c)
	     CRIT="$OPTARG"
	     THRESHOLD=1
             ;;
	 w)
	     WARN="$OPTARG"
	     THRESHOLD=1
	     ;;
         \?)
             exit 1
             ;;
     esac
done

determine_command

if [ $THRESHOLD -eq 0 ]; then

	for no in $NOOFPROCESS
	do
		NO=$(/usr/bin/printf "%d\n" $no 2>/dev/null)

		if [ $(echo $NO '>' $DEFAULT | bc -l)  -eq 1 ]; then
			CRIT_STATUS=$((CRIT_STATUS+1))
		elif [ $(echo $NO '>' $DEFAULT | bc -l) -eq 1 ]; then
			WARN_STATUS=$((WARN_STATUS+1))
		else
			OK_STATUS=$((OK_STATUS+1))
		fi
	done

fi

if [ $THRESHOLD -eq 1 ]; then

	for no in $NOOFPROCESS
	do
		NO=$(/usr/bin/printf "%d\n" $no 2>/dev/null)

		if [ $(echo $NO '>' $CRIT | bc -l)  -eq 1 ]; then
			CRIT_STATUS=$((CRIT_STATUS+1))
		elif [ $(echo $NO '>' $WARN | bc -l) -eq 1 ]; then
			WARN_STATUS=$((WARN_STATUS+1))
		else
			OK_STATUS=$((OK_STATUS+1))
		fi
	done
fi

if [ $CRIT_STATUS -gt 0 ]; then
	echo "No of process CRITICAL: $NOOFPROCESS"
	exit $CRITICAL
elif [ $WARN_STATUS -gt 0 ]; then
	echo "No of process WARNING: $NOOFPROCESS"
	exit $WARNING
else
	echo "No of process: $NOOFPROCESS"
	exit $OK
fi
