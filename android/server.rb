require 'English'
require 'rubygems'
require 'sinatra'
require 'json'
require 'socket'
require 'open3'
require 'net/http'
require 'uri'
require 'fileutils'
require 'bsdwh'
require 'base64'
require 'so_timeout_util'
require 'screenshot_manager'
require 'securerandom'
require 'static_conf'
require 'server_info'
require 'android_toolkit'
require 'dotenv/load'
require 'nokogiri'
require 'sirenlogs'
require 'timeout'
require 'env_middleware'
require 'interaction_sync_stability_tester'

BS_HOME = "/usr/local/.browserstack".freeze
DIR_HOME = "#{BS_HOME}/mobile".freeze
MOBILE_COMMON_HOME = "#{BS_HOME}/mobile-common".freeze
CONFIG_DIR = '/usr/local/.browserstack/config'.freeze

require "#{DIR_HOME}/common/helpers"
require "#{DIR_HOME}/android/helpers/utils"
require "#{DIR_HOME}/android/helpers/session_save_polling"
require "#{DIR_HOME}/android/helpers/rtcapp_release_helper"
require "#{DIR_HOME}/android/helpers/http_utils"
require "#{DIR_HOME}/android/helpers/network_simulator"
require "#{DIR_HOME}/android/helpers/custom_media_manager"
require "#{DIR_HOME}/android/helpers/camera_media_injector"
require "#{DIR_HOME}/android/helpers/file_injector"
require "#{DIR_HOME}/android/helpers/audio_injector"
require "#{DIR_HOME}/android/helpers/local_testing_chrome_extension_helper"
require "#{DIR_HOME}/android/helpers/download_files_helper"
require "#{DIR_HOME}/android/helpers/performance_statistics"
require "#{DIR_HOME}/android/helpers/device_browser_history"
require "#{DIR_HOME}/android/helpers/snapshotter"
require "#{DIR_HOME}/android/helpers/write_session_info"
require "#{DIR_HOME}/android/helpers/format_packages_param"
require "#{DIR_HOME}/android/helpers/battery_helper"
require "#{DIR_HOME}/android/helpers/live_media_injector"
require "#{DIR_HOME}/android/helpers/set_date_time_helper"
require "#{DIR_HOME}/android/playwright/playwright.rb"
require "#{DIR_HOME}/android/ai_proxy/ai_proxy.rb"
require "#{DIR_HOME}/android/helpers/crypto_mining_detection_helper"
require "#{DIR_HOME}/android/helpers/battery_instrumentor"
require "#{DIR_HOME}/android/helpers/popup_helper"
require "#{DIR_HOME}/android/helpers/settings_helper"
require "#{DIR_HOME}/android/espresso/helpers/espresso_helper.rb"
require "#{DIR_HOME}/android/maestro/helpers/maestro_helper.rb"
require "#{DIR_HOME}/android/espresso/helpers/logcat_buffer_manager.rb"
require "#{DIR_HOME}/android/detox/helpers/detox_helper.rb"
require "#{DIR_HOME}/android/helpers/crash_logs_helper.rb"
require "#{DIR_HOME}/android/lib/custom_exceptions.rb"
require "#{DIR_HOME}/android/lib/google_playstore_login_helper.rb"
require "#{DIR_HOME}/android/lib/device_driver.rb"
require "#{DIR_HOME}/android/lib/network_usage_tracker.rb"
require "#{DIR_HOME}/android/lib/device_logger_metric.rb"
require "#{DIR_HOME}/android/lib/app_actions.rb"
require "#{DIR_HOME}/android/helpers/app_injection"
require "#{DIR_HOME}/android/lib/lighthouse.rb"
require "#{DIR_HOME}/android/lib/bstack_reverse_tether_controller.rb"
require "#{DIR_HOME}/android/lib/automate_funnel.rb"
require "#{DIR_HOME}/android/lib/screenshot_instrumentation.rb"
require "#{DIR_HOME}/android/lib/internet_instrumentation.rb"
require "#{DIR_HOME}/android/helpers/device_passcode"
require "#{DIR_HOME}/android/helpers/google_pay_helper"
require "#{DIR_HOME}/android/helpers/fix_device_rotation"
require "#{DIR_HOME}/android/helpers/device_sensors_manager"
require "#{DIR_HOME}/android/helpers/device_time_format_manager"
require "#{DIR_HOME}/android/helpers/app_accessibility/app_accessibility.rb"
require "#{DIR_HOME}/android/version_managers/input_injector_main_app_manager.rb"
require "#{DIR_HOME}/android/version_managers/restriction_scripts_manager.rb"
require "#{DIR_HOME}/android/lib/browserstack_watcher_helper.rb"
require "#{DIR_HOME}/android/lib/talkback_helper.rb"
require "#{DIR_HOME}/android/lib/feature_flags"
require "#{DIR_HOME}/android/helpers/browser_helper.rb"
require "#{DIR_HOME}/android/percy/percy_session.rb"
require "#{DIR_HOME}/android/espresso/app_percy/app_percy_session.rb"
require "#{DIR_HOME}/android/percy/tiles_manager.rb"
require "#{DIR_HOME}/android/constants.rb"
require "#{DIR_HOME}/android/espresso/app_percy/cli_manager.rb"
require "#{DIR_HOME}/android/helpers/systemd_helper"
require "#{DIR_HOME}/android/helpers/unknown_apps_check.rb"

require "#{DIR_HOME}/common/push_to_zombie.rb"
require "#{DIR_HOME}/common/hooter.rb"

require "#{MOBILE_COMMON_HOME}/mobile_session_info/lib/mobile_session_info"

require 'browserstack_logger'
require "#{MOBILE_COMMON_HOME}/utils/server_utils"
require "#{MOBILE_COMMON_HOME}/utils/log_parse_util"
require "#{MOBILE_COMMON_HOME}/utils/app_patching_util"

require_relative "lib/os_utils"
require_relative 'lib/usb_vpn'
require_relative 'lib/overridden/thread'
require_relative 'helpers/utils'
require_relative 'helpers/browser_activity_monitoring'
require_relative 'proxies/proxy'
require_relative 'proxies/adb_forwarder/adb_forwarder'
require_relative 'models/android_device'
require_relative 'helpers/account_helper'
require_relative 'helpers/device_sim_helper'
require_relative 'user_exposed_adb'
require_relative 'device_preferences_manager'
require_relative 'mobile_cspt'
require_relative 'version_managers/geoguard_manager'
require_relative 'device_logger/device_logger'
require_relative 'helpers/custom_certificate_helper'
require_relative 'helpers/transparent_network_mode'
require_relative 'version_managers/device_owner_manager'
require_relative "helpers/network_logs_util"

include BrowserStack # rubocop:todo Style/MixinUsage
include StaticConf # rubocop:todo Style/MixinUsage
include AppPatchingUtil # rubocop:todo Style/MixinUsage
include Proxy # rubocop:todo Style/MixinUsage
include SystemDHelper # rubocop:todo Style/MixinUsage

static_conf = StaticConf::StaticConfHelper.setup

set :static_conf, static_conf

register StaticConf
register Sirenlogs

URL_REGEX = %r{^(http|https)://[a-z0-9]+([\-.]{1}[a-z0-9]+)*(:[0-9]{1,5})?(/.*)?$}ix.freeze

# set timezone to utc
ENV['TZ'] = 'UTC'

SCRIPT = "#{DIR_HOME}/android/driver_actions.sh".freeze
JS_SCRIPT = "#{DIR_HOME}/android/js_testing/js_driver.sh".freeze
LIVE_SCRIPT = "#{DIR_HOME}/android/live/scripts/live_actions.sh".freeze
APP_LIVE_SCRIPT = "#{DIR_HOME}/android/live/scripts/app_live_actions.sh".freeze
SELENIUM_SCRIPT = "#{DIR_HOME}/android/helpers/selenium_helper.sh".freeze
SELENIUM_SCRIPT_PARALLEL = "#{DIR_HOME}/android/helpers/selenium_helper_parallel.sh".freeze

ESPRESSO_SESSION_TRIGGER = "#{DIR_HOME}/android/espresso/scripts/espresso_trigger.sh".freeze
ESPRESSO_SCRIPT = "#{DIR_HOME}/android/espresso/scripts/espresso_actions.sh".freeze
COMMON = "#{DIR_HOME}/common".freeze
CONFIG_FILE = "#{CONFIG_DIR}/config.json".freeze
DEVICE_FILTER_PATH = [
  "/start", "/app_start", "/stop", "/tunnel", "/snapshot", "/snapshot_hub",
  "/screenshot", "/device_params", "/handle_media_projection_permission", "/geturl", "/update_device_locale_and_region",
  "/selenium_command", "/start_espresso_session", "/timeout_session",
  "/push_file", "/orientation", "/custom_screenshot", "/remove_device",
  "/update_app", "/app_update_pid", "/update_device_log_level", "/set_geolocation", "/get_geolocation",
  "/kill_all_apps", "/log_bridge_commands", "/user_installed_apps",
  "/user_installed_app_details", "/update_network", "/reset_app", "/accessibility_info", "/toggle_continuous_scanning",
  "/stop_espresso_session", "/get_snapshot_details", "/get_screenshot", "/uninstall_user_apps", "/install_app",
  "/inject_image", "/inject_camera_media", "/inject_audio", "/play_audio", "/stop_audio", "/execute_adb_command",
  "/app_percy/screenshot", "/percy/start_server", "/percy/stop_server", "/percy/health_check",
  "/percy/start_jackproxy", "/percy/stop_jackproxy", "/percy/capture_tile", "/percy/capture_finalize",
  "/percy/is_running", "/percy/keep_alive", "/media/push_to_device", "/percy/clean_mobile",
  "/percy/setup_automate_session", "/percy/screenshot", "/percy/get_metadata", "/percy/dom_metadata_finalize",
  "/percy/dom_metadata_upload", "/start_fluttertest_session", "/stop_fluttertest_session", "/set_cleanup_policy",
  "/download_files", "/settings", "/restart", "/launch_detox_instrumentation", "/trigger_interaction_sync_script",
  "/recover_device", "/app_percy/disable_animation", "/is_device_rooted",
  "/start_maestro_session", "/stop_maestro_session", "/device_logs"
].freeze
GET_URL = "#{DIR_HOME}/android/live/scripts/getLastUrl.sh".freeze
DEVTOOLS_URL = "https://%s/json?debug_port=%s".freeze
DEBUG_SCREENSHOT_SCRIPT = "#{DIR_HOME}/android/helpers/screenshot.rb".freeze
VIDEO_DEFAULT_WIDTH = "800".freeze # default width changed to 800 since in landscape mode,
                            # the previous width of 400 resulted in loss of details & blurred video.
VIDEO_DEFAULT_HEIGHT = "800".freeze
SESSION_START_DIR = "#{STATE_FILES_DIR}/session_start".freeze

APP_ACTION_NEW_WHITELIST_CONF = File.read("#{PRIVOXY_TEMPLATES}/app_action_new_whitelist.conf")
CUSTOM_PROP = "debug.test".freeze

APPIUM_BRIDGE_START_MARKER = ">>>>BrowserStack>>>>".freeze
APPIUM_BRIDGE_STOP_MARKER = "<<<<BrowserStack<<<<".freeze
WHATSMYIP = begin
  File.read("/usr/local/.browserstack/whatsmyip").strip
rescue StandardError
  ""
end

SERVER_LOG = "/var/log/browserstack/server.log".freeze
SOTIMEOUT_PROCESS_TIMEOUT = 120

# do not use in prod endpoints
WHITE_LISTED_APPS_FOR_QA_AUTOMATION_TESTING = [
  "com.android.packageinstaller", "com.google.android.packageinstaller",
  "com.android.contacts", "com.google.android.dialer", "com.android.dialer",
  "com.samsung.android.contacts", "com.lge.lockscreensettings",
  "com.samsung.android.dialer", "com.samsung.android.MtpApplication.USBConnection",
  "com.android.chrome"
].freeze

WHITELISTED_PATHS_FOR_SAFE_PARAMS = [
  "/adb", "/execute_adb_command"
].freeze

DEDICATED_DEVICE_FILE_IN_DEVICE_PATH = "/sdcard/private_cloud".freeze
RESTRICTION_MANAGER_VERSION_FILE = "/data/local/tmp/restrictions_manager.version".freeze

STORAGE_PERMISSIONS = %w[android.permission.READ_EXTERNAL_STORAGE android.permission.WRITE_EXTERNAL_STORAGE].freeze

PUSH_FEATURE_USAGE_FOR_PATHS = ["/start_espresso_session", "/selenium_command", "/start_fluttertest_session",
                                "/start_maestro_session"].freeze

FREETOOLS_WORKSPACE = "#{BS_HOME}/free_tools".freeze
LIGHTHOUSE_LOG_PATH = "/var/log/browserstack/freetools_lighthouse.log".freeze
DEV_TOOL_PORT = 443

# Here, watcher app keeps running while devices are not in session but we want to restart watcher on session start.
#                         __________Huawei P30___________
RESTART_WATCHER_MODELS = ["ELE-L04", "ELE-L09", "ELE-L29"].freeze

feature_flags = FeatureFlags.new
SECRET_SCANNING_FLAG = feature_flags&.get_flag("secret_scanning")
RAILS_ENV_RESTRICIONS_FLAG = feature_flags&.get_flag("rails_env_restrictions")

REDACTION_STRING = "[REDACTED]".freeze

include ServerUtils # rubocop:todo Style/MixinUsage
include LogParseUtil # rubocop:todo Style/MixinUsage

def script_logger_args(progname='')
  params = BrowserStack.logger.params.merge({ progname: progname })
  BrowserStack::ScriptFormatter.new.call(params)
end

def script_logger_args_v2(progname='')
  params = BrowserStack.logger.params.merge({ progname: progname })
  BrowserStack::ScriptFormatterWithoutDate.new.call(params)
end

def start_logger(params={})
  session = params[:app_live_session_id] || params[:live_session_id] || params[:automate_session_id]
  session = get_session_id(params[:device]) if session.nil? || session.empty?

  BrowserStack.logger.params[:component] = 'server.rb'
  BrowserStack.logger.params[:device] = params[:device]
  BrowserStack.logger.params[:subcomponent] = request.path_info
  BrowserStack.logger.params[:session] = session
end

configure do
  logger_params = { component: 'server.rb' }
  BrowserStack.init_logger(SERVER_LOG, logger_params)
end

# rubocop:disable Layout/LineLength
before do
  unless safe_params?(params)
    start_logger
    BrowserStack.logger.error("Illegal character(s) detected in params: #{params}")
    unless WHITELISTED_PATHS_FOR_SAFE_PARAMS.include?(request.path_info)
      halt(400, 'Illegal character(s) found in request params')
    end
  end

  start_logger(params)
  BrowserStack.logger.info(
    "New #{request.request_method} request for url: #{request.url} with params: #{redact_params(params, request)} | #{request.scheme} #{request.ip} #{request.port} #{request.path_info}"
  )
  @eds_obj = EDS.new({}, BrowserStack.logger)
  @influxdb_client = BrowserStack::AndroidInfluxdbClient.new(
    BrowserStack.logger,
    BrowserStack.logger.params
  )
end
# rubocop:enable Layout/LineLength

DEVICE_FILTER_PATH.each do |path|
  before path do
    @hooter = Hooter.new
    @device = params["device"]
    halt 404, {}, "device not found" if @device.nil?
    config = JSON.parse(read_with_lock(CONFIG_FILE))
    @devices_json = config["devices"]
    halt 500, {}, "Not my device" unless @devices_json[@device]
    @device_name = @devices_json[@device]["device_name"]
    unless path == '/remove_device' # some broken devices that need to be removed
      # will throw error here if we try to read from db
      extended_params = add_device_features_to_params(params, @devices_json)
      params.merge!(extended_params)
    end

    headers['Access-Control-Allow-Methods'] = 'GET, POST'
    headers['Access-Control-Allow-Origin'] = '*'
  end

  after path do
    unless @device_name.nil?
      browser = "chrome" if path == "/geturl"
      @hooter.send(@device_name, path.sub(%r{/}, ""), browser)
    end
  end
end

BrowserStack::ENV_START_REQUEST_MIDDLEWARE_PATHS.each do |path|
  before path do
    status = EnvMiddleware.process_start_request(params['device'], request, path)
    if RAILS_ENV_RESTRICIONS_FLAG['enabled'] && status == :reject
      halt 409, "Request rejected due to Rails env restrictions"
    end
  end
end

BrowserStack::ENV_NON_START_REQUEST_MIDDLEWARE_PATHS.each do |path|
  before path do
    status = EnvMiddleware.process_non_start_request(params['device'], request, path)
    if RAILS_ENV_RESTRICIONS_FLAG['enabled'] && status == :reject
      halt 409, "Request rejected due to Rails env restrictions"
    end
  end
end

after do
  Thread.bs_run do
    if SECRET_SCANNING_FLAG['sinatra']
      regex_to_match = SECRET_SCANNING_FLAG['regex_to_match']
      regex_to_redact = SECRET_SCANNING_FLAG['regex_to_redact']
      EnvMiddleware.scan_request_for_secrets(request, regex_to_match, regex_to_redact,
                                             BrowserStack::SECRET_SCANNING_WHITELISTED_REQUESTS)
    end
  end
end

PUSH_FEATURE_USAGE_FOR_PATHS.each do |path|
  after path do
    break if params[:genre].nil?

    eds_event = if params[:genre].to_s.eql?(
      'app_automate'
    )
                  EdsConstants::APP_AUTOMATE_TEST_SESSIONS
                else
                  EdsConstants::AUTOMATE_TEST_SESSIONS
                end
    data_to_push = {
      feature_usage: @feature_usage,
      hashed_id: get_session_id_from_params(params),
      timestamp: Time.now.to_i
    }

    @eds_obj.push_logs(eds_event, data_to_push)
  end
end

def write_params_to_file(params, filename)
  File.open(filename, "w+") do |file|
    file.write(params.to_json)
  end
end

def get_session_id_from_params(params)
  params["app_live_session_id"] || params["live_session_id"] || params["automate_session_id"] || params["session_id"]
end

def is_automate_or_app_automate(genre)
  %w[selenium app_automate].include?(genre)
end

def disable_app_profiling?(params)
  app_automate_custom_params = params["app_automate_custom_params"] || {}
  app_automate_custom_params["disable_app_profiling"].to_s == "true"
rescue StandardError => e
  BrowserStack.logger.error("Error in disable_app_profiling?: #{e.message}")
  false
end

def pre_install_app?(params)
  app_automate_custom_params = params["app_automate_custom_params"] || {}
  app_automate_custom_params["preInstallApp"].to_s == "true"
rescue StandardError => e
  BrowserStack.logger.error("Error in checking preInstallApp: #{e.message}")
  false
end

def force_resign_app?(params)
  app_automate_custom_params = params["app_automate_custom_params"] || {}
  app_automate_custom_params["force_resign_aed"].to_s == "true"
rescue StandardError => e
  BrowserStack.logger.error("Error in fetching force_resign flag for AED: #{e.message}")
  false
end

#For automate sessions, check that each session is receiving chrome requests in privoxy logs
def check_no_chrome_requests(params)
  device = params["device"]
  privoxy_log_path = "/var/log/browserstack/privoxy-#{device}.log"
  session = get_session_id_from_params(params)
  if params["genre"] == "automate"
    chrome_request_received = `grep -c User-Agent.*Chrome #{privoxy_log_path}`
    if !chrome_request_received.to_s == "0"
      report = File.open("#{STATE_FILES_DIR}/no_chrome_requests_found_#{device}", "w") do |f|
        f.write("No chrome requests for #{device} during session #{session}")
      end
    end
  end
end

# TODO: move to common code
def validate_url(url, block_google_signup)
  url ||= "http://www.google.com/ncr"
  if block_google_signup == true && url.include?("accounts.google.com/lifecycle/steps/signup")
    BrowserStack.logger.info 'Reseting the google signup url'
    url = "http://www.google.com/ncr"
  end
  uri = URI.parse(url)
  url
rescue URI::InvalidURIError => e
  # FIXME: URI.encode is considered harmful and deprecated
  # See: https://stackoverflow.com/a/********/6743243
  URI.encode(url)
end

def check_and_log_usb_internet(params, vpn_disabled)
  BrowserStack.logger.info 'Checking usb internet'
  internet_stats = BrowserStack::InternetInstrumentation.new(params["genre"], @device,
                                                             get_session_id_from_params(params))

  if params["no_vpn_device"] || vpn_disabled == true || !File.exist?("/usr/local/.browserstack/usbVpn")
    if params["genre"] == "app_automate" || params["genre"] == "live_testing"
      push_to_cls(params, "device_internet_onstart", '', { "internet_through" => "wifi" })
    end
    BrowserStack.logger.info 'not doing usb internet check'
    internet_stats.push_internet_type_to_zombie("wifi", params)
    return
  end

  BrowserStack.logger.info 'Launching process to check internet'

  Thread.bs_run do
    tunexists = UsbVPN.new(@device).vpn_tunnel_exists?
    if !tunexists
      internet_stats.push_internet_type_to_zombie("fallback-to-wifi", params)
      if params["genre"] == "app_automate" || params["genre"] == "live_testing"
        push_to_cls(params, "device_internet_onstart", '', { "internet_through" => "wifi - no tun" })
        return
      end
      `#{COMMON}/push_to_cls.rb #{CONFIG_DIR}/rtc_service_#{@device} 'no tun interface' '' device_id:#{@device}`
      `#{COMMON}/push_to_zombie.rb "android" "android-usb-vpn" "no tun interface : #{@device}" "#{@device_name}"`
      @influxdb_client.track_event("usb-internet-not-configured", "server", "session_start", @device, 'true')
      BrowserStack.logger.error 'no tun interface found!'
    else
      internet_stats.push_internet_type_to_zombie("usb", params)
      if params["genre"] == "app_automate" || params["genre"] == "live_testing"
        push_to_cls(params, "device_internet_onstart", '', { "internet_through" => "usb" })
        return
      end
      out =
        `#{COMMON}/push_to_cls.rb #{CONFIG_DIR}/rtc_service_#{@device} 'this device has vpn' '' device_id:#{@device}`

    end

    if params["genre"] == "app_live_testing"
      eds_data = {
        event_name: "VPNEnabled",
        genre: params["genre"],
        device_id: @device.to_s,
        device_version: params[:device_version],
        device_name: params["version"],
        tun_exists: tunexists,
        session_id: get_session_id_from_params(params),
        product: "app_live",
        streaming_through_wire_flag: params['streaming_through_wire'] || "false"
      }
      @eds_obj.push_logs(EdsConstants::APP_LIVE_WEB_EVENTS, eds_data, params)
    end
  end
end

def kill_all_apps_and_restart_current_app
  app_actions = BrowserStack::AppActions.new({ device_id: @device })
  current_app_activity = app_actions.get_current_app_activity
  return if current_app_activity.to_s == ""

  script_logger = script_logger_args("#{File.basename(SCRIPT)}_update_device_locale_and_region")
  app_actions.kill_all_user_apps(script_logger)
  app_actions.start_app(current_app_activity)
end

get '/' do
  "to be or not to be!"
end

get '/devices' do
  config = JSON.parse(File.read(CONFIG_FILE))
  config["devices"].to_json
end

get '/git_info' do
  [200, ServerInfo.git_info.to_json]
rescue StandardError => e
  BrowserStack.logger.error("Error fetching git details: #{e.message}")
  [500, { error: e.message }.to_json]
end

get '/devices_connected' do
  device_status = {}
  device_threads = []
  devices = `adb devices | grep device | grep -v devices | awk '{print $1}'`.split("\n")
  devices.each do |x|
    device_threads << Thread.bs_run(x) do
      timeout(5) do
        `timeout 5 adb -s #{x} shell "date"`
        device_status[x] = 'on adb'
      end
    rescue Timeout::Error => e
      device_status[x] = 'adb stuck'
    end
  end
  device_threads.each(&:join)
  device_status.to_json
end

get '/open_app' do
  if static_conf['rails_endpoint']&.include?("browserstack.com")
    halt 404,
         { statusCode: 1, statusMessage: "Not available on production",
           data: nil }.to_json
  end

  device = params[:device]
  package_name = params[:package_name]

  unless WHITE_LISTED_APPS_FOR_QA_AUTOMATION_TESTING.include? package_name
    halt 422,
         { statusCode: 1, statusMessage: "#{package_name} can't be opened",
           data: nil }.to_json
  end

  app_start_command = "adb -s #{device} shell am start -n $(adb -s #{device} shell cmd package "\
                      "resolve-activity --brief -c android.intent.category.LAUNCHER #{package_name} | tail -1 )"
  system(app_start_command)
  { statusCode: 0, statusMessage: "#{package_name} has been launched", data: nil }.to_json
end

get '/custom_screenshot' do
  orientation = params[:orientation].to_s
  x = params[:x].to_s
  y = params[:y].to_s
  width = params[:width].to_s
  height = params[:height].to_s
  ret_val = system("sh", SELENIUM_SCRIPT, "custom_screenshot", @device.to_s, orientation, x, y, width, height)
  img_data = open("/tmp/screenshot_#{@device}.png", 'rb', &:read)
  system("rm /tmp/screenshot_#{@device}.png")
  { device: @device, status: 0, value: Base64.encode64(img_data) }.to_json
end

def enable_requested_browser(browser_name, device, os_version, session_id, genre)
  browser_helper = BrowserHelper.new(device, os_version, BrowserStack.logger, session_id, genre)
  browser_helper.enable_browser(BROWSER_PACKAGE_MAP[browser_name]) if BROWSER_PACKAGE_MAP[browser_name]
end

get '/start' do
  params[:event_hash] = {
    absolute_start_time: (Time.now.to_f * 1000).to_i
  }
  mark_event_start('total_ruby_start_time', params[:event_hash])

  mark_event_start('pre_start_check', params[:event_hash])
  halt 500, 'Terminal already allocated!' unless pre_start(@device, params)
  mark_event_end('pre_start_check', params[:event_hash])

  device_name = @devices_json[@device]["device_name"]

  mark_event_start('start_session_save_polling', params[:event_hash])
  params["set_cookies"] = true
  start_session_save_polling(@device, params)
  mark_event_end('start_session_save_polling', params[:event_hash])

  mark_event_start('assign_variables', params[:event_hash])
  is_audio_injection_session = ["true", true].include?(params[:is_audio_injection_session])
  is_google_pay_session = params[:is_google_pay_session].to_s == "true"
  enable_google_account_sign_in = params[:enable_google_account_sign_in].to_s == "true"
  is_sync_webrtc = ["true", true].include?(params[:is_sync_webrtc])

  params[:debugger_port] = @devices_json[@device]["debugger_port"]
  blank_url = params.key?('use_blank_url') ? params[:use_blank_url] : "0"
  using_new_thread = params[:android_new_thread_execution].to_s == "true"
  mark_event_end('assign_variables', params[:event_hash])

  GeoGuardManager.new(@device, BrowserStack.logger).ensure_install(params[:mobile_install_geoguard_app])
  enable_mobile_data = check_and_enable_mobile_data?(@device, params, @devices_json[@device])
  if enable_mobile_data
    enable_brt_for_browserstack_apps(@device)
    zombie_push('android', "enabled-mobile-data", "", "", params['genre'], @device, get_session_id_from_params(params))
  end

  mark_event_start('device_logger_init', params[:event_hash])
  device_logger_helper = DeviceLogger.new(@device)
  device_logger_helper.init
  mark_event_end('device_logger_init', params[:event_hash])

  mark_event_start('crypto_mining_detection', params[:event_hash])
  # Enable dns logs capture to detect possible crypto mining
  exec_block(using_new_thread) do
    CryptoMiningDetectionHelper.new(@device, BrowserStack.logger).enable_network_logging
  end
  mark_event_end('crypto_mining_detection', params[:event_hash])

  mark_event_start('write_session_info', params[:event_hash])

  block_google_signup = params.key?('abuse_prevention_google_signup_blocking') &&
                          params[:abuse_prevention_google_signup_blocking].to_s == "true"
  params[:url] = validate_url(params[:url], block_google_signup)

  params[:device_browser] = if params.key?('device_browser') && !params['device_browser'].empty?
                              params['device_browser']
                            else
                              "chrome"
                            end
  WriteSessionInfo.new(BrowserStack.logger, @device, params).save
  if params[:device_browser] == "samsung"
    params[:device_browser] = "internet"
    params["device_browser"] = "internet"
    add_samsung_command_line_file_live_session(@device)
  end
  if params[:device_browser] == "ucbrowser"
    params[:device_browser] = "ucmobile"
    params["device_browser"] = "ucmobile"
  end
  mark_event_end('write_session_info', params[:event_hash])

  mark_event_start('tunnel_setup', params[:event_hash])
  isLocalEnabled = (params["hosts"].to_s != "")

  if params["localTestingChromeExtensionEnabled"].to_s == "true"
    localTestingChromeExtension = LocalTestingChromeExtension.new(@device, get_session_id_from_params(params))
    localTestingChromeExtension.touch_state_file
  end

  if !enable_mobile_data && (isLocalEnabled || (params[:new_error_page] == "true" && params[:tunnelPresent] != "true" &&
                        params[:is_api].to_s != "true"))
    BrowserStack.logger.info "Start doing host setting: #{params['host']}"
    proxy_port = calculate_privoxy_port(@devices_json[@device]["port"])
    tunnel_setup_thread = exec_block(using_new_thread) do
      tunnel_setup(params)
    end
  end
  port = @devices_json[@device]["port"]
  tun_ip = calc_usb_tunnel_ip(port)
  vpn_disabled = false

  tun_counter = port.to_i - 8078
  mark_event_end('tunnel_setup', params[:event_hash])

  if params[:trigger_public_cleanup].to_s == "true"
    FileUtils.touch("#{STATE_FILES_DIR}/forced_public_cleanup_#{@device}")
  end

  if params[:is_api]
    mark_event_start('is_api', params[:event_hash])
    proxy_port = "none" if proxy_port.nil? || proxy_port.empty?
    start_video_rec(@device, params, 4) if params[:video].to_s == "true"

    # Device check will not know the device is in session unless this file exists.
    FileUtils.touch("#{STATE_FILES_DIR}/session_#{@device}")
    chrome_flags = params.key?(:js_chrome_flags) ? JSON.parse(params[:js_chrome_flags]).join("|") : ""
    script_logger = script_logger_args("#{File.basename(JS_SCRIPT)}_start")
    cmd = "sh #{JS_SCRIPT} start #{@device} \"#{params[:url]}\" #{proxy_port} \"#{chrome_flags}\"  2>&1 | "\
          "while read line; do echo #{script_logger} \"$line\"; done"
    BrowserStack.logger.info "Starting js_testing with command: #{cmd}"
    system(cmd)
    mark_event_end('is_api', params[:event_hash])
  elsif params[:genre].to_s == "live_testing"
    params[:device_version] = @devices_json[@device]["device_version"]
    device_version = params[:device_version].to_f
    if device_version < 14.0 || params[:is_multitab_devtool_enabled].to_s == "true"
      params[:debugger_url] = format(DEVTOOLS_URL, @devices_json[@device]["ip"], params[:debugger_port])
    end
    session_id = get_session_id_from_params(params)

    mark_event_start('enable_requested_browser', params[:event_hash])
    os_version = @devices_json[@device]["device_version"]
    if params['device_browser'] && params['device_browser'].downcase != "chrome" &&
       os_version.to_i >= 7 && os_version.to_i < 10
      begin
        enable_requested_browser(params['device_browser'].downcase, @device, os_version, session_id, "live_testing")
      rescue BrowserHelperError => e
        BrowserStack.logger.error(e.message.to_s)
        return 500, "failed to enable requested browser #{params['device_browser']}"
      end
    end
    mark_event_end('enable_requested_browser', params[:event_hash])

    mark_event_start('image_injection', params[:event_hash])
    is_camera_toggled = (params[:is_camera_toggled_live] == "true") && (params[:device_browser] == "chrome")
    live_media_injection(params[:image_injection_media_data]) if is_camera_toggled &&
                                                                 params[:image_injection_media_data]
    mark_event_end('image_injection', params[:event_hash])

    mark_event_start('write_rtc_params', params[:event_hash])
    args = write_rtc_params(params)
    if File.exist?("#{STATE_FILES_DIR}/session_#{@device}")
      copy_file("#{CONFIG_DIR}/rtc_service_#{@device}", "#{STATE_FILES_DIR}/session_#{@device}")
    end
    mark_event_end('write_rtc_params', params[:event_hash])

    async_start = params[:async_start] || false

    mark_event_start('stop_vpn_for_device', params[:event_hash])
    # MOB-10446 Switching off USB internet only issue on some devices running Android 8.0
    if device_version <= 8.0 && !params["geoLocation"] && should_stop_vpn?(isLocalEnabled, params[:device_browser])
          # Route the web traffic via WiFi
      # so we need to stop the usb-vpn-tether running which routes mobile traffic from the device via the host server
      stop_vpn_for_device(tun_counter)
      vpn_disabled = true
    end
    mark_event_end('stop_vpn_for_device', params[:event_hash])

    mark_event_start('enable_fake_media_stream', params[:event_hash])
    fake_media_stream = BrowserStack::LiveMediaInjector.enable_fake_media_stream(@device) if is_camera_toggled
    mark_event_end('enable_fake_media_stream', params[:event_hash])

    mark_event_start('script_logger', params[:event_hash])
    script_logger = script_logger_args("#{File.basename(LIVE_SCRIPT)}_start")
    mark_event_end('script_logger', params[:event_hash])

    mark_event_start('audio_injection', params[:event_hash])
    inject_audio_at_session_start(params) if is_audio_injection_session
    mark_event_end('audio_injection', params[:event_hash])

    mark_event_start('stop_talkback', params[:event_hash])
    # enable audio handles media projection pop up
    if params[:use_rtc_app] == "v2" && params[:device_version].to_i >= 10
      params[:enable_audio] = "true"
      exec_block(using_new_thread) do
        talkback_helper = TalkbackHelper.new(@device)
        talkback_helper.stop_talkback if talkback_helper.talkback_on?
      end
    else
      params[:use_rtc_app_audio] = "false"
    end
    mark_event_end('stop_talkback', params[:event_hash])

    mark_event_start('live_actions_bash', params[:event_hash])
    BrowserStack.logger.info "MediaProjection params: use_rtc_app=#{params[:use_rtc_app]}, device_version="\
                             "#{params[:device_version].to_i}, enable_audio=#{params[:enable_audio]},"\
                              "enable_bs_ime=#{params[:enable_bs_ime]}"

    start_method = params[:android_parallel_execution_of_methods].to_s == "true" ? "async_start" : "start"
    cmd = ["bash", LIVE_SCRIPT, start_method, @device.to_s, args[:interaction_args].to_s, \
           params[:url].to_s, args[:stream_width].to_s, \
           async_start.to_s, tun_ip.to_s, proxy_port.to_s, params[:debugger_port].to_s, params[:use_users_url].to_s, \
           blank_url.to_s, params[:loader_trigger].to_s, params[:device_browser].to_s, params[:enable_audio].to_s, \
           params[:use_rtc_app].to_s, fake_media_stream.to_s, params[:use_rtc_app_audio].to_s, \
           params[:enable_rotation_using_app_orientation].to_s, is_audio_injection_session.to_s, \
           params[:skip_blank_page_before_url_opening].to_s, params[:enable_bs_ime].to_s, params[:enable_sim_live].to_s,
           is_sync_webrtc.to_s, is_google_pay_session.to_s, enable_google_account_sign_in.to_s,
           params[:handle_mp_during_rotation].to_s, { err: %i[child out] }]

    cmd_out = "while read line; do echo #{script_logger} $line; done"
    cmd_string = "#{cmd.inspect} | #{cmd_out} &"
    BrowserStack.logger.info "Starting Live with command: #{cmd_string}"
    begin
      Open3.pipeline_start(cmd, cmd_out) unless File.exist?("#{STATE_FILES_DIR}/cleanupdone_#{@device}")
    rescue Exception => e
      BrowserStack.logger.error "Command #{cmd_string} failed with #{e.message}"
    end
    mark_event_end('live_actions_bash', params[:event_hash])

    mark_event_start('chrome_version_check', params[:event_hash])
    if @devices_json[@device]["mobile_chrome_version"].nil? || @devices_json[@device]["mobile_chrome_version"].empty? ||
       @devices_json[@device]["mobile_chrome_version"].split(".").first.to_i < 45
      chrome_version = @devices_json[@device]['mobile_chrome_version']
      cmd = %(#{COMMON}/push_to_cls.rb #{CONFIG_DIR}/rtc_service_#{@device} )
      cmd += %( 'chrome version mismatch' '' )
      cmd += %( "device_id:#{@device} , chrome_version:#{chrome_version}" )
      out = `#{cmd}`
    end
    mark_event_end('chrome_version_check', params[:event_hash])

    mark_event_start('enable_bs_ime', params[:event_hash])
    exec_block(using_new_thread) do
      enable_bs_ime(@device) if params[:enable_bs_ime]
    end
    mark_event_end('enable_bs_ime', params[:event_hash])

    mark_event_start('set_gps_location', params[:event_hash])
    set_gpslocation(params["latitude"], params["longitude"], params["genre"], session_id)
    mark_event_end('set_gps_location', params[:event_hash])

    mark_event_start('log_repeater_connectivity', params[:event_hash])
    log_repeater_connectivity(proxy_port) if isLocalEnabled && proxy_port
    mark_event_end('log_repeater_connectivity', params[:event_hash])
  end

  mark_event_start('start_browser_activity_monitoring', params[:event_hash])
  start_browser_activity_monitoring(@device, params)
  mark_event_end('start_browser_activity_monitoring', params[:event_hash])

  mark_event_start('check_and_log_usb_internet', params[:event_hash])
  check_and_log_usb_internet(params, vpn_disabled)
  mark_event_end('check_and_log_usb_internet', params[:event_hash])

  mark_event_start('start_network_usage_tracker', params[:event_hash])
  start_network_usage_tracker(@device, params[:session_id], "live_testing")
  mark_event_end('start_network_usage_tracker', params[:event_hash])

  mark_event_start('monitor_device_logger_metric', params[:event_hash])
  monitor_device_logger_metric(@device, get_session_id_from_params(params))
  mark_event_end('monitor_device_logger_metric', params[:event_hash])

  mark_event_end('total_ruby_start_time', params[:event_hash])

  if block_google_signup
    BrowserStack.logger.info("server abuse_prevention_google_signup_blocking adding file")
    mark_event_start('add_google_signup_restriction_file_watcher', params[:event_hash])
    watcher_helper =
      WatcherHelper.new(@device, get_session_id_from_params(params), params[:genre], BrowserStack.logger)
    watcher_helper.add_google_restriction_file_to_watcher
    mark_event_end('add_google_signup_restriction_file_watcher', params[:event_hash])
  end

  BrowserStack.logger.info("LSE ruby_start_instrumentation: #{params[:event_hash]}")
  Thread.bs_run do
    push_to_cls(params, 'ruby_start_instrumentation', '', params[:event_hash])
  end

  # tunnel_setup is critical for session start
  # waiting for it to complete
  tunnel_setup_thread&.join

  if params[:is_devtools_enabled_on_edge].to_s == "true" && params[:device_browser] == 'edge'
    begin
      adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
      adb.shell("am force-stop com.android.chrome")
      BrowserStack.logger.info "is_devtools_enable_on_edge: Sent command to kill Chrome for Edge session"
    rescue StandardError => e
      BrowserStack.logger.error "is_devtools_enable_on_edge: Error while killing Chrome: #{e.message}"
    end
  end
  { "port" => port.to_s, "proxy_port" => proxy_port.to_s }.to_json
rescue Exception => e
  status 500
  BrowserStack.logger.error("Exception in /start #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
  push_to_cls(
    params,
    'Exception in /start',
    e.message.to_s, {
      "device_id" => @device,
      "backtrace" => e.backtrace.join("\n")[0..2000],
      "error_message" => e.message.to_s
    }
  )
  e.message
end

def start_browser_activity_monitoring(device, params)
  return if params[:enable_url_detection].to_s != "true"

  is_monitoring_running = BrowserActivityMonitoring.running?(device)

  session_id = params[:genre] == "app_live_testing" ? params[:app_live_session_id] : params[:live_session_id]

  # if monitoring not running
  # create instance of BrowserActivityMonitoring
  # call start - this will start a while loop inside a thread
  # otherwise, update session id of already running monitoring
  if !is_monitoring_running
    browser_activity_monitoring = BrowserActivityMonitoring.new(session_id, device,
                                                                params[:debugger_port], params[:genre])
    BrowserStack.logger.info("start_browser_activity_monitoring calling start for #{session_id}")
    browser_activity_monitoring.start
  else
    BrowserStack.logger.info("start_browser_activity_monitoring updating session_id to #{session_id}")
    File.open(BrowserActivityMonitoring.start_file(device), 'w') do |file|
      file.flock(File::LOCK_EX)
      file.puts(session_id)
      file.flock(File::LOCK_UN)
    end
  end
rescue Exception => e
  BrowserStack.logger.error("start_browser_activity_monitoring exception: #{e.message}")
  File.delete(BrowserActivityMonitoring.start_file(device)) if BrowserActivityMonitoring.running?(device)
end

def start_session_save_polling(device, params)
  return if params["additional_action"] != "enable_cookie_restore"

  params[:debugger_port] = @devices_json[@device]["debugger_port"]

  BrowserStack.logger.info("[SessionSavePolling] Starting session save polling from " \
    "start_session_save_polling method, params[:live_session_id]: #{params[:live_session_id]}, " \
    "params[:debugger_port]: #{params[:debugger_port]}, params[:genre]: #{params[:genre]}, " \
    "params[:set_cookies]: #{params[:set_cookies]}")

  BrowserStack.logger.info("[SessionSavePolling] Using pre_signed_url to save cookies: " \
    "#{params[:pre_signed_url]}")
  local_file_path = "/usr/local/.browserstack/state_files/#{device}_cookie_data_from_s3.json"
  if BrowserStack::HttpUtils.download_from_s3_with_presigned_url(params[:pre_signed_url], local_file_path)
    BrowserStack.logger.info("[SessionSavePolling] Downloaded the cookies file from S3")
  else
    BrowserStack.logger.error("[SessionSavePolling] Failed to download cookies file from S3")
  end

  is_polling_running = SessionSavePolling.running?(device)

  if !is_polling_running
    session_save_polling = SessionSavePolling.new(params[:live_session_id], device,
                                                  params[:debugger_port], params[:genre], params[:set_cookies])
    BrowserStack.logger.info("[SessionSavePolling] start_session_save_polling calling start for " \
      "#{params[:live_session_id]}")
    session_save_polling.start
  else
    BrowserStack.logger.info("[SessionSavePolling] start_session_save_polling updating session_id to " \
      "#{params[:live_session_id]}")
    File.open(SessionSavePolling.start_file(device), 'w') do |file|
      file.flock(File::LOCK_EX)
      file.puts(params[:live_session_id])
      file.flock(File::LOCK_UN)
    end
  end
rescue Exception => e
  BrowserStack.logger.error("[SessionSavePolling] start_session_save_polling exception: #{e.message}")
  File.delete(SessionSavePolling.start_file(device)) if SessionSavePolling.running?(device)
end

def enable_bs_ime(device)
  adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
  adb.shell("ime enable #{BrowserStack::BS_IME}")
rescue Exception => e
  BrowserStack.logger.error "Exception in enable_bs_ime : #{e.message}"
end

# Endpoint to reload session without doing start action again
# Basically a lightweight start request
# Only for live and mobile devices currently for interaction sync
get '/restart' do
  BrowserStack.logger.info("[SessionSavePolling] inside reached /restart params: #{params}")
  if params[:genre] == 'live_testing' && params[:required_action].to_s != 'ip-geolocation'
    add_local_params_on_restart(params)
  end
  restart(params)
  status 200
rescue Exception => e
  status 500
  BrowserStack.logger.error("Exception in /restart #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
  push_to_cls(
    params,
    'Exception in /restart',
    e.message.to_s, {
      "device_id" => @device,
      "backtrace" => e.backtrace.join("\n")[0..2000],
      "error_message" => e.message.to_s
    }
  )
  e.message
end

def add_local_params_on_restart(params)
  params_require_for_local = [
    "tunnelPresent",
    "tunnelHostServer",
    "tunnelPorts",
    "proxy_type",
    "host_only",
    "force_local",
    "cors_header_override",
    "resolve_common_hosts_on_device",
    "accept_intercepted_requests",
    "hosts"
  ]

  begin
    filename = "#{STATE_FILES_DIR}/session_#{@device}"
    session_params = JSON.parse(File.read(filename))

    if session_params["tunnelPresent"].to_s == "true"
      params_require_for_local.each do |local_param|
        params[local_param] = session_params[local_param]
      end
    end
  rescue StandardError => e
    BrowserStack.logger.error("[add_local_params_on_restart] exception received: #{e.message}")
    push_to_cls(
      params,
      'Exception in /restart',
      e.message.to_s, {
        "device_id" => @device,
        "backtrace" => e.backtrace.join("\n")[0..2000],
        "error_message" => e.message.to_s
      }
    )
  end
end

def reconnect_streaming(params)
  device_id = params["device"]
  terminal_ip = params["terminal_ip"]
  genre = params["genre"]
  device_obj = BrowserStack::AndroidDevice.new(device_id, "server.rb - reconnect_streaming", BrowserStack.logger)
  device_name = device_obj.model
  if !File.exist?("#{CONFIG_DIR}/rtc_service_#{device_id}") || device_name.nil?
    BrowserStack.logger.info("[reconnect_streaming] Invalid device_id:#{device_id}")
    raise "[reconnect_streaming] Invalid device_id:#{device_id}"
  end

  rtc_params = JSON.parse(File.read("#{CONFIG_DIR}/rtc_service_#{device_id}"))
  interaction_json = read_interaction_json
  interaction_params = interaction_json[device_name]
  package_name = rtc_params["use_rtc_app"] == "v1" ? "fr.pchab.AndroidRTC" : "fr.pchab.AndroidRTC2"
  enable_audio = rtc_params["use_rtc_app"] == "v2" && rtc_params["device_version"].to_i >= 10
  use_rtc_app_audio = rtc_params["use_rtc_app_audio"] == "true"
  enable_rotation_using_app_orientation = rtc_params["enable_rotation_using_app_orientation"] == "true"
  enable_bs_ime = rtc_params["enable_bs_ime"] == "true"
  handle_mp_during_rotation = rtc_params["handle_mp_during_rotation"] == "true"
  streaming_width = interaction_params["streaming_width"]
  streaming_height = interaction_params["streaming_height"]
  max_width = interaction_params["max_width"]
  max_height = interaction_params["max_height"]
  if [streaming_width, streaming_height, max_width, max_height].any?(&:nil?)
    BrowserStack.logger.info("[reconnect_streaming] Invalid streaming variable" \
    ":#{streaming_width}|#{streaming_height}|#{max_width}|#{max_height}|")
    raise "[reconnect_streaming] Invalid streaming variable"
  end

  adb = AndroidToolkit::ADB.new(udid: device_id, path: BrowserStack::ADB)
  rtc_service_stop_cmd = "am startservice --user 0 -n #{package_name}/.RTCService --es" \
  " action \"stop\" --es genre \"live_testing\" --es free-space-path /sdcard/free-space"
  adb.shell(rtc_service_stop_cmd, timeout: 5, retries: 1)
  adb.shell("am force-stop #{package_name}", timeout: 5, retries: 1)
  BrowserStack.logger.info("[reconnect_streaming] Stopped and killed RTC app")

  restart_interactions_server(device_id, device_name)
  restart_input_injector(device_id)
  BrowserStack.logger.info("[reconnect_streaming] Restarted interaction server and input injector")

  calculated_args = "--ei width #{streaming_width} --ei height #{streaming_height} --ei" \
  " max_x #{max_width} --ei max_y #{max_height}"
  start_rtc_service = "am startservice --user 0 -n #{package_name}/.RTCService --es action \"start\" --es ip" \
  " \"#{terminal_ip}\" #{calculated_args} --es genre \"#{genre}\" --es enableAudio \"#{enable_audio}\" --es" \
  " enableV2Audio \"#{use_rtc_app_audio}\" --es enableRotationUsingAppOrientation" \
  " \"#{enable_rotation_using_app_orientation}\" --es enableBSIME \"#{enable_bs_ime}\" --es" \
  " handleMediaProjectionPermissionDuringRotation \"#{handle_mp_during_rotation}\""
  BrowserStack.logger.info("[reconnect_streaming] start_rtc_service:#{start_rtc_service}")
  adb.shell(start_rtc_service, timeout: 5)
  `timeout 15 sh #{LIVE_SCRIPT} handle_media_projection_permission #{device_id}`
end

# This method will be used to execute the reload the session operations for app-live sessions
def execute_app_live_session_restart_action(params, actions)
  actions.each do |action|
    case action
    when 'app-live-interaction-sync'
      session_id = params[:app_live_session_id]
      user_id = params[:user_id]
      tab_id = params[:tab_id]
      device_id = params[:device]
      location = params[:location]
      interaction_sync_server_url = params[:interaction_sync_host]
      adb_command = "am broadcast -a com.browserstack.watcher.APP_LIVE_INTERACTION_SYNC " \
                "--es sessionId '#{session_id}' --es userId '#{user_id}' --es tabId '#{tab_id}' " \
                "--es deviceId '#{device_id}' --es location '#{location}' " \
                "--es interactionSyncServerUrl '#{interaction_sync_server_url}'"

      BrowserStack.logger.info("[app_live_interaction_sync]: Executing adb command: #{adb_command}")
      adb = AndroidToolkit::ADB.new(udid: device_id, path: BrowserStack::ADB)
      adb.shell(adb_command, timeout: 5, retries: 1)

    when 'proxy-setting'
      proxy_port = tunnel_setup(params) if params["applive_disable_privoxy_for_session"].to_s != "true"
      kill_all_apps_and_restart_current_app
    when 'network_config_change'
      # Can't enable the network tracking if privoxy itself is disabled
      next if params["applive_disable_privoxy_for_session"].to_s == "true"

      if params[:network_logs_2_0_enabled].to_s == "true"
        network_logs_util = NetworkLogsUtil.new(@device, @devices_json, params, @eds_obj)
        network_logs_util.run
        next
      end

      # TODO: Cleanup after network logs 2.0 is stable.
      params["tunnel_setup_flow"] = action
      # In case of network config check, hosts isn't required to set-up mitm and privoxy
      params["hosts"] = []

      start_time = Time.now
      port = @devices_json[@device]["port"]
      BrowserStack.logger.info "Start doing host setting: #{params['host']}"
      proxy_port = calculate_privoxy_port(port)
      params["network_logs_port"] = @devices_json[@device]["network_logs_port"]
      tun_ip = calc_usb_tunnel_ip(port)
      device_ips = [@devices_json[@device]["mobile_ip"], tun_ip]
      tunnel_setup(params, device_ips)

      BrowserStack.logger.info("[execute_restart_action] tunnel_setup time_taken: #{Time.now - start_time}")

      # Now we need to update the state_files_dir file so that devtools proxy can able to create a websocket connection
      state_file_name = "#{STATE_FILES_DIR}/al_session_#{get_session_id_from_params(params)}"
      json_data = JSON.parse(File.read(state_file_name))
      json_data['network_logs_port'] = @devices_json[@device]["network_logs_port"]

      File.open(state_file_name, 'w') do |file|
        file.write(JSON.pretty_generate(json_data))
      end
      start_time = Time.now
      # Let BRT know that all the request of current session required transparent proxied flow
      device_obj = BrowserStack::AndroidDevice.new(@device, "Server.rb - /app_start", BrowserStack.logger)
      should_enable_transparent_mode =
        params[:enable_transparent_mode].to_s == 'true' && device_obj.uses_bstack_internet_app?
      if should_enable_transparent_mode
        brt_controller = BStackReverseTetherController.new(device_obj, BrowserStack.logger)
        brt_controller.toggle_transparent_mode(stop: false)
      end
      BrowserStack.logger.info("[execute_restart_action] enable_transparent_mode time_taken: #{Time.now - start_time}")

      if params[:app_hashed_id] == "play_store"
        params[:package] = PLAY_STORE_PACKAGE_NAME
        params[:launcher_activity] = PLAY_STORE_LAUNCHER_ACTIVITY_NAME
      end

      start_time = Time.now
      cmd = "bash #{APP_LIVE_SCRIPT} handle_app_launch_for_network_logs " \
      "#{params[:app_hashed_id]} #{@device} #{params[:launcher_activity]} " \
      "#{params[:package]} #{get_session_id_from_params(params)} #{proxy_port}"
      output = `#{cmd}`

      BrowserStack.logger.info(
        "[execute_restart_action] handle_app_launch_for_network_logs script is: \
        #{cmd} and output: #{output} time taken #{Time.now - start_time}"
      )
    when 'reconnect-streaming'
      reconnect_streaming(params)
    else
      BrowserStack.logger.error("[execute_restart_action] received invalid action_name")
    end
  rescue StandardError => e
    BrowserStack.logger.error("[execute_restart_action] exception received: #{e.message}")
    raise e
  end
end

def restart(params)
  begin
    actions_to_execute = params['actions'].split(',')
    BrowserStack.logger.info("[SessionSavePolling] Received actions: #{actions_to_execute}")
  rescue StandardError => e
    BrowserStack.logger.error("[SessionSavePolling] received invalid actions, error: #{e.message}")
    raise e
  end

  return execute_app_live_session_restart_action(params, actions_to_execute) if params[:genre] == 'app_live_testing'

  params[:device_browser] = if params.key?('device_browser') && !params['device_browser'].empty?
                              params['device_browser']
                            else
                              "chrome"
                            end

  if params['url'].nil?
    begin
      Timeout.timeout(3) do
        device_browser_history = DeviceBrowserHistory.new(@device, BrowserStack.logger)
        params['url'] = device_browser_history.last_non_empty_url
      end
    rescue Timeout::Error
      BrowserStack.logger.error "[restart] last_non_empty_url timed out"
      params['url'] = ""
    end
  end

  if params["interaction_sync"].to_s == "true"
    eds_data = {
      event_name: 'live_interaction_sync_url_opened',
      session_id: params['live_session_id'],
      url_opened: params['url'],
      device_id: params['device'],
      remote_browser: params['device_browser'],
      platform: 'android',
      product: 'live',
      team: 'live'
    }
    @eds_obj.push_logs(EdsConstants::LIVE_WEB_EVENTS, eds_data)

    if params['should_skip_chrome_clear_cache_interaction_sync'].to_s != "true"
      Thread.bs_run do
        cmd = "timeout 25 #{BrowserStack::ADB} -s #{@device} shell pm clear com.android.chrome"
        start_time = Time.now
        system(cmd)
        BrowserStack.logger.info("[interaction_sync] Cleared chrome cache with command: #{cmd}, time_taken: #{Time.now - start_time}") # rubocop:todo Layout/LineLength
      end
    else
      BrowserStack.logger.info("[interaction_sync] Skipping chrome cache clear as should_skip_chrome_clear_cache_interaction_sync flag is true") # rubocop:todo Layout/LineLength
    end
  end
  actions_to_execute.each do |action|
    execute_restart_action(action, params)
  end
end

def execute_restart_action(action, params)
  case action
  when 'proxy-setting'
    proxy_port = tunnel_setup(params)
    script_logger = script_logger_args("#{File.basename(LIVE_SCRIPT)}_start")
    loader = params["interaction_sync"].nil? ? "proxy-setting" : "interaction-sync"
    cmd = ["bash", LIVE_SCRIPT, "restart", params['device'].to_s, '', params['url'].to_s, '', '', '', proxy_port,
           '', '', '', loader, params[:device_browser]]
    BrowserStack.logger.info("[execute_restart_action] proxy-setting action, cmd: #{cmd}")
    cmd_out = "while read line; do echo #{script_logger} $line; done"
    Open3.pipeline_start(cmd, cmd_out)
  when 'reconnect-streaming'
    reconnect_streaming(params)
  when 'cookie_restore_settings'
    cookie_restore_settings(params)
  else
    BrowserStack.logger.error("[execute_restart_action] received invalid action_name")
  end
rescue StandardError => e
  BrowserStack.logger.error("[execute_restart_action] exception received: #{e.message}")
  raise e
end

def cookie_restore_settings(params)
  if params["additional_action"] == "enable_cookie_restore"
    params["set_cookies"] = false
    BrowserStack.logger.info("[SessionSavePolling] Starting session save polling from " \
      "cookie_restore_settings request #{@device}")
    start_session_save_polling(@device, params)
  else
    BrowserStack.logger.error("[SessionSavePolling] received invalid additional_action")
  end
end

def live_media_injection(media_data_params)
  session_id = get_session_id_from_params(params)
  media_data = begin
    JSON.parse(media_data_params)
  rescue StandardError
    nil
  end
  injection_thread = Thread.bs_run do
    is_camera_toggled_start_time = Time.now
    BrowserStack.logger.info("Injecting image::session start::device=#{@device}::session=#{session_id}")
    raise "media_data is empty" if media_data.nil?

    BrowserStack::LiveMediaInjector.inject_media(@device, "user", media_data)
    BrowserStack.logger.info("Image injected successfully::session start::device=#{@device}::session=#{session_id}"\
                             "::time taken=#{Time.now - is_camera_toggled_start_time}")
    zombie_push('android', "session-start-inject-image-success", "", "", "", @device, session_id)
  rescue StandardError => e
    zombie_push('android', 'session-start-inject-image-failure', e.message.to_s, '', '', @device, session_id)
    BrowserStack.logger.error("Exception in inject_image #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
  end
  injection_thread.join
end

def should_stop_vpn?(local_enabled, browser_name)
  # Checks cases where we should stop the usb vpn app from running
  device_name = @devices_json[@device]["device_name"]
  device_version = @devices_json[@device]["device_version"]
  is_bsrun_device = BrowserStack::ModelDatabase
                    .new(device_name, device_version)
                    .property(:bsrun_device)

  BrowserStack.logger.info("Checking if we should_stop_vpn for device: "\
                           "#{device_name}, is_bsrun_device: #{is_bsrun_device}")

  stop_vpn_browsers = ["internet", "ucmobile"]
  stop_vpn_browsers << "firefox" unless is_bsrun_device
  using_stop_vpn_browser = stop_vpn_browsers.include?(browser_name)

  vpn_exception_device = uses_wifi?(device_name, device_version)
  BrowserStack.logger.info("should_stop_vpn check results: local_enabled #{local_enabled}, "\
                      "using_stop_vpn_browser #{using_stop_vpn_browser}, vpn_exception_device #{vpn_exception_device}")

  # Stop vpn if local is enabled, for certain browsers and devices
  local_enabled && using_stop_vpn_browser && !vpn_exception_device
end

def stop_vpn_for_device(tun_counter)
  script_logger = script_logger_args("#{File.basename(SCRIPT)}_stop_vpn")
  BrowserStack.logger.info "Stopping VPN for #{@device}"
  cmd = "bash #{SCRIPT} stop_vpn #{@device} #{tun_counter} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\"; done"
  system(cmd)
end

def get_app_live_instrumentation_type(params)
  return "screencap" unless params[:backfill].nil?
  return "webview" if params[:is_webview_toggled] == "true"

  is_biometric_toggled = params[:is_biometric_toggled] == "true"
  is_camera_toggled = params[:is_camera_toggled] == "true"
  return "instrumented" if is_biometric_toggled && is_camera_toggled
  return "camera" if is_camera_toggled
  return "biometrics" if is_biometric_toggled || !params[:biometric_hash].nil?
  return "passcode" if params[:is_passcode_toggled] == "true"
  return "mobileData" if params[:is_mobile_data_toggled] == "true"

  "none"
end

def handle_cleanup_policy_file(cleanup_type, cleanup_configuration)
  cleanup_policy_file = "#{STATE_FILES_DIR}/dedicated_cleanup_#{@device}"
  file_exists = File.exist?(cleanup_policy_file)
  case cleanup_type
  when "public"
    FileUtils.rm_f(cleanup_policy_file)
  when "dedicated"
    File.open(cleanup_policy_file, 'w') { |file| file.write(cleanup_configuration) }
  end
end

post '/set_cleanup_policy' do
  begin
    data = JSON.parse(request.body.read)
  rescue JSON::ParserError => e
    return 400, { error: "Please provide a valid json" }.to_json
  end
  cleanup_type = data["cleanup_type"]
  valid_cleanup_type = ["public", "dedicated"].include?(cleanup_type)

  return 400, { error: "Invalid cleanup_type: #{cleanup_type}" }.to_json unless valid_cleanup_type
  return 400, { error: "Invalid device: #{@device}" }.to_json if @device.nil? || @device.empty?

  config_missing = cleanup_type == "dedicated" && !data.key?("dedicated_cleanup_config")
  return 400, { error: "Please provide dedicated_cleanup_config in input json" }.to_json if config_missing

  dedicated_device_file = "#{STATE_FILES_DIR}/dedicated_device_#{@device}"
  dedicated_restriction_file = "#{STATE_FILES_DIR}/dedicated_restriction_#{@device}"
  if data.key?("moved_to_public") && data["moved_to_public"] == "true"
    adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    if File.exist?(dedicated_device_file)
      BrowserStack.logger.info "Deleting dedicated device file for - #{@device}"
      FileUtils.rm_f(dedicated_device_file)
    end
    if File.exist?(dedicated_restriction_file)
      BrowserStack.logger.info "Deleting dedicated restriction file for - #{@device}"
      FileUtils.rm_f(dedicated_restriction_file)
      BrowserStack.logger.info "Deleting version restriction file for - #{@device}"
      adb.shell("rm -f #{RESTRICTION_MANAGER_VERSION_FILE}")
    end
    begin
      adb.shell("rm #{DEDICATED_DEVICE_FILE_IN_DEVICE_PATH}")
    rescue Exception => e
      BrowserStack.logger.info "Failed to delete #{DEDICATED_DEVICE_FILE_IN_DEVICE_PATH}"
    end
  end
  BrowserStack.logger.info "Setting cleanup policy to #{cleanup_type} for #{@device}"
  cleanup_configuration = cleanup_type == "dedicated" ? data["dedicated_cleanup_config"] : ""
  handle_cleanup_policy_file(cleanup_type, cleanup_configuration)
  return 200, {}.to_json
end

get '/accessibility_info' do
  BrowserStack.logger.info("Triggering dom extraction for accessibility info, product #{params['product']}")
  first_scan = params['first_scan'].to_s == "true"
  async_mode = params['async_mode'].to_s == "true"
  automate_obj = {}
  automate_obj['async_mode'] = async_mode
  automate_obj['auth_token'] = params['auth_token']
  automate_obj['test_run_uuid'] = params['test_uuid']
  automate_obj['th_auth_token'] = params['th_auth_token']
  automate_obj['th_build_uuid'] = params['build_uuid']
  automate_obj['region'] = params['region']
  automate_obj['s3_access_key'] = params['s3_access_key']
  automate_obj['s3_secret_key'] = params['s3_secret_key']
  automate_obj['s3_bucket'] = params['s3_bucket']
  automate_obj['scan_timestamp'] = params['scan_timestamp']
  automate_obj['command'] = params['command']
  info_obj = AppAccessibility.new(
    params['device'], params['session_id'], params['product'],
    first_scan: first_scan, automate_obj: automate_obj
  )
  res = info_obj.capture_accessibility_info
  return 200, res.to_json(max_nesting: 400)
rescue StandardError => e
  BrowserStack.logger.error("Exception in getting accessibility info : #{e.message} : \n#{e.backtrace.join("\n")}")
  return 500, { "msg" => "Error in accessibility_info #{e.message}" }.to_json
end

get '/toggle_continuous_scanning' do
  pusher = {
    pusher_url: params['pusher_url'],
    pusher_channel: params['pusher_channel'],
    pusher_token: params['pusher_token']
  }
  rule_engine_callback = {
    session_id: params['session_id'],
    bs_user_id: params['bs_user_id'],
    report_id: params['report_id'],
    rule_engine_subdomain: params['rule_engine_subdomain']
  }

  info_obj = AppAccessibility.new(
    params['device'],
    params['session_id'],
    params['product']
  )

  res = info_obj.broadcast_continuous_scanning_session(
    pusher,
    rule_engine_callback,
    params['mode']
  )
  BrowserStack.logger.info("response is : #{res}")
  res = res.to_json
  [200, res]
rescue StandardError => e
  # Log + return 500 on error
  BrowserStack.logger.error("Exception setting up continuous scanning: #{e.message}\n#{e.backtrace.join("\n")}")
  [500, { msg: "Error in continuous scanning: #{e.message}" }.to_json]
end

get '/start_cspt_session' do
  code, response = MCSPT.start_session_async(
    params[:session_id].to_s,
    params[:user_id].to_s,
    params[:device_id].to_s,
    params[:app_package].to_s,
    params[:app_relaunch].to_s.downcase == "true",
    params[:device_data].to_s,
    region: params[:region].to_s,
    app_startup_time_flag: params[:app_startup_time_flag].to_s.downcase == "true"
  )
  BrowserStack.logger.info("Started CSPT session")
  return [code, response.to_json]
end

get '/stop_cspt_session' do
  code, response = MCSPT.stop_session_async(
    params[:session_id].to_s,
    params[:user_id].to_s,
    params[:device_id].to_s,
    cancelled: params[:cancelled].to_s.downcase == "true"
  )
  BrowserStack.logger.info("Stopped CSPT session")
  return [code, response.to_json]
end

get '/get_cspt_analytics' do
  data = MCSPT.get_analytics(
    params[:session_id].to_s,
    params[:device_id].to_s,
    params[:app_package].to_s
  )
  return [200, data.to_json]
end

post '/app_start' do
  # Incase no data is present in the POST request body
  return 400 if request.body.nil?

  request_body_str = request.body.read
  return 400 if request_body_str == ""

  request_body_json = {}

  begin
    request_body_json = JSON.parse(request_body_str)
  rescue Exception => e
    BrowserStack.logger.error("Error in JSON Parsing #{e}")
    return 400
  end

  request_body_json_str = {}
  # This is done because in JSON.parse the integers/boolean/others are all parsed.
  # But in GET it used to be in string form. This is done to keep all in sync and tamper minimal.
  request_body_json.each do |key, value|
    request_body_json_str[key] = value.to_s
  end
  params.merge!(request_body_json_str)

  # The following code is exactly the same as of the get '/app_start'.
  # Will gradually deprecate the get and will consider this post flow only.

  startTime = Time.now
  params[:event_hash] = {
    absolute_start_time: (Time.now.to_f * 1000).to_i,
    request_type: 'post /app_start'
  }
  mark_event_start('total_ruby_start_time', params[:event_hash])

  mark_event_start('pre_start_check', params[:event_hash])
  halt 500, 'Terminal already allocated!' unless pre_start(@device, params)
  mark_event_end('pre_start_check', params[:event_hash])

  device_name = @devices_json[@device]["device_name"]
  using_new_thread = params[:android_new_thread_execution].to_s == "true"

  enable_mobile_data = check_and_enable_mobile_data?(@device, params, @devices_json[@device])
  if enable_mobile_data
    enable_brt_for_browserstack_apps(@device)
    zombie_push('android', "enabled-mobile-data", "", "", params['genre'], @device, get_session_id_from_params(params))
  end

  mark_event_start('write_session_info', params[:event_hash])
  is_local_update = false
  vpn_disabled = false

  packages = [params[:app_package]]
  params["packages"] = FormatPackagesParam.create_package_params(packages)
  WriteSessionInfo.new(BrowserStack.logger, @device, params).save
  mark_event_end('write_session_info', params[:event_hash])

  mark_event_start('mcspt_stop_session', params[:event_hash])
  # Cancelling any running MCSPT Session on the device
  MCSPT.stop_session_running_on_device_async(@device, cancelled: true)
  mark_event_end('mcspt_stop_session', params[:event_hash])

  mark_event_start('crypto_mining_detection', params[:event_hash])
  # Enable dns logs capture to detect possible crypto mining
  exec_block(using_new_thread) do
    CryptoMiningDetectionHelper.new(@device, BrowserStack.logger).enable_network_logging
  end
  mark_event_end('crypto_mining_detection', params[:event_hash])

  mark_event_start('add_patch_log_file', params[:event_hash])
  # Pushing patching.log to device for storing app patch logs
  if AppInjection.app_patching_enabled_in_app_live?(params)
    AppInjection.add_patch_log_file(@device, get_session_id_from_params(params))
  end
  save_app_live_logs_params(@device, params)
  params.delete("app_testing_aws_key")
  params.delete("app_testing_aws_secret")
  mark_event_end('add_patch_log_file', params[:event_hash])
  device_logger_helper = DeviceLogger.new(@device)
  device_logger_helper.init

  port = @devices_json[@device]["port"]
  tun_ip = calc_usb_tunnel_ip(port)
  mark_event_start('tunnel_setup', params[:event_hash])

  if params[:trigger_public_cleanup].to_s == "true"
    FileUtils.touch("#{STATE_FILES_DIR}/forced_public_cleanup_#{@device}")
  end

  # Avoid to re-write the network_logs_port in restart-api
  params["network_logs_port"] = @devices_json[@device]["network_logs_port"]
  if params["hosts"].to_s != "" || (params[:new_error_page] == "true" && params[:tunnelPresent] != "true")
    BrowserStack.logger.info "Start doing host setting: #{params['host']}"
    proxy_port = calculate_privoxy_port(port)
    tunnel_setup_thread = exec_block(using_new_thread) do
      device_ips = [@devices_json[@device]["mobile_ip"], tun_ip]
      tunnel_setup(params, device_ips) if params["applive_disable_privoxy_for_session"].to_s != "true"
    end
    is_local_update = true
  end
  mark_event_end('tunnel_setup', params[:event_hash])

  mark_event_start('remove_port_forwarding', params[:event_hash])
  appium_port = @devices_json[@device]["selenium_port"]
  ui_automator_server_port = get_ui_automator_server_port(appium_port.to_i)
  remove_forwarded_port(ui_automator_server_port, "uiautomator")
  BrowserStack.logger.debug "Stopped uiautomator port forward for appium at "\
                            "port #{ui_automator_server_port}"
  mark_event_end('remove_port_forwarding', params[:event_hash])

  mark_event_start('start_adb_forwarder', params[:event_hash])

  should_start_adb_forwarder = params[:start_adb_forwarder].to_s == "true"
  if should_start_adb_forwarder
    exec_block(using_new_thread) do
      adb_forwarder_port = @devices_json[@device]["adb_forwarder_port"]
      AdbForwarder.start_adb_forwarder_service(@device, adb_forwarder_port, get_session_id_from_params(params))
    end
  end

  mark_event_end('start_adb_forwawrder', params[:event_hash])

  mark_event_start('start_tranparent_proxy_mode', params[:event_hash])

  device_obj = BrowserStack::AndroidDevice.new(@device, "Server.rb - /app_start", BrowserStack.logger)
  should_enable_transparent_mode =
    params[:enable_transparent_mode].to_s == 'true' && device_obj.uses_bstack_internet_app?
  if should_enable_transparent_mode
    brt_controller = BStackReverseTetherController.new(device_obj, BrowserStack.logger)
    brt_controller.toggle_transparent_mode(stop: false)
  end

  mark_event_end('start_tranparent_proxy_mode', params[:event_hash])

  mark_event_start('write_rtc_params', params[:event_hash])
  params[:device_version] = @devices_json[@device]["device_version"]
  args = write_rtc_params(params)
  if File.exist?("#{STATE_FILES_DIR}/session_#{@device}")
    copy_file("#{CONFIG_DIR}/rtc_service_#{@device}", "#{STATE_FILES_DIR}/session_#{@device}")
  end

  begin
    FileUtils.touch("#{STATE_FILES_DIR}/al_session_#{get_session_id_from_params(params)}")
    copy_file(
      "#{CONFIG_DIR}/rtc_service_#{@device}",
      "#{STATE_FILES_DIR}/al_session_#{get_session_id_from_params(params)}"
    )
  rescue StandardError
    BrowserStack.logger.error("Failed to write #{STATE_FILES_DIR}/al_session_#{get_session_id_from_params(params)}")
  end
  mark_event_end('write_rtc_params', params[:event_hash])

  mark_event_start('stop_vpn_for_device', params[:event_hash])
  tun_counter = port.to_i - 8078
  device_name = @devices_json[@device]["device_name"]
  device_version = @devices_json[@device]["device_version"]

  # Local doesn't work for android version <= 6 with VPN setup
  if (is_local_update && (uses_wifi?(device_name, device_version) ||
    device_version.to_i <= 6 || device_version.to_f == 7.1)) ||  # rubocop:todo Lint/FloatComparison
    params["applive_disable_privoxy_for_session"].to_s == "true"
    script_logger = script_logger_args("#{File.basename(SCRIPT)}_stop_vpn")
    BrowserStack.logger.info 'Stopping VPN for App Live Session because local is set to true'
    cmd = "bash #{SCRIPT} stop_vpn #{@device} #{tun_counter} 2>&1 | "\
          "while read line; do echo #{script_logger} \"$line\"; done"
    system(cmd)
    vpn_disabled = true
  else
    BrowserStack.logger.info 'VPN should already be setup on the device during cleanup'
    system("touch /tmp/internet_via_usb_#{@device}")
  end
  mark_event_end('stop_vpn_for_device', params[:event_hash])

  mark_event_start('assign_variables', params[:event_hash])
  async_start = params[:async_start] || false
  relaunch_flow = ['local', 'ip_change', 'network_config_change'].include?(params[:startElement]) &&
                  !(params[:startElement] == "ip_change" &&
                    params[:ipLocationCode] == "-1")
  network_logs = params[:networkLogs] == "true"
  play_store_disabled = params[:app_live_lft] == "true" && params[:app_live_store_access_blocked] == "true"
  install_experiment_enabled = params[:install_experiment_enabled] == "true"
  synchronous_install = params[:synchronous_install] == "true"
  install_timeout = params[:install_timeout] || 90
  app_download_timeout = params[:app_download_timeout] || 90
  should_zip_align = params[:app_zip_align] == "true"
  is_biometric_toggled = params[:is_biometric_toggled] == "true"
  is_old_flow_enabled = params[:use_old_biometric_failure_flow].to_s == "true"
  is_camera_toggled = params[:is_camera_toggled] == "true"
  is_camera_preview_layer_toggled = params[:is_camera_preview_layer_toggled] == "true"
  is_video_toggled = params[:is_video_toggled] == "true"
  use_instant_app = params[:is_an_instant_app].to_s == "true" && params[:instant_app_enabled_group] == "true"
  is_chooser_intent_support_enabled = params[:chooserIntentSupport] == "true"
  is_webview_toggled = params[:is_webview_toggled] == "true"
  is_passcode_toggled = params[:is_passcode_toggled] == "true"
  is_screenshot_block_toggled = params[:is_screenshot_block_toggled] == "true"
  is_mobile_data_toggled = params[:is_mobile_data_toggled] == "true"
  is_audio_injection_session = ["true", true].include?(params[:is_audio_injection_session])
  is_sync_webrtc = ["true", true].include?(params[:is_sync_webrtc])
  is_network_logs_patch_toggled = ["true", true].include?(params[:is_network_logs_patch_toggled])
  mark_event_end('assign_variables', params[:event_hash])

  mark_event_start('start_webview_tcp_port', params[:event_hash])
  start_webview_tcp_port if is_webview_toggled
  mark_event_end('start_webview_tcp_port', params[:event_hash])

  mark_event_start('image_injection', params[:event_hash])
  if (is_camera_toggled || is_video_toggled) && params[:image_injection_media_data]
    media_data = begin
      JSON.parse(params[:image_injection_media_data])
    rescue StandardError
      nil
    end
    begin
      BrowserStack.logger.info("Injecting Camera Media at session start for device: #{@device} "\
                              "and session #{params[:session_id]}")
      raise "media_data is empty" if media_data.nil?

      CameraMediaInjector.inject_media(@device, media_data)
      BrowserStack.logger.info("Camera Media injected successfully at session start for device #{@device} and "\
                              "session #{params[:session_id]}")
      zombie_push('android', "session-start-inject-image-success", "", "", "", @device,
                  params[:session_id], is_app_accessibility: params[:is_app_accessibility])
    rescue StandardError => e
      zombie_push('android', 'session-start-inject-image-failure', e.message.to_s, '', '', @device,
                  params[:session_id], is_app_accessibility: params[:is_app_accessibility])
      BrowserStack.logger.error("Exception in inject_image #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
    end
  end
  mark_event_end('image_injection', params[:event_hash])
  mark_event_start('relaunch_flow', params[:event_hash])

  if is_chooser_intent_support_enabled.to_s == "true"
    AppInjection.touch_app_injection_state_file(@device, ENABLE_CHOOSER_INTENT_SUPPORT_FILE)
  end
  AppInjection.touch_app_injection_state_file(@device, ENABLE_BIOMETRIC_FRIDA) if is_biometric_toggled
  if is_biometric_toggled && !is_old_flow_enabled
    AppInjection.touch_app_injection_state_file(@device,
                                                ENABLE_MULTIPLE_BIOMETRIC_FAILURES)
  end
  AppInjection.touch_app_injection_state_file(@device, ENABLE_CAMERA_FRIDA) if is_camera_toggled
  AppInjection.touch_app_injection_state_file(@device, ENABLE_NETWORK_LOGS_FRIDA) if is_network_logs_patch_toggled

  if is_camera_preview_layer_toggled
    AppInjection.touch_app_injection_state_file(@device,
                                                ENABLE_CAMERA_PREVIEW_LAYER_FILE)
  end
  if is_video_toggled
    AppInjection.touch_app_injection_state_file(@device,
                                                ENABLE_VIDEO_INJECTION_FILE)
  end

  mark_event_start('relaunch_flow', params[:event_hash])
  if relaunch_flow && params['oldLive_session_id']
    # Case: relaunch flow enabled after network config change
    NetworkLogsUtil.perform_clean_up(@eds_obj, params['oldLive_session_id'])
    FileUtils.rm_f("#{STATE_FILES_DIR}/al_session_#{params['oldLive_session_id']}")
    cmd = "curl --connect-timeout 20 -k -i 'https://localhost:#{DEV_TOOL_PORT}/wd/hub/refreshBridge?rails_session_id=#{params['oldLive_session_id']}&new_rails_session_id=#{get_session_id_from_params(params)}'"
    response = `#{cmd}`
    BrowserStack.logger.info("[Appium App-Live] refreshBridge response: #{response}")
  end
  mark_event_end('relaunch_flow', params[:event_hash])

  mark_event_start('audio_injection', params[:event_hash])
  inject_audio_at_session_start(params) if is_audio_injection_session
  mark_event_end('audio_injection', params[:event_hash])

  mark_event_start('instrumentation_params', params[:event_hash])
  instrumentation_params = params[:instrumentation_hash] || params[:backfill] ||
                           params[:biometric_hash] || params[:webview_hash] || ""

  instrumentation_params = begin
    JSON.parse(instrumentation_params)
  rescue StandardError
    {}
  end

  instrumentation_types = instrumentation_params["instrumentation_types"] || [get_app_live_instrumentation_type(params)]

  unless instrumentation_params.empty?
    instrumentation_params["instrumentation_types"] = instrumentation_types
    instrumentation_params = instrumentation_params.to_json
  end

  endTime = Time.now
  timeBeforeStartApp = endTime - startTime
  eds_data = {
    event_name: "timeBeforeStartApp",
    genre: "app_live_testing",
    timeBeforeStartApp: timeBeforeStartApp,
    session_id: get_session_id_from_params(params),
    product: "app_live"
  }
  @eds_obj.push_logs(EdsConstants::APP_LIVE_WEB_EVENTS, eds_data, params)
  mark_event_end('instrumentation_params', params[:event_hash])

  mark_event_start('script_logger', params[:event_hash])
  script_logger = script_logger_args("#{File.basename(APP_LIVE_SCRIPT)}_start_app")
  mark_event_end('script_logger', params[:event_hash])

  mark_event_start('stop_talkback', params[:event_hash])
  # enable audio handles media projection pop up
  params[:lang_en_media_popup] = "false" # param added for app live session
  if params[:use_rtc_app] == "v2" && params[:device_version].to_i >= 10
    params[:lang_en_media_popup] = params[:locale] unless params[:locale].nil?
    params[:enable_audio] = "true"
    exec_block(using_new_thread) do
      talkback_helper = TalkbackHelper.new(@device)
      talkback_helper.stop_talkback if talkback_helper.talkback_on?
    end
  else
    # Disable async app launch flow for non MP sessions
    params[:enable_async_app_launch_flow] = "false"
  end
  mark_event_end('stop_talkback', params[:event_hash])

  mark_event_start('dedicated_cleanup', params[:event_hash])
  if params[:dedicated_cleanup] == "true"
    BrowserStack.logger.info("Got dedicated_cleanup request")
    dedicated_cleanup_config = params.key?(:dedicated_cleanup_config) ? params[:dedicated_cleanup_config] : ""
    handle_cleanup_policy_file("dedicated", dedicated_cleanup_config)
  end
  mark_event_end('dedicated_cleanup', params[:event_hash])

  # set default timezone
  mark_event_start('set_default_timezone', params[:event_hash])
  if params[:timezone].to_s != ""
    BrowserStack.logger.info "Setting default timezone to #{params[:timezone]}"
    settings_helper = BrowserStack::SettingsHelper.new(
      @device,
      BrowserStack.logger,
      params[:product],
      params[:session_id]
    )
    settings_helper.apply_setting('timezone', params[:timezone])
  end
  mark_event_end('set_default_timezone', params[:event_hash])

  mark_event_start('app_live_actions_bash', params[:event_hash])
  BrowserStack.logger.info "MediaProjection params: use_rtc_app=#{params[:use_rtc_app]}, device_version="\
                           "#{params[:device_version].to_i}, enable_audio=#{params[:enable_audio]}," \
                           "enable_bs_ime=#{params[:enable_bs_ime]}"
  #start_contact_enabling_flow
  enable_contacts = params[:enable_contacts_app_access] || false
  BrowserStack.logger.info("Enable contacts app = #{enable_contacts}")

  obb_file_url = params[:obb_file_url]
  obb_file_name = params[:obb_file_name]
  username = params[:username] || ""
  params[:debugger_port] = @devices_json[@device]["debugger_port"]
  BrowserStack.logger.info("[obb_file_helper_log] obb_file_url #{obb_file_url}, obb_file_name #{obb_file_name}")
  app_has_trusted_user_cert = params[:network_logs_2_0_enabled].to_s == 'true' ? params[:user_certs_accepted].to_s : ""

  start_method = params[:android_parallel_execution_of_methods].to_s == "true" ? "async_start_app" : "start_app"
  cmd = ["bash", APP_LIVE_SCRIPT, start_method, @device.to_s, args[:interaction_args].to_s, args[:stream_width].to_s, \
         async_start.to_s, tun_ip.to_s, proxy_port.to_s, \
         params[:new_app_download_url].to_s, is_local_update.to_s, params[:app_hashed_id].to_s, \
         params[:launcher_activity].to_s, params[:app_package].to_s, relaunch_flow.to_s, \
         play_store_disabled.to_s, install_experiment_enabled.to_s, synchronous_install.to_s, \
         should_zip_align.to_s, install_timeout.to_s, params["enable_apksigner"].to_s, \
         app_download_timeout.to_s, instrumentation_types.to_s, instrumentation_params.to_s, \
         params[:enable_audio].to_s,  is_biometric_toggled.to_s, is_camera_toggled.to_s, \
         params[:use_rtc_app].to_s, is_webview_toggled.to_s, is_screenshot_block_toggled.to_s, \
         network_logs.to_s, params[:lang_en_media_popup].to_s, is_passcode_toggled.to_s, \
         params[:use_rtc_app_audio].to_s, enable_contacts.to_s, is_audio_injection_session.to_s, \
         params[:enable_rotation_using_app_orientation].to_s, \
         obb_file_url.to_s, obb_file_name.to_s, params[:enable_bs_ime].to_s, \
         params[:enableSim].to_s, is_sync_webrtc.to_s, username, \
         is_network_logs_patch_toggled.to_s, is_camera_preview_layer_toggled.to_s, \
         is_video_toggled.to_s, params[:is_app_accessibility].to_s.downcase, \
         use_instant_app.to_s, params[:handle_mp_during_rotation].to_s, \
         params[:enable_async_app_launch_flow].to_s, app_has_trusted_user_cert.to_s, \
         params[:async_metadata_extraction_enabled].to_s, params[:debugger_port].to_s, { err: %i[child out] }]

  cmd_out = "while read line; do echo #{script_logger} $line; done"
  cmd_string = "#{cmd.inspect} | #{cmd_out} &"  # To add the command in logs
  # NOTE: If you are sending any new parameter to above bash script, make sure to accomodate similar changes in
  # /update_app endpoint, which uses the same script
  BrowserStack.logger.info "Starting App Live with command: #{cmd_string}"
  begin
    Open3.pipeline_start(cmd, cmd_out) unless File.exist?("#{STATE_FILES_DIR}/cleanupdone_#{@device}")
  rescue Exception => e
    BrowserStack.logger.error "App Live command #{cmd_string} failed with #{e.message}"
  end
  mark_event_end('app_live_actions_bash', params[:event_hash])

  mark_event_start('enable_bs_ime', params[:event_hash])
  exec_block(using_new_thread) do
    enable_bs_ime(@device) if params[:enable_bs_ime]
  end
  mark_event_end('enable_bs_ime', params[:event_hash])

  mark_event_start('set_default_location', params[:event_hash])
  set_default_location(params)
  mark_event_end('set_default_location', params[:event_hash])

  mark_event_start('set_default_locale', params[:event_hash])
  set_default_locale(params)
  mark_event_end('set_default_locale', params[:event_hash])

  mark_event_start('start_browser_activity_monitoring', params[:event_hash])
  start_browser_activity_monitoring(@device, params)
  mark_event_end('start_browser_activity_monitoring', params[:event_hash])

  mark_event_start('check_and_log_usb_internet', params[:event_hash])
  check_and_log_usb_internet(params, vpn_disabled)
  mark_event_end('check_and_log_usb_internet', params[:event_hash])

  mark_event_start('start_network_usage_tracker', params[:event_hash])
  start_network_usage_tracker(@device, params[:session_id], "app_live_testing")
  mark_event_end('start_network_usage_tracker', params[:event_hash])

  mark_event_start('monitor_device_logger_metric', params[:event_hash])
  monitor_device_logger_metric(@device, get_session_id_from_params(params))
  mark_event_end('monitor_device_logger_metric', params[:event_hash])

  mark_event_start('app_live_profiling', params[:event_hash])
  if params[:app_live_profiling] == true
    BrowserStack.logger.info 'starting PerformanceStatistics'
    PerformanceStatistics.new(@device, BrowserStack.logger).start(get_session_id_from_params(params))
  end
  mark_event_end('app_live_profiling', params[:event_hash])

  mark_event_end('total_ruby_start_time', params[:event_hash])

  BrowserStack.logger.info("LSE ruby_start_instrumentation: #{params[:event_hash]}")
  Thread.bs_run do
    push_to_cls(params, 'ruby_start_instrumentation', '', params[:event_hash])
  end

  if params.key?('abuse_prevention_google_signup_blocking') &&
    params[:abuse_prevention_google_signup_blocking].to_s == "true"
    BrowserStack.logger.info("server abuse_prevention_google_signup_blocking adding file")
    mark_event_start('add_google_signup_restriction_file_watcher', params[:event_hash])
    watcher_helper =
      WatcherHelper.new(@device, get_session_id_from_params(params), params[:genre], BrowserStack.logger)
    watcher_helper.add_google_restriction_file_to_watcher
    mark_event_end('add_google_signup_restriction_file_watcher', params[:event_hash])
  end

  # tunnel_setup is critical for session start
  # waiting for it to complete
  tunnel_setup_thread&.join
rescue Exception => e
  status 500
  BrowserStack.logger.error("Exception in /app_start #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
  push_to_cls(
    params,
    'Exception in /app_start',
    e.message.to_s, {
      "device_id" => @device,
      "backtrace" => e.backtrace.join("\n")[0..2000],
      "error_message" => e.message.to_s
    }
  )
  e.message
end

get '/app_start' do
  startTime = Time.now
  params[:event_hash] = {
    absolute_start_time: (Time.now.to_f * 1000).to_i,
    request_type: 'get /app_start'
  }
  mark_event_start('total_ruby_start_time', params[:event_hash])

  mark_event_start('pre_start_check', params[:event_hash])
  halt 500, 'Terminal already allocated!' unless pre_start(@device, params)
  mark_event_end('pre_start_check', params[:event_hash])

  device_name = @devices_json[@device]["device_name"]
  using_new_thread = params[:android_new_thread_execution].to_s == "true"

  enable_mobile_data = check_and_enable_mobile_data?(@device, params, @devices_json[@device])
  if enable_mobile_data
    enable_brt_for_browserstack_apps(@device)
    zombie_push('android', "enabled-mobile-data", "", "", params['genre'], @device, get_session_id_from_params(params))
  end

  mark_event_start('write_session_info', params[:event_hash])
  is_local_update = false
  vpn_disabled = false

  packages = [params[:app_package]]
  params["packages"] = FormatPackagesParam.create_package_params(packages)
  WriteSessionInfo.new(BrowserStack.logger, @device, params).save
  mark_event_end('write_session_info', params[:event_hash])

  mark_event_start('mcspt_stop_session', params[:event_hash])
  # Cancelling any running MCSPT Session on the device
  MCSPT.stop_session_running_on_device_async(@device, cancelled: true)
  mark_event_end('mcspt_stop_session', params[:event_hash])

  mark_event_start('crypto_mining_detection', params[:event_hash])
  # Enable dns logs capture to detect possible crypto mining
  exec_block(using_new_thread) do
    CryptoMiningDetectionHelper.new(@device, BrowserStack.logger).enable_network_logging
  end
  mark_event_end('crypto_mining_detection', params[:event_hash])

  mark_event_start('remove_port_forwarding', params[:event_hash])
  appium_port = @devices_json[@device]["selenium_port"]
  ui_automator_server_port = get_ui_automator_server_port(appium_port.to_i)
  remove_forwarded_port(ui_automator_server_port, "uiautomator")
  BrowserStack.logger.debug "Stopped uiautomator port forward for appium at "\
                            "port #{ui_automator_server_port}"
  mark_event_end('remove_port_forwarding', params[:event_hash])

  mark_event_start('add_patch_log_file', params[:event_hash])
  # Pushing patching.log to device for storing app patch logs
  if AppInjection.app_patching_enabled_in_app_live?(params)
    AppInjection.add_patch_log_file(@device, get_session_id_from_params(params))
  end
  save_app_live_logs_params(@device, params)
  params.delete("app_testing_aws_key")
  params.delete("app_testing_aws_secret")
  mark_event_end('add_patch_log_file', params[:event_hash])
  device_logger_helper = DeviceLogger.new(@device)
  device_logger_helper.init

  port = @devices_json[@device]["port"]
  tun_ip = calc_usb_tunnel_ip(port)
  mark_event_start('tunnel_setup', params[:event_hash])
  if params["hosts"].to_s != "" || (params[:new_error_page] == "true" && params[:tunnelPresent] != "true")
    BrowserStack.logger.info "Start doing host setting: #{params['host']}"
    proxy_port = calculate_privoxy_port(port)
    params["network_logs_port"] = @devices_json[@device]["network_logs_port"]
    tunnel_setup_thread = exec_block(using_new_thread) do
      tunnel_setup(params, [@devices_json[@device]["mobile_ip"], tun_ip])
    end
    is_local_update = true
  end
  mark_event_end('tunnel_setup', params[:event_hash])

  mark_event_start('write_rtc_params', params[:event_hash])
  params[:device_version] = @devices_json[@device]["device_version"]
  args = write_rtc_params(params)
  if File.exist?("#{STATE_FILES_DIR}/session_#{@device}")
    copy_file("#{CONFIG_DIR}/rtc_service_#{@device}", "#{STATE_FILES_DIR}/session_#{@device}")
  end

  begin
    FileUtils.touch("#{STATE_FILES_DIR}/al_session_#{get_session_id_from_params(params)}")
    copy_file(
      "#{CONFIG_DIR}/rtc_service_#{@device}",
      "#{STATE_FILES_DIR}/al_session_#{get_session_id_from_params(params)}"
    )
  rescue StandardError
    BrowserStack.logger.error("Failed to write #{STATE_FILES_DIR}/al_session_#{get_session_id_from_params(params)}")
  end
  mark_event_end('write_rtc_params', params[:event_hash])

  mark_event_start('stop_vpn_for_device', params[:event_hash])
  tun_counter = port.to_i - 8078
  device_name = @devices_json[@device]["device_name"]
  device_version = @devices_json[@device]["device_version"]

  # Local doesn't work for android version <= 6 with VPN setup
  if is_local_update && (uses_wifi?(device_name, device_version) ||
     device_version.to_i <= 6 || device_version.to_f == 7.1) # rubocop:todo Lint/FloatComparison
    script_logger = script_logger_args("#{File.basename(SCRIPT)}_stop_vpn")
    BrowserStack.logger.info 'Stopping VPN for App Live Session because local is set to true'
    cmd = "bash #{SCRIPT} stop_vpn #{@device} #{tun_counter} 2>&1 | "\
          "while read line; do echo #{script_logger} \"$line\"; done"
    system(cmd)
    vpn_disabled = true
  else
    BrowserStack.logger.info 'VPN should already be setup on the device during cleanup'
    system("touch /tmp/internet_via_usb_#{@device}")
  end
  mark_event_end('stop_vpn_for_device', params[:event_hash])

  mark_event_start('assign_variables', params[:event_hash])
  async_start = params[:async_start] || false
  relaunch_flow = ['local', 'ip_change', 'network_config_change'].include?(params[:startElement]) &&
                  !(params[:startElement] == "ip_change" &&
                    params[:ipLocationCode] == "-1")
  network_logs = params[:networkLogs] == "true"
  play_store_disabled = params[:app_live_lft] == "true" && params[:app_live_store_access_blocked] == "true"
  install_experiment_enabled = params[:install_experiment_enabled] == "true"
  synchronous_install = params[:synchronous_install] == "true"
  install_timeout = params[:install_timeout] || 90
  app_download_timeout = params[:app_download_timeout] || 90
  should_zip_align = params[:app_zip_align] == "true"
  is_biometric_toggled = params[:is_biometric_toggled] == "true"
  is_camera_toggled = params[:is_camera_toggled] == "true"
  is_video_toggled = params[:is_video_toggled] == "true"
  is_chooser_intent_support_enabled = params[:chooserIntentSupport] == "true"
  is_webview_toggled = params[:is_webview_toggled] == "true"
  is_passcode_toggled = params[:is_passcode_toggled] == "true"
  is_screenshot_block_toggled = params[:is_screenshot_block_toggled] == "true"
  is_mobile_data_toggled = params[:is_mobile_data_toggled] == "true"
  is_audio_injection_session = ["true", true].include?(params[:is_audio_injection_session])
  is_sync_webrtc = ["true", true].include?(params[:is_sync_webrtc])
  is_network_logs_patch_toggled = ["true", true].include?(params[:is_network_logs_patch_toggled])
  mark_event_end('assign_variables', params[:event_hash])

  mark_event_start('start_webview_tcp_port', params[:event_hash])
  start_webview_tcp_port if is_webview_toggled
  mark_event_end('start_webview_tcp_port', params[:event_hash])

  mark_event_start('image_injection', params[:event_hash])
  if is_camera_toggled && params[:image_injection_media_data]
    media_data = begin
      JSON.parse(params[:image_injection_media_data])
    rescue StandardError
      nil
    end
    begin
      BrowserStack.logger.info("Injecting image at session start for device: #{@device} "\
                              "and session #{params[:session_id]}")
      raise "media_data is empty" if media_data.nil?

      CameraMediaInjector.inject_media(@device, media_data)
      BrowserStack.logger.info("Image injected successfully at session start for device #{@device} and "\
                              "session #{params[:session_id]}")
      zombie_push(
        'android', "session-start-inject-image-success", "", "", "", @device, params[:session_id],
        is_app_accessibility: params[:is_app_accessibility]
      )
    rescue StandardError => e
      zombie_push(
        'android', 'session-start-inject-image-failure', e.message.to_s, '', '', @device, params[:session_id],
        is_app_accessibility: params[:is_app_accessibility]
      )
      BrowserStack.logger.error("Exception in inject_image #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
    end
  end
  mark_event_end('image_injection', params[:event_hash])
  mark_event_start('relaunch_flow', params[:event_hash])

  if is_chooser_intent_support_enabled.to_s == "true"
    AppInjection.touch_app_injection_state_file(@device, ENABLE_CHOOSER_INTENT_SUPPORT_FILE)
  end
  AppInjection.touch_app_injection_state_file(@device, ENABLE_BIOMETRIC_FRIDA) if is_biometric_toggled
  AppInjection.touch_app_injection_state_file(@device, ENABLE_CAMERA_FRIDA) if is_camera_toggled
  AppInjection.touch_app_injection_state_file(@device, ENABLE_NETWORK_LOGS_FRIDA) if is_network_logs_patch_toggled

  if relaunch_flow && params['oldLive_session_id']
    FileUtils.rm_f("#{STATE_FILES_DIR}/al_session_#{params['oldLive_session_id']}")
    cmd = "curl --connect-timeout 20 -k -i 'https://localhost:#{DEV_TOOL_PORT}/wd/hub/refreshBridge?rails_session_id=#{params['oldLive_session_id']}&new_rails_session_id=#{get_session_id_from_params(params)}'"
    response = `#{cmd}`
    BrowserStack.logger.info("[Appium App-Live] refreshBridge response: #{response}")
  end
  mark_event_end('relaunch_flow', params[:event_hash])

  mark_event_start('audio_injection', params[:event_hash])
  inject_audio_at_session_start(params) if is_audio_injection_session
  mark_event_end('audio_injection', params[:event_hash])

  mark_event_start('instrumentation_params', params[:event_hash])
  instrumentation_params = params[:instrumentation_hash] || params[:backfill] ||
                           params[:biometric_hash] || params[:webview_hash] || ""

  instrumentation_params = begin
    JSON.parse(instrumentation_params)
  rescue StandardError
    {}
  end

  instrumentation_types = instrumentation_params["instrumentation_types"] || [get_app_live_instrumentation_type(params)]

  unless instrumentation_params.empty?
    instrumentation_params["instrumentation_types"] = instrumentation_types
    instrumentation_params = instrumentation_params.to_json
  end

  endTime = Time.now
  timeBeforeStartApp = endTime - startTime
  eds_data = {
    event_name: "timeBeforeStartApp",
    genre: "app_live_testing",
    timeBeforeStartApp: timeBeforeStartApp,
    session_id: get_session_id_from_params(params),
    product: "app_live"
  }
  @eds_obj.push_logs(EdsConstants::APP_LIVE_WEB_EVENTS, eds_data, params)
  mark_event_end('instrumentation_params', params[:event_hash])

  mark_event_start('script_logger', params[:event_hash])
  script_logger = script_logger_args("#{File.basename(APP_LIVE_SCRIPT)}_start_app")
  mark_event_end('script_logger', params[:event_hash])

  mark_event_start('stop_talkback', params[:event_hash])
  # enable audio handles media projection pop up
  params[:lang_en_media_popup] = "false" # param added for app live session
  if params[:use_rtc_app] == "v2" && params[:device_version].to_i >= 10
    params[:lang_en_media_popup] = params[:locale] unless params[:locale].nil?
    params[:enable_audio] = "true"
    exec_block(using_new_thread) do
      talkback_helper = TalkbackHelper.new(@device)
      talkback_helper.stop_talkback if talkback_helper.talkback_on?
    end
  end
  mark_event_end('stop_talkback', params[:event_hash])

  mark_event_start('dedicated_cleanup', params[:event_hash])
  if params[:dedicated_cleanup] == "true"
    BrowserStack.logger.info("Got dedicated_cleanup request")
    dedicated_cleanup_config = params.key?(:dedicated_cleanup_config) ? params[:dedicated_cleanup_config] : ""
    handle_cleanup_policy_file("dedicated", dedicated_cleanup_config)
  end
  mark_event_end('dedicated_cleanup', params[:event_hash])

  mark_event_start('app_live_actions_bash', params[:event_hash])
  BrowserStack.logger.info "MediaProjection params: use_rtc_app=#{params[:use_rtc_app]}, device_version="\
                           "#{params[:device_version].to_i}, enable_audio=#{params[:enable_audio]}," \
                           "enable_bs_ime=#{params[:enable_bs_ime]}"
  #start_contact_enabling_flow
  enable_contacts = params[:enable_contacts_app_access] || false
  BrowserStack.logger.info("Enable contacts app = #{enable_contacts}")

  obb_file_url = params[:obb_file_url]
  obb_file_name = params[:obb_file_name]
  username = params[:username] || ""
  params[:debugger_port] = @devices_json[@device]["debugger_port"]
  BrowserStack.logger.info("[obb_file_helper_log] obb_file_url #{obb_file_url}, obb_file_name #{obb_file_name}")

  start_method = params[:android_parallel_execution_of_methods].to_s == "true" ? "async_start_app" : "start_app"
  cmd = ["bash", APP_LIVE_SCRIPT, start_method, @device.to_s, args[:interaction_args].to_s, args[:stream_width].to_s, \
         async_start.to_s, tun_ip.to_s, proxy_port.to_s, \
         params[:new_app_download_url].to_s, is_local_update.to_s, params[:app_hashed_id].to_s, \
         params[:launcher_activity].to_s, params[:app_package].to_s, relaunch_flow.to_s, \
         play_store_disabled.to_s, install_experiment_enabled.to_s, synchronous_install.to_s, \
         should_zip_align.to_s, install_timeout.to_s, params["enable_apksigner"].to_s, \
         app_download_timeout.to_s, instrumentation_types.to_s, instrumentation_params.to_s, \
         params[:enable_audio].to_s,  is_biometric_toggled.to_s, is_camera_toggled.to_s, \
         params[:use_rtc_app].to_s, is_webview_toggled.to_s, is_screenshot_block_toggled.to_s, \
         network_logs.to_s, params[:lang_en_media_popup].to_s, is_passcode_toggled.to_s, \
         params[:use_rtc_app_audio].to_s, enable_contacts.to_s, is_audio_injection_session.to_s, \
         params[:enable_rotation_using_app_orientation].to_s, \
         obb_file_url.to_s, obb_file_name.to_s, params[:enable_bs_ime].to_s, \
         params[:enableSim].to_s, is_sync_webrtc.to_s, username, \
         is_network_logs_patch_toggled.to_s, is_camera_preview_layer_toggled.to_s, \
         is_video_toggled.to_s, params[:is_app_accessibility].to_s.downcase, \
         use_instant_app.to_s, params[:handle_mp_during_rotation].to_s, \
         '', '', '', params[:debugger_port].to_s, { err: %i[child out] }]

  cmd_out = "while read line; do echo #{script_logger} $line; done"
  cmd_string = "#{cmd.inspect} | #{cmd_out} &"  # To add the command in logs
  # NOTE: If you are sending any new parameter to above bash script, make sure to accomodate similar changes in
  # /update_app endpoint, which uses the same script
  BrowserStack.logger.info "Starting App Live with command: #{cmd_string}"
  begin
    Open3.pipeline_start(cmd, cmd_out) unless File.exist?("#{STATE_FILES_DIR}/cleanupdone_#{@device}")
  rescue Exception => e
    BrowserStack.logger.error "App Live command #{cmd_string} failed with #{e.message}"
  end
  mark_event_end('app_live_actions_bash', params[:event_hash])

  mark_event_start('enable_bs_ime', params[:event_hash])
  exec_block(using_new_thread) do
    enable_bs_ime(@device) if params[:enable_bs_ime]
  end
  mark_event_end('enable_bs_ime', params[:event_hash])

  mark_event_start('set_default_location', params[:event_hash])
  set_default_location(params)
  mark_event_end('set_default_location', params[:event_hash])

  mark_event_start('set_default_locale', params[:event_hash])
  set_default_locale(params)
  mark_event_end('set_default_locale', params[:event_hash])

  mark_event_start('start_browser_activity_monitoring', params[:event_hash])
  start_browser_activity_monitoring(@device, params)
  mark_event_end('start_browser_activity_monitoring', params[:event_hash])

  mark_event_start('check_and_log_usb_internet', params[:event_hash])
  check_and_log_usb_internet(params, vpn_disabled)
  mark_event_end('check_and_log_usb_internet', params[:event_hash])

  mark_event_start('start_network_usage_tracker', params[:event_hash])
  start_network_usage_tracker(@device, params[:session_id], "app_live_testing")
  mark_event_end('start_network_usage_tracker', params[:event_hash])

  mark_event_start('monitor_device_logger_metric', params[:event_hash])
  monitor_device_logger_metric(@device, get_session_id_from_params(params))
  mark_event_end('monitor_device_logger_metric', params[:event_hash])

  mark_event_start('app_live_profiling', params[:event_hash])
  if params[:app_live_profiling] == true
    BrowserStack.logger.info 'starting PerformanceStatistics'
    PerformanceStatistics.new(@device, BrowserStack.logger).start(get_session_id_from_params(params))
  end
  mark_event_end('app_live_profiling', params[:event_hash])

  mark_event_end('total_ruby_start_time', params[:event_hash])

  BrowserStack.logger.info("LSE ruby_start_instrumentation: #{params[:event_hash]}")
  Thread.bs_run do
    push_to_cls(params, 'ruby_start_instrumentation', '', params[:event_hash])
  end

  # tunnel_setup is critical for session start
  # waiting for it to complete
  tunnel_setup_thread&.join
rescue Exception => e
  status 500
  BrowserStack.logger.error("Exception in /app_start #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
  push_to_cls(
    params,
    'Exception in /app_start',
    e.message.to_s, {
      "device_id" => @device,
      "backtrace" => e.backtrace.join("\n")[0..2000],
      "error_message" => e.message.to_s
    }
  )
  e.message
end

get '/app_update_pid' do
  updated_pid = params[:pid]
  filename = "#{STATE_FILES_DIR}/session_#{@device}"
  BrowserStack.logger.info '/app_update_pid received'
  session_params = JSON.parse(File.read(filename))
  if session_params["genre"] == "app_live_testing"
    BrowserStack.logger.info 'app_update_pid: Nothing to do for App Live session'
  else
    session_params["app_pid"] = updated_pid
    File.open(filename, "w+") do |file|
      file.write(session_params.to_json)
    end
    device_logger_helper = DeviceLogger.new(@device)
    device_logger_helper.update
  end
end

get '/update_device_log_level' do
  log_level = begin
    Integer(params[:log_level])
  rescue StandardError
    nil
  end
  return 400, { error: "Invalid log_level: #{params[:log_level]}" }.to_json if log_level.nil?

  update_device_log_file_path = BrowserStack::APP_LIVE_UPDATE_DEVICE_LOGS_PATH % @device

  BrowserStack.logger.info "Writing log_level: #{log_level} to file: #{update_device_log_file_path}"
  cmd = "mkdir -p #{BrowserStack::APP_LIVE_UPDATE_DEVICE_LOGS_DIR}; echo #{log_level} > #{update_device_log_file_path}"
  system(cmd)
  return 200, {}.to_json
end

def handle_dedicated_device_file(device, params)
  dedicated_device_file = "#{STATE_FILES_DIR}/dedicated_device_#{device}"
  dedicated_device_file_exists = File.exist?(dedicated_device_file)
  dedicated_minimized_cleanup_reserved_file = "#{STATE_FILES_DIR}/dedicated_minimized_cleanup_reserved_#{device}"
  dedicated_minimized_cleanup_reserved_file_exists = File.exist?(dedicated_minimized_cleanup_reserved_file)
  adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
  if params["is_dedicated_cloud_session"].to_s != "true"
    begin
      output = adb.shell("ls #{DEDICATED_DEVICE_FILE_IN_DEVICE_PATH}")
      if output == DEDICATED_DEVICE_FILE_IN_DEVICE_PATH
        adb.shell("rm #{DEDICATED_DEVICE_FILE_IN_DEVICE_PATH}")
        dedicated_device_file_exists = true
      end
    rescue Exception => e
      BrowserStack.logger.info "Failed to delete #{DEDICATED_DEVICE_FILE_IN_DEVICE_PATH}"
    end
    if dedicated_device_file_exists || dedicated_minimized_cleanup_reserved_file_exists
      BrowserStack.logger.info "Stray dedicated_device file found for - #{device}. Deleting it"
      FileUtils.rm_f(dedicated_device_file)
      FileUtils.rm_f(dedicated_minimized_cleanup_reserved_file) if dedicated_minimized_cleanup_reserved_file_exists
      reason = "Stray dedicate_cleanup file found"
      zombie_push(
        'android',
        "android-stray-dedicated-device-file",
        reason,
        nil,
        {
          team: "bridgecloud"
        },
        device,
        params[:automate_session_id],
        is_app_accessibility: params[:is_app_accessibility]
      )
      return false
    end
  else
    FileUtils.touch(dedicated_device_file) unless dedicated_device_file_exists
    if params['dedicated_minified_cleanup'].to_s == "true"
      unless dedicated_minimized_cleanup_reserved_file_exists
        FileUtils.touch(dedicated_minimized_cleanup_reserved_file)
        params.delete('dedicated_minified_cleanup') # first session start should be normal, not minified
      end
      params['dedicated_cleanup'] = "true"
    elsif dedicated_minimized_cleanup_reserved_file_exists
      FileUtils.rm_f(dedicated_minimized_cleanup_reserved_file)
    end

    adb.shell("touch #{DEDICATED_DEVICE_FILE_IN_DEVICE_PATH}")
  end
  true
end

def start_webview_tcp_port
  config = @devices_json || JSON.parse(read_with_lock(CONFIG_FILE))["devices"]
  webview_port = config[@device]["webview_port"]
  # move this to adb gem going forward
  system("adb -s #{@device} forward tcp:#{webview_port} tcp:9111")
  adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
  adb.shell("touch /data/local/tmp/usenetsock")
end

def pre_start(device, params)
  if File.exist?(cleanup_file_for_device(device))
    BrowserStack.logger.info "Cannot start session. #{device} is under cleanup."\
                             "(Found: #{cleanup_file_for_device(device)})"
    return false
  end

  minimized_cleanup_reserved_file = "#{STATE_FILES_DIR}/minimized_cleanup_reserved_#{device}"
  if params["reserveDevice"].to_s != "true" && File.exist?(minimized_cleanup_reserved_file)
    BrowserStack.logger.info "Cannot start session. #{device} is under reserved flow."\
                             "(Found: #{minimized_cleanup_reserved_file})"
    zombie_push('android', 'terminal_sharing_detected', '', params[:genre],
                "session attempted on an already reserved device", device,
                get_session_id_from_params(params), params[:user_id],
                is_app_accessibility: params[:is_app_accessibility])
    return false
  end

  return false unless handle_dedicated_device_file(device, params)
  return false unless check_and_set_cleanup_policy(device, params)

  return true unless File.exist?("#{STATE_FILES_DIR}/session_#{device}")

  old_session_params = JSON.parse(File.read("#{STATE_FILES_DIR}/session_#{device}"))
  return true if old_session_params.empty?

  if old_session_params["user_id"] != params[:user_id]
    BrowserStack.logger.fatal("terminal being shared by two users! new_params: #{params} <=> "\
                              "old_params: #{old_session_params}")
    device_session_utc = File.mtime("#{STATE_FILES_DIR}/session_#{device}").utc
    BrowserStack.logger.fatal "session file modified at: #{device_session_utc}"

    new_session_info = session_merge_info(params, 'new')
    old_session_info = session_merge_info(old_session_params, 'old')

    BrowserStack.logger.fatal("Session merge! old_session_info: #{old_session_info} <=> "\
                              "new_session_info: #{new_session_info}")

    zombie_push('android', 'terminal_sharing_detected', '', '', "#{new_session_info} <=> #{old_session_info}",
                device, get_session_id_from_params(params), params[:user_id],
                is_app_accessibility: params[:is_app_accessibility])
    return false
  else
    BrowserStack.logger.info 'checking to upload logs from device logs'
    if File.exist?("/tmp/pm_tools_used_#{device}") &&
       File.exist?(app_live_logs_params_file(device)) &&
       old_session_params && old_session_params["app_live_session_id"]
      BrowserStack.logger.info 'uploading device logs from pre start'
      cmd = "sed -E 's/^[0-9]+ //' /var/log/browserstack/app_log_#{device}.log "\
            " > /tmp/app_log_#{old_session_params['app_live_session_id']}.log"
      `#{cmd}`
      app_live_logs_params = read_app_live_logs_params(device)
      if app_live_logs_params
        system("bash #{APP_LIVE_SCRIPT} upload_app_live_logs_forked #{app_live_logs_params} >> "\
               "/var/log/browserstack/devicelogs_#{device}.log 2>&1 &")
      end
    end
    system("ps aux | grep -v grep | grep \"#{device} logcat\" | awk '{print $2}' | sudo xargs kill -9")
    system("rm -f /tmp/pm_tools_used_#{device} #{SESSION_START_DIR}/#{device} #{app_live_logs_params_file(device)}")
  end
  true
end

def check_and_set_cleanup_policy(device, params)
  cleanup_policy_file = "#{STATE_FILES_DIR}/dedicated_cleanup_#{device}"
  file_exists = File.exist?(cleanup_policy_file)
  if params[:dedicated_cleanup] == "true" || params["dedicated_cleanup"] == "true"
    BrowserStack.logger.info("Setting dedicated cleanup")
    FileUtils.touch(cleanup_policy_file)
  elsif file_exists
    reason = "Stray dedicate_cleanup file found"
    BrowserStack.logger.info("Deleting dedicated cleanup file")
    FileUtils.rm_f(cleanup_policy_file)
    zombie_push(
      'android',
      "android-stray-dedicated-cleanup-file",
      reason,
      nil,
      {
        team: "bridgecloud"
      },
      device,
      params[:automate_session_id],
      is_app_accessibility: params[:is_app_accessibility]
    )
    BrowserStack::HttpUtils.send_get("#{CLEANUP_ENDPOINT}?device=#{device}&full_cleanup=true&retry_cleanup=true&"\
      "from=#{params[:genre]} start&reason=#{reason}&check_and_clean=true")
    return false
  end
  true
end

def cleanup_file_for_device(device)
  "#{STATE_FILES_DIR}/cleanupdone_#{device}"
end

get '/update_app' do
  script_logger = script_logger_args("#{File.basename(APP_LIVE_SCRIPT)}_update_app")
  is_biometric_toggled = params[:is_biometric_toggled] == "true"
  is_camera_toggled = params[:is_camera_toggled] == "true"
  is_webview_toggled = params[:is_webview_toggled] == "true"
  is_screenshot_block_toggled = params[:is_screenshot_block_toggled] == "true"
  is_passcode_toggled = params[:is_passcode_toggled] == "true"
  is_video_toggled = params[:is_video_toggled] == "true"
  start_webview_tcp_port if is_webview_toggled

  instrumentation_params = params[:instrumentation_hash] || params[:backfill] ||
                           params[:biometric_hash] || params[:webview_hash] || ""

  instrumentation_params = begin
    JSON.parse(instrumentation_params)
  rescue StandardError
    {}
  end

  instrumentation_types = instrumentation_params["instrumentation_types"] || [get_app_live_instrumentation_type(params)]

  unless instrumentation_params.empty?
    instrumentation_params["instrumentation_types"] = instrumentation_types
    instrumentation_params = instrumentation_params.to_json
  end
  is_network_logs_patch_toggled = ["true", true].include?(params[:is_network_logs_patch_toggled])
  app_download_timeout = params[:app_download_timeout] || 90
  username = params[:username] || ""

  # check if mitm proxy is running, we don't send networkLogs key from frontend for app-update
  network_logs = system("ps aux | grep -v grep | grep mitmdump | grep #{@device}")

  cmd = ["bash", APP_LIVE_SCRIPT, "update_app", @device.to_s, params[:new_app_download_url].to_s, \
         params[:app_hashed_id].to_s, params[:app_testing_bundle_id].to_s, \
         params[:launcher_activity].to_s, params[:app_package].to_s, params['enable_apksigner'].to_s, \
         app_download_timeout.to_s, instrumentation_types.to_s, instrumentation_params.to_s, \
         is_biometric_toggled.to_s, is_camera_toggled.to_s, is_webview_toggled.to_s, network_logs.to_s, \
         is_screenshot_block_toggled.to_s, is_passcode_toggled.to_s, username, \
         is_network_logs_patch_toggled.to_s, is_video_toggled.to_s, { err: %i[child out] }]
  cmd_out = "while read line; do echo #{script_logger} \"$line\"; done"
  cmd_string = "#{cmd.inspect} | #{cmd_out} &"
  BrowserStack.logger.info "updating app for App Live with Command : #{cmd_string}"
  begin
    Open3.pipeline_start(cmd, cmd_out)
  rescue Exception => e
    BrowserStack.logger.error "Command #{cmd_string} failed with #{e.message}"
  end

  if params[:app_live_profiling]
    BrowserStack.logger.info 'updating PerformanceStatistics package list'
    PerformanceStatistics.new(@device, BrowserStack.logger).update_app([params[:app_package]])
  end
end

def inject_audio_at_session_start(params)
  AudioInjector.touch_state_file(@device)
  if params[:audio_injection_media_data]
    AudioInjector.log_info("Injecting audio at session start for device: #{@device} "\
                            "and session #{params[:session_id]}")
    begin
      media_data = begin
        JSON.parse(params[:audio_injection_media_data])
      rescue StandardError => e
        AudioInjector.log_info("Error parsing JSON #{e.message}")
        nil
      end
      raise "media_data is empty" if media_data.nil?

      AudioInjector.inject_audio(@device,
                                 media_data["file_url"],
                                 media_data["media_hashed_id"],
                                 media_data["format"],
                                 media_data["session_id"],
                                 media_data["product"])
      AudioInjector.log_info("Injected audio at session start for device: #{@device} "\
                            "and session: #{params[:session_id]}")
    rescue StandardError => e
      AudioInjector.log_error("Failed to inject audio at session start for device: #{@device} "\
                              "and session: #{params[:session_id]}, #{e.message} #{e.backtrace.join("\n")}")
    end
  end
end

def send_app_live_url_to_eds(session_id, params = {})
  begin
    Timeout.timeout(5) do
      url = get_url
    end
  rescue Timeout::Error
    BrowserStack.logger.error "get_url timed out"
    url = "get_url_timeout"
  end
  if !url.nil? && !url.empty?
    eds_data = {
      event_name: "app_live_url_opened",
      session_id: session_id,
      url_opened: url,
      platform: "android"
    }
    @eds_obj.push_logs(EdsConstants::APP_LIVE_WEB_EVENTS, eds_data, params)
  end
end

def remove_forwarded_port(port, tag = '')
  cmd = "adb forward --list | grep #{@device}.*#{port} | awk '{print $2}' | "\
        "xargs -t -r -n 1 adb -s #{@device} forward --remove"
  result, status = OSUtils.execute(cmd, true)
  unless status.success?
    BrowserStack.logger.info "#{tag} remove forwarded port failed, return code #{status}, output #{result}"
    zombie_push('android', "#{tag}_remove_fwd_port_failed", result, port.to_s, status.to_s, @device.to_s)
  end
end

def component_breakdown(component, component_start, component_breakdown_hash)
  component_time = (Time.now - component_start).round(2)
  BrowserStack.logger.debug("#{component} done in: #{component_time}")
  component_breakdown_hash[component] = component_time
end

get '/stop' do
  params[:stop_req_timestamp] = Time.now.to_i
  session_id = get_session_id_from_params(params)
  process_logs_in_async = params[:process_logs_in_async] && (params[:process_logs_in_async].to_s == "true")
  is_minimized_flow = false

  previous_session_info_filename = "#{BrowserStack::STATE_FILES_DIR}/previous_session_#{params[:device]}.txt"
  processing_stop_file = "#{BrowserStack::STATE_FILES_DIR}/processing_stop_#{@device}_#{session_id}"
  processed_stop_file = "#{BrowserStack::STATE_FILES_DIR}/processed_stop_#{@device}_#{session_id}"

  if File.exist?(processed_stop_file)
    BrowserStack.logger.info "Stop request already processed"
    stop_total_time = File.read(processed_stop_file)
    return stop_total_time    # Already processed
  elsif File.exist?(processing_stop_file)
    BrowserStack.logger.info "Skipping stop as original request is still processing"
    return 409                # Still processing
  end
  FileUtils.touch(processing_stop_file)

  File.write(previous_session_info_filename, "Genre: #{params[:genre]}. DeviceLogs: #{params[:devicelogs]}. "\
             "Video: #{params[:video]}. AW: #{params[:active_window]}. ")

  component_start = start = Time.now
  component_breakdown_hash = {}
  clean_in_session(@device)
  # Stopping the proxy checker first before killing privoxy & mitm
  stop_proxy_checker(@device)
  begin
    check_privoxy_process(@device)
  rescue Exception => e
    BrowserStack.logger.error "Could not check privoxy process #{e.message}"
  end

  component_breakdown("check_privoxy_process", component_start, component_breakdown_hash)

  component_start = Time.now
  begin
    # TODO: Move entire /stop to use AndroidDevice
    android_toolkit_adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    state = android_toolkit_adb.devices_info[@device]["state"]
    BrowserStack.logger.info "Device: #{@device} state on /stop: #{state}"

    if state != "online"
      zombie_key_value(
        kind: 'mid-session-off-adb',
        error: params[:genre],
        data: state,
        device: @device,
        session: session_id,
        user: params[:user_id],
        is_app_accessibility: params[:is_app_accessibility]
      )
    end
  rescue Exception => e
    BrowserStack.logger.error "Could not check if device went off adb during session: #{e.message}"
  end

  component_breakdown("mid-session-off-adb", component_start, component_breakdown_hash)

  # Should be after mid-session-off-adb check above
  # stop dns network logging
  CryptoMiningDetectionHelper.new(@device, BrowserStack.logger).disable_network_logging

  device_obj = BrowserStack::AndroidDevice.new(@device, "Server.rb - /stop", BrowserStack.logger)

  if ["SM-X706B", "SM-X716B"].include?(device_obj.model) && !device_obj.screen_on?
    device_obj.capture_screenshot_and_pull("/tmp/snapshot_screen_on_#{session_id}_#{@device}")
    zombie_push(
      'android', "screen-off-during-session", params[:genre],
      @devices_json.keys.to_s, state, @device, session_id, params[:user_id],
      is_app_accessibility: params[:is_app_accessibility]
    )
  end

  if device_obj.uses_bstack_internet_app?
    brt_controller = BStackReverseTetherController.new(device_obj, BrowserStack.logger)
    brt_controller.check_app_and_forwarder(params[:genre], session_id, params)

    # check if transparent proxy mode needs to be turned off
    if transparent_flow_enabled?(session_id, params[:genre], device_obj).to_s == "true"
      brt_controller.toggle_transparent_mode(stop: true)
      TransparentNetworkMode.remove_state_file(device_obj.id)
    end
  end

  device_logger_helper = DeviceLogger.new(@device)
  device_logger_helper.stop

  component_start = Time.now
  logfile = "/var/log/browserstack/app_log_#{@device}.log"
  temp_logfile = "/var/log/browserstack/temp_app_log_#{@device}.log"
  `cp #{logfile} #{temp_logfile}`

  # Will have to do both paths cleanup to handle sessions during deploy
  BrowserStack.logger.info "Stopping privoxy"
  systemd_stop_service("privoxy", device: @device) if systemd_check_if_service_in_list("privoxy", device: @device)
  system(
    "sudo bash #{BS_DIR}/mobile/android/helpers/create_privoxy_service.sh #{@device} ' ' stop #{session_id}"
  )
  system("ps -ax | grep 'privoxy' | grep #{@device}.txt | awk '{print $1}' | sudo xargs kill -9")
  system("ps -ax | grep 'privoxy_push' | grep #{@device} | awk '{print $1}' | sudo xargs kill -9")

  component_breakdown("stopping_privoxy", component_start, component_breakdown_hash)

  port = @devices_json[@device]["port"]
  tun_counter = port.to_i - 8078
  tun_ip = calc_usb_tunnel_ip(port)

  stop_insecure_ws_proxy_server([@devices_json[@device]["mobile_ip"], tun_ip])

  component_start = Time.now

  AdbForwarder.stop_adb_forwarder_service(@device)

  component_breakdown("stopping_adb_forwarder", component_start, component_breakdown_hash)

  session_rtc_params = begin
    JSON.parse(File.read("#{CONFIG_DIR}/rtc_service_#{params[:device]}"))
  rescue StandardError
    {}
  end
  session_info = MobileSessionInfo.read(params[:device])
  app_bundle_id = begin
    session_info["packages"][0]["name"]
  rescue StandardError
    ""
  end

  params[:genre] = session_rtc_params['genre'] if params[:genre].to_s == ''

  session_type = params[:genre]
  feature_usage = {}
  enable_feature_usage_tracking = is_automate_or_app_automate(params["genre"])
  component_start = Time.now
  if ['app_live_testing', 'app_automate'].include?(params[:genre])
    begin
      BrowserStack.logger.info "Inject app: stop: parse_app_patching_logs_and_push_to_eds called"
      app_patching_log_path = BrowserStack::TMP_PATCHED_APP_LOGS_PATH % @device
      AppInjection.pull_patch_log_file(@device, session_id)
      parse_app_patching_logs_and_push_to_eds(logfile, session_id, params, "android",
                                              SERVER_LOG, true, app_patching_log_path)
    rescue StandardError => e
      BrowserStack.logger.error("Inject app: stop: Exception in parsing app patcher logs from device logs "\
                                "#{session_id}, #{e.message} #{e.backtrace}")
    end
  end

  component_breakdown("parse_app_patching_logs_and_push_to_eds", component_start, component_breakdown_hash)
  case params[:genre]
  when "js_testing"
    script_logger = script_logger_args("#{File.basename(JS_SCRIPT)}_stop")

    cmd = "bash #{JS_SCRIPT} stop #{@device} 2>&1 | "\
          "while read line; do echo #{script_logger} \"$line\"; done"
    BrowserStack.logger.info "Stopping js_testing with command #{cmd}"
    system(cmd)

    FileUtils.rm_f("#{STATE_FILES_DIR}/session_#{@device}")
    # JS sessions don't run cleanup - they just clear all the browsers (see browser.sh clear).
  when "live_testing"
    begin
      if BrowserActivityMonitoring.running?(@device)
        File.delete(BrowserActivityMonitoring.start_file(@device))
        BrowserStack.logger.info("/stop BrowserActivityMonitoring removing start file for #{session_id}")
      end
    rescue StandardError => e
      BrowserStack.logger.error("/stop BrowserActivityMonitoring removing start file exception: #{e.message}")
    end
    begin
      local_file_path = "/usr/local/.browserstack/state_files/#{@device}_cookie_data_from_s3.json"
      if params["additional_action"] == "enable_cookie_restore"
        BrowserStack.logger.info("[SessionSavePolling] Found presigned URL for cleanup: " \
          "#{params[:pre_signed_url]}")
        if BrowserStack::HttpUtils.upload_to_s3_with_presigned_url(params[:pre_signed_url], local_file_path)
          BrowserStack.logger.info("[SessionSavePolling] Uploaded the cookies file to S3")
        else
          BrowserStack.logger.error("[SessionSavePolling] Failed to upload cookies file to S3")
        end
      end

      if File.exist?(local_file_path)
        File.delete(local_file_path)
        BrowserStack.logger.info("[SessionSavePolling] Deleted the cookies file from local path")
      end
      if SessionSavePolling.running?(@device)
        File.delete(SessionSavePolling.start_file(@device))
        BrowserStack.logger.info("[SessionSavePolling] /stop SessionSavePolling removing start file for " \
          "#{session_id}")
      end
    rescue StandardError => e
      BrowserStack.logger.error("[SessionSavePolling] /stop SessionSavePolling removing start file " \
        "exception: #{e.message}")
    end
    debugger_port = @devices_json[@device]["debugger_port"]
    script_logger = script_logger_args("#{File.basename(LIVE_SCRIPT)}_stop")
    cmd = "bash #{LIVE_SCRIPT} stop #{@device} #{debugger_port} 2>&1 | "\
          "while read line; do echo #{script_logger} \"$line\"; done"
    BrowserStack.logger.info "Stopping Live with command: #{cmd}"
    system(cmd)

  when 'app_live_testing'
    begin
      if BrowserActivityMonitoring.running?(@device)
        File.delete(BrowserActivityMonitoring.start_file(@device))
        BrowserStack.logger.info("/stop BrowserActivityMonitoring removing start file for #{session_id}")
      end
    rescue StandardError => e
      BrowserStack.logger.error("/stop BrowserActivityMonitoring removing start file exception: #{e.message}")
    end
    component_start = Time.now
    MCSPT.stop_session_running_on_device_async(@device, cancelled: true)
    component_breakdown("mcspt_stop_session_async", component_start, component_breakdown_hash)

    component_start = Time.now
    NetworkLogsUtil.perform_clean_up(@eds_obj, session_id)
    component_breakdown("network_logs_2_0_json_event", component_start, component_breakdown_hash)

    component_start = Time.now
    app_profiling_file = "/tmp/#{session_type}_#{session_id}.csv"
    dup_profiling_file = "/tmp/app_profiling_log_#{@device}.csv"

    send_app_live_url_to_eds(session_id, params)
    component_breakdown("send_app_live_url_to_eds", component_start, component_breakdown_hash)

    component_start = Time.now
    `sed -E 's/^[0-9]+ //' #{logfile} > /tmp/app_log_#{session_id}.log` if File.exist?("/tmp/pm_tools_used_#{@device}")
    component_breakdown("update_app_log_file_since_pm_tools_used", component_start, component_breakdown_hash)

    component_start = Time.now

    `cp #{app_profiling_file} #{dup_profiling_file}`
    component_breakdown("copy_app_profiling_file", component_start, component_breakdown_hash)

    component_start = Time.now
    script_logger = script_logger_args("#{File.basename(APP_LIVE_SCRIPT)}_stop_app")
    cmd = %(bash #{APP_LIVE_SCRIPT} stop_app #{@device} #{params[:is_app_accessibility].to_s.downcase} 2>&1)
    cmd += %( | while read line; do echo #{script_logger} \"$line\"; done)
    BrowserStack.logger.info "Stopping App Live with command: #{cmd}"
    if params["applive_stop_apps_in_different_thread"].to_s == "true"
      Thread.bs_run do
        system(cmd)
      end
    else
      system(cmd)
    end

    component_breakdown("app_live_stop_app", component_start, component_breakdown_hash)
    component_start = Time.now

    begin
      FileUtils.rm_f("#{STATE_FILES_DIR}/al_session_#{session_id}")
      cmd = "curl --connect-timeout 20 -k -i https://localhost:#{DEV_TOOL_PORT}/wd/hub/breakAppiumBridge?rails_session_id=#{session_id}"
      response = `#{cmd}`
      BrowserStack.logger.info("[Appium App-Live] breakAppiumBridge response: #{response}")
    rescue StandardError
      BrowserStack.logger.error("Failed to delete #{STATE_FILES_DIR}/al_session_#{session_id}")
    end
    component_breakdown("appium_break_bridge", component_start, component_breakdown_hash)

  when 'app_automate', 'app_automate-detox'
    minimized_cleanup_reserved_file = "#{BrowserStack::STATE_FILES_DIR}/minimized_cleanup_reserved_#{@device}"
    dedicated_minimized_cleanup_reserved_file = "#{BrowserStack::STATE_FILES_DIR}/dedicated_minimized_cleanup_reserved_#{@device}"  # rubocop:todo Layout/LineLength
    preserve_app_state_reserved_file = "#{BrowserStack::STATE_FILES_DIR}/preserve_app_state_reserved_#{@device}"
    if params["reserveDevice"].to_s == "true" || File.exist?(dedicated_minimized_cleanup_reserved_file)
      is_minimized_flow = true
      process_logs_in_async = true
      FileUtils.mkdir_p(BrowserStack::STATE_FILES_DIR) unless Dir.exist? BrowserStack::STATE_FILES_DIR
      FileUtils.touch(preserve_app_state_reserved_file) if params[:preserve_app_state].to_s == "true"
    else
      FileUtils.rm_rf([minimized_cleanup_reserved_file, preserve_app_state_reserved_file])
    end

    # Stop adb crash logs process
    component_start = Time.now
    pid = get_key_from_duplicate_session_summary(@device, 'crash_logs_command_pid')
    if !pid.nil? && pid != ""
      BrowserStack.logger.info(
        "stopping #{pid}"
      )
      system("kill #{SIGINT} #{pid}")
      crash_logs_helper = CrashLogsHelper.new(params)
      number_of_crash_reports = crash_logs_helper.process_crash_logs
      component_breakdown("app_crash_logs_stop", component_start, component_breakdown_hash)
      # number_of_crash_reports > 0 condition help in skipping the crash log upload, which happens only
      # if params file is created in save_logs_params
      if number_of_crash_reports.to_i > 0
        component_start = Time.now
        crash_logs_helper.inform_rails_about_crash(number_of_crash_reports)
        component_breakdown("inform_rails_about_app_crash", component_start, component_breakdown_hash)
        save_logs_params(@device, params, "crash")
      end
    end

    # Stopping MCSPT session
    component_start = Time.now
    MCSPT.stop_session_running_on_device_async(@device, cancelled: false)
    component_breakdown("mcspt_stop_session_async", component_start, component_breakdown_hash)
    BrowserStack.logger.info("[STOP] params : #{params}, is_minimized_flow : #{is_minimized_flow}, "\
                             "minimized_cleanup_reserved_file_present : "\
                             "#{File.exist?(minimized_cleanup_reserved_file)}, "\
                             "preserve_app_state_reserved_file_present : "\
                             "#{File.exist?(preserve_app_state_reserved_file)}")

    if process_logs_in_async
      FileUtils.mkdir_p(BrowserStack::ASYNC_UPLOAD_REQUEST_DIR) unless Dir.exist? BrowserStack::ASYNC_UPLOAD_REQUEST_DIR
      FileUtils.touch("#{BrowserStack::ASYNC_UPLOAD_REQUEST_DIR}/async_#{params[:automate_session_id]}")
    end

    if params[:devicelogs].to_s == "true"
      component_start = Time.now
      device_model = @devices_json[@device]['device_name']
      logcat_file = "/var/log/browserstack/logcat_#{@device}.log"

      output_file = if process_logs_in_async
                      "/var/log/browserstack/app_log_#{@device}_#{params[:automate_session_id]}.log"
                    else
                      "/tmp/app_log_#{@device}.log"
                    end

      `cp #{temp_logfile} #{output_file}`
      `rm -f #{temp_logfile}`
      `truncate -s 0 #{logfile}`

      file_size_kb = OSUtils.execute("expr $(ls -l #{output_file}  | awk '{print $5}') / 1024").strip.to_f
      devices_needing_reboot = ["V2050"]

      if file_size_kb < 1 && devices_needing_reboot.include?(device_model)
        # More details: https://browserstack.atlassian.net/browse/AA-6655
        needs_reboot_file = "#{STATE_FILES_DIR}/needs_reboot_#{@device}"
        FileUtils.touch(needs_reboot_file) unless File.exist?(needs_reboot_file)
        BrowserStack.logger.info("Touched reboot: #{needs_reboot_file} file, for device logs stability")
      end

      session_specific_logcat_file = "/tmp/session_specific_logcat_#{@device}"

      begin
        check_firebase_and_billing_error(@device, @device_name, params[:automate_session_id],
                                         output_file, params[:user_id])
        check_app_main_thread_load(@device, @device_name, params[:automate_session_id], params[:user_id],
                                   app_bundle_id, output_file)
        check_crypto_configuration_error(@device, @device_name, params[:automate_session_id], params[:user_id],
                                         app_bundle_id, output_file)
        process_device_logs(params, app_bundle_id, output_file)
        check_privoxy_issues(@device, params[:automate_session_id], params[:user_id])
        check_device_logs_network_issues(@device, params[:automate_session_id], output_file)
        check_billing_issues(@device, @device_name, params[:automate_session_id], params[:user_id], logcat_file)
        extract_session_specific_lines(params[:automate_session_id], logcat_file, session_specific_logcat_file)
        check_device_time_sync_issues(@device, @device_name, params[:automate_session_id], params[:user_id],
                                      session_specific_logcat_file)
        check_firebase_logcat_error(@device, @device_name, params[:automate_session_id], params[:user_id],
                                    session_specific_logcat_file)
        check_play_services_used(@device, @device_name, params[:automate_session_id], params[:user_id],
                                 session_specific_logcat_file)
        check_keystore_issues(@device, @device_name, params[:automate_session_id], params[:user_id],
                              session_specific_logcat_file)
        check_for_midsession_chrome_and_webview_updates(@device, @device_name, params[:automate_session_id],
                                                        session_specific_logcat_file, params[:user_id])
        check_zygote_connection_failure(@device, @device_name, params[:automate_session_id],
                                        session_specific_logcat_file, params[:user_id])
        check_app_kill(@device, @device_name, params[:automate_session_id], session_specific_logcat_file,
                       params[:user_id], app_bundle_id)
        unless File.exist?("/tmp/network_simulation_#{@device}") \
        && !File.exist?("#{BrowserStack::ALLOW_LIMITED_NETWORK_FILE}_#{@device}")
          wifi_lost_logfile = "/tmp/wifi_lost_count_#{@device}"
          check_for_wifi_lost_issues(session_specific_logcat_file, wifi_lost_logfile)
        end
      rescue StandardError => e
        BrowserStack.logger.error("Exception occured during parsing logs #{e.message} #{e.backtrace.join("\n")}")
      ensure
        File.delete(session_specific_logcat_file) if File.exist?(session_specific_logcat_file)
      end

      if !process_logs_in_async
        save_logs_params(@device, params, "device")
      else
        uploader_request_file = "#{BrowserStack::ASYNC_UPLOAD_REQUEST_DIR}/OTHER/device_file_#{SecureRandom.uuid}.json"
        save_logs_params_for_async(@device, params, "device", output_file, "device-file", uploader_request_file)
      end
      if params["youiengine_driver_port"]
        youiengine_driver_port = params["youiengine_driver_port"]
        remove_forwarded_port(youiengine_driver_port, 'youie')
      end
      if params["flutter_port"]
        flutter_port = params["flutter_port"].to_i
        # For flutter, the port forwarding is done through appium-flutter-driver code,
        # `inside node_modules/appium-flutter-driver/build/driver/lib/sessions/android.js`
        # we are removing it if the port forwarding is still happening in stop request
        remove_forwarded_port(flutter_port, 'flutter')
      end
      component_breakdown("device_logs_process", component_start, component_breakdown_hash)
    else
      feature_usage["deviceLogs"] = { success: "disabled", exception: "" }
    end
    stop_streaming_and_interaction(params) if session_rtc_params["use_rtc_app"] == "v2"
  else
    minimized_cleanup_reserved_file = "#{BrowserStack::STATE_FILES_DIR}/minimized_cleanup_reserved_#{@device}"
    dedicated_minimized_cleanup_reserved_file = "#{BrowserStack::STATE_FILES_DIR}/dedicated_minimized_cleanup_reserved_#{@device}"  # rubocop:todo Layout/LineLength
    if params["reserveDevice"].to_s == "true" || File.exist?(dedicated_minimized_cleanup_reserved_file)
      is_minimized_flow = true
      process_logs_in_async = true
      FileUtils.mkdir_p(BrowserStack::STATE_FILES_DIR) unless Dir.exist? BrowserStack::STATE_FILES_DIR
    else
      FileUtils.rm_rf(minimized_cleanup_reserved_file)
    end

    BrowserStack.logger.info("[STOP] params : #{params}, is_minimized_flow : #{is_minimized_flow}, "\
                           "minimized_cleanup_reserved_file_present : #{File.exist?(minimized_cleanup_reserved_file)}")

    if process_logs_in_async
      FileUtils.mkdir_p(BrowserStack::ASYNC_UPLOAD_REQUEST_DIR) unless Dir.exist? BrowserStack::ASYNC_UPLOAD_REQUEST_DIR
      FileUtils.touch("#{BrowserStack::ASYNC_UPLOAD_REQUEST_DIR}/async_#{params[:automate_session_id]}")
    end
  end

  component_start = Time.now
  save_video_params(@device, params) if params[:video].to_s == "true"

  if File.exist?("/tmp/google_login_#{params[:automate_session_id]}")
    save_logs_params(@device, params, "screenshot")
    FileUtils.rm_rf("/tmp/google_login_#{params[:automate_session_id]}")
  end

  component_breakdown("video_screenshot_params", component_start, component_breakdown_hash)

  if params["genre"] == "selenium" && !enable_feature_usage_tracking && params[:appiumLogs].to_s == 'true'
    session_appium_logs_path = session_appium_logs(@device, get_session_id_from_params(params), process_logs_in_async)
    FileUtils.cp("/var/log/browserstack/appium_#{@device}.log", session_appium_logs_path)
    parse_appium_logs_for_stability_reason(session_appium_logs_path,
                                           get_session_id_from_params(params),
                                           params, SERVER_LOG)
  end

  if params["genre"].to_s == 'app_automate-detox'
    detox_helper = DetoxHelper.new(@device, port)
    if detox_helper.running_session?
      BrowserStack.logger.info "Stopping detox session with sessionId #{session_id}"
      detox_helper.force_stop_session
    else
      BrowserStack.logger.error "Session was not running for session_id : #{session_id}"
      zombie_push('android', "detox-stop-error", '', '', '', @device,
                  session_id, params[:user_id])
    end
  end

  component_start = Time.now
  if enable_feature_usage_tracking
    if params[:appiumLogs].to_s == 'true'
      process_appium_logs(@device, params, process_logs_in_async)
      if !process_logs_in_async
        save_logs_params(@device, params, "appium")
      else
        uploader_request_file = "#{BrowserStack::ASYNC_UPLOAD_REQUEST_DIR}/OTHER/appium_file_#{SecureRandom.uuid}.json"
        save_logs_params_for_async(@device, params, "appium",
                                   session_appium_logs(@device, params[:automate_session_id], process_logs_in_async),
                                   "appium-file", uploader_request_file)
      end
    else
      feature_usage["appiumLogs"] = { success: "disabled", exception: "" }
    end
    screenshot_instrumentation = BrowserStack::ScreenshotInstrumentation.new(params[:automate_session_id])
    screenshot_instrumentation.add_session_stop_mark(params["debug"], params["genre"])
  end

  if File.exist?("/tmp/playwright_server_#{params[:automate_session_id]}.log")
    save_logs_params(@device, params, "playwright")
  end

  component_breakdown("feature_usage_tracking", component_start, component_breakdown_hash)

  component_start = Time.now
  # Kill mitmproxy in all cases.
  cmd = "bash #{SELENIUM_SCRIPT} stop_mitm #{@device} #{params[:automate_session_id]}"
  output = `#{cmd}`
  BrowserStack.logger.info "IN SERVER MITM Stop Output: #{output}"
  mitmdump_log_file = get_mitmdump_path(@device)
  send_script_injection_status(session_id, @device)
  send_untrusted_certificates_status(session_id, @device)

  if params[:networkLogs].to_s == "true"
    process_mitmdump_logs(params, mitmdump_log_file)
    if !process_logs_in_async
      save_logs_params(@device, params, "network")
    else
      browser_name = params.fetch("browser", "").downcase
      if params["genre"].to_s.casecmp("app_automate").zero? || browser_name == 'samsung'
        log_copy_path = "/var/log/browserstack/mitm_flow_#{@device}_#{session_id}.txt"
        log_source_path = "/tmp/mitm_flow_file_#{@device}.txt"
      else
        log_copy_path = "/tmp/mitm_har_file_#{@device}_#{session_id}.txt"
        log_source_path = "/tmp/har_file_#{@device}_#{session_id}.har"
      end
      begin
        FileUtils.copy_file(log_source_path, log_copy_path)
      rescue Exception => e
        BrowserStack.logger.error("Error copying network logs from #{log_source_path} to #{log_copy_path} "\
                                  "#{e.message} #{e.backtrace.join("\n")}")
      end
      uploader_request_file = "#{BrowserStack::ASYNC_UPLOAD_REQUEST_DIR}/NETWORK/har_file_#{SecureRandom.uuid}.json"
      save_logs_params_for_async(@device, params, "network", log_copy_path, "har-file", uploader_request_file)
    end
  else
    feature_usage["networkLogs"] = { success: "disabled", exception: "" } if enable_feature_usage_tracking
    process_mitmdump_logs(params, mitmdump_log_file) if params[:acceptInsecureCerts].to_s == "true"
  end

  component_breakdown("network_logs_process", component_start, component_breakdown_hash)

  component_start = Time.now
  session_id = params["local_key"]
  if params[:active_window]
    BrowserStack.logger.debug 'active_window'
    script_logger = script_logger_args("#{File.basename(SELENIUM_SCRIPT)}_stop")
    cmd = "bash #{SELENIUM_SCRIPT} active_window #{@device} #{params[:automate_session_id]} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\" >> /var/log/browserstack/selenium_#{@device}.log; done &"
    system(cmd)
  end

  component_breakdown("active_window", component_start, component_breakdown_hash)

  component_start = Time.now
  #check whether chrome requests were present in the session
  check_no_chrome_requests(params)

  component_breakdown("check_no_chrome_requests", component_start, component_breakdown_hash)
  feature_usage["errors"] = instrument_page_load_errors(params) if params["genre"] == "selenium"

  component_start = Time.now
  if session_rtc_params.key?('geoLocation')
    BrowserStack.logger.info 'geolocation_bandwidth_stats'
    measure_bandwidth_stats(session_rtc_params, params[:device])

    component_breakdown("geolocation_bandwidth_stats", component_start, component_breakdown_hash)
  elsif session_rtc_params["tunnelPresent"] || session_rtc_params["hosts"].to_s
    BrowserStack.logger.info 'privoxy_stats'
    privoxy_stats(params, params[:device])

    component_breakdown("privoxy_stats", component_start, component_breakdown_hash)
  else
    BrowserStack.logger.info 'process_network_logs'
    process_network_logs(params, params[:device])

    component_breakdown("network_logs", component_start, component_breakdown_hash)
  end

  if File.exist?("/tmp/network_simulation_#{@device}") && !is_minimized_flow
    File.open(previous_session_info_filename, 'a') { |f| f.print('Network  File Exists. ') }
    ns = NetworkSimulator.new(@device, port, "server", get_session_id_from_params(params))
    ns.reset_simulation("", params)
  end

  #
  # driver_actions stop should only be called for automate sessions.
  # this is not needed for live sessions
  #
  if !params[:automate_session_id].nil? && !params[:automate_session_id].empty?
    component_start = Time.now
    session_type ||= "selenium"
    BrowserStack.logger.debug "local selenium id: #{session_id} going to run stop method driver_actions.sh script"
    chromedriver_port = @devices_json[@device]["chrome_driver_port"]
    appium_port = @devices_json[@device]["selenium_port"]
    selenium_debug_port = @devices_json[@device]["dev_tool_port"]
    chromedriverPorts_start = @devices_json[@device]["chromedriverPorts"][0][0]
    chromedriverPorts_end = @devices_json[@device]["chromedriverPorts"][0][1]

    appium_pid = `lsof -t -i :#{appium_port}`
    BrowserStack.logger.info("[stop] stopping appium with pid : #{appium_pid} running on port #{appium_port}")

    # stop the ai proxy
    ai_proxy_port = appium_port.to_i + AI_PROXY_PORT_OFFSET
    kill_ai_proxy(ai_proxy_port, @devices_json[@device]["ip"])
    BrowserStack.logger.info("[stop] calling kill ai_proxy on #{ai_proxy_port}")

    # stop the cdp proxy
    cdp_port = appium_port.to_i + CDP_PORT_OFFSET
    kill_cdp_proxy(cdp_port, @devices_json[@device]["ip"])
    BrowserStack.logger.info("[stop] calling kill cdp on #{cdp_port}")

    # stop playwright server
    playwright_port = appium_port.to_i + PLAYWRIGHT_ANDROID_PORT_OFFSET
    BrowserStack.logger.info("[stop] calling kill ps on playwright #{playwright_port}")
    kill_playwright_server(playwright_port)

    File.open(previous_session_info_filename, 'a') { |f| f.print('DA Stop being called.') }
    script_logger = script_logger_args("#{File.basename(SCRIPT)}_stop")
    cmd = "bash #{SCRIPT} stop #{@device} #{chromedriver_port} #{appium_port} #{selenium_debug_port} "\
      "#{chromedriverPorts_start} #{chromedriverPorts_end} 2>&1 | "\
      "while read line; do echo #{script_logger} \"$line\" >> #{SERVER_LOG}; done"
    system(cmd)
    BrowserStack.logger.debug "local selenium id: #{session_id}  done with stop method driver_actions.sh script"

    component_breakdown("driver_actions_stop", component_start, component_breakdown_hash)

    component_start = Time.now
    # Archive and truncate appium logs for syncing current session logs to .archive
    truncate_log_file(appium_log_path)

    component_breakdown("truncate_log_file", component_start, component_breakdown_hash)

    BrowserStack.logger.info("Truncating playwright url logs at #{plawyright_url_log_path}")
    component_start = Time.now
    truncate_log_file(plawyright_url_log_path)
    component_breakdown("playwright_url_truncate_log_file", component_start, component_breakdown_hash)

    component_start = Time.now
    #sending event to eds. indication of last event(active_window_bucket for automate),
    # so that it can start merging data.
    event = {}
    event["active_window_bucket"] = ""
    event["hashed_id"] = params[:automate_session_id]
    feature_usage_has_errors = feature_usage.key?("errors") && !feature_usage['errors'].empty?
    event["feature_usage"] = feature_usage if enable_feature_usage_tracking || feature_usage_has_errors
    eds = EDS.new(params, BrowserStack.logger)
    case params[:genre]
    when 'app_automate'
      eds.push_logs(EdsConstants::APP_AUTOMATE_TEST_SESSIONS, event)
    when "js_testing"
      eds.push_logs(EdsConstants::JS_TEST_SESSIONS, event)
    else
      eds.push_logs(EdsConstants::AUTOMATE_TEST_SESSIONS, event)
    end

    component_breakdown("eds_push_logs", component_start, component_breakdown_hash)
    if session_rtc_params["use_rtc_app"] == "v2"
      component_start = Time.now
      component_breakdown("stop_streaming", component_start, component_breakdown_hash)
      stop_streaming_and_interaction(params)
      component_breakdown("stop_streaming", component_start, component_breakdown_hash)
    end
  end

  browser_name = params.key?("browser") ? params["browser"] : nil
  unless browser_name.nil?
    File.open("/tmp/sessionbrowseris_#{@device}", "w") do |f|
      f.write(browser_name.to_s.downcase)
    end
  end
  File.open("/tmp/sessionis_#{@device}", "w") { |f| f.write(session_type) }
  total_time = (Time.now - start).round(2)
  BrowserStack.logger.debug "Stop done in: #{total_time}, time_breakdown: #{component_breakdown_hash}"
  component_breakdown_hash["total_time"] = total_time

  zombie_push('android', "android-stop-time", params[:genre], total_time, component_breakdown_hash, @device,
              get_session_id_from_params(params), params[:user_id], is_app_accessibility: params[:is_app_accessibility])

  FileUtils.mv(processing_stop_file, processed_stop_file)
  File.write(processed_stop_file, "Stop: #{total_time}")
  "Stop: #{total_time}"
rescue StandardError => e
  FileUtils.rm_f(processing_stop_file)
  FileUtils.rm_f(processed_stop_file)
  return 500
end

get '/screenshot' do
  params[:port] = @devices_json[@device]["port"]

  script_logger = script_logger_args("#{File.basename(SCRIPT)}_screenshot")
  cmd = "bash #{SCRIPT} screenshot #{@device} #{params[:port]} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\" >> #{SERVER_LOG}; done"
  system(cmd)

  proxy_url = nil
  proxy_url = tunnel_setup(params) if params["hosts"]

  params[:quality] ||= "high"
  params[:waitTime] ||= "2000"
  params[:cropImage] ||= "false"

  params[:hostname] ||= "www.browserstack.com"
  params[:s3bucket] ||= "browsershots"
  params[:timeoutUrlLoad] ||= "70000"
  params[:timeoutScreenshot] ||= "20000"
  params[:timeoutDriverQuit] ||= "10000"
  params[:orientation] ||= "portrait"
  params[:branding] ||= "on"

  filename = "/usr/local/.browserstack/mobile/android/#{device}"

  write_params_to_file(params, filename)

  script_logger = script_logger_args("mobile-screenshot.jar_post_orientation")
  cmd = "cd /usr/local/.browserstack/android/ && /usr/bin/java mobile-screenshot.jar #{filename} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\" >> /var/log/browserstack/screenshot.log; done"
  spawn_process(cmd)

  "Screenshot processing"
end

get '/remove_device' do
  device_check_running = system("ps aux | grep -v grep | grep -c device_check.rb")
  halt "Device check is running" if device_check_running

  begin
    dirs_to_check = [STATE_FILES_DIR, LOG_DIR_PATH, CONFIG_DIR, "#{CONFIG_DIR}/custom_device_configs/", "/tmp/"]
    dirs_to_check.each do |d|
      Dir.glob("/#{d}/*").select { |device_file| /#{@device}/.match(device_file) }.each do |file|
        File.delete(file)
      end
    end
  rescue StandardError => e
    BrowserStack.logger.info("Unable to remove file: #{e.message}")
  end

  config = JSON.parse(read_with_lock(CONFIG_FILE))
  device_selenium_port = config["devices"][@device]["selenium_port"]
  system("sudo bash /usr/local/.browserstack/mobile/android/helpers/create_appium_service.sh "\
         "#{@device} #{device_selenium_port} '' '' '' remove")
  config["devices"].delete(@device)
  write_config_with_lock(CONFIG_FILE, config.to_json)
  "done"
end

get '/recover_device' do
  device_id = params["device"]
  device_recovery_file = RECOVER_DEVICE_FILE + device_id
  state_session_file = "#{STATE_FILES_DIR}/session_#{device_id}"
  action_needed = File.exist?(device_recovery_file) && !File.exist?(state_session_file)
  if action_needed
    host_command = File.read(device_recovery_file)
    `#{COMMON}/push_to_zombie.rb "android" "android-recover-device" "#{host_command}" "#{@device}" `
    File.delete(device_recovery_file)
    host_command.to_s
  else
    "recover check completed, device is in session or no action needed"
  end
end

get '/tunnel' do
  tunnel_setup(params)
  "done"
end

get '/snapshot_hub' do
  return 404 unless Snapshotter.valid_request?(params)

  session_file = MobileSessionInfo.file_path(params["device"])

  unless Snapshotter.valid_session?(session_file, params["folder"])
    BrowserStack.logger.info "Got snapshot request for previous session #{params['folder']}"
    zombie_push('android', "snapshot_request_previous_session", '', '', '', params["device"], params["folder"], nil)
    return 404
  end

  filename = "#{params['bucket']}###{params['folder']}###{params['file']}"
  selenium_debug_port = @devices_json[@device]["dev_tool_port"]
  device_version_gte12 = Gem::Version.new(@devices_json[@device]["device_version"]) >= Gem::Version.new(12)
  check_black_screenshot = params["instrumentBlackScreenshot"] || false

  # check no of screenshot process
  unless Snapshotter.can_take_screenshots?(@device, DEBUG_SCREENSHOT_SCRIPT, BrowserStack.logger)
    BrowserStack.logger.info 'skipping hub_snapshot'
    # params['folder'] is the session_id
    screenshot_instrumentation = BrowserStack::ScreenshotInstrumentation.new(params["folder"])
    screenshot_instrumentation.update_screenshot_instrumentation_with_lock(nil, nil, {})
    return 'done'
  end

  use_adb_for_screenshot = params["useADBforScreenshot"].to_s.casecmp("true").zero?

  script_logger = script_logger_args("#{File.basename(DEBUG_SCREENSHOT_SCRIPT)}_snapshot_hub")
  cmd = "ruby #{DEBUG_SCREENSHOT_SCRIPT} #{@device} #{selenium_debug_port} #{filename} #{params['orientation']} "\
        "#{params['key']} #{params['secret']} #{device_version_gte12} #{use_adb_for_screenshot} "\
        "#{check_black_screenshot} 2>&1 | while read line; do echo #{script_logger} \"$line\" >> "\
        "/var/log/browserstack/hub_snapshot_#{@device}.log; done &"
  BrowserStack.logger.info "Taking screenshot: #{cmd}"

  Snapshotter.take_screenshot(cmd)
  "done"
end

get '/snapshot' do
  filename = params["name"]
  return 404 if filename.nil?

  quality = params[:quality] || 20
  filename = "/usr/local/.browserstack/#{filename}-#{@device}"
  script_logger = script_logger_args("#{File.basename(SCRIPT)}_snapshot")
  cmd = "bash #{SCRIPT} snapshot #{@device} #{filename} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\" >> #{SERVER_LOG}; done"
  system(cmd)
  if params[:cheap_stream]
    content_type "image/jpeg"
  else
    content_type "application/octet-stream"
  end
  begin
    img = nil
    if params[:cheap_stream]
      # copy file with jpeg extension
      cp_jpeg_file = "cp #{filename} #{filename}.jpeg"
      img_qlty_cmd = "#{ENV['IMAGEMAGICK_CONVERT']} #{filename}.jpeg -quality #{quality} #{filename}.jpeg"
      system("#{cp_jpeg_file} && #{img_qlty_cmd}")
      img = open("#{filename}.jpeg", "rb", &:read) # rubocop:todo Security/Open
      rm_jpeg_file = "rm #{filename}.jpeg"
      system(rm_jpeg_file)
    else
      img = open(filename, "rb", &:read) # rubocop:todo Security/Open
    end
  rescue Exception => e
    zombie_push('android', 'snapshot_error', '', '', e.inspect[0, 300].to_s, @device.to_s)
    @hooter.send(@device_name, 'snapshot_error')
    img = nil
  end
  File.delete(filename)
  headers["Refresh"] = '0.1' if params[:cheap_stream]
  img
end

get '/reset' do
  config = JSON.parse(read_with_lock(CONFIG_FILE))
  devices_json = config["devices"]
  if devices_json
    old_port_map = {}
    devices = `adb devices | grep device | grep -v devices | awk '{print $1}'`.split("\n")
    devices.each do |device|
      old_port_map[device] = devices_json[device]["port"] if devices_json[device]
    end
    File.open("#{CONFIG_DIR}/port_config.json", 'w') do |file|
      file.write(old_port_map.to_json)
    end
  end
  File.open(CONFIG_FILE, "w") { |f| f << "{}" }
  system("/usr/local/.browserstack/mobile/android/device_check.rb")
  "hope is a good thing"
end

get '/device_params' do
  json_obj = begin
    File.read("#{CONFIG_DIR}/rtc_service_#{@device}")
  rescue StandardError
    nil
  end
  BrowserStack.logger.info "Called for device params - #{json_obj}"
  json_obj
end

# rotation in S24 family of devices leads to media projection popup
# this is called from within the RTC2 app to handle that popup
get '/handle_media_projection_permission' do
  Thread.bs_run do
    default_locale = "en-US"
    change_locale = (params[:locale] != default_locale)
    endpoint_retry = params[:retryCount].nil? ? '1' : params[:retryCount]
    orientation = params[:orientation].nil? ? 'nil' : params[:orientation]

    # set locale to en-US
    set_device_locale(@device, default_locale, BrowserStack.logger, { async: false }) if change_locale

    session_filename = "#{STATE_FILES_DIR}/session_#{@device}"
    begin
      session_params = JSON.parse(File.read(session_filename))
    rescue JSON::ParserError, Errno::ENOENT => e
      session_params = {}
    end
    is_live_testing = session_params['genre'] == 'live_testing'
    session_id = is_live_testing ? session_params['live_session_id'] : session_params['app_live_session_id']

    data = {
      event_name: 's24_media_projection_popup_handler',
      product: session_params['genre'] == 'live_testing' ? 'LIVE' : 'APP_LIVE',
      os: 'Android',
      team: session_params['genre'] == 'live_testing' ? 'live' : 'app_live',
      session_id: session_id,
      endpoint_retry: endpoint_retry,
      orientation: orientation
    }
    # accept media projection popup
    # `sh #{LIVE_SCRIPT} handle_media_projection_permission #{@device}`
    Timeout.timeout(60) do
      # accept media projection popup using keyevent
      result = `sh #{LIVE_SCRIPT} handle_media_projection_permission_via_keyevent #{@device}`
      handle_attempts, is_popup_visible, is_popup_closed = result.split

      data = data.merge({
        event_json: {
          handling_method: "handle_media_projection_permission_via_keyevent",
          handle_attempts: handle_attempts.to_i,
          is_popup_visible: is_popup_visible == 'true',
          is_popup_closed: is_popup_closed == 'true'
        }
      })
    end

    # reset locale
    set_device_locale(@device, params[:locale], BrowserStack.logger, { async: false }) if change_locale
  rescue Timeout::Error => e
    `sh #{LIVE_SCRIPT} handle_media_projection_permission #{@device}`
    data = data.merge({
      event_json: {
        handling_method: "handle_media_projection_permission"
      }
    })
  rescue Exception => e
    BrowserStack.logger.error "exception in handle_media_projection_permission #{e.message} #{e.backtrace}"
    data = data.merge({
      event_json: {
        error: e.message.to_s
      }
    })
  ensure
    @eds_obj.push_logs("web_events", data)
  end
  "running handle_media_projection_permission in a thread"
end

def check_file(file, _log_head)
  if file.nil? || file.empty? || !File.exist?(file)
    BrowserStack.logger.info "File not found at #{file}"
    return false
  end
  true
end

get '/push_file' do
  log_head = "push_file"
  push_file = params["push_file"]
  read_file = "/tmp/upload_#{@device}"

  return 404 unless check_file(push_file, log_head)

  BrowserStack.logger.info "Execute Selenium script to push file #{push_file} to "\
                           "device #{@device}, log file #{SERVER_LOG}"
  script_logger = script_logger_args("#{File.basename(SELENIUM_SCRIPT)}_push_file")
  cmd = ["bash", SELENIUM_SCRIPT, "push_file", @device.to_s, push_file.to_s, { err: %i[child out] }]
  cmd_out = "while read line; do echo #{script_logger} \"$line\" >> #{SERVER_LOG}; done"
  cmd_string = "#{cmd.inspect} | #{cmd_out} &"
  begin
    Open3.pipeline_start(cmd, cmd_out)
  rescue Exception => e
    BrowserStack.logger.error "Command #{cmd_string} failed with #{e.message}"
  end
  return 404 unless check_file(read_file, log_head)

  target_file = File.read(read_file).strip
  return 404 if target_file.match(/skipped_due_to_size/)

  return target_file
end

def get_url
  begin
    device_browser_history = DeviceBrowserHistory.new(@device, BrowserStack.logger)
    result = device_browser_history.last_url
  rescue StandardError => e
    result = "error occurred getting last visited url from device: #{e}"
  end

  begin
    result = result.encode "UTF-8" unless result.nil?
  rescue Encoding::UndefinedConversionError
    result = "utf8 encoding error: #{result}"
  end

  if !result.nil? && result.start_with?('data:')
    # data URLs are unable to be handled upstream and should be filtered out silently:
    BrowserStack.logger.info "Filtered out data url: #{result}"
    result = ''
  elsif !result.nil? && !result.empty? && (result =~ URL_REGEX).nil?
    # send error to zombie, geturl_error is also used for iOS JB sessions
    zombie_push('android', 'geturl_error', '', '', result.to_s, @device.to_s)
    zombie_push('android', 'android_get_url_error', '', '', result.to_s, @device.to_s)
    BrowserStack.logger.error "URL from geturl: #{result}"
    @hooter.send(@device_name, 'geturl_error', 'chrome')
    result = ""
  end
  BrowserStack.logger.info("URL from geturl: #{result}") unless result.nil? || result.empty?
  result
end

get '/geturl' do
  return "" if params["no_geturl"]

  get_url
end

get '/orientation' do
  read_file = "/tmp/orientation_#{@device}"

  script_logger = script_logger_args("#{File.basename(SELENIUM_SCRIPT)}_get_orientation")
  cmd = "bash #{SELENIUM_SCRIPT} get_orientation #{@device} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\" >> /var/log/browserstack/selenium_#{@device}.log; done"
  system(cmd)
  BrowserStack.logger.info "get orientation: #{cmd}"
  error 500, "Selenium Script Error" unless File.exist?(read_file)
  return File.read(read_file).strip
end

post '/orientation' do
  required_orientation = params["orientation"]
  error 500, "Wrong Orientation requested" if required_orientation.nil? ||
                                              !required_orientation.downcase.match(/^portrait$|^landscape$/)
  script_logger = script_logger_args("#{File.basename(SELENIUM_SCRIPT)}_post_orientation")
  cmd = "bash #{SELENIUM_SCRIPT} set_orientation #{@device} #{required_orientation.downcase} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\" >> /var/log/browserstack/selenium_#{@device}.log; done &"
  system(cmd)
  BrowserStack.logger.info "set orientation: #{cmd}"
  return required_orientation
end

get '/log_bridge_commands' do
  begin
    appium_logs_append_marker(params)
  rescue StandardError => e
    BrowserStack.logger.info("Exception while adding marker: #{e.message}")
    BrowserStack::Zombie.push_logs("appium-marker-failed", e.message.to_s,
                                   { "session_id" => params['automate_session_id'], "device" => @device })
  end
  return 200, {}.to_json
end

def set_user_specified_prop(setprop, session_id)
  success = true
  props = begin
    JSON.parse(setprop)
  rescue StandardError
    nil
  end
  if props
    props.each do |prop_key, prop_value|
      prop = "#{CUSTOM_PROP}.#{prop_key}"
      prop_value = prop_value.to_s.strip

      `timeout 3 adb -s #{@device} shell $'setprop #{prop} "#{prop_value}"'`

      # verify
      set_value = `timeout 3 adb -s #{@device} shell getprop #{prop}`.strip
      success &&= set_value == prop_value
    end
    zombie_push('android', 'setprop-failed', '', @device_name, "", @device, session_id) unless success
  end
end

def initialize_feature_usage
  @feature_usage = {}
  features_to_track = [
    'airplaneMode',
    'playstoreLogin',
    'customMedia',
    'gpsLocation',
    'geoLocation',
    'customNetwork',
    'language',
    'locale',
    'timezone',
    'disableAnimations',
    'otherApps',
    'midSessionInstallApps',
    'orientation',
    'customMedia_contacts',
    'devicePreferences'
  ]
  features_to_track.each do |feature|
    @feature_usage[feature] = {
      success: "disabled",
      exception: ""
    }
  end
end

def update_feature_usage(feature, success, error="")
  @feature_usage[feature] = { success: success, exception: error } if @feature_usage
end

def forward_port_for_youiengine(host_port, device_port)
  retries = 0
  while retries < 3

    cmd = "adb -s #{@device} forward tcp:#{host_port} tcp:#{device_port}"
    OSUtils.execute(cmd)
    check_forwarding_cmd = "adb forward --list | grep -c #{@device}.*#{host_port}"
    output = OSUtils.execute(check_forwarding_cmd)
    return if output.to_i > 0

    BrowserStack.logger.info("port forwarding for youiengine might have failed on attempt "\
                             "#{retries + 1}, output:  #{output}")
    retries += 1
  end
  zombie_push('android', 'youie_fwd_port_failed', "", host_port.to_s, device_port.to_s, @device.to_s)
  raise FireCMDException, "port forwarding failed for driver port: #{host_port}, app port: #{device_port} "\
                          "after #{retries} attempts"
end

def start_network_usage_tracker(device, session_id, genre)
  pid = Process.fork do
    cmd = "ruby #{DIR_HOME}/android/lib/network_usage_tracker.rb #{session_id} #{device} #{genre} 'save_state' "
    exec(cmd)
  end
  Process.detach(pid)
rescue StandardError => e
  BrowserStack.logger.error("Unable to start network usage tracker: #{e.message} #{e.backtrace.join("\n")}")
end

def monitor_device_logger_metric(device_id, session_id)
  BrowserStack.logger.info "Starting device logger metric monitoring"
  pid = Process.fork do
    cmd = "ruby #{DIR_HOME}/android/lib/device_logger_metric.rb 'start_monitoring' #{device_id} #{session_id}"
    exec(cmd)
  end
  Process.detach(pid)
rescue StandardError => e
  BrowserStack.logger.error("Unable to start device logger metric monitoring: #{e.message} #{e.backtrace.join("\n")}")
end

def add_samsung_command_line_file(params, default_port, device)
  android_toolkit_adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
  acceptsslEnabled = params["samsungAcceptSslCert"]
  terminal_ip = params["terminal_ip"]
  privoxy_port = calculate_privoxy_port(default_port)
  command_line_value = "chrome --disable-help-ux --proxy-server=http://#{terminal_ip}:#{privoxy_port} "\
                       "--proxy-bypass-list=\"<-loopback>\""
  command_line_value += " --ignore-certificate-errors" if acceptsslEnabled.to_s.casecmp("true").zero?
  result = android_toolkit_adb.shell("echo '#{command_line_value}' > /data/local/tmp/terrace-command-line")
  BrowserStack.logger.info "Samsung Browser cmd line: #{command_line_value}, result: #{result}"
  result
end

def add_samsung_command_line_file_live_session(device)
  android_toolkit_adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
  command_line_value = "chrome --disable-help-ux"
  result = android_toolkit_adb.shell("echo '#{command_line_value}' > /data/local/tmp/terrace-command-line")
  BrowserStack.logger.info "Samsung Browser cmd line: #{command_line_value}, result: #{result}"
  result
end

def kill_interaction_server_and_uiautomator
  kill_uiautomator_cmd = "adb -s #{@device} shell ps | grep 'uiautomator' | awk '{print $2}' | "\
                         "xargs adb -s #{@device} shell kill"
  spawn_process(kill_uiautomator_cmd)
  kill_interactionserver_cmd = "adb -s #{@device} shell ps | grep 'interactions_server' | awk '{print $2}' | "\
                               "xargs adb -s #{@device} shell kill"
  spawn_process(kill_interactionserver_cmd)
end

def start_streaming_and_interaction(params)
  BrowserStack.logger.info("Starting Streaming and Interaction")
  begin
    if @devices_json[@device]["device_version"].to_f < 14.0
      params[:debugger_url] = format(DEVTOOLS_URL, @devices_json[@device]["ip"], params[:debugger_port])
    end
    args = write_rtc_params(params)
    state_session_file = "#{STATE_FILES_DIR}/session_#{@device}"
    rtc_service_file = "#{CONFIG_DIR}/rtc_service_#{@device}"
    if File.exist?(state_session_file)
      begin
        state_session_data = JSON.parse(File.read(state_session_file))
        rtc_service_data = JSON.parse(File.read(rtc_service_file))
        state_session_data.each_key do |key|
          writeable_param = !BLACKLISTED_RTC_PARAMS.include?(key)
          rtc_service_data[key] = state_session_data[key] if rtc_service_data[key].nil? && writeable_param
        end
        File.open(state_session_file, 'w+') { |file| file.write(rtc_service_data.to_json) }
      rescue StandardError => e
        BrowserStack.logger.error("Error in merging rtc and session data: #{e.message} #{e.backtrace.join("\n")}")
        copy_file(rtc_service_file, state_session_file)
      end
    end
    script_logger = script_logger_args("#{File.basename(LIVE_SCRIPT)}_selenium")
    if params[:use_rtc_app] != "v2"
      cmd = "bash #{LIVE_SCRIPT} start_webrtc_streaming_automate #{@device} '#{args[:interaction_args]}' '' " \
      "'#{args[:stream_width]}' '' '' '' #{params[:debugger_port]} 2>&1 | " \
      "while read line; do echo #{script_logger} \"$line\"; done &"
      BrowserStack.logger.info("Starting streaming for Automate using Live script with command: #{cmd}")
      system(cmd)
      input_injector_apk_path = InputInjectorMainAppManager.new(@device, BrowserStack.logger).apk_path
      system("adb -s #{@device} push #{input_injector_apk_path} /data/local/tmp/InputInjector.apk")
      system("adb -s #{@device} shell 'CLASSPATH=\"/data/local/tmp/InputInjector.apk\" app_process / "\
              "com.browserstack.inputinjector.Main &' &")
      interactive_session_file = "#{STATE_FILES_DIR}/interactive_session_file_#{@device}"
      FileUtils.touch(interactive_session_file)
    else
      BrowserStack.logger.info "MediaProjection params: use_rtc_app=#{params[:use_rtc_app]}, device_version="\
                                "#{params['version'].to_i}, use_rtc_app_audio=#{params[:use_rtc_app_audio]}"

      enable_audio = "false" unless params[:enable_audio].nil?
      # enable audio handles media projection pop up
      enable_audio = "true" if params["version"].to_i >= 10

      cmd = "bash #{LIVE_SCRIPT} start_webrtc_streaming_automate #{@device} '#{args[:interaction_args]}' '' "\
      "'#{args[:stream_width]}' '' '' '' #{params[:debugger_port]} '' '' '' '' '#{enable_audio}' "\
      "'#{params[:use_rtc_app]}' '' '#{params[:use_rtc_app_audio]}' 2>&1 | "\
      "while read line; do echo #{script_logger} $line; done &"

      BrowserStack.logger.info("Starting streaming for Automate using Live script with command: #{cmd}")
      system(cmd)
    end
  rescue StandardError => e
    BrowserStack.logger.error("Error in starting streaming and interaction #{e}")
  end
end

def stop_streaming_and_interaction(params)
  BrowserStack.logger.info("Stopping Streaming and Interaction")
  stop_streaming_error = []
  begin
    if @devices_json[@device]["device_version"].to_f < 14.0
      params[:debugger_url] = format(DEVTOOLS_URL, @devices_json[@device]["ip"], params[:debugger_port])
    end
    args = write_rtc_params(params)

    script_logger = script_logger_args("#{File.basename(LIVE_SCRIPT)}_selenium")
    cmd = "bash #{LIVE_SCRIPT} stop_webrtc_streaming_automate #{@device} '#{args[:interaction_args]}' '' "\
          "'#{args[:stream_width]}' 2>&1 | while read line; do echo #{script_logger} \"$line\"; done &"

    BrowserStack.logger.info("Stopping streaming for Automate using Live script with command: #{cmd}")
    system(cmd)

    interactive_session_file = "#{STATE_FILES_DIR}/interactive_session_file_#{@device}"
    FileUtils.rm_f(interactive_session_file)
  rescue StandardError => e
    BrowserStack.logger.error("Error in stopping streaming and interaction #{e}")
    session_id = get_session_id_from_params(params)
    zombie_push(
      'android', 'stop-interactive-session-error',
      '', '', e.inspect[0, 300].to_s, params[:device], session_id
    )
    stop_streaming_error << e.inspect
  ensure
    if is_automate_or_app_automate(params["genre"]) && !stop_streaming_error.empty?
      push_to_cls(params, "stop-interactive-session-error", stop_streaming_error.join(",").to_s,
                  { "data" => params.to_json, "instance_id" => params[:device] })
    end
  end
end

def execute_fire_cmd(params) # rubocop:todo Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/MethodLength, Metrics/PerceivedComplexity
  unless pre_start(@device, params)
    halt 500, { error: "Device under cleanup/already allocated",
                kind: "unknown_exception",
                type: "browserstack_error" }.to_json
  end

  params[:event_hash] = {
    absolute_start_time: (Time.now.to_f * 1000).to_i
  }

  enable_mobile_data = check_and_enable_mobile_data?(@device, params, @devices_json[@device])
  if enable_mobile_data
    enable_brt_for_browserstack_apps(@device)
    zombie_push('android', "enabled-mobile-data", "", "", params['genre'], @device, get_session_id_from_params(params))
  end

  # Enable dns logs capture to detect possible crypto mining
  mark_event_start('crypto_detector', params[:event_hash])
  CryptoMiningDetectionHelper.new(@device, BrowserStack.logger).enable_network_logging
  mark_event_end('crypto_detector', params[:event_hash])

  mark_event_start('device_logger_init', params[:event_hash])
  device_logger_helper = DeviceLogger.new(@device)
  device_logger_helper.init
  mark_event_end('device_logger_init', params[:event_hash])

  mark_event_start('assigned_variables', params[:event_hash])
  response_hash = {}
  video_sync_hash = {
    "start_firecmd_request" => Time.now.to_f
  }

  automate_funnel = AutomateFunnel.new

  automate_funnel.mark_block_start('AndroidFireCMD')
  automate_funnel.mark_breakup_start
  automate_funnel.mark_block_start('DeviceSetup')

  session_id = get_session_id_from_params(params)
  default_port = @devices_json[@device]["port"]
  selenium_debug_port = @devices_json[@device]["dev_tool_port"]
  appium_port = @devices_json[@device]["selenium_port"]
  chrome_driver_port = @devices_json[@device]["chrome_driver_port"]
  chromedriverPorts = @devices_json[@device]["chromedriverPorts"]
  android_bootstrap_port = appium_port.to_i + 100
  device_name = params[:deviceName].split("-")[0].gsub("__", " ").downcase
  BrowserStack.logger.info "Device name #{device_name}"
  if params["dedicated_cleanup"] == "true"
    BrowserStack.logger.info("Got dedicated_cleanup request")
    dedicated_cleanup_config = params.key?("dedicated_cleanup_config") ? params["dedicated_cleanup_config"] : ""
    handle_cleanup_policy_file("dedicated", dedicated_cleanup_config)
  end
  stream_width = get_stream_width(device_name)
  device_orientation = params["orientation"] || "portrait"
  default_appium_version = get_default_appium_version(@devices_json[@device]["device_version"])
  appium_version = params["appium"] || default_appium_version
  automation_version = params["automationVersion"]

  timezone = params["timezone"] if params["timezone"]
  hosts = params["hosts"]
  automationName = params["automationName"].to_s.downcase || ''
  device_version = params["version"]
  chrome_driver_version = params["chromeDriverVersion"] || ''
  appium_version_caps = "appium-default-#{default_appium_version}"
  appium_version_caps = "appium-non-default-#{appium_version}" if appium_version != default_appium_version
  initialize_feature_usage
  is_app_testing = !params[:genre].nil? && params[:genre].to_s.eql?('app_automate')
  bundle_id = is_app_testing ? params[:app_testing_bundle_id].to_s : ""
  device = params[:device]

  if is_app_testing
    params["app_automate_custom_params"] = begin
      JSON.parse(params["app_automate_custom_params"])
    rescue StandardError
      {}
    end

    enable_apksigner = params["app_automate_custom_params"]["enable_apksigner"].to_s
    skip_resign = params["resignApp"].to_s == "false" ? "true" : "false"
    is_apks_archive = params["app_automate_custom_params"]["is_apks_archive"]
    install_experiment_enabled = params["install_experiment_enabled"] =
      params["app_automate_custom_params"]["install_experiment_enabled"].to_s
    subregion_app_caching_enabled =
      params["app_automate_custom_params"]["subregion_app_caching_proxy_enabled"].to_s == "true"
    params["async_app_download_install"] =
      params["app_automate_custom_params"]["async_app_download_install"].to_s == "true"
    error_kind = is_apks_archive ? 'apks' : 'app'
    download_and_install_thread = nil
  end

  if automationName.to_s.casecmp("youiengine").zero?
    begin
      youiengine_app_port = params["youiEngineAppPort"]
      youiengine_driver_port = params["youiengine_driver_port"]
      forward_port_for_youiengine(youiengine_driver_port, youiengine_app_port)
    rescue FireCMDException => e
      BrowserStack.logger.error("[FIRE_CMD_ERROR] youiengine port forward  failed #{e.message} "\
                                "backtrace: #{e.backtrace.join('\n')}.")
      automate_funnel.mark_block_end('DeviceSetup', 'failure', 'youiengine_port_forward_failure')
      automate_funnel.mark_breakup_end
      automate_funnel.mark_block_end('AndroidFireCMD', 'failure', 'youiengine_port_forward_failure')
      return 500, { :error => e.message.to_s,
                    :kind => "youiengine_port_forward_failure",
                    :type => e.type.to_s, 'funnel_data' => automate_funnel.generate_data_json }.to_json
    rescue StandardError => e
      BrowserStack.logger.error("[FIRE_CMD_ERROR] youiengine port forward  failed: #{e.message} "\
                                "backtrace: #{e.backtrace.join('\n')}.")
      automate_funnel.mark_block_end('DeviceSetup', 'failure', 'youiengine_port_forward_failure')
      automate_funnel.mark_breakup_end
      automate_funnel.mark_block_end('AndroidFireCMD', 'failure', 'youiengine_port_forward_failure')
      return 500, { :error => e.message.to_s,
                    :kind => "youiengine_port_forward_failure",
                    :type => BROWSERSTACK_ERROR_STRING, 'funnel_data' => automate_funnel.generate_data_json }.to_json
    end
  end

  automate_funnel.mark_block_end('DeviceSetup', 'success')

  mark_event_end('assigned_variables', params[:event_hash])

  sess_details = {
    genre: params["genre"],
    host: params["host"],
    session_id: params["automate_session_id"],
    user_id: params["user_id"],
    device: @device,
    android_version: @devices_json[@device]["device_version"],
    device_name: device_name,
    test_params: params[:test_params],
    automate_session_id: params["automate_session_id"]
  }

  mark_event_start('app_bundle_ids', params[:event_hash])
  app_bundle_ids = [bundle_id]

  if params["other_apps"]
    other_app_bundle_ids = get_dependent_app_bundle_ids(params["other_apps"])
    (app_bundle_ids << other_app_bundle_ids).flatten!
  end

  if params["mid_session_install_apps"]
    mid_session_app_bundle_ids = get_dependent_app_bundle_ids(params["mid_session_install_apps"])
    (app_bundle_ids << mid_session_app_bundle_ids).flatten!
  end

  if params["espresso_server_app"]
    # We want to keep same flow as otherApps, hence, ESA even if singular is wrapped inside arrays.
    espresso_server_app_bundle_id = get_dependent_app_bundle_ids(params["espresso_server_app"]).first
    (app_bundle_ids << espresso_server_app_bundle_id).flatten!
  end

  skip_tunnel_setup = false
  if params["detox_app_client"]
    skip_tunnel_setup = true if params["networkLogs"].nil? || params["networkLogs"].to_s.downcase == "false"
    detox_helper = DetoxHelper.new(@device, default_port)
    # We want to keep same flow as otherApps, ESA, hence, Detox App Client even though singular is wrapped
    # inside arrays.
    detox_app_client_bundle_id = get_dependent_app_bundle_ids(params["detox_app_client"]).first
    (app_bundle_ids << detox_app_client_bundle_id).flatten!
  end

  mark_event_end('app_bundle_ids', params[:event_hash])

  params["packages"] = FormatPackagesParam.create_package_params(app_bundle_ids)
  WriteSessionInfo.new(BrowserStack.logger, @device, params).save
  dedicated_cloud_session = params['is_dedicated_cloud_session'].to_s == "true"
  dedicated_minified_cleanup = dedicated_cloud_session && params['dedicated_minified_cleanup'].to_s == "true"

  params["is_minimized_flow"] = is_minimized_flow = false
  minimized_cleanup_reserved_file = "#{STATE_FILES_DIR}/minimized_cleanup_reserved_#{@device}"
  if dedicated_minified_cleanup
    minimized_cleanup_reserved_file = "#{STATE_FILES_DIR}/dedicated_minimized_cleanup_reserved_#{@device}"
  end
  minimized_cleanup_unreserved_file = "#{STATE_FILES_DIR}/minimized_cleanup_unreserved_#{@device}"

  if params["reserveDevice"].to_s == "true" || dedicated_minified_cleanup
    dirname = File.dirname(minimized_cleanup_reserved_file)
    FileUtils.mkdir_p(dirname) unless File.directory?(dirname)
    params["is_minimized_flow"] = is_minimized_flow = File.exist?(minimized_cleanup_reserved_file)
    # minimized_cleanup_reserved_file should not be touched everytime, as the last modified time of this file
    # is used to identify if the reserved session flow duration is less than RESERVED_FLOW_SESSION_MAX_TIME_IN_MINUTES
    FileUtils.touch(minimized_cleanup_reserved_file) unless File.exist?(minimized_cleanup_reserved_file)
  end
  minimized_cleanup_reserved_file_present = File.exist?(minimized_cleanup_reserved_file)
  BrowserStack.logger.info("[FIRECMD] is_minimized_flow : #{is_minimized_flow}, "\
                           "minimized_cleanup_reserved_file_present : #{minimized_cleanup_reserved_file_present}")

  enable_async_main_app_install = is_async_install_required?(params, is_minimized_flow, is_app_testing)
  if enable_async_main_app_install
    params["enable_async_main_app_install"] = enable_async_main_app_install
    BrowserStack.logger.info("[FIRECMD] Starting a new thread to download and install App.")

    download_and_install_thread = Thread.bs_run do
      install_app_firecmd(params, sess_details, {
        is_apks_archive: is_apks_archive,
        bundle_id: bundle_id,
        enable_apksigner: enable_apksigner,
        install_experiment_enabled: install_experiment_enabled,
        is_minimized_flow: is_minimized_flow,
        skip_resign: skip_resign,
        subregion_app_caching_enabled: subregion_app_caching_enabled,
        is_app_testing: is_app_testing
      })
    end
  end

  mark_event_start('mock_battery_properties', params[:event_hash])
  unless is_minimized_flow
    battery_helper = BrowserStack::BatteryHelper.new(@device, device_version, BrowserStack.logger) # MOB-7071
    battery_helper.mock_property('status', 3) # Sets battery state to 'not charging'
  end
  mark_event_end('mock_battery_properties', params[:event_hash])
  browser_name = begin
    params["browserName"].downcase
  rescue StandardError
    nil
  end
  @os_version = Gem::Version.new(device_version)
  if !is_app_testing && @os_version >= Gem::Version.new(7) && @os_version < Gem::Version.new(10) &&
     browser_name && !['chrome_android', 'android', 'chrome'].include?(browser_name)
    begin
      enable_requested_browser(browser_name, @device, @os_version.to_s, params["automate_session_id"], params["genre"])
    rescue BrowserHelperError => e
      BrowserStack.logger.error(e.message.to_s)
      return 500, "failed to enable requested browser #{browser_name}"
    end
  end
  mark_event_start('local_tunnel_setup', params[:event_hash])
  automate_funnel.mark_block_start('LocalTunnelSetup')
  # We don't want to start privoxy for detox sessions.
  # Thus, detox_app_client_bundle_id param is used to verify existence of detox session
  insecure_ws_server_device_ips = nil
  if is_app_testing
    insecure_ws_server_device_ips = [
      @devices_json[@device]["mobile_ip"],
      calc_usb_tunnel_ip(default_port)
    ]
  end

  allow_device_mock_server = false
  allow_device_mock_server = true if params["allow_device_mock_server"].to_s.downcase == "true"

  tunnel_setup(params, insecure_ws_server_device_ips) if  !hosts.nil? &&
                                                          !hosts.empty? &&
                                                          !allow_device_mock_server &&
                                                          !skip_tunnel_setup

  automate_funnel.mark_block_end('LocalTunnelSetup', 'success')
  mark_event_end('local_tunnel_setup', params[:event_hash])

  if is_app_testing
    sess_details[:packageName] = bundle_id
    sess_details[:other_app_bundle_ids] = other_app_bundle_ids if params["other_apps"]
    sess_details[:mid_session_app_bundle_ids] = mid_session_app_bundle_ids if params["mid_session_install_apps"]
    # TODO: This can be removed because espresso_server_app_bundle_id is added to duplicate session file
    # using testPackageName key below and that is used to fetch pid in device-logger repo for adding logs
    # related to that bundle_id in device logs
    sess_details[:espresso_server_app_bundle_id] = espresso_server_app_bundle_id if params["espresso_server_app"]
  end

  # Duplicate session file is used during cleanup to get  info on the previously run session.
  # The session file created in the call to WriteSessionInfo.new is deleted before cleanup.
  mark_event_start('write_duplicate_session_params', params[:event_hash])
  File.open("/tmp/duplicate_session_#{@device}", 'w') { |f| f.write(sess_details.to_json) }
  mark_event_end('write_duplicate_session_params', params[:event_hash])

  enable_automate_devicelogs = true if params["genre"] == "automate"

  # we push media on the device and try to insert it into Android mediastore
  # from here on there is a race condition between firecmd and mediastore insertion
  # if mediastore insertion does not complete by the end of this function, firecmd error will be thrown
  # now it will be used fro automate / app automate
  custom_media_manager = CustomMediaManager.new(
    @device, @feature_usage, SERVER_LOG, BrowserStack.logger, BrowserStack.logger.params
  )

  if is_minimized_flow
    params.delete("custom_media")
    BrowserStack.logger.info("Removing custom_media from params as we are in MINIFIED FLOW")
  end

  if params["custom_media"]
    update_feature_usage("customMedia", true)
    mark_event_start('custom_media_download_and_push_time', params[:event_hash])
    begin
      custom_media_manager.download_and_push(params)
    rescue FireCMDException => e
      BrowserStack.logger.error("[FIRE_CMD_ERROR] CustomMediaManager download and push failed: #{e.message} "\
                                "backtrace: #{e.backtrace.join('\n')}.")
      update_feature_usage("customMedia", false, "custom media download and push failed")
      ensure_thread_killed(download_and_install_thread)
      return 500, { error: e.message.to_s,
                    kind: "custom_media_download_update_failed",
                    type: e.type.to_s }.to_json
    ensure
      mark_event_end('custom_media_download_and_push_time', params[:event_hash])
    end
  end

  if DeviceSIMHelper.public_sim?(@device)
    mark_event_start('blockSimPages', params[:event_hash])
    begin
      BrowserStack.logger.info "Block SIM pages initialised for deviceId: #{@device}, sessionId: #{session_id}"
      sim_helper = DeviceSIMHelper.new(params)
      result = sim_helper.block_sim_pages
      BrowserStack.logger.info "Block SIM pages completed for deviceId: #{@device}, sessionId: #{session_id}"
    rescue StandardError => e
      BrowserStack.logger.error "Block SIM pages failed for deviceId: #{@device}, "\
      "sessionId: #{session_id} with error: #{e.message} #{e.backtrace.join("\n")}"

      return 500, { error: e.message.to_s,
                    kind: "block_sim_pages_failed",
                    type: BROWSERSTACK_ERROR_STRING }.to_json
    ensure
      mark_event_end('blockSimPages', params[:event_hash])
    end
  end

  if params["enableSim"].to_s == "true"
    mark_event_start('enableSim', params[:event_hash])
    begin
      BrowserStack.logger.info "Android Sim setup Initialised for deviceId: #{@device}, sessionId: #{session_id}"
      sim_helper = DeviceSIMHelper.new(params)
      result = sim_helper.setup
      BrowserStack.logger.info "Android Sim setup completed for deviceId: #{@device}, sessionId: #{session_id}"
      update_feature_usage("enableSim", true)
    rescue StandardError => e
      BrowserStack.logger.error(
        "Android Sim setup failed for deviceId: #{@device}, sessionId: #{session_id} "\
        "with error : #{e.message} #{e.backtrace.join("\n")}"
      )
      error_message = "[BROWSERSTACK_ANDROID_SIM_SETUP_FAILED] Could not start the session "\
      "with sim capability due to an internal issue/error. For more details, "\
      "please reach out to support. "
      update_feature_usage("enableSim", false, error_message[0, 100])

      ensure_thread_killed(download_and_install_thread)
      return 500, { error: error_message, kind: "android_sim_setup_failed",
                    type: BROWSERSTACK_ERROR_STRING }.to_json
    ensure
      mark_event_end('enableSim', params[:event_hash])
    end
  end

  if is_app_testing
    internet_over_usb = params["internet_over_usb"] = params["app_automate_custom_params"]["internet_over_usb"].to_s
    params[:limit_network_usage] = params["app_automate_custom_params"]["limit_network_usage"].to_s
    # more info at https://browserstack.atlassian.net/browse/MOB-9373
    internet_over_usb = "true" if params["user_id"] && params["user_id"].to_s == "3635234"
    params[:use_scrcpy_for_video_recording] = params["app_automate_custom_params"]["use_scrcpy_for_video_recording"]

    mark_event_start('set_prop', params[:event_hash])
    set_user_specified_prop(params["setprop"], params["automate_session_id"]) if params["setprop"] && !is_minimized_flow
    mark_event_end('set_prop', params[:event_hash])

    device_obj = BrowserStack::AndroidDevice.new(@device, "Server.rb", BrowserStack.logger)

    if params["disableAnimations"].to_s == "true" && !is_minimized_flow
      mark_event_start('disable_animations', params[:event_hash])
      success = disable_device_animations(@device)
      update_feature_usage("disableAnimations", success)
      mark_event_end('disable_animations', params[:event_hash])
    end

    if params["chooserIntentSupport"].to_s == "true" && !is_minimized_flow
      mark_event_start('touch_app_injection_state_file', params[:event_hash])
      AppInjection.touch_app_injection_state_file(@device, ENABLE_CHOOSER_INTENT_SUPPORT_FILE)
      mark_event_end('touch_app_injection_state_file', params[:event_hash])
    end

    # Add code to push state file based on caps related to injection
    if params["enableCameraImageInjection"].to_s == "true"
      mark_event_start('touch_camera_injection_state_file', params[:event_hash])
      AppInjection.touch_app_injection_state_file(@device, ENABLE_CAMERA_FRIDA)
      mark_event_end('touch_camera_injection_state_file', params[:event_hash])
    end

    if params["enableCameraVideoInjection"].to_s == "true"
      mark_event_start('touch_video_injection_state_file', params[:event_hash])
      AppInjection.touch_app_injection_state_file(@device, ENABLE_VIDEO_INJECTION_FILE)
      mark_event_end('touch_video_injection_state_file', params[:event_hash])
    end

    if params["enableBiometric"].to_s == "true"
      mark_event_start('touch_biometric_injection_state_file', params[:event_hash])
      AppInjection.touch_app_injection_state_file(@device, ENABLE_BIOMETRIC_FRIDA)
      useOldBiometricFailureFlow = params["app_automate_custom_params"] &&
        params["app_automate_custom_params"]["useOldBiometricFailureFlow"].to_s == "true"
      disableNewBiometricFailureFlow = params["app_automate_custom_params"] &&
        params["app_automate_custom_params"]["disableNewBiometricFailureFlow"].to_s == "true"
      unless useOldBiometricFailureFlow || disableNewBiometricFailureFlow
        AppInjection.touch_app_injection_state_file(@device,
                                                    ENABLE_MULTIPLE_BIOMETRIC_FAILURES)
      end
      mark_event_end('touch_biometric_injection_state_file', params[:event_hash])
    end

    if is_true?(params["networkLogsPatch"])
      mark_event_start('touch_network_log_state_file', params[:event_hash])
      BrowserStack.logger.info("Creating state file on device #{device} for network logs patch")
      AppInjection.touch_app_injection_state_file(@device, ENABLE_NETWORK_LOGS_FRIDA)
      mark_event_end('touch_network_log_state_file', params[:event_hash])
    end

    if AppInjection.app_patching_enabled_in_app_automate?(params)
      # Adding a log file for logging app patch logs
      mark_event_start('add_patch_log_file', params[:event_hash])
      AppInjection.add_patch_log_file(@device, session_id)
      mark_event_end('add_patch_log_file', params[:event_hash])
    end
    if params["s3_app_url"] && !enable_async_main_app_install && !pre_install_app?(params)
      begin
        install_app_firecmd(params, sess_details, {
          is_apks_archive: is_apks_archive,
          bundle_id: bundle_id,
          enable_apksigner: enable_apksigner,
          install_experiment_enabled: install_experiment_enabled,
          is_minimized_flow: is_minimized_flow,
          skip_resign: skip_resign,
          subregion_app_caching_enabled: subregion_app_caching_enabled,
          is_app_testing: is_app_testing
        })
      rescue FireCMDException => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] App download and install failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        kind = e.kind.nil? ? "#{error_kind}_download_install_failure" : e.kind
        return 500, { error: e.message.to_s, kind: kind, type: e.type.to_s }.to_json
      rescue Exception => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] App download and install failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        return 500, { error: e.message.to_s,
                      kind: "#{error_kind}_download_install_failure",
                      type: BROWSERSTACK_ERROR_STRING }.to_json
      end
    end

    if espresso_server_app_bundle_id
      begin
        t1 = Time.now.to_i
        mark_event_start('espresso_download_and_install_app', params[:event_hash])
        install_dependent_apps(
          params["espresso_server_app"], params["automate_session_id"], enable_apksigner, params, "test"
        )
        mark_event_end('espresso_download_and_install_app', params[:event_hash])
        BrowserStack.logger.info "[App Automate Espresso Server] Espresso Server Download and "\
          "install time: #{Time.now.to_i - t1}"

        # The below keys will prevent ESA to get installed in app upgrade flow.
        sess_details["downloaded_other_apps_details"] = params["downloaded_other_apps_details"]
        sess_details["installed_other_apps_details"] = params["installed_other_apps_details"]
        sess_details["testPackageName"] = espresso_server_app_bundle_id
        File.open("/tmp/duplicate_session_#{@device}", 'w') { |f| f.write(sess_details.to_json) }
      rescue FireCMDException => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] Espresso Server download and install failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        kind = e.kind.nil? ? "espresso_app_download_install_failure" : e.kind
        ensure_thread_killed(download_and_install_thread)
        return 500, { error: e.message.to_s, kind: kind, type: e.type.to_s }.to_json
      rescue StandardError => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] Espresso Server download and install failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        ensure_thread_killed(download_and_install_thread)
        return 500, { error: e.message.to_s,
                      kind: "espresso_app_download_install_failure",
                      type: BROWSERSTACK_ERROR_STRING }.to_json
      end
    end

    if detox_app_client_bundle_id
      begin
        t1 = Time.now.to_i
        mark_event_start('dac_download_and_install_app', params[:event_hash])
        install_dependent_apps(
          params["detox_app_client"], params["automate_session_id"], enable_apksigner, params, "test"
        )
        mark_event_end('dac_download_and_install_app', params[:event_hash])
        BrowserStack.logger.info "[App Automate Detox App Client] Detox App Client Download and "\
          "install time: #{Time.now.to_i - t1}"

        # The below keys will prevent DAC to get installed in app upgrade flow.
        sess_details["downloaded_other_apps_details"] = params["downloaded_other_apps_details"]
        sess_details["installed_other_apps_details"] = params["installed_other_apps_details"]
        sess_details["testPackageName"] = detox_app_client_bundle_id
        File.open("/tmp/duplicate_session_#{@device}", 'w') { |f| f.write(sess_details.to_json) }
      rescue FireCMDException => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] Detox App Client download and install failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        kind = e.kind.nil? ? "dac_download_and_install_app" : e.kind
        ensure_thread_killed(download_and_install_thread)
        return 500, { error: e.message.to_s, kind: kind, type: e.type.to_s }.to_json
      rescue StandardError => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] Detox App Client download and install failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        ensure_thread_killed(download_and_install_thread)
        return 500, { error: e.message.to_s,
                      kind: "dac_download_and_install_app",
                      type: BROWSERSTACK_ERROR_STRING }.to_json
      end
    end

    if other_app_bundle_ids
      begin
        mark_event_start('other_apps_download_and_install_time', params[:event_hash])
        install_dependent_apps(params["other_apps"], params["automate_session_id"], enable_apksigner, params)
        mark_event_end('other_apps_download_and_install_time', params[:event_hash])
        sess_details["downloaded_other_apps_details"] = params["downloaded_other_apps_details"]
        sess_details["installed_other_apps_details"] = params["installed_other_apps_details"]
        File.open("/tmp/duplicate_session_#{@device}", 'w') { |f| f.write(sess_details.to_json) }
        update_feature_usage("otherApps", true)
      rescue FireCMDException => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] Other Apps download and install failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        kind = e.kind.nil? ? "other_apps_install_failure" : e.kind
        update_feature_usage("otherApps", false, e.message[0, 100])
        ensure_thread_killed(download_and_install_thread)
        return 500, { error: e.message.to_s, kind: kind, type: e.type.to_s }.to_json
      rescue Exception => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] Other Apps download and install failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        update_feature_usage("otherApps", false, e.message[0, 100])
        ensure_thread_killed(download_and_install_thread)
        return 500, { error: e.message.to_s,
                      kind: "other_apps_install_failure",
                      type: BROWSERSTACK_ERROR_STRING }.to_json
      end
    end

    if mid_session_app_bundle_ids
      # These apps need to be treated as otherApps but not installed in firecmd
      begin
        mark_event_start('mid_session_app_download_time', params[:event_hash])
        install_dependent_apps(params["mid_session_install_apps"], params["automate_session_id"],
                               enable_apksigner, params, "mid_session_install_apps")
        sess_details["downloaded_other_apps_details"] = params["downloaded_other_apps_details"]
        File.open("/tmp/duplicate_session_#{@device}", 'w') { |f| f.write(sess_details.to_json) }
        update_feature_usage("midSessionInstallApps", true)
        mark_event_end('mid_session_app_download_time', params[:event_hash])
      rescue FireCMDException => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] Mid Session Apps download failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        kind = e.kind.nil? ? "mid_session_apps_download_failure" : e.kind
        update_feature_usage("midSessionInstallApps", false, e.message[0, 100])
        ensure_thread_killed(download_and_install_thread)
        return 500, { error: e.message.to_s, kind: kind, type: e.type.to_s }.to_json
      rescue Exception => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] Mid Session Apps download failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        update_feature_usage("midSessionInstallApps", false, e.message[0, 100])
        ensure_thread_killed(download_and_install_thread)
        return 500, { error: e.message.to_s,
                      kind: "mid_session_apps_download_failure",
                      type: BROWSERSTACK_ERROR_STRING }.to_json
      end
    end

    if is_minimized_flow
      BrowserStack.logger.info("Removing app_store_username, app_store_password, language, "\
                               "localization, timezone, latitude, longitude from params as we are in MINIFIED FLOW")
      ["app_store_username", "app_store_password", :language, "language", :localization, "localization",
       "timezone", "latitude", "longitude", :networkSimulation, "networkSimulation"].each do |param_to_remove|
        params.delete(param_to_remove)
      end
      BrowserStack.logger.info("MINIFIED FLOW updated params : #{params}")
    end

    t1 = Time.now.to_i
    devicelogs_enabled = params["deviceLogs"].to_s == "true"

    copy_file("/tmp/duplicate_session_#{@device}", "#{STATE_FILES_DIR}/session_#{@device}")

    mark_event_start('device_logger_start', params[:event_hash])
    device_logger_helper.start if devicelogs_enabled
    mark_event_end('device_logger_start', params[:event_hash])

    BrowserStack.logger.info "[App Automate] device log enable: #{Time.now.to_i - t1}"

    mark_event_start('chrome_version_check', params[:event_hash])
    cmd = "adb -s #{@device} shell dumpsys package com.android.chrome | grep versionName >> #{SERVER_LOG}"
    spawn_process(cmd)
    mark_event_end('chrome_version_check', params[:event_hash])

    is_local_update = hosts.to_s != ""
    t1 = Time.now.to_i
    tun_counter = default_port.to_i - 8078
    tun_ip = calc_usb_tunnel_ip(default_port)
    begin
      vpn_disabled = false
      apps_string = ''
      mark_event_start('custom_vpn_tunnel_setup_time', params[:event_hash])

      if is_local_update && internet_over_usb != "true" && !allow_device_mock_server
        vpn_disabled = true
        BrowserStack.logger.info "Stopping VPN for App Automate Session because local is set to true and "\
                                 "internet_over_usb is not enabled"
        if device_obj.uses_bstack_internet_app?
          controller = BStackReverseTetherController.new(device_obj, BrowserStack.logger)
          controller.stop
        else
          script_logger = script_logger_args("#{File.basename(SCRIPT)}_stop_vpn")
          cmd = "bash #{SCRIPT} stop_vpn #{@device} #{tun_counter} 2>&1 | "\
            "while read line; do echo #{script_logger} \"$line\" >> /var/log/browserstack/selenium_#{@device}.log; done"
          system(cmd)
        end
      elsif !is_minimized_flow
        # NOTE: going ahead if we enable APP internet over usb for AL
        # we can combine these two file
        if internet_over_usb == "true"
          # touching this file, to reset vpn in cleanup
          # currently done only for AA
          system("touch /tmp/app_internet_via_usb_#{@device}")
        end
        if device_obj.uses_bstack_internet_app?

          BrowserStack.logger.info "Doing nothing, tunnel should already exist"
        else
          BrowserStack.logger.info "Doing redovpn"
          script_logger = script_logger_args("#{File.basename(SCRIPT)}_redovpn")
          apps_string = app_bundle_ids.join("/")

          cmd = "bash #{SCRIPT} redovpn #{@device} #{tun_ip} #{apps_string} 2>&1 | "\
            "while read line; do echo #{script_logger} \"$line\" >> /var/log/browserstack/selenium_#{@device}.log; done"
          system(cmd)
        end

         # touching this file, to reset network simulation over usb
         # currently done for both AA and AL
        system("touch /tmp/internet_via_usb_#{@device}")
      end

      check_and_log_usb_internet(params, vpn_disabled)
    rescue Exception => e
      BrowserStack.logger.error("[FIRE_CMD_ERROR] Local Setup Failed: #{e.message} "\
                                "backtrace: #{e.backtrace.join('\n')}")
      ensure_thread_killed(download_and_install_thread)
      return 500, { error: "Could not start a session : Something went wrong with local testing. "\
                              "Please try to run the test again.",
                    kind: "local_setup_failed", type: BROWSERSTACK_ERROR_STRING }.to_json
    ensure
      mark_event_end('custom_vpn_tunnel_setup_time', params[:event_hash])
    end
    BrowserStack.logger.info "[App Automate] Tunnel time: #{Time.now.to_i - t1}"

    begin
      # perform_google_login step should always be done before sel_cmd since we set the orientation in sel_cmd
      # and this step reverts the orientation back to portrait
      perform_google_login(params, device_name, device_version)
    rescue GoogleLoginException => e
      zombie_key_value(
        platform: 'android',
        kind: 'google-login-failure-screen-state',
        browser: @device_name,
        data: device_obj.screen_on?,
        device: @device.to_s,
        session: session_id
      )
      adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
      adb.shell("screencap -p /sdcard/screenshot.png; exit")
      adb.pull("/sdcard/screenshot.png", "/tmp/snapshot_google_login_exception_#{session_id}_#{@device}.png")
      adb.shell("rm /sdcard/screenshot.png")
      BrowserStack.logger.error("[FIRE_CMD_ERROR] Google login failed: #{e.message} "\
                                "backtrace: #{e.backtrace.join('\n')}.")
      ensure_thread_killed(download_and_install_thread)
      return 500, { error: e.message.to_s, kind: e.kind, type: e.type.to_s }.to_json
    rescue Exception => e
      update_feature_usage("playstoreLogin", false, e.message[0, 100])
      ensure_thread_killed(download_and_install_thread)
      return 500, { error: e.message.to_s, kind: "google_login_failure",
                    type: BROWSERSTACK_ERROR_STRING }.to_json
    end

    # Start capturing crash logs for android
    mark_event_start('app_crash_logs_start', params[:event_hash])
    crash_logs_helper = CrashLogsHelper.new(params)
    crash_logs_helper.clear_crash_logs_buffer
    crash_logs_helper.start_crash_logs_capture
    mark_event_end('app_crash_logs_start', params[:event_hash])
  end

  if params["enableAudioInjection"].to_s == "true"
    mark_event_start('enableAudioInjection', params[:event_hash])
    begin
      AudioInjector.log_info("Audio Injection setup Initialised for deviceId: #{@device}, sessionId: #{session_id}")
      AudioInjector.touch_state_file(@device)
      product = is_app_testing ? "app_automate" : "automate"
      AudioInjector.setup(@device, session_id, product, send_to_bq: true)
      AudioInjector.log_info("Audio Injection setup completed for deviceId: #{@device}, sessionId: #{session_id}")
      update_feature_usage("enableAudioInjection", true)
    rescue StandardError => e
      AudioInjector.log_error(
        "Audio Injection setup failed for deviceId: #{@device}, sessionId: #{session_id} "\
        "with error : #{e.message} #{e.backtrace.join("\n")}"
      )
      error_message = "[BROWSERSTACK_AUDIO_INJECTION_SETUP_FAILED] Could not start the session "\
      "with audio injection capability due to an internal issue/error. For more details, "\
      "please reach out to support. "
      update_feature_usage("enableAudioInjection", false, error_message[0, 100])

      ensure_thread_killed(download_and_install_thread)
      return 500, { error: error_message, kind: "audio_injection_setup_failed",
                    type: BROWSERSTACK_ERROR_STRING }.to_json
    ensure
      mark_event_end('enableAudioInjection', params[:event_hash])
    end
  end

  is_ai_enabled = false
  is_testbed_collection_enabled = false
  begin
    unless params['ai_enabled_session'].nil?
      ai_details = JSON.parse(params['ai_enabled_session'])
      is_ai_enabled = ai_details['enabled'] == true
      is_testbed_collection_enabled = !ai_details['keyObject'].nil? \
                                      && ai_details['keyObject']['testbedCollection'] == true
    end
  rescue JSON::ParserError => e
    BrowserStack.logger.info "Some error happened in enabling AI. Exception details: #{e.message}"
    is_ai_enabled = false
    is_testbed_collection_enabled = false
  end

  mark_event_start('enable_android_watcher', params[:event_hash])
  enable_android_watcher = params&.[]("app_automate_custom_params")&.[]("enable_android_watcher")&.to_s == "true"
  unless enable_android_watcher
    browserstack_watcher_manager = WatcherHelper.new(@device, session_id, params[:genre], BrowserStack.logger)
    browserstack_watcher_manager.stop
  end

  if is_app_testing && is_testbed_collection_enabled && enable_android_watcher
    browserstack_watcher_manager = WatcherHelper.new(@device, session_id, params[:genre], BrowserStack.logger)
    browserstack_watcher_manager.add_flags_for_testbed_collection(bundle_id)
  end
  mark_event_end('enable_android_watcher', params[:event_hash])
  # For automate VPN is never disabled, hence vpn_disabled arg is false
  check_and_log_usb_internet(params, false) unless is_app_testing

  # This code block needs to be placed before sel_command because this involves an activity/new window launch.
  # If its put after sel_cmd, it consumes the [device]orientation appium settings set in sel command and the
  # main app is launched in default portrait.
  language_or_locale = params[:language] || params[:localization]
  if language_or_locale && params[:use_rtc_app] != "v2"
    mark_event_start('locale_language', params[:event_hash])
    BrowserStack.logger.info "Setting device language to #{language_or_locale}"
    success = set_device_locale(@device, language_or_locale, BrowserStack.logger, { async: false })
    params[:language] ? update_feature_usage("language", success) : update_feature_usage("locale", success)
    mark_event_end('locale_language', params[:event_hash])
  end

  if STOP_UIAUTOMATOR_PORT_FORWARD_APPIUM_VERSION.include? appium_version.to_s
    mark_event_start('remove_port_forwarding', params[:event_hash])
    ui_automator_server_port = get_ui_automator_server_port(appium_port.to_i)
    remove_forwarded_port(ui_automator_server_port, "uiautomator")
    BrowserStack.logger.debug "#{session_id} Stopped uiautomator port forward for appium #{appium_version} at "\
                              "port #{ui_automator_server_port}"
    mark_event_end('remove_port_forwarding', params[:event_hash])
  end

  #starting streaming and interaction here because in case of AA redovpn causes websocket disconnection.
  if params[:webrtc_session_id] && params["network_airplane_mode"].to_s != "true" &&
     params["network_wifi"].to_s != "false"
    params[:debugger_port] = @devices_json[@device]["debugger_port"]
    mark_event_start('streaming_and_interaction', params[:event_hash])
    automate_funnel.mark_block_start("StreamingAndInteraction")
    start_streaming_and_interaction(params)
    automate_funnel.mark_block_end("StreamingAndInteraction")
    mark_event_end('streaming_and_interaction', params[:event_hash])
  end
  mark_event_start('selenium_script.total', params[:event_hash])

  enable_contacts = (
    if params["app_automate_custom_params"] \
    && params["app_automate_custom_params"]["enable_contacts_app_access"] == true
      true
    else
      false
    end
  )
  BrowserStack.logger.info("Enable contacts app = #{enable_contacts}")
  automate_funnel.mark_block_start('SeleniumScript')
  target_script = if !params['parallel_approach'].nil? && params['parallel_approach'] == 'true'
                    SELENIUM_SCRIPT_PARALLEL
                  else
                    SELENIUM_SCRIPT
                  end

  if is_ai_enabled
    begin
      ai_proxy_port = appium_port.to_i + AI_PROXY_PORT_OFFSET
      BrowserStack.logger.info "Starting AI Proxy"
      ai_proxy = AIProxy.new(params, ai_proxy_port, appium_port, @device)
      is_ai_proxy_started = ai_proxy.start_ai_proxy
      cmd = "bash #{SCRIPT} ensure_screen_is_unlocked #{@device}"
      system(cmd)
    rescue StandardError => e
      BrowserStack.logger.info "Some error happened in starting AI Proxy. Exception details: #{e.inspect}"
    end
  end
  is_playwright_android = !params['is_playwright'].nil? && params['is_playwright'] == "true"
  if is_playwright_android
    playwright_port = appium_port.to_i + PLAYWRIGHT_ANDROID_PORT_OFFSET
    cdp_port = appium_port.to_i + CDP_PORT_OFFSET
    ret_val, is_cdp_proxy_started, playwright_url = playwright_command(
      params,
      cdp_port,
      playwright_port,
      @device,
      device_orientation
    )
  else
    # Redirecting STDERR to STDOUT results in the below script to not return
    start_mode = is_minimized_flow ? "minified_start" : "start"
    sel_cmd = "bash #{target_script} #{start_mode} \"#{@device}\" \"#{selenium_debug_port}\" \"#{stream_width}\" "\
              "\"#{device_orientation}\" \"#{appium_version}\" \"#{appium_port}\" \"#{chrome_driver_port}\" "\
              "\"#{android_bootstrap_port}\" \"#{timezone}\" \"#{is_app_testing}\" \"#{bundle_id}\" "\
              "\"#{devicelogs_enabled}\" \"#{session_id}\" \"#{chrome_driver_version}\" \"#{enable_contacts}\" "\
              "\"#{automationName}\" \"#{automation_version}\""
    BrowserStack.logger.info "Selenium Command: #{sel_cmd}"
    ret_val = system(sel_cmd)
  end
  unless ret_val
    @hooter.send(@device_name, "firecmd_error")
    if is_ai_enabled
      BrowserStack.logger.error("[FIRE_CMD_ERROR] Start Failed: non-zero exit status for "\
      "AI Proxy. Returning.")
      automate_funnel.mark_block_end('SeleniumScript', 'failure', 'ai_proxy_startup_failure')
      automate_funnel.mark_breakup_end
      automate_funnel.mark_block_end('AndroidFireCMD', 'failure', 'ai_proxy_startup_failure')
      response = { :error => "Could not start a session : Something went wrong with starting AI Proxy server. "\
                             "Please try to run the test again.",
                   :kind => "ai_proxy_startup_failure",
                   :type => "browserstack_error", 'funnel_data' => automate_funnel.generate_data_json }.to_json

      ensure_thread_killed(download_and_install_thread)
      return response unless is_ai_proxy_started
    end

    if is_playwright_android
      BrowserStack.logger.error("[FIRE_CMD_ERROR] Start Failed: non-zero exit status for "\
      "playwright_command. Returning.")
      automate_funnel.mark_block_end('SeleniumScript', 'failure', 'playwright_startup_failure')
      automate_funnel.mark_breakup_end
      automate_funnel.mark_block_end('AndroidFireCMD', 'failure', 'playwright_startup_failure')
      response = { :error => "Could not start a session : Something went wrong with starting Playwright server. "\
                             "Please try to run the test again.",
                   :kind => "playwright_startup_failure",
                   :type => "browserstack_error", 'funnel_data' => automate_funnel.generate_data_json }.to_json

      return response if playwright_url.nil?

      response = { :error => "Could not start a session : Something went wrong with starting CDP Proxy server. "\
                             "Please try to run the test again.",
                   :kind => "cdp_startup_failure",
                   :type => "browserstack_error",
                   'funnel_data' => automate_funnel.generate_data_json }.to_json
      return response unless is_cdp_proxy_started
    end

    BrowserStack.logger.error("[FIRE_CMD_ERROR] Start Failed: non-zero exit status for "\
                              "create_appium_service. Returning.")
    automate_funnel.mark_block_end('SeleniumScript', 'failure', 'appium_startup_failure')
    automate_funnel.mark_breakup_end
    automate_funnel.mark_block_end('AndroidFireCMD', 'failure', 'appium_startup_failure')
    ensure_thread_killed(download_and_install_thread)
    return { :error => "Could not start a session : Something went wrong with Appium server. "\
                       "Please try to run the test again.",
             :kind => "appium_startup_failure",
             :type => "browserstack_error", 'funnel_data' => automate_funnel.generate_data_json }.to_json
  end
  automate_funnel.mark_block_end('SeleniumScript', 'success')
  mark_event_end('selenium_script.total', params[:event_hash])

  response_hash["playwright_url"] = playwright_url unless playwright_url.nil?
  BrowserStack.logger.info "playwright_command: ret_val #{ret_val}, playwright_url #{playwright_url}, "\
  "is_cdp_proxy_started #{is_cdp_proxy_started}, response_hash #{response_hash.inspect}, "\
  "session_id #{params['automate_session_id']}"

  if params["browserName"].to_s.casecmp("samsung").zero?
    mark_event_start('samsung_cmd_line', params[:event_hash])
    add_samsung_command_line_file(params, default_port, @device)
    mark_event_end('samsung_cmd_line', params[:event_hash])
  end

  if params["timezone"]
    # timezone is set in sel_cmd
    mark_event_start('timezone', params[:event_hash])
    device_timezone = get_timezone(@device)
    BrowserStack.logger.info "[App Automate] timezone: #{timezone} device_timezone: #{device_timezone}"
    success = (timezone == device_timezone)
    update_feature_usage("timezone", success)
    mark_event_end('timezone', params[:event_hash])
  end

  if params["orientation"]
    # orientation is set in sel_cmd
    mark_event_start('orientation', params[:event_hash])
    current_orientation = get_orientation_from_settings(@device)
    success = (current_orientation == device_orientation.to_s.downcase)
    update_feature_usage("orientation", success)
    mark_event_end('orientation', params[:event_hash])
  end

  # Calling this after appium is switched so the io.appium.settings app of that appium version is used
  latitude = params["latitude"]
  longitude = params["longitude"]
  mark_event_start('set_gpslocation', params[:event_hash])
  if device_name.downcase.include?("pixel 6 pro") && device_version.to_i >= 15
    begin
      android_toolkit_adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
      # Fix for: https://browserstack.atlassian.net/browse/MOBPL-6093
      broadcast_result = android_toolkit_adb.shell(
        "am broadcast -a io.appium.settings.location " \
        "-n io.appium.settings/.receivers.LocationInfoReceiver --ez forceUpdate true",
        timeout: 30
      )
      BrowserStack.logger.info("LocationInfoReceiver Broadcast result: #{broadcast_result}")
      if broadcast_result.include?("result=0")
        latitude ||= "0"
        longitude ||= "0"
      end
    rescue AndroidToolkit::ADB::ExecutionError => e
      BrowserStack.logger.error("Unable to fetch location via io.appium.settings app")
      latitude ||= "0"
      longitude ||= "0"
    end
  end
  set_gpslocation(latitude, longitude, params["genre"],
                  params["automate_session_id"], false, appium_version)
  mark_event_end('set_gpslocation', params[:event_hash])

  mark_event_start('read_instrumentation_logs_from_file', params[:event_hash])
  params[:event_hash].deep_merge! read_instrumentation_logs_from_file(
    "/tmp/#{@device}-instrumentation.log", BrowserStack.logger, "selenium_script"
  )
  mark_event_end('read_instrumentation_logs_from_file', params[:event_hash])

  mark_event_start('write_rtc_file', params[:event_hash])
  write_rtc_params(params)
  mark_event_end('write_rtc_file', params[:event_hash])

  if is_app_testing && !disable_app_profiling?(params)
    mark_event_start('app_profiling_setup', params[:event_hash])
    begin
      PerformanceStatistics.new(@device, BrowserStack.logger).start(session_id)
    rescue StandardError => e
      BrowserStack.logger.error(
        "Performance Statistics Script Failed for deviceId: #{@device}, sessionId: #{session_id} "\
        "with error : #{e.message} #{e.backtrace.join("\n")}"
      )
      update_feature_usage("profiling", false, e.message[0, 100])
    end
    mark_event_end('app_profiling_setup', params[:event_hash])
  end

  if params[:video].to_s == "true"
    mark_event_start('start_video', params[:event_hash])
    automate_funnel.mark_block_start('StartVideo')
    start_video_rec(@device, params)
    video_sync_hash["video_start_time"] = Time.now.to_f
    automate_funnel.mark_block_end('StartVideo', 'success')
    mark_event_end('start_video', params[:event_hash])
  end

  if params[:networkSimulation].to_s == "true" || (params[:limit_network_usage].to_s == "true" && !is_minimized_flow)
    mark_event_start('network_simulation_time', params[:event_hash])
    setup_network_simulation(params, @device, session_id, apps_string)
    mark_event_end('network_simulation_time', params[:event_hash])
  end

  if params["device_preferences"]
    mark_event_start('device_preferences_time', params[:event_hash])
    begin
      setup_device_preferences(params, @device, session_id)
    rescue FireCMDException => e
      mark_event_end('device_preferences_time', params[:event_hash])
      # show adb error only in case adb command errors out (not timeout)
      reason = if e.message.split(":")[-1].strip == "[ADB startup preferences] error"
                 'device_preferences_setup_failed_adb'
               else
                 'device_preferences_setup_failed_internal'
               end
      ensure_thread_killed(download_and_install_thread)
      return 500, { error: e.message.to_s, kind: reason, type: e.type.to_s }.to_json
    end
    mark_event_end('device_preferences_time', params[:event_hash])
  end

  if params["enablePasscode"].to_s == "true" && is_app_testing
    mark_event_start('enablePasscode', params[:event_hash])
    passcode_manager = DevicePasscodeManager.new(@device, BrowserStack.logger, params["automate_session_id"])
    is_passcode_set = passcode_manager.set_passcode
    update_feature_usage("enablePasscode", true)
    error_message = "[Device Passcode] Set Passcode failed"
    unless is_passcode_set
      update_feature_usage("enablePasscode", false, error_message[0, 100])
      ensure_thread_killed(download_and_install_thread)
      return 500, { error: error_message, kind: "set_passcode_failed",
                    type: BROWSERSTACK_ERROR_STRING }.to_json
    end
    mark_event_end('enablePasscode', params[:event_hash])
  end

  if params.key?('customCertificateFile') && is_app_testing
    begin
      mark_event_start('customCertificateFile', params[:event_hash])
      BrowserStack.logger.info("Processing Custom Certificate")
      filetype = nil
      begin
        cert_details = JSON.parse(params['customCertificateFile'])
        filetype = JSON.parse(cert_details)['filetype']
        BrowserStack.logger.info("Extracted filetype: #{filetype}")
      rescue StandardError => e
        BrowserStack.logger.error("Error parsing customCertificateFile: #{e.message}")
      end

      if filetype && ['certificates_cer_ios', 'certificates_cer_android'].include?(filetype)
        custom_cert_manager = CustomCertificate.new(@device, session_id, cert_details, BrowserStack.logger)
        custom_cert_manager.install_ca_cert
        BrowserStack.logger.info("Successfully installed CA certificate")
      else
        # Follow the normal PFX certificate flow
        BrowserStack.logger.info("Installing PFX Certificate")
        install_custom_pfx_certificate(@device, params)
      end
    rescue StandardError => e
      if e.message.include?("Invalid password")
        BrowserStack.logger.info("CustomCertificate: Invalid password or corrupted file #{e.message}")
        ensure_thread_killed(download_and_install_thread)
        return 500, { error: e.message, kind: "certificate_install_invalid_password",
                      type: USER_ERROR_STRING }.to_json
      else
        BrowserStack.logger.error("CustomCertificate: Installation failed #{e.message}")
        ensure_thread_killed(download_and_install_thread)
        return 500, { error: e.message, kind: "browserstack_certificate_install_failure",
                      type: BROWSERSTACK_ERROR_STRING }.to_json
      end
    ensure
      mark_event_end('customCertificateFile', params[:event_hash])
    end
  end

  if download_and_install_thread
    mark_event_start('join_download_and_install_thread', params[:event_hash])
    begin
      BrowserStack.logger.info("[FIRECMD] Joining download_and_install_thread Thread with main thread")
      download_and_install_thread.join
      mark_event_end('join_download_and_install_thread', params[:event_hash])
    rescue FireCMDException => e
      BrowserStack.logger.error("[FIRE_CMD_ERROR] App download and install failed: #{e.message} "\
                                "backtrace: #{e.backtrace.join('\n')}.")
      kind = e.kind.nil? ? "#{error_kind}_download_install_failure" : e.kind
      ensure_thread_killed(download_and_install_thread)
      return 500, { error: e.message.to_s, kind: kind, type: e.type.to_s }.to_json

    rescue Exception => e
      BrowserStack.logger.error("[FIRE_CMD_ERROR] App download and install failed: #{e.message} "\
                                "backtrace: #{e.backtrace.join('\n')}.")
      ensure_thread_killed(download_and_install_thread)
      return 500, { error: e.message.to_s,
                    kind: "#{error_kind}_download_install_failure",
                    type: BROWSERSTACK_ERROR_STRING }.to_json
    end
  end

  mark_event_start('start_mcspt_session', params[:event_hash])
  if is_app_testing && params["appProfiling"].to_s == "true"
    code, response = MCSPT.start_session_async(
      session_id.to_s, params[:user_id].to_s, @device, bundle_id.to_s, false, nil, app_automate: true
    )
    BrowserStack.logger.info("Started CSPT session")
  end
  mark_event_end('start_mcspt_session', params[:event_hash])

  if params["custom_media"]
    mark_event_start('custom_media_verification', params[:event_hash])
    begin
      retries ||= 0
      custom_media_manager.verify_insertion_to_mediastore(params)
    rescue StandardError => e
      if retries < 5 && !is_app_testing
        retries += 1
        sleep 1
        retry
      else
        BrowserStack.logger.error("[FIRE_CMD_ERROR] CustomMediaManager Verification failed: #{e.message} "\
                                  "backtrace: #{e.backtrace.join('\n')}.")
        update_feature_usage("customMedia", false, "custom media insertion to mediastore failed")
        kind = is_app_testing ? "aa-custom-media-verification-failed" : "aut-custom-media-verification-failed"
        zombie_push('android', kind, e.message.to_s, '', '', @device, session_id)
        return 500, { error: e.message.to_s, kind: kind,
                      type: e.type.to_s }.to_json
      end
    ensure
      FileUtils.rm_rf("/tmp/custom_media_insertion_status_#{@device}.txt", secure: true)
      mark_event_end('custom_media_verification', params[:event_hash])
    end
  end

  if params["enableTransparentMode"].to_s == "true" && is_app_testing
    mark_event_start('enableTransparentMode', params[:event_hash])
    TransparentNetworkMode.enable(device_obj)
    mark_event_end('enableTransparentMode', params[:event_hash])
  end

  # For starting android devicelogs capture, keep the following touch command at the end of fire cmd
  copy_file("/tmp/duplicate_session_#{@device}", "#{STATE_FILES_DIR}/session_#{@device}") if enable_automate_devicelogs
  mark_event_start('device_logger_helper_start', params[:event_hash])
  device_logger_helper.start if (is_app_testing && devicelogs_enabled) || enable_automate_devicelogs
  mark_event_end('device_logger_helper_start', params[:event_hash])
  video_sync_hash["end_firecmd_request"] = Time.now.to_f

  # Following Hash sends some time components to firecmd response to hub, these time values are also shown
  # on UI for AppAutomte dashboard. These values are also pushed to rawLogs.
  session_setup_time = {
    "downloading_app" => params[:event_hash]["app_download_time"] || 0,
    "installing_app" => params[:event_hash]["app_install_time"] || 0,
    "setting_up_appium" => params[:event_hash]["selenium_script.total"] || 0
  }

  if other_app_bundle_ids || mid_session_app_bundle_ids ||
    espresso_server_app_bundle_id || detox_app_client_bundle_id
    session_setup_time["downloading_and_installing_dependent_apps"] =
      params[:event_hash]["other_apps_download_and_install_time"].to_i +
      params[:event_hash]["mid_session_app_download_time"].to_i +
      params[:event_hash]["espresso_download_and_install_app"].to_i +
      params[:event_hash]["dac_download_and_install_app"].to_i
  end

  session_setup_time["setting_up_network_connection"] = params[:event_hash]["local_tunnel_setup"]

  # When reserveDevice param is true, For the sessions where the device got chained,
  # we avoid showing following steps in Text Logs
  if is_minimized_flow
    ["downloading_app", "installing_app", "setting_up_appium", "setting_up_network_connection"].each do |step|
      session_setup_time.delete(step)
    end
  end

  needs_adb_proxy = BrowserStack::ModelDatabase.new(@devices_json[@device]["device_name"],
                                                    @devices_json[@device]["device_version"]).property(:needs_adb_proxy)
  response_hash["adb_proxy_port"] = ADB_PROXY_PORT if needs_adb_proxy

  response_hash.merge!(video_sync_hash)
  response_hash["chromedriverPorts"] = chromedriverPorts
  response_hash["privoxy_port"] = calculate_privoxy_port(default_port)
  response_hash["platform_time_stats"] = session_setup_time

  mark_event_start('check_appium_running', params[:event_hash])
  appium_pid = OSUtils.execute("lsof -t -i :#{appium_port}")
  BrowserStack.logger.info("[selenium_command] appium running with pid : #{appium_pid} on port #{appium_port}")
  mark_event_end('check_appium_running', params[:event_hash])

  if detox_app_client_bundle_id
    begin
      mark_event_start('detox_setup', params[:event_hash])
      detox_helper = DetoxHelper.new(@device, default_port)
      detox_server_port = detox_helper.detox_port
      result = false
      dac_instrumentation_name = get_apps_instrumentation_name(params["detox_app_client"]).first
      result = detox_helper.detox_app_client_installed?(dac_instrumentation_name) if dac_instrumentation_name
      raise FireCMDException, "Detox Android Client not installed" unless result

      pid = detox_helper.start_detox_server
      sess_details['detox_server_pid'] = pid
      sess_details["dac_instrumentation_name"] = dac_instrumentation_name
      File.open("/tmp/duplicate_session_#{@device}", 'w') { |f| f.write(sess_details.to_json) }
    rescue FireCMDException => e
      BrowserStack.logger.error(
        "[FIRE_CMD_ERROR] [APP_AUTOMATE_DETOX_ERROR] Error "\
        "while starting detox server or reading instrumentation message: #{e.message} "\
        "backtrace: #{e.backtrace.join('\n')}."
      )
      kind = e.kind.nil? ? "detox_startup_failure" : e.kind
      return 500, { error: e.message.to_s, kind: kind, type: e.type.to_s }.to_json
    ensure
      mark_event_end('detox_setup', params[:event_hash])
    end
  end

  mark_event_start('monitor_device_logger_metric', params[:event_hash])
  monitor_device_logger_metric(@device, get_session_id_from_params(params))
  mark_event_end('monitor_device_logger_metric', params[:event_hash])

  mark_event_start('start_network_usage_tracker', params[:event_hash])
  start_network_usage_tracker(@device, params['automate_session_id'], params[:genre].to_s)
  mark_event_end('start_network_usage_tracker', params[:event_hash])

  if detox_app_client_bundle_id
    response_hash['detox_server_url'] =
      "ws://127.0.0.1:#{detox_helper.detox_port}"
  end

  if params[:use_rtc_app] == "v2" &&
    params[:webrtc_session_id] &&
    params["network_airplane_mode"].to_s != "true" &&
    params["network_wifi"].to_s != "false"
    mark_event_start('handle_media_projection_popup', params[:event_hash])
    automate_funnel.mark_block_start('PopupHandlerRtcV2InFireCMD')
    begin
      BrowserStack.logger.error "[Automate] Handling popup #{params['automate_session_id']}"
      # handle mp in start only if it's not already provisioned
      unless RTCAppReleaseHelper.new.mp_already_provisioned?(@device)
        handle_automate_media_projection_popup(@device, params, @devices_json[@device]["device_version"])
      end
    rescue Exception => e
      BrowserStack.logger.error "[Automate] Exception while handling media projection popup #{e.message}"
    end
    automate_funnel.mark_block_end('PopupHandlerRtcV2InFireCMD', 'success')
    automate_funnel.mark_breakup_end
    # changing locale for app automate
    # skipped same functionality in ruby code else this media pop-up fails to be accepted
    language_or_locale = params[:language] || params[:localization]
    if language_or_locale
      BrowserStack.logger.info "Setting device language to #{language_or_locale}"
      success = set_device_locale(@device, language_or_locale, BrowserStack.logger, { async: false })
      params[:language] ? update_feature_usage("language", success) : update_feature_usage("locale", success)
    end
    mark_event_end('handle_media_projection_popup', params[:event_hash])
  end

  automate_funnel.mark_block_end('AndroidFireCMD', 'success')
  response_hash['funnel_data'] = automate_funnel.generate_data_json

  if is_app_testing
    key = "firecmd_time"
    kind = EdsConstants::APP_AUTOMATE_TEST_SESSIONS
    send_attrs_to_eds("framework_version", appium_version_caps, params, kind)
    send_attrs_to_eds("tertiary_params", { "is_canary" => canary? }, params, kind)
  else
    key = "total"
    kind = EdsConstants::AUTOMATE_SESSION_TIME_COMPONENTS
  end
  mark_event_end(key, params[:event_hash])
  send_to_eds(params, kind)

  begin
    appium_version_kind = is_app_testing ? "app-appium-version-firecmd-time" : "appium-version-firecmd-time"
    zombie_push("android", appium_version_kind, appium_version, '', params[:event_hash][key], @device,
                params["automate_session_id"])
  rescue Exception => e
    BrowserStack.logger.error "Exception in sending appium version to zombie : #{e.message}"
  end
  if is_app_testing && params['app_automate_custom_params']['force_stop_app'].to_s.downcase == 'true'
    result = force_stop_bundle_id(@device, bundle_id)
    BrowserStack.logger.info("Force stopped #{bundle_id} #{result ? 'success' : 'failed'}")
  end
  response_hash.to_json
rescue Exception => e
  BrowserStack.logger.error(
    "[FIRE_CMD_ERROR] [Unhandled Exception] Error : #{e.message} "\
    "backtrace: #{e.backtrace.join('\n')}."
  )
  ensure_thread_killed(download_and_install_thread)
  [500, { error: e.message.to_s, kind: "unhandled_exception",
          type: BROWSERSTACK_ERROR_STRING }.to_json]
end

get '/selenium_command' do
  BrowserStack.logger.info("Received GET selenium_command request with params: #{params}")
  execute_fire_cmd(params)
end

post '/selenium_command' do
  # Incase no data is present in the POST request body
  return 400 if request.body.nil?

  request_body_str = request.body.read
  return 400 if request_body_str == ""

  request_body_json = {}

  begin
    request_body_json = JSON.parse(request_body_str)
  rescue Exception => e
    BrowserStack.logger.error("Error in JSON Parsing #{e}")
    return 400
  end

  request_body_json_str = {}
  # This is done because in JSON.parse the integers/boolean/others are all parsed.
  # But in GET it used to be in string form. This is done to keep all in sync and tamper minimal.
  request_body_json.each do |key, value|
    request_body_json_str[key] = value.to_s
  end
  params.merge!(request_body_json_str)
  BrowserStack.logger.info("Received POST selenium_command request with params: #{params}")
  execute_fire_cmd(params)
end

# This end point supports two of the major detox commands namely: launch_app, terminate_app
# This end point starts Detox Android Client Instrumentation process and verifies if AUT is launched within
# the time limit: #reties * seconds or terminates the AUT depending upon type of command used
get '/launch_detox_instrumentation' do
  start_time = Time.now

  if File.exist?("/tmp/duplicate_session_#{@device}")
    default_port = @devices_json[@device]["port"]

    session_details = JSON.parse(File.read("/tmp/duplicate_session_#{@device}"))

    detox_helper = DetoxHelper.new(@device, default_port)
    bundle_id = session_details["packageName"] # AUT package

    if params['terminate_app'].to_s == 'true'
      status = detox_helper.terminate_app(bundle_id, DETOX_TERMINATE_APP_RETRIES)
      return 200, { success: true, message: "#{bundle_id} terminated successfully" }.to_json if status == true

      return 500, { success: false, message: "Error while terminating #{bundle_id}" }.to_json
    end

    dac_instrumentation_name = session_details["dac_instrumentation_name"] # DAC instrumentation
    test_package_name = session_details["testPackageName"] # DAC package
    test_runner_path = "#{test_package_name}/#{dac_instrumentation_name}"
    session_id = params['session_id']
    launch_args = begin
      JSON.parse(params['args'])["launchArgs"]
    rescue StandardError
      {}
    end

    detox_helper.start_detox_client_instrumentation(session_id, test_runner_path, launch_args)
    launch_success = detox_helper.verify_app_launch(bundle_id, DETOX_INSTRUMENTATION_LAUNCH_VERIFY_RETRIES)

    return 200, { success: launch_success,
                  message: "Launched DAC instrumentation in #{Time.now - start_time}s" }.to_json
  end

  raise "Something went wrong in launching detox instrumentation."
rescue StandardError => e
  BrowserStack.logger.error("Error while launching detox instrumentation: #{@device}\
  \n sessionid: #{session_id} \n error: #{e} \n")
  return 500, { success: false, message: "Error while launch_detox_instrumentation: #{e.message}" }.to_json
end

#TODO: change this api endpoint to set_gpsLocation
get '/set_geolocation' do
  set_gpslocation(params["latitude"], params["longitude"], params["genre"], params["session_id"], true)
end

get '/get_geolocation' do
  get_geolocation
end

get '/update_device_locale_and_region' do
  start_time = Time.now
  set_device_locale(@device, params[:locale], BrowserStack.logger)
  time_taken = Time.now - start_time
  session_params = begin
    JSON.parse(File.read("#{CONFIG_DIR}/rtc_service_#{@device}"), symbolize_names: true)
  rescue StandardError
    {}
  end
  if session_params[:genre] == "app_live_testing"
    kill_all_apps_and_restart_current_app
    BrowserStack.logger.info "Sending data to EDS - update_device_locale_and_region, time_taken: "\
                             "#{time_taken} locale_change: #{params[:locale]}"
    @eds_obj.push_logs(EdsConstants::APP_LIVE_TEST_SESSIONS, {
      session_id: session_params[:app_live_session_id],
      product: {
        language_change_count: 1
      }
    })
  else
    `#{COMMON}/push_to_cls.rb #{CONFIG_DIR}/rtc_service_#{@device} \
'locale_change' '' "locale_change:#{params[:locale]}, time_taken:#{time_taken}"`
  end
  return {}.to_json
end

get '/settings' do
  BrowserStack.logger.info "Started getting settings state for params: #{params}"

  settings_helper = BrowserStack::SettingsHelper.new(@device, BrowserStack.logger, params[:product],
                                                     params[:session_id])
  settings_state = {
    device_passcode_toggle: settings_helper.passcode_enabled?,
    fix_device_rotation: settings_helper.fix_rotation_enabled? || settings_helper.device_sensors_disabled?
  }

  BrowserStack.logger.info "Successfully got settings state: #{settings_state} for request with params: #{params}"
  return 200, settings_state.to_json
rescue StandardError => e
  BrowserStack.logger.error "Failed to get settings state for request with params: #{params}, \
  error: #{e.message}, #{e.backtrace.join("\n")}"
  return 500, { message: "Failed to get settings state" }.to_json
end

post '/settings' do
  request_body = JSON.parse(request.body.read)
  BrowserStack.logger.info "Started setting settings for request with params: #{params} and body: #{request_body}"
  setting = request_body['setting']
  value = request_body['value']
  product = request_body['product']
  session_id = request_body['session_id']
  raise SettingsException.new("Invalid params", 400) if !setting || !value

  settings_helper = BrowserStack::SettingsHelper.new(@device, BrowserStack.logger, session_id,
                                                     product)
  settings_helper.apply_setting(setting, value)

  BrowserStack.logger.info "Successfully set settings for request with params: #{params}, and body: #{request_body}"
  return 200, { message: "Successfully set settings" }.to_json
rescue StandardError => e
  BrowserStack.logger.error "Failed to set settings for request with params: #{params}, \
  error: #{e.message}, #{e.backtrace.join("\n")}"
  message = "Failed to set settings"
  status = 500
  if e.is_a? SettingsException
    message = e.message
    status = e.status
  end
  return status, { message: message }.to_json
end

get '/kill_all_apps' do
  script_logger = script_logger_args("#{File.basename(APP_LIVE_SCRIPT)}_kill_all_user_apps")
  cmd = %(bash #{APP_LIVE_SCRIPT} kill_all_user_apps #{@device} 2>&1 )
  cmd += %( | while read line; do echo #{script_logger} \"$line\"; done)
  BrowserStack.logger.info "Killing all user installed apps : #{cmd}"
  system(cmd)
  return {}.to_json
end

get '/uninstall_user_apps' do
  script_logger = script_logger_args("#{File.basename(APP_LIVE_SCRIPT)}_uninstall_all_user_apps")
  cmd = %(bash #{APP_LIVE_SCRIPT} uninstall_all_user_apps #{@device} 2>&1 )
  cmd += %( | while read line; do echo #{script_logger} \"$line\"; done)
  BrowserStack.logger.info "Uninstalling all user installed apps : #{cmd}"
  begin
    spawn_process(cmd)
    return 200, {}.to_json
  rescue StandardError => e
    BrowserStack.logger.error("Exception  in uninstall_user_apps for session #{params[:app_live_session_id]} "\
                              "error #{@device}: #{e.message} \n")
    return 500, { error: "Error in uninstall_user_apps #{e.message}" }.to_json
  end
end

get '/user_installed_apps' do
  app_list = get_user_installed_apps
  PerformanceStatistics.new(@device, BrowserStack.logger).update_app(app_list) if params[:app_live_profiling] == true
  return { apps: app_list }.to_json
end

# this is specific to pm tools integration
get '/user_installed_app_details' do
  # Do not touch if geo restricted flow
  `touch /tmp/pm_tools_used_#{@device}` unless is_geo_restricted_user(params)
  user_installed_apps = get_user_installed_apps
  result = user_installed_apps.map do |package|
    version = `timeout 3 adb -s #{@device} shell dumpsys package #{package} \
| grep -i versionName | awk -F"=" '{print $2}'`
    { bundle_id: package, version: version.strip, display_name: nil }
  end
  return { apps: result }.to_json
end

def playwright_command(params, cdp_port, playwright_port, device, device_orientation)
  playwright = Playwright.new(params, cdp_port, playwright_port, device)
  # running start_cdp_proxy and start_playwright_server methods in parallel
  playwright_threads = []
  is_cdp_proxy_started = false
  playwright_threads << Thread.bs_run do
    is_cdp_proxy_started = playwright.start_cdp_proxy
  end
  playwright_url = nil
  playwright_threads << Thread.bs_run do
    playwright.start_playwright_server(params)
    playwright_url = playwright.fetch_playwright_url
  end
  playwright_threads.map(&:join)

  BrowserStack.logger.info "Playwright URL: #{playwright_url}"
  return [false, is_cdp_proxy_started, nil] if playwright_url.nil?

  cmd = "bash #{SCRIPT} ensure_screen_is_unlocked_and_set_orientation #{device} #{device_orientation}"
  BrowserStack.logger.info "Playwright Command: #{cmd}"
  system(cmd)
  playwright.set_chrome_flags
  [true, is_cdp_proxy_started, playwright_url]
rescue StandardError => e
  BrowserStack.logger.info "Some error happened in playwright_command method. Exception details: #{e.inspect}"
  [false, false, nil]
end

def install_custom_pfx_certificate(device, params)
  mark_event_start('fire_cmd.install_custom_certificate_time', params[:event_hash])
  custom_certificate_details = JSON.parse(params['customCertificateFile'])
  raise "Invalid custom certificate details" if custom_certificate_details.empty?

  custom_certificate_helper = CustomCertificate.new(device, params["automate_session_id"], custom_certificate_details,
                                                    BrowserStack.logger)
  custom_certificate_helper.install_pfx_certificate
  update_feature_usage("install_pfx_certificate", true)
rescue StandardError => e
  BrowserStack.logger.error("Failed to install custom certificate with error: #{e}
   for session #{params['automate_session_id']}")
  update_feature_usage("install_pfx_certificate", false, e.message[0, 100])
  raise FireCMDException, "browserstack_certificate_install_failure - #{e.message[0, 500]}"
ensure
  mark_event_end('fire_cmd.install_custom_certificate_time', params[:event_hash])
end

def is_geo_restricted_user(params)
  geo_param_present = !params[:is_geo_restricted].nil?
  is_geo_restricted = params[:is_geo_restricted] == "true"
  geo_param_present && is_geo_restricted
end

def stop_privoxy
  script_logger = script_logger_args("#{File.basename(APP_LIVE_SCRIPT)}_stop_privoxy")
  cmd = %(bash #{APP_LIVE_SCRIPT} stop_privoxy #{@device} 2>&1 \
| while read line; do echo #{script_logger} "$line"; done)
  BrowserStack.logger.info "Stopping privoxy : #{cmd}"
  system(cmd)
end

def set_default_location(params)
  location_on_start = "not set"
  location_on_start = "set" if !params["latitude"].nil? && !params["latitude"].empty? &&
                               !params["longitude"].nil? && !params["longitude"].empty?

  if params["genre"] == "app_live_testing"
    @eds_obj.push_logs(EdsConstants::APP_LIVE_TEST_SESSIONS, {
      session_id: params["app_live_session_id"],
      product: {
        location_status_on_start: location_on_start.to_s
      }
    }, params)
  else
    cmd = "ruby #{COMMON}/push_to_cls.rb #{CONFIG_DIR}/rtc_service_#{@device} 'location_status_on_start' '' "\
          "location_status_on_start:#{location_on_start} &"
    spawn_process(cmd)
  end
  session_id = get_session_id_from_params(params)
  set_gpslocation(params["latitude"], params["longitude"], params["genre"], session_id, false, nil, params)
end

def set_default_locale(params)
  if params["genre"] == "app_live_testing"
    @eds_obj.push_logs(EdsConstants::APP_LIVE_TEST_SESSIONS, {
      session_id: params["app_live_session_id"],
      product: {
        language_status_on_start: 1
      }
    }, params)
  else
    cmd = "ruby #{COMMON}/push_to_cls.rb #{CONFIG_DIR}/rtc_service_#{@device} 'language_on_start' '' "\
          "language_on_start:#{params['locale']} &"
    spawn_process(cmd)
  end
  # skipping for v2 app becuase media pop-up needs to be handled
  # locale is changed after media pop-up
  # refer handle_media_projection_popup() in live_common.sh
  if !(params[:use_rtc_app] == "v2" && params[:device_version].to_i >= 10) && !params["locale"].nil?
    set_device_locale(@device, params["locale"], BrowserStack.logger)
  end
end

def set_gpslocation(latitude, longitude, genre, session_id, sendToCls = false, appium_version = nil, params = {}) # rubocop:todo Naming/MethodParameterName, Style/OptionalBooleanParameter
  return '{}' if latitude.nil? || latitude.empty? || longitude.nil? || longitude.empty?

  zombie_push('android', "location_change_started_for_#{genre}", '', '', Time.now.to_i.to_s, @device,
              session_id, is_app_accessibility: params[:is_app_accessibility])

  mock_location_app = get_mock_location_app(appium_version)
  BrowserStack.logger.info("Using #{mock_location_app} to mock location for appium version: #{appium_version}")

  cmd = "bash #{SCRIPT} mock_location #{@device} #{latitude} #{longitude} #{mock_location_app}"
  BrowserStack.logger.info "Mocking location: #{cmd}"

  if ['live_testing', 'app_live_testing'].include?(genre)
    spawn_process(cmd)
  else
    # Waiting for the io.appium.settings' service to start
    # so that the settings app of that appium version is used
    system(*cmd.split)
  end

  if genre == "app_live_testing"
    BrowserStack.logger.info "Sending data to EDS"
    @eds_obj.push_logs(EdsConstants::APP_LIVE_TEST_SESSIONS, {
      session_id: session_id,
      product: {
        location_change_count: 1
      }
    }, params)
  elsif sendToCls
    cmd = "ruby #{COMMON}/push_to_cls.rb #{CONFIG_DIR}/rtc_service_#{@device} 'location_change' '' "\
          "location_change:changed &"
    spawn_process(cmd)
  end

  update_feature_usage("gpsLocation", true) if is_automate_or_app_automate(genre)
  Thread.bs_run do
    confirm_device_location(latitude, longitude, session_id, genre, params)
  end

  zombie_push('android', "location_change_ended_for_#{genre}", '', '', Time.now.to_i.to_s, @device,
              session_id, is_app_accessibility: params[:is_app_accessibility])

  '{}'
end

def copy_file(file, destination)

  BrowserStack.logger.info("Copying #{file} to #{destination}")
  FileUtils.cp(file, destination)
rescue StandardError => e
  BrowserStack.logger.error("Failed to copy file #{file} to #{destination}: #{e.message} "\
                            "backtrace: #{e.backtrace.join('\n')}.")
  [500, { error: "Failed to copy file: #{e.message}" }.to_json]

end

def trim_to_digits(num, size = 4)
  num = num.to_f + 0.0 # force convert -0.0 to 0.0
  format("%.#{size}f", num)
end

def confirm_device_location(latitude, longitude, session_id, genre, params = {})
  rec_time = Time.now.to_i.to_s
  BrowserStack.logger.info "android #{genre} location_change_initiated #{@device} #{session_id} #{rec_time}"
  zombie_push('android', "location_change_initiated_for_#{genre}", '', '', rec_time.to_s, @device.to_s,
              session_id, is_app_accessibility: params[:is_app_accessibility])
  max_attempt = 6
  max_attempt.times do |n|
    sleep 5
    BrowserStack.logger.info "confirm_location_change - executing #{n}"
    if compare_device_location(latitude, longitude)
      zombie_push('android', "location_change_confirmed_for_#{genre}", '', '', rec_time.to_s, @device.to_s,
                  session_id, is_app_accessibility: params[:is_app_accessibility])
      BrowserStack.logger.info "android #{genre} location_change_confirmed #{@device} #{session_id} #{rec_time}"
      break
    elsif n == 5
      if is_automate_or_app_automate(genre)
        update_feature_usage("gpsLocation", false, "location_change_confirmation_failed")
      end
    end
  end
end

def compare_device_location(target_lat, target_long)
  cur_loc = get_geolocation
  cur_lat, cur_long = cur_loc.split(',')
  cur_lat, cur_long, target_lat, target_long = [cur_lat, cur_long, target_lat, target_long]
                                               .map { |num| trim_to_digits(num) }
  BrowserStack.logger.info "compare_device_location - comparing, #{cur_lat} == "\
                           "#{target_lat} && #{cur_long} == #{target_long}"
  if cur_lat == "0.0010" && cur_long == "0.0010" && target_lat == "0.0000" && target_long == "0.0000"
    # pass with success for hardcoded locked locations as per android/driver_actions.sh:1373
    return true
  end

  cur_lat == target_lat && cur_long == target_long
end

def get_geolocation
  coordinates = `timeout 5 adb -s #{@device} shell dumpsys location \
| grep "mock]" | head -1 | cut -d "[" -f2 | awk '{print $2}'`
  BrowserStack.logger.info "Geolocation coordinates: #{coordinates}"
  coordinates
end

def start_video_rec(_device, params, start_delay=0)
  width = params[:video_width].nil? ? VIDEO_DEFAULT_WIDTH : params[:video_width]
  height = params[:video_height].nil? ? VIDEO_DEFAULT_HEIGHT : params[:video_height]
  session_id = get_session_id_from_params(params)
  genre = params[:genre]
  device_model = @devices_json[@device]['device_name']
  os_version = @devices_json[@device]['device_version']
  use_scrcpy = params[:use_scrcpy_for_video_recording].nil? ? false : params[:use_scrcpy_for_video_recording]
  BrowserStack.logger.info "Starting video record for #{@device} with res #{width}x#{height}"

  cmd = if start_delay > 0
          "sleep #{start_delay}; "
        else
          ""
        end

  cmd += "bash #{SELENIUM_SCRIPT} start_video #{@device} #{width}x#{height} #{session_id} #{genre} "\
         "'#{device_model}' #{os_version} #{use_scrcpy}"
  spawn_process(cmd)
end

def save_video_params(device, params)
  BrowserStack.logger.info "Saving video record params for #{device}"

  session_id = params[:video_session_id]
  aws_key = params[:video_aws_keys]
  aws_secret = params[:video_aws_secret]
  aws_bucket = params[:video_aws_bucket]
  aws_storage_class = params[:video_aws_storage_class] || 'STANDARD'
  aws_region = params[:video_aws_region]
  video_file = params[:video_file]
  genre = params[:genre]
  stop_req_timestamp = params[:stop_req_timestamp] || 0
  use_scrcpy_for_video_recording = if params[:use_scrcpy_for_video_recording].nil?
                                     false
                                   else
                                     params[:use_scrcpy_for_video_recording]
                                   end
  device_model = @devices_json[device]['device_name']
  os_version = @devices_json[device]['device_version']
  use_rtc_app = ["espresso", "fluttertest"].include?(params[:browserstack_framework]) ? params[:use_rtc_app] : "nil"

  File.open("/tmp/video_params_#{device}", 'w') do |f|
    f.write("#{device} #{session_id} #{aws_key} #{aws_secret} #{aws_region} #{aws_bucket} "\
            "#{video_file} #{genre} #{stop_req_timestamp} '#{device_model}' #{os_version} "\
            "#{use_scrcpy_for_video_recording} #{aws_storage_class} #{use_rtc_app}")
  end

end

def save_logs_params_for_async(device, params, type, log_copy_path, upload_type, uploader_request_file)

  json_data = {
    upload_type: upload_type,
    file_name: log_copy_path,
    s3_params: params.select { |key, _value| key.to_s.include?('aws') },
    session_id: params[:automate_session_id],
    genre: params[:genre] || params['genre'],
    device_identifier: device,
    user_id: params[:user_id]
  }
  dir_name = File.dirname(uploader_request_file)
  FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
  genre = params[:genre]
  is_app_automate = genre.to_s.downcase == 'app_automate'

  case type
  when "network"
    network_logs_capture_content = params[:networkLogsCaptureContent]
    network_logs_capture_content = params['networkLogsCaptureContent'] if network_logs_capture_content.nil?
    # Capture content flag is set as false by default and would be set as true
    # only if network_logs_capture_content explicitly sent as true
    json_data[:captureContentFlag] = !network_logs_capture_content.nil? &&
                                      network_logs_capture_content.to_s.downcase == 'true'

    zip_nw_logs_android_aut = params[:zip_nw_logs_android_aut]
    zip_nw_logs_android_aut = params['zip_nw_logs_android_aut'] if zip_nw_logs_android_aut.nil?
    json_data[:s3_zip_nw_logs_android] = !zip_nw_logs_android_aut.nil? &&
                                              zip_nw_logs_android_aut.to_s.downcase == 'true'

    if is_app_automate
      zip_nw_logs_android_aa = params[:zip_nw_logs_android_app_aut]
      zip_nw_logs_android_aa = params['zip_nw_logs_android_app_aut'] if zip_nw_logs_android_aa.nil?
      json_data[:s3_zip_nw_logs_android] = !zip_nw_logs_android_aa.nil? &&
                                                zip_nw_logs_android_aa.to_s.downcase == 'true'
    end
  when "appium"
    zip_appium_logs_android_aut = params[:zip_appium_logs_android_aut]
    zip_appium_logs_android_aut = params['zip_appium_logs_android_aut'] if zip_appium_logs_android_aut.nil?
    json_data[:s3_zip_appium_logs_android] = !zip_appium_logs_android_aut.nil? &&
                                                  zip_appium_logs_android_aut.to_s.downcase == 'true'

    if is_app_automate
      zip_appium_logs_android_aa = params[:zip_appium_logs_android_app_aut]
      zip_appium_logs_android_aa = params['zip_appium_logs_android_app_aut'] if zip_appium_logs_android_aa.nil?
      json_data[:s3_zip_appium_logs_android] = !zip_appium_logs_android_aa.nil? &&
                                                    zip_appium_logs_android_aa.to_s.downcase == 'true'
    end
  end

  write_to_file(uploader_request_file, json_data)
  BrowserStack.logger.info("#{type} upload request created as #{uploader_request_file}")
end

def save_logs_params(device, params, type = "device")
  BrowserStack.logger.info "Saving #{type}logs record params for #{@device}, params #{params}"

  session_id = params[:automate_session_id]
  if ["screenshot", "playwright"].include?(type)
    aws_key = params[:appiumlogs_aws_key]
    aws_secret = params[:appiumlogs_aws_secret]
    aws_bucket = params[:appiumlogs_aws_bucket]
    aws_region = params[:appiumlogs_aws_region]
    aws_storage_class = params[:appiumlogs_aws_storage_class]
  else
    aws_key = params[:"#{type}logs_aws_key"]
    aws_secret = params[:"#{type}logs_aws_secret"]
    aws_bucket = params[:"#{type}logs_aws_bucket"]
    aws_region = params[:"#{type}logs_aws_region"]
    aws_storage_class = params[:"#{type}logs_aws_storage_class"]
  end
  genre = params[:genre]
  is_app_automate = genre.to_s.downcase == 'app_automate'

  File.open("/tmp/#{type}logs_params_#{device}", 'w') do |f|
    case type
    when "network"
      network_logs_capture_content = params[:networkLogsCaptureContent]
      network_logs_capture_content = params['networkLogsCaptureContent'] if network_logs_capture_content.nil?
      # Capture content flag is set as false by default and would be set as true
      # only if network_logs_capture_content explicitly sent as true
      capture_content_flag = !network_logs_capture_content.nil? &&
                             network_logs_capture_content.to_s.downcase == 'true'

      zip_nw_logs_android_aut = params[:zip_nw_logs_android_aut]
      zip_nw_logs_android_aut = params['zip_nw_logs_android_aut'] if zip_nw_logs_android_aut.nil?
      s3_zip_nw_logs_android = !zip_nw_logs_android_aut.nil? &&
                                    zip_nw_logs_android_aut.to_s.downcase == 'true'

      if is_app_automate
        zip_nw_logs_android_aa = params[:zip_nw_logs_android_app_aut]
        zip_nw_logs_android_aa = params['zip_nw_logs_android_app_aut'] if zip_nw_logs_android_aa.nil?
        s3_zip_nw_logs_android = !zip_nw_logs_android_aa.nil? &&
                                      zip_nw_logs_android_aa.to_s.downcase == 'true'
      end

      f.write(
        "#{device} #{session_id} #{aws_key} #{aws_secret} "\
        "#{aws_region} #{aws_bucket} #{genre} #{capture_content_flag} "\
        "#{aws_storage_class} #{s3_zip_nw_logs_android}"
      )
    when "appium"
      zip_appium_logs_android_aut = params[:zip_appium_logs_android_aut]
      zip_appium_logs_android_aut = params['zip_appium_logs_android_aut'] if zip_appium_logs_android_aut.nil?
      s3_zip_appium_logs_android = !zip_appium_logs_android_aut.nil? &&
                                        zip_appium_logs_android_aut.to_s.downcase == 'true'

      if is_app_automate
        zip_appium_logs_android_aa = params[:zip_appium_logs_android_app_aut]
        zip_appium_logs_android_aa = params['zip_appium_logs_android_app_aut'] if zip_appium_logs_android_aa.nil?
        s3_zip_appium_logs_android = !zip_appium_logs_android_aa.nil? &&
                                          zip_appium_logs_android_aa.to_s.downcase == 'true'
      end

      f.write(
        "#{device} #{session_id} #{aws_key} #{aws_secret} "\
        "#{aws_region} #{aws_bucket} #{genre} "\
        "#{aws_storage_class} #{s3_zip_appium_logs_android}"
      )
    else
      f.write("#{device} #{session_id} #{aws_key} #{aws_secret} #{aws_region} "\
              "#{aws_bucket} #{genre} #{aws_storage_class}")
    end
  end
end

def save_app_live_logs_params(device, params)
  BrowserStack.logger.info "Saving app_live_logs record params for #{@device}"

  session_id = params[:app_live_session_id]
  aws_key = params[:app_testing_aws_key]
  aws_secret = params[:app_testing_aws_secret]
  aws_bucket = params[:app_testing_aws_bucket]
  aws_region = params[:app_testing_aws_region]

  File.open(app_live_logs_params_file(device), 'w') do |f|
    f.write("#{device} #{session_id} #{aws_key} #{aws_secret} #{aws_region} #{aws_bucket}")
  end
rescue Exception => e
  BrowserStack.logger.error "Error in save_app_live_logs_params due to error: #{e.message}"
  params[:event_hash][:save_app_live_logs_params_error] = e.message
end

def read_app_live_logs_params(device)

  File.read(app_live_logs_params_file(device))
rescue StandardError
  nil

end

def app_live_logs_params_file(device)
  "/tmp/app_live_logs_params_#{device}"
end

def process_device_logs(params, app_bundle_id, device_log_path)
  session_id = params[:automate_session_id]
  parse_device_logs_for_stability_reason(device_log_path, session_id, params, app_bundle_id, SERVER_LOG)
end

def process_mitmdump_logs(params, mitmdump_log_path)
  session_id = params[:automate_session_id]
  parse_mitmdump_logs(mitmdump_log_path, session_id, params, SERVER_LOG)
end

def process_appium_logs(device, params, process_logs_in_async)
  session_id = params[:automate_session_id]
  FileUtils.cp(appium_log_path, session_appium_logs(device, session_id, process_logs_in_async))
  #this is defined in mobile_commons/utils/log_parse_util
  parse_appium_logs_for_stability_reason(session_appium_logs(device, session_id, process_logs_in_async), session_id,
                                         params, SERVER_LOG)

  remove_unwanted_text(device, session_id, process_logs_in_async)
  check_and_push_failure_reason(device, params, process_logs_in_async)
end

def remove_unwanted_text(device, session_id, process_logs_in_async)

  session_appium_logs_file = session_appium_logs(device, session_id, process_logs_in_async)
  # Removing text between markers inserted by Selenium Hub
  `sed -i '/^#{APPIUM_BRIDGE_START_MARKER}/,/^#{APPIUM_BRIDGE_STOP_MARKER}/d' #{session_appium_logs_file}`

  # Removing NSS version check chromedriver warning
  `sed -i '/NSS_VersionCheck/,/^#18.*__clone/d' #{session_appium_logs_file}`

  # Removing percy automate scripts
  `sed -i '/percy_automate_script/d' #{session_appium_logs_file}`

  # Redacting host machine ip
  hostip_port_regex = "#{WHATSMYIP.gsub(/\./, '\.')}:[0-9]*"
  `sed -i -e 's/#{hostip_port_regex}/[REDACTED]/g' #{session_appium_logs_file}`

  # Removing system commands
  `sed -i '/.run.*sudo su /d' #{session_appium_logs_file}`
rescue StandardError => e
  BrowserStack.logger.info("Exception while processing appium logs: #{e.message}")
  BrowserStack::Zombie.push_logs("appium-marker-failed", e.message.to_s,
                                 { "session_id" => session_id, "device" => @device })

end

def check_and_push_failure_reason(device, params, process_logs_in_async)
  session_id = params[:automate_session_id]
  session_appium_logs_file = session_appium_logs(device, session_id, process_logs_in_async)
  return unless params[:last_request] && params[:reason]

  eds = EDS.new(params, BrowserStack.logger)
  event = {}
  event["hashed_id"] = params[:automate_session_id]
  if params[:genre] == 'app_automate'
    eds_event_type = EdsConstants::APP_AUTOMATE_TEST_SESSIONS
    kind = "app-"
  else
    eds_event_type = EdsConstants::AUTOMATE_TEST_SESSIONS
    kind = ""
  end
  params[:sotimeout] = {
    last_request: params[:last_request],
    reason: params[:reason],
    last_raw_log: params[:last_raw_log],
    session_limit: params[:session_limit]
  }
  begin
    # reason object returned from AppiumLogParser.check_so_timeout_reason
    # {
    #     :reason => String - of the format "#SOTIMEOUT-bucket-diagnostic_reason",
    #     :validations => Array list of validators that passed,
    #     :data => Hash of marker
    # }
    Timeout.timeout(SOTIMEOUT_PROCESS_TIMEOUT) do
      reason_obj = SoTimeoutUtil::AppiumLogParser.new('android', session_appium_logs_file, params)
                                                 .check_so_timeout_reason
      kind += reason_obj[:reason].split("-").first(2).join("-")
      zombie_push('android', kind, params[:genre], '', { "s" => params[:sotimeout],
                                                         "v" => reason_obj[:validations],
                                                         "r" => reason_obj[:reason],
                                                         "f" => reason_obj[:is_false_hub_timeout].to_s }.to_s,
                  @device.to_s, session_id, params[:user_id], reason_obj[:is_false_hub_timeout].to_s)
      event["secondary_diagnostic_reason"] = reason_obj[:reason]
      event["product"] = { "stability" => { "reason" => "error-socket-timeout" } } if params[:genre] == 'app_automate'
      eds.push_logs(eds_event_type, event)
    end
  rescue Timeout::Error => e
    BrowserStack.logger.info("SOTIMEOUT: Parsing timedout: #{e.message}")
    zombie_push('android', "#{kind}SOTIMEOUT-check-timeout", params[:genre], "", { "s" => params[:sotimeout],
                                                                                   "e" => e.message }.to_s,
                @device.to_s, session_id, params[:user_id])
  rescue StandardError => e
    BrowserStack.logger.info("SOTIMEOUT: Exception while processing failure reason: #{e.message}")
    zombie_push('android', "#{kind}SOTIMEOUT-check-failed", params[:genre], "", { "s" => params[:sotimeout],
                                                                                  "e" => e.message }.to_s,
                @device.to_s, session_id, params[:user_id])
  end
end

def appium_logs_append_marker(params)
  marker = appium_marker(params[:log_type])
  BrowserStack.logger.info("echoing #{marker} #{params[:bridge_name]} to #{appium_log_path}")
  `echo "\n#{marker} #{params[:bridge_name]}" >> #{appium_log_path}`
end

def appium_marker(log_type)
  log_type == "start" ? APPIUM_BRIDGE_START_MARKER : APPIUM_BRIDGE_STOP_MARKER
end

def appium_log_path
  File.join("/var/log/browserstack", "appium_#{@device}.log")
end

def plawyright_log_path(session_id)
  File.join("/tmp", "playwright_server_#{session_id}.log")
end

def plawyright_url_log_path
  File.join("/var/log/browserstack", "playwright_url_#{@device}.log")
end

def cdp_log_path
  File.join("/var/log/browserstack", "ws-reconnect-proxy_#{@device}.log")
end

def session_appium_logs(device, session_id, process_logs_in_async)
  if process_logs_in_async
    "/var/log/browserstack/appium_#{device}_#{session_id}.log"
  else
    "/tmp/appium_processed_#{session_id}.log"
  end
end

def get_dependent_app_bundle_ids(other_apps)
  bundle_ids = []
  JSON.parse(other_apps).each do |other_app|
    bundle_ids << other_app["bundle_id"]
  end
  bundle_ids
end

def get_apps_instrumentation_name(apps)
  instrumentation_names = []
  JSON.parse(apps).each do |app|
    instrumentation_names << app["instrumentation_name"]
  end
  instrumentation_names
end

def get_test_params(test_params)
  test_params_parsed = ""
  JSON.parse(test_params).each do |k, v|
    next if k == 'cucumberOptions'

    value = v.is_a?(Array) ? v.join(",") : v
    test_params_parsed += " -e #{k} #{value}"
  end

  test_params_parsed
end

def install_dependent_apps(dependent_apps, session_id, enable_apksigner, params, app_kind = "other_apps")

  total = 0
  bundle_ids = []
  # downloaded_other_apps and installed_other_apps will be stored in session file.
  # The same keys is used for both midSessionInstallApps and otherApps.
  # It will be used to determine whether app is already installed or not during install app command.
  downloaded_other_apps = {}
  installed_other_apps = {}
  JSON.parse(dependent_apps).each do |dependent_app|
    t = Time.now
    bundle_id = dependent_app["bundle_id"]
    app_hashed_id = dependent_app["hashed_id"]
    app_type = app_kind == "test" ? "test" : "dependent"
    skip_app_installation = (bundle_id == params['app_testing_bundle_id']) || bundle_ids.include?(bundle_id) ||
                              app_kind == "mid_session_install_apps"
    subregion_app_caching_enabled = params["app_automate_custom_params"] &&
      params["app_automate_custom_params"]["subregion_app_caching_proxy_enabled"].to_s == "true"
    zip_align = (dependent_app["zip_align"].to_s == 'true')
    bundle_ids << bundle_id
    other_app_download_path = download_and_install_app(
      bundle_id, dependent_app["download_url"], false,
      dependent_app["download_timeout"], session_id,
      {
        app_type: app_type,
        zip_align: zip_align,
        enable_apksigner: enable_apksigner,
        skip_install: skip_app_installation,
        install_experiment_enabled: params["install_experiment_enabled"],
        is_minimized_flow: params["is_minimized_flow"],
        force_resign: force_resign_app?(params).to_s,
        subregion_app_caching_enabled: subregion_app_caching_enabled,
        is_app_testing: !params[:genre].nil? && params[:genre].to_s.eql?('app_automate')
      },
      params["user_id"]
    )
    if skip_app_installation
      downloaded_other_apps[app_hashed_id] = { app_path: other_app_download_path, bundle_id: bundle_id,
                                               zip_align: zip_align, enable_apksigner: enable_apksigner }
    else
      installed_other_apps[app_hashed_id] = { app_path: other_app_download_path, bundle_id: bundle_id }
    end
    time_taken = Time.now - t
    BrowserStack.logger.info "[Dependent apps] Download and install time: #{time_taken}"
    total += time_taken
  end
  params['downloaded_other_apps_details'] = downloaded_other_apps
  params['installed_other_apps_details'] = installed_other_apps
  BrowserStack.logger.info "[Dependent apps] Download and install total time: #{total}"
  push_to_cls(params, "dependent_apps_download_and_install_done", '', { "other_apps" => dependent_apps.to_json,
                                                                        "total_download_time" => total,
                                                                        "device_name" => @device_name,
                                                                        "instance_id" => params[:device],
                                                                        "session_id" => session_id })
rescue Exception => e
  raise FireCMDException, e.message
end

def get_folder_path_details_from_s3_url(app_url, skip_resign = nil, file_extension = "apk")

    #  URL: https://d1f4l50qmgi5dp.cloudfront.net/08db046d94aa52c2d761320de7f046f0803af7fd/08db046d94aa52c2d761320de7f046f0803af7fd.apk
    # URI.path: /08db046d94aa52c2d761320de7f046f0803af7fd/08db046d94aa52c2d761320de7f046f0803af7fd.apk

    # In future, the urls will be
    #  URL: https://browserstack-userapps-stag-use1.s3.amazonaws.com/data/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4.apk
    #  URI.path: /data/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4.apk

  app_identifier = URI.parse(app_url).path.split('/').last.split(".#{file_extension}")[0]
  raise "app_identifier is nil from s3 url" if app_identifier.nil?

  if skip_resign.to_s == "true"
    BrowserStack.logger.info "[Download Apps] Appending unsigned to app_identifier: #{app_identifier}"
    app_identifier += "_unsigned"
  end

  app_identifier
rescue StandardError => e
  BrowserStack.logger.error("Malformed app_url: #{app_url} Error message: #{e.message} stacktrace: #{e.backtrace}")
  raise "app_identifier is nil from s3 url"

end

def wait_for_uuid_folder(download_folder, retries)
  total_received_retries = retries
  while retries > 0
    BrowserStack.logger.info("[APP_NOT_FOUND] Waiting for a complete file. Retries Left: #{retries}")
    # Of the format: ["/tmp/apps/app_identifier/uuid.complete"]
    completed_files = Dir.glob("#{download_folder}/*.complete")
    if !completed_files.nil? && !completed_files.empty?
      uuid = completed_files.first.split('/').last.split('.').first
      BrowserStack.logger.info(".complete file found inside download folder with uuid: #{uuid}")
      return uuid, (total_received_retries - retries)
    end

    sleep 1
    retries -= 1
  end
  [nil, (total_received_retries - retries)]
end

def install_apps(package, download_params, session_id, download_path, user_id = "", force_resign = "false")
  instant_app = params[:is_an_instant_app].to_s == "true" && params[:instant_app_enabled_group] == "true"
  return install_instant_app(download_path) if instant_app

  cmd = if download_params[:is_apks_archive]
          "bash #{APP_LIVE_SCRIPT} install_apks \"#{@device}\" \"#{package}\" "\
          "\"#{session_id}\" \"#{user_id}\" \"#{download_path}\""
        elsif params[:is_an_instant_app].to_s == "true" && params[:instant_app_enabled_group] == "true"
          "bash #{APP_LIVE_SCRIPT} install_instant_app \"#{@device}\" \"#{package}\" "\
          "\"#{session_id}\" \"#{user_id}\" \"#{download_path}\""
        else
          "bash #{APP_LIVE_SCRIPT} install_app \"#{@device}\" \"#{package}\" \"-d -r -t\" 0 \"true\" "\
          "\"#{download_params[:zip_align]}\" \"#{session_id}\" \"#{download_params[:enable_apksigner]}\" "\
          "\"#{@device_name}\" \"#{@devices_json[@device]['device_version']}\" \"#{user_id}\" \"true\" "\
          "\"#{download_path}\" \"#{download_params[:install_experiment_enabled]}\" \"#{force_resign}\" "\
          "\"#{download_params[:skip_resign]}\""
        end
  BrowserStack.logger.info("Running for install_apps: #{cmd}")

  if params["enable_async_main_app_install"].to_s == "true" && download_params[:app_type] == "main"
    BrowserStack.logger.info("[ASYNC FLOW] Begin install process")
    pid_file = "#{APP_INSTALL_PID_PREFIX_PATH}_#{@device}"
    log_result_file = "#{APP_INSTALL_LOG_PREFIX_PATH}_#{@device}.log"

    pid = Process.spawn(cmd, out: log_result_file, err: log_result_file)
    BrowserStack.logger.info("[ASYNC FLOW] Installation spawned with pid: #{pid}")
    write_config_with_lock(pid_file, pid.to_s)
    BrowserStack.logger.info("[ASYNC FLOW] Pid file content written: #{File.read(pid_file)}") if File.exist?(pid_file)
    pid, status = Process.wait2(pid)
    exit_status_code = status.exitstatus

    install_output = File.exist?(log_result_file) ? File.read(log_result_file) : ""
    BrowserStack.logger.info("[ASYNC FLOW] install_exit_code: #{exit_status_code}, install_output: #{install_output}")
    BrowserStack.logger.info("[ASYNC FLOW] Scheduling async app install files cleanup")
    clean_async_app_install_files
    [install_output, exit_status_code]
  else
    output = `#{cmd}`
    exit_status_code = $CHILD_STATUS.exitstatus
    [output, exit_status_code]
  end
end

def install_instant_app(download_path)
  BrowserStack.logger.info("Running install_instant_app")
  begin
    adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    install_out = adb.install(download_path, "-r", "-t", "--instantapp")
  rescue StandardError => e
    BrowserStack.logger.error "Failed install_instant_app: #{e.message} #{e.backtrace.join("\n")}"
    return [e.message, 1]
  end

  BrowserStack.logger.info("Install success. Output: #{install_out}")
  [install_out, 0]
end

def check_for_already_downloaded_apps(download_folder, app_identifier, session_id, file_extension = "apk")
  total_retries = 0
  if File.exist?(download_folder)
    apk_list = Dir.glob("#{download_folder}/*.#{file_extension}")
    started_downloads = Dir.glob("#{download_folder}/*.starting")
    if apk_list.empty?
      # if no folder is there and no .starting file then go ahead with downloading the app
      unless started_downloads.empty?
        uuid, total_retries = wait_for_uuid_folder(download_folder, 7)
        if uuid.nil?
          BrowserStack.logger.info("[#{session_id}] All the retries exhausted while waiting for download "\
                                   "to get completed, no dir was present initially.")
          zombie_push('android', "app-download-multiple", params[:genre], "no-#{file_extension}-present",
                      app_identifier, @device, session_id)
        else
          BrowserStack.logger.info("[#{session_id}] Folder found after total #{total_retries} when only "\
                                   "starting file was present.")
        end
      end
    else
      uuid, total_retries = wait_for_uuid_folder(download_folder, 2)
      if uuid.nil?
        BrowserStack.logger.info("[#{session_id}] All the retries exhausted while waiting for .complete file to be "\
                                 "present, #{file_extension} was present already.")
        zombie_push('android', "app-download-multiple", params[:genre], "#{file_extension}-present", app_identifier,
                    @device, session_id)
      else
        BrowserStack.logger.info("[#{session_id}] Folder found after total #{total_retries} when only folder was "\
                                 "already present but complete file not present.")
        zombie_push('android', "app-download-multiple", params[:genre], "dir-present-#{file_extension}-not-present",
                    app_identifier, @device, session_id, nil, uuid, total_retries)
      end
    end
  end
  [uuid, total_retries]
end

def get_app_caching_proxy_url(s3_url)
  uri = URI.parse(s3_url)
  original_host = uri.host.dup
  uri.host.replace(SUB_REGION_APP_CACHING_PROXY_HOST)
  uri.scheme = "http"
  [original_host, uri.to_s]
end

def get_injection_params(download_params)
  app_type = download_params[:app_type]
  injection_request_json = download_params[:inject_app] ? JSON.parse(params[:inject_app]) : nil

  # Return nil in case of no inject_app object present in the params.
  return nil if !app_type || app_type != "main" || !injection_request_json

  injection_request_json
end

def download_apps( # rubocop:todo Metrics/MethodLength, Metrics/AbcSize
  download_folder, app_identifier, package, app_url, timeout,
  session_id, download_params, file_extension = "apk"
)
  begin
    injection_request_obj = get_injection_params(download_params)
    if injection_request_obj
      mark_event_start("inject_app", params[:event_hash])

      instrumentation_host = injection_request_obj["instrumentation_host"]
      unless instrumentation_host
        BrowserStack.logger.error "Instrumentation host is nil"
        raise "Instrumentation host is nil in inject_app request"
      end

      options = {
        app_url: app_url,
        patched_app_download_url: injection_request_obj["patched_app_download_url"],
        app_hashed_id: injection_request_obj["app_hashed_id"],
        instrumentation_host: injection_request_obj["instrumentation_host"],
        rails_host: injection_request_obj["rails_host"],
        product: injection_request_obj["product"],
        app_upload_identifier: injection_request_obj["app_upload_identifier"],
        session_id: session_id,
        instrumentation_type: injection_request_obj["instrument_type"],
        instrumentation_types: injection_request_obj["instrumentations"] || [injection_request_obj["instrument_type"]],
        browserstack_framework_version: injection_request_obj["browserstack_framework_version"],
        screenshot_block_version: injection_request_obj["screenshot_block_version"],
        s3_suffix_key: injection_request_obj["s3_suffix_key"],
        use_complete_s3_suffix_key: injection_request_obj["use_complete_s3_suffix_key"],
        patch_value: injection_request_obj["patch_value"],
        device_id: @device,
        appFramework: injection_request_obj["appFramework"],
        patching_code: injection_request_obj["patching_code"],
        launcher_activity: injection_request_obj["launcher_activity"],
        extract_native_libs: injection_request_obj["extract_native_libs"],
        use_s3_suffix: injection_request_obj["use_s3_suffix"]
      }

      options[:mobileData] = injection_request_obj["mobileData"] unless injection_request_obj["mobileData"].nil?
      options[:s3_config] = injection_request_obj["s3_config"] if injection_request_obj["s3_config"]

      BrowserStack.logger.info "Params for inject_app request are - #{download_params}"
      app_url, failure_reason = AppInjection.inject_app(options)

      unless app_url
        zombie_push("android", "app-injection-failed", "", "",
                    { "app_hashed_id" => injection_request_obj["app_hashed_id"],
                      "instrumentation_host" => injection_request_obj["instrumentation_host"],
                      "device_name" => @device_name },
                    @device, session_id)
        raise FireCMDException, get_err_msg_for_failed_injection(failure_reason, options["instrument_type"])
      end
      #if app_url is nil get_folder_path_details_from_s3_url will raise an exception
      app_identifier = get_folder_path_details_from_s3_url(app_url, download_params[:skip_resign])
      download_folder = "#{APPS_DOWNLOAD_FOLDER}/#{app_identifier}"

      mark_event_end("inject_app", params[:event_hash])
    end
  rescue StandardError => e
    BrowserStack.logger.error "Code injection failed: #{e.message} :: backtrace: #{e.backtrace.join("\n")}"
    zombie_push("android", "inject-app-raised-exception", "", "",
                { "app_hashed_id" => injection_request_obj["app_hashed_id"],
                  "app_uploader_host" => injection_request_obj["app_uploader_host"],
                  "device_name" => @device_name, "product" => injection_request_obj["product"],
                  "error" => e.message },
                @device, session_id)
    raise FireCMDException, e.message
  end

  uuid, total_retries = check_for_already_downloaded_apps(download_folder, app_identifier, session_id, file_extension)

  is_app_download_required = uuid.nil? ? true : false

  if !is_app_download_required
    case download_params[:app_type]
    when "main"
      set_event_time("app_download_time", params[:event_hash], 0)
    when "test"
      set_event_time("test_download_time", params[:event_hash], 0)
    end
    download_path = "#{download_folder}/#{uuid}.#{file_extension}"
    # What all data needs to sent to Zombie
    zombie_push('android', "app-download-reused", params[:genre], total_retries, app_identifier, @device, session_id)
    BrowserStack.logger.info("[#{session_id}] App already downloaded in #{download_folder}")
  else
    FileUtils.mkdir_p(download_folder)
    uuid = SecureRandom.uuid
    FileUtils.touch("#{download_folder}/#{uuid}.starting")
    download_path = "#{download_folder}/#{uuid}.#{file_extension}"
    BrowserStack.logger.info("[#{session_id}] App Downloaded for the first time to #{download_path}")
    begin
      t1 = Time.now.to_f
      BrowserStack.logger.info "[App Automate] download_params: #{download_params}"
      case download_params[:app_type]
      when "main"
        mark_event_start("app_download_time", params[:event_hash])
      when "test"
        mark_event_start("test_download_time", params[:event_hash])
      end

      headers_file = "#{APP_DOWNLOAD_HEADER_FILE_PREFIX_PATH}_#{session_id}_#{app_identifier}"
      download_from_s3_fallback_flag = false
      begin
        # Fallback to normal download without caching proxy if either
        # is_app_testing is False or subregion_app_caching_enabled is false
        # Both Have to be true to proceed with App Caching Proxy
        raise StandardError, "App Caching not enabled" unless
          is_subregion_caching_enabled?(download_params)

        original_host, proxy_url = get_app_caching_proxy_url(app_url)
        BrowserStack.logger.info "[App Automate] App Caching enabled flow. " \
        "Proxy URL: #{proxy_url} | Original host: #{original_host}"

        BrowserStack::HttpUtils.download_file(proxy_url, download_path, "app",
                                              { retry_count: 0,
                                                timeout: timeout,
                                                kill_in_cleanup: true,
                                                device_id: @device,
                                                header: "X-Domain: #{original_host}",
                                                dump_headers_to_file: headers_file,
                                                is_app_testing: download_params[:is_app_testing] })
      rescue StandardError => e
        download_from_s3_fallback_flag = true
        BrowserStack.logger.info "[App Automate] Falling back to s3 download flow. Error: #{e.message}"
        BrowserStack::HttpUtils.download_file(app_url, download_path, "app",
                                              { retry_count: 1,
                                                timeout: timeout,
                                                kill_in_cleanup: true,
                                                device_id: @device })
      end

      app_download_time = ((Time.now.to_f - t1) * 1000).to_i

      case download_params[:app_type]
      when "main"
        mark_event_end("app_download_time", params[:event_hash])
      when "test"
        mark_event_end("test_download_time", params[:event_hash])
      end
      push_to_cls(params, "#{download_params[:app_type]}_app_downloaded", '',
                  { "app_type" => download_params[:app_type],
                    "app_url" => app_url, "app_download_time" => app_download_time,
                    "timeout" => timeout, "device_name" => @device_name,
                    "instance_id" => params[:device],
                    "package" => package,
                    "session_id" => session_id })

      BrowserStack.logger.info "[App Automate] Download time: #{app_download_time}"
      kind_prefix = download_params[:is_apks_archive] ? 'apks' : 'app'
      zombie_push("android", "#{kind_prefix}-#{download_params[:app_type]}-app-download-time", "", "",
                  { "app_download_time" => app_download_time,
                    "device_name" => @device_name,
                    "package" => package }, @device, session_id)

      # Instrument App Caching Metrics
      if is_subregion_caching_enabled?(download_params)
        cache_hit_status = ""
        if !download_from_s3_fallback_flag && File.file?(headers_file)
          File.read(headers_file).split("\n").each do |line|
            if line.match("X-Cache-Status")
              cache_hit_status = line.match("HIT") ? "HIT" : "MISS"
              break
            end
          end
          BrowserStack.logger.info "[App Automate] Cache Hit Status: #{cache_hit_status}"
        end
        zombie_push("android", "#{kind_prefix}-#{download_params[:app_type]}-app-caching-status", "", "",
                    {
                      "cache_hit_status" => cache_hit_status,
                      "download_fallback_hit" => download_from_s3_fallback_flag,
                      "app_identifier" => app_identifier,
                      "device_name" => @device_name,
                      "package" => package
                    }, @device, session_id)
      end
      if File.file?(headers_file)
        BrowserStack.logger.info "[App Automate] Found app download headers file. Deleting"
        File.delete(headers_file)
      end
    rescue StandardError => e
      if File.file?(headers_file)
        BrowserStack.logger.info "[App Automate] Found app download headers file. Deleting"
        File.delete(headers_file)
      end

      zombie_push('android', "#{file_extension}-download-failed", '', @device_name,
                  e.message.to_s, @device.to_s, session_id)
      BrowserStack.logger.error "#{file_extension} download failed: #{e.message}"
      raise FireCMDException, e.message
    end
  end
  [download_path, uuid]
end

def get_err_msg_for_failed_injection(reason, injection_type)
  err_msg = "Failed injecting the app"
  if reason == "proguarded_app"
    #Adding custom error message for proguarded apps to tag as user error and show appr msg to user
    case injection_type
    when "biometrics", "instrumented"
      err_msg = "BIOMETRICS_INJECTING_FAILED_AS_PROGUARDED"
    when "camera"
      err_msg = "CAMERA_INJECTING_FAILED_AS_PROGUARDED"
    end
  end
  err_msg
end

def is_subregion_caching_enabled?(params)
  params && params[:is_app_testing] && params[:subregion_app_caching_enabled]
end

def is_subregion_caching_error?(error)
  SUBREGION_CACHING_RELATED_INSTALL_ERRORS.any? { |e| e =~ error.inspect[0, 300].to_s }
end

def is_async_install_required?(params, is_minimized_flow, is_app_testing)
  is_app_testing &&
    params["async_app_download_install"].to_s.downcase == "true" &&
    params["s3_app_url"] &&
    !is_minimized_flow &&
    !pre_install_app?(params)
end

def ensure_thread_killed(download_and_install_thread)
  if defined?(download_and_install_thread) && download_and_install_thread&.alive?
    BrowserStack.logger.info "[FIRECMD] Killing download_and_install_thread"
    download_and_install_thread.kill
  end
end

def clean_async_app_install_files
  pid_file = "#{APP_INSTALL_PID_PREFIX_PATH}_#{@device}"
  log_result_file = "#{APP_INSTALL_LOG_PREFIX_PATH}_#{@device}.log"

  files_to_clean = [log_result_file, pid_file]

  files_to_clean.each do |file|
    if file && File.exist?(file)
      BrowserStack.logger.info("Cleaning up file: #{file}")
      File.delete(file)
      BrowserStack.logger.info("Successfully deleted file: #{file}")
    else
      BrowserStack.logger.warn("File not found, skipping cleanup: #{file}")
    end
  rescue Errno::EACCES
    BrowserStack.logger.error("Permission denied while deleting file: #{file}")
  rescue Errno::ENOENT
    BrowserStack.logger.warn("File already deleted or missing: #{file}")
  rescue StandardError => e
    BrowserStack.logger.error("Error while deleting file: #{file} - #{e.message}")
  end
end

def install_app_firecmd(params, sess_details, opts = {})
  t1 = Time.now.to_i
  zip_align = params["zip_align"].to_s == 'true'
  mark_event_start('download_and_install_app', params[:event_hash])
  sess_details[:main_app_download_path] = download_and_install_app(
    opts[:bundle_id], params["s3_app_url"], true,
    params["appDownloadTimeout"],
    params["automate_session_id"],
    {
      app_type: "main",
      zip_align: zip_align.to_s,
      enable_apksigner: opts[:enable_apksigner],
      inject_app: params["inject_app"],
      install_experiment_enabled: opts[:install_experiment_enabled],
      is_minimized_flow: opts[:is_minimized_flow],
      force_resign: force_resign_app?(params).to_s,
      skip_resign: opts[:skip_resign],
      is_apks_archive: opts[:is_apks_archive],
      subregion_app_caching_enabled: opts[:subregion_app_caching_enabled],
      is_app_testing: opts[:is_app_testing]
    },
    params["user_id"]
  )
  mark_event_end('download_and_install_app', params[:event_hash])
  BrowserStack.logger.info "[App Automate] Download and install time: #{Time.now.to_i - t1}"

  # TODO: Verify/Test if sess_details is overriden with old vals.
  File.open("/tmp/duplicate_session_#{@device}", 'w') { |f| f.write(sess_details.to_json) }
end

def download_and_install_app( # rubocop:todo Metrics/ParameterLists, Metrics/AbcSize
  # rubocop:todo Style/OptionalBooleanParameter
  package, app_url, unlock = true, timeout = 30, session_id="", download_params={}, user_id=""
  # rubocop:enable Style/OptionalBooleanParameter
)
  device_obj = BrowserStack::AndroidDevice.new(@device, "Server.rb", BrowserStack.logger)
  test_framework = download_params[:browserstack_framework].to_s.downcase
  is_espresso_test_apk = (
    download_params.key?(:app_type) && download_params[:app_type] == "test" && test_framework != "maestro")
  file_extension = download_params[:is_apks_archive] ? 'apks' : 'apk'

  file_extension = 'zip' if test_framework == "maestro" && download_params[:app_type] == "test"

  app_identifier = get_folder_path_details_from_s3_url(app_url, download_params[:skip_resign], file_extension)

  BrowserStack.logger.info("[#{session_id}] Obtained from S3 Url - App Identifier: #{app_identifier}")
  download_folder = "#{APPS_DOWNLOAD_FOLDER}/#{app_identifier}"

  download_path, uuid = download_apps(download_folder, app_identifier, package, app_url, timeout,
                                      session_id, download_params, file_extension)

  # Need to recalculate the download_folder path as it might change in case of app injection
  download_folder = download_path.split("/#{uuid}.#{file_extension}")[0]

  # Touching the .session file before install_app, as install_app can take time and if delete_app_downloads
  # script ran in between, apk couldn't be found. This denotes, the session has picked up this apk file.
  FileUtils.touch("#{download_folder}/#{session_id}_#{uuid}.session")
  BrowserStack.logger.info("Session file: #{download_folder}/#{session_id}_#{uuid}.session touched.")

  begin
    if unlock
      mark_event_start("app_unlock_time", params[:event_hash])
      t1 = Time.now.to_i
      system("bash #{SCRIPT} device_screen '#{@device}'")
      BrowserStack.logger.info "[App Automate] Unlock time: #{Time.now.to_i - t1}"
      mark_event_end("app_unlock_time", params[:event_hash])
    end

    case download_params[:app_type]
    when "main"
      mark_event_start("app_install_time", params[:event_hash])
    when "test"
      mark_event_start("test_install_time", params[:event_hash])
    when "dependent"
      mark_event_start("dependent_app_install_time", params[:event_hash])
    end
    skip_install = should_skip_install?(params, download_params, device_obj, is_espresso_test_apk,
                                        package) ||
                  (test_framework == "maestro" && download_params[:app_type] == "test")
    force_resign = download_params[:force_resign] || "false"
    t1 = Time.now.to_f
    unless skip_install
      output, exit_status_code = install_apps(
        package, download_params, session_id, download_path, user_id, force_resign
      )
    end
    BrowserStack.logger.info "#{file_extension} installation output: #{output}, exit_status_code: #{exit_status_code}"
    if !skip_install && exit_status_code != 0
      app_install_failure = parse_app_install_output(output, exit_status_code, download_params[:is_apks_archive])
      raise app_install_failure.to_s
    end

    # .complete file denotes app has successfully been downloaded and the apk path is ready to use. The block should
    # be done after app got zip aligned and moved to actual location i.e. download_path to avoid race condition
    # download_path will contain zip_aligned apk if app is to be zip aligned (It will not contain unsigned apk)
    FileUtils.touch("#{download_folder}/#{uuid}.complete")
    BrowserStack.logger.info("Complete file: #{download_folder}/#{uuid}.complete touched.")

    app_install_time = ((Time.now.to_f - t1) * 1000).to_i

    case download_params[:app_type]
    when "main"
      mark_event_end("app_install_time", params[:event_hash])
    when "test"
      mark_event_end("test_install_time", params[:event_hash])
    when "dependent"
      mark_event_start("dependent_app_install_time", params[:event_hash])
    end

    push_to_cls(params, "#{download_params[:app_type]}_app_installed", '',
                { "app_type" => download_params[:app_type],
                  "app_url" => app_url, "app_install_time" => app_install_time,
                  "timeout" => timeout, "device_name" => @device_name, "instance_id" => params[:device],
                  "package" => package, "session_id" => session_id })
    kind_prefix = download_params[:is_apks_archive] ? 'apks' : 'app'
    BrowserStack.logger.info "[App Automate] Install time: #{app_install_time}"
    zombie_push("android", "#{kind_prefix}-#{download_params[:app_type]}-app-install-time", "", "",
                { app_install_time: app_install_time, device_name: @device_name, package: package },
                @device_name, session_id)

    download_path
  rescue Exception => e
    BrowserStack.logger.error "#{file_extension} installation failed: #{e.message}"
    # Cleaning up the app cached on platform
    BrowserStack.logger.info("Deleting the #{file_extension} caching folder on platform")
    FileUtils.rm_rf(download_folder.to_s)

    if is_subregion_caching_enabled?(download_params) && is_subregion_caching_error?(e)
      zombie_push('android', "#{file_extension}-app-caching-retry-hit", '', @device_name,
                  e.inspect[0, 300].to_s, @device.to_s, session_id)
        # Disable this flow and try again
      download_params[:subregion_app_caching_enabled] = false

      # Clear async app install files before retrying
      if params["enable_async_main_app_install"]
        BrowserStack.logger.info("Cleaning up the async app install before retrying")
        pid_file = "#{APP_INSTALL_PID_PREFIX_PATH}_#{@device}"
        pid = File.read(pid_file) if File.exist?(pid_file)
        system("kill #{pid}") if pid
        clean_async_app_install_files

        if File.exist?("#{STATE_FILES_DIR}/cleanupdone_#{@device}")
          raise AppInstallFailedException, "Cleanup is running"
        end
      end

      return download_and_install_app(package, app_url, unlock, timeout, session_id, download_params, user_id)
    end

    file_extension = download_params[:is_apks_archive] ? 'apks' : 'apk'
    zombie_push('android', "#{file_extension}-install-failed", '', @device_name,
                e.inspect[0, 300].to_s, @device.to_s, session_id)
    push_to_cls(params, "#{download_params[:app_type]}_app_install_failed", e.message.to_s,
                { "app_type" => download_params[:app_type], "app_url" => app_url, "app_install_start_time" => t1,
                  "device_name" => @device_name, "instance_id" => params[:device], "package" => package,
                  "session_id" => session_id })
    raise AppInstallFailedException, e
  end
end

def grant_permissions_for_flutter(app_bundle_id, session_id)
  android_toolkit_adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
  permissions = android_toolkit_adb.shell("dumpsys package #{app_bundle_id} |
  sed -n '/requested permissions:/,/install permissions:/{//!p;}' | grep 'android.permission'")
  permissions = permissions.split("\n")
  BrowserStack.logger.info "Permissions requested: #{permissions} device #{@device} session #{session_id}"
  permissions.each  do |permission|
    android_toolkit_adb.grant_permission(app_bundle_id, permission.gsub(": restricted=true", "").strip)
  rescue AndroidToolkit::ADB::ExecutionError => e
    case e.message
    when /not a changeable permission type/i, /Unknown permission/i
      #some permissions like internet,foreground service are granted by default
      # not a changeable permission type error is thrown for such permission
      # Ignoring such errors similar to Appium's autograntpermission
      BrowserStack.logger.info "Permissions grant failure ignored: #{permission}"
    else
      BrowserStack.logger.error "Permission grant failed: #{e.message}"
      zombie_key_value(
        platform: 'android',
        kind: 'permission-autogrant-failed',
        browser: @device_name,
        data: e.inspect[0, 300].to_s,
        device: @device.to_s,
        session: session_id
      )
    end
  end
end

def app_file
  "/tmp/app-#{@device}.apk"
end

def should_skip_install?(params, download_params, device_obj, is_espresso_test_apk, package)
  if params[:forceReinstall].to_s == "true" && device_obj.dedicated_cleanup?
    BrowserStack.logger.info "Received forceReinstall caps, so uninstalling package - #{package}"
    adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    app_checker = UnknownAppsChecker.new(@device, adb.model, BrowserStack.logger)
    app_checker.uninstall_apps([package])
    BrowserStack.logger.info "forceReinstall - uninstall package - #{package} done"
    return false
  end

  minimized_flow = download_params[:is_minimized_flow].to_s == "true"
  non_dedicated_minimized_flow = minimized_flow && params[:dedicated_minified_cleanup].to_s != "true"
  skip_install = download_params[:skip_install] || non_dedicated_minimized_flow
  if !is_espresso_test_apk \
    && device_obj.dedicated_cleanup? \
    && device_obj.fetch_all_apps.include?(package)
    current_version = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB).version_names(package).max
    desired_version = params[:app_testing_app_version]
    skip_install = Gem::Version.new(current_version) == Gem::Version.new(desired_version)
    BrowserStack.logger.info "Skip app reinstall #{skip_install} current #{current_version} desired #{desired_version}"
  end
  skip_install
rescue StandardError => e
  BrowserStack.logger.error "Error in should_skip_install? #{e}"
  false
end

def save_session_params(params)
  BrowserStack.logger.info "Saving #{params[:browserstack_framework]} session params for #{@device}"

  summary = {
    'build_id' => params[:build_id],
    'session_id' => params[:automate_session_id],
    'device' => params[:user_device_name],
    'deviceLogs' => params[:deviceLogs],
    'networkLogs' => params[:networkLogs],
    'networkLogsCaptureContent' => params[:networkLogsCaptureContent].to_s.downcase == 'true',
    'acceptInsecureCerts' => params[:acceptInsecureCerts],
    'video' => params[:video],
    'start_time' => Time.now.to_s,
    'duration' => '',
    'idle_timeout' => params[:idle_timeout],
    'error_reason' => '',
    'screenshots' => params[:upload_debug_screenshots],
    'coverage' => params[:coverage],
    'singleRunnerInvocation' => params[:singleRunnerInvocation],
    'useOrchestrator' => params[:useOrchestrator],
    'clearPackageData' => params[:clearPackageData],
    'use_rtc_app' => params[:use_rtc_app],
    'splitLogs' => params[:log_split]
  }
  summary['app_details'] = {
    'url' => params[:app_details_url],
    'bundle_id' => params[:app_details_bundle_id],
    'custom_id' => params[:app_details_custom_id],
    'version' => params[:app_details_version],
    'name' => params[:app_details_name]
  }
  summary['test_suite_details'] = {
    'url' => params[:test_suite_url],
    'bundle_id' => params[:test_suite_bundle_id],
    'custom_id' => params[:test_suite_custom_id],
    'name' => params[:test_suite_name],
    'instrumentation' => params[:test_suite_instrumentation_name],
    'is_cucumber_test_suite' => params[:is_cucumber_test_suite]
  }

  File.open("/tmp/#{params[:browserstack_framework]}_summary_#{@device}", 'w') do |f|
    f.write(summary.to_json)
  end

  # Session Summary File v2
  BrowserStack.logger.info "Saving #{params[:browserstack_framework]} session params for #{@device} [V2]"
  summary_v2 = {
    'build_id' => params[:build_id],
    'session_id' => params[:automate_session_id],
    # For the sake of using these keys as flags in Summary File,
    # Below keys will be deleted before uploading to s3.
    'device' => params[:user_device_name],
    'video' => params[:video],
    'screenshots' => params[:upload_debug_screenshots],
    'is_cucumber_test_suite' => params[:is_cucumber_test_suite],
    'use_rtc_app' => params[:use_rtc_app],
    'sessionData' => params[:is_session_data_enabled]
  }
  File.open("/tmp/#{params[:browserstack_framework]}_summary_#{@device}_v2", 'w') do |f|
    f.write(summary_v2.to_json)
  end

  rails_callback = "http://#{params[:rails_host]}/app-automate/session_done"
  timeout_sidekiq_worker_hashed_id = params[:timeout_sidekiq_worker_hashed_id]
  auth_key = params[:auth_key]

  File.open("/tmp/#{params[:browserstack_framework]}_callback_#{@device}", 'w') do |f|
    callback_hash = {
      build_id: summary['build_id'],
      session_id: summary['session_id'],
      timeout_sidekiq_worker_hashed_id: timeout_sidekiq_worker_hashed_id,
      auth_key: auth_key,
      rails_callback: rails_callback,
      device: @device,
      error_reason: ""
    }
    f.write(callback_hash.to_json)
  end
end

def set_hidden_policy(device_version)
  BrowserStack.logger.error "setting hidden_api_policy"
  adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
  begin
    adb.put_setting('global', 'hidden_api_policy', 1) if Gem::Version.new(device_version) >= Gem::Version.new(10)

    if Gem::Version.new(device_version) == Gem::Version.new(9)
      adb.put_setting('global', 'hidden_api_policy_pre_p_apps', 1)
      adb.put_setting('global', 'hidden_api_policy_p_apps', 1)
    end
  rescue AndroidToolkit::ADB::ExecutionError => e
    BrowserStack.logger.error "Exception while setting hidden_api_policy: #{e.message}"
  end
end

# Disable all kinds of animations of the device, this is sometimes used by users
# to reduce flakiness of the tests.
def disable_device_animations(device)
  unless device
    BrowserStack.logger.info "[App Automate] Unable to disable animations, device is undefined."
    return false
  end

  success = disable_animation(device, 'window_animation_scale') &&
            disable_animation(device, 'transition_animation_scale') &&
            disable_animation(device, 'animator_duration_scale')
  BrowserStack.logger.info "[App Automate] Disabling Animations for device: #{device} status = #{success}"
  success
end

def disable_animation(device, animation_type)
  command = "timeout 5 adb -s #{device} shell settings put global #{animation_type} 0"
  _, status = OSUtils.execute(command, true)

  unless status.exitstatus == 0
    BrowserStack.logger.info "Retrying #{command} as exit status #{status.exitstatus} is non-zero"
    _, status = OSUtils.execute(command, true)
  end

  status.exitstatus == 0
end

get(%r{/start_(espresso|fluttertest|maestro)_session}) do
  halt 500, 'Terminal already allocated!' unless pre_start(@device, params)

  device_name_without_downcase = params[:device_name].split("-")[0].gsub("__", " ")
  device_name = device_name_without_downcase.downcase
  BrowserStack.logger.info "Device name #{device_name}"

  stream_width = get_stream_width(device_name)
  device_orientation = params["orientation"] || "portrait"
  timezone = params["timezone"] if params["timezone"]

  device_version = @devices_json[@device]["device_version"]
  device_logger_helper = DeviceLogger.new(@device)
  device_logger_helper.init

  test_params = ""
  res = ""
  app_percy_params = nil
  test_suite_download_path = ""
  # rails http timeout = platform_timeout + rails buffer = espresso_setup_time + ESPRESSO_SESSION_TRIGGER_TIME
  #
  # HTTP timeout in rails is (platform_timeout + rails buffer). rails buffer is defined in rails.
  #
  # Timeout for espresso setup i.e. everything except triggering espresso_actions.sh and minor post-tasks,
  # is (platform_timeout - ESPRESSO_SESSION_TRIGGER_TIME).
  #
  # This ensures that we have at least ESPRESSO_SESSION_TRIGGER_TIME + rails buffer for triggering
  # espresso_actions.sh and minor post-tasks.
  espresso_setup_time = params[:platform_timeout].to_i - ESPRESSO_SESSION_TRIGGER_TIME
  BrowserStack.logger.info "Starting espresso setup. Will timing out in #{espresso_setup_time} seconds."

  Timeout.timeout espresso_setup_time do
    params[:browserstack_framework] = params[:captures].first # The capture group from the endpoint

    params[:event_hash] = {
      absolute_start_time: (Time.now.to_f * 1000).to_i
    }
    mark_event_start('assigned_variables', params[:event_hash])
    test_app_bundle_id = params[:test_suite_bundle_id].to_s
    default_port = @devices_json[@device]["port"]

    hosts = params["hosts"]
    is_app_testing = !params[:genre].nil? && params[:genre].to_s.eql?('app_automate')
    app_bundle_id = params[:app_details_bundle_id].to_s

    initialize_feature_usage
    capture_devicelogs = false
    capture_video = false
    File.open("/tmp/sessionis_#{@device}", "w") { |f| f.write(params[:browserstack_framework]) }

    set_user_specified_prop(params["setprop"], params["automate_session_id"]) if params["setprop"]
    # Enable dns logs capture to detect possible crypto mining
    CryptoMiningDetectionHelper.new(@device, BrowserStack.logger).enable_network_logging

    test_params = get_test_params(params[:test_params]) unless params[:test_params].nil?
    save_session_params(params)
    sess_details = { genre: params["genre"], host: params["host"], session_id: params["automate_session_id"],
                     user_id: params["user_id"], packageName: app_bundle_id, device: @device,
                     testPackageName: test_app_bundle_id,
                     android_version: @devices_json[@device]["device_version"] }

    params["app_automate_custom_params"] = begin
      JSON.parse(params["app_automate_custom_params"])
    rescue StandardError
      {}
    end

    logcat_buffer_data = params["app_automate_custom_params"]["logcat_buffer_data"]
    if logcat_buffer_data
      LogcatBufferManager.new(
        @device,
        BrowserStack.logger,
        get_session_id_from_params(params),
        logcat_buffer_data
      ).set_buffer
    end

    app_bundle_ids = [app_bundle_id]
    app_bundle_ids.push(test_app_bundle_id) if params[:is_multi_module]
    sess_details[:test_framework_log_file] = "/tmp/#{params[:browserstack_framework]}_instrumentation_#{@device}"

    if params["other_apps"]
      other_app_bundle_ids = get_dependent_app_bundle_ids(params["other_apps"])
      sess_details[:other_app_bundle_ids] = other_app_bundle_ids
      (app_bundle_ids << other_app_bundle_ids).flatten!
    end

    BrowserStack.logger.info("Set logcat capture format #{params['deviceLogs']} #{params[:log_split]}")
    if is_true?(params[:log_split])
      sess_details[:logcat_capture_format] = %w[UTC year]
      BrowserStack.logger.info("Setting logcat capture format #{sess_details[:logcat_capture_format]}")
    end

    File.open("/tmp/duplicate_session_#{@device}", 'w') { |f| f.write(params.merge(sess_details).to_json) }

    params["packages"] = FormatPackagesParam.create_package_params(app_bundle_ids)
    WriteSessionInfo.new(BrowserStack.logger, @device, params, params[:browserstack_framework]).save
    mark_event_end('assigned_variables', params[:event_hash])

    if params["disableAnimations"].to_s == 'true'
      mark_event_start('disable_animations', params[:event_hash])
      success = disable_device_animations(@device)
      update_feature_usage("disableAnimations", success)
      mark_event_end('disable_animations', params[:event_hash])
    end

    if params["biometricFrameworkSession"].to_s == 'true'
      AppInjection.touch_app_injection_state_file(@device, ENABLE_BIOMETRIC_FRIDA)
      disableNewBiometricFailureFlow = params["app_automate_custom_params"] &&
        params["app_automate_custom_params"]["disableNewBiometricFailureFlow"].to_s == "true"
      unless disableNewBiometricFailureFlow
        AppInjection.touch_app_injection_state_file(@device,
                                                    ENABLE_MULTIPLE_BIOMETRIC_FAILURES)
      end
    end

    mark_event_start('set_hidden_policy', params[:event_hash])
    set_hidden_policy(device_version)
    mark_event_end('set_hidden_policy', params[:event_hash])

    main_app_zip_align = params[:app_zip_align].to_s == 'true'
    test_suite_zip_align = params[:test_suite_zip_align].to_s == 'true'
    enable_android_watcher = params["app_automate_custom_params"]["enable_android_watcher"].to_s == "true"
    enable_apksigner = params["app_automate_custom_params"]["enable_apksigner"].to_s
    params[:use_scrcpy_for_video_recording] = \
      params["app_automate_custom_params"]["use_scrcpy_for_video_recording"].to_s
    internet_over_usb = params["internet_over_usb"] = params["app_automate_custom_params"]["internet_over_usb"].to_s
    params[:limit_network_usage] = params["app_automate_custom_params"]["limit_network_usage"].to_s
    subregion_app_caching_enabled =
      params["app_automate_custom_params"]["subregion_app_caching_proxy_enabled"].to_s == "true"
    # more info at https://browserstack.atlassian.net/browse/MOB-9373
    internet_over_usb = "true" if params["user_id"] && params["user_id"].to_s == "3635234"
    app_type = "main"

    begin
      unless params[:is_multi_module]
        t1 = Time.now.to_i
        download_and_install_app(
          app_bundle_id, params[:app_s3_url].to_s, true, params[:app_download_timeout],
          params["automate_session_id"],
          {
            app_type: app_type,
            zip_align: main_app_zip_align,
            enable_apksigner: enable_apksigner,
            inject_app: params["inject_app"],
            force_resign: !params["inject_app"].nil? || params[:biometricFrameworkSession],
            subregion_app_caching_enabled: subregion_app_caching_enabled,
            is_app_testing: is_app_testing,
            browserstack_framework: params[:browserstack_framework]
          }, params["user_id"]
        )
        BrowserStack.logger.info(
          "[App Automate #{params[:browserstack_framework].capitalize}] "\
          "App Download and install time: #{Time.now.to_i - t1}"
        )
      end

      app_type = "test"
      t1 = Time.now.to_i
      test_suite_download_path = download_and_install_app(
        test_app_bundle_id, params[:test_suite_s3_url].to_s, false,
        params[:test_suite_download_timeout], params["automate_session_id"],
        {
          app_type: app_type, zip_align: test_suite_zip_align,
          enable_apksigner: enable_apksigner,
          force_resign: !params["inject_app"].nil? || params[:biometricFrameworkSession],
          subregion_app_caching_enabled: subregion_app_caching_enabled,
          is_app_testing: is_app_testing,
          browserstack_framework: params[:browserstack_framework]
        }, params["user_id"]
      )
      BrowserStack.logger.info(
        "[App Automate #{params[:browserstack_framework].capitalize}] Test Download "\
        "and install time: #{Time.now.to_i - t1}"
      )
    rescue FireCMDException => e
      BrowserStack.logger.error(
        "[FIRE_CMD_ERROR] [APP_AUTOMATE_#{params[:browserstack_framework].upcase}_ERROR] App "\
        "download and install failed: #{e.message} backtrace: #{e.backtrace.join('\n')}."
      )
      kind = e.kind.nil? ? "#{app_type}_app_download_install_failure" : e.kind
      return 500, { error: e.message.to_s, kind: kind, type: e.type.to_s }.to_json
    rescue Exception => e
      BrowserStack.logger.error(
        "[FIRE_CMD_ERROR] [APP_AUTOMATE_#{params[:browserstack_framework].upcase}_ERROR] App "\
        "download and install failed: #{e.message} backtrace: #{e.backtrace.join('\n')}."
      )

      return 500, { error: e.message.to_s, kind: "#{app_type}_app_download_install_failure",
                    type: BROWSERSTACK_ERROR_STRING }.to_json
    end

    if other_app_bundle_ids
      begin
        mark_event_start('other_apps_download_and_install_time', params[:event_hash])
        install_dependent_apps(params["other_apps"], params["automate_session_id"], enable_apksigner, params)
        mark_event_end('other_apps_download_and_install_time', params[:event_hash])
        update_feature_usage("otherApps", true)
      rescue FireCMDException => e
        BrowserStack.logger.error(
          "[FIRE_CMD_ERROR] [APP_AUTOMATE_#{params[:browserstack_framework].upcase}_ERROR] Other "\
          "Apps download and install failed: #{e.message} backtrace: #{e.backtrace.join('\n')}."
        )

        kind = e.kind || "other_apps_install_failure"
        update_feature_usage("otherApps", false, e.message[0, 100])
        return 500, { error: e.message.to_s, kind: kind, type: e.type.to_s }.to_json
      rescue Exception => e
        BrowserStack.logger.error(
          "[FIRE_CMD_ERROR] [APP_AUTOMATE_#{params[:browserstack_framework].upcase}_ERROR] Other "\
          "Apps download and install failed: #{e.message} backtrace: #{e.backtrace.join('\n')}."
        )

        update_feature_usage("otherApps", false, e.message[0, 100])
        return 500, { error: e.message.to_s, kind: "other_apps_install_failure",
                      type: BROWSERSTACK_ERROR_STRING }.to_json
      end
    end

    custom_media_manager = CustomMediaManager.new(
      @device, @feature_usage, SERVER_LOG, BrowserStack.logger, BrowserStack.logger.params
    )

    if params["custom_media"]
      update_feature_usage("customMedia", true)
      mark_event_start('custom_media_download_and_push_time', params[:event_hash])
      begin
        custom_media_manager.download_and_push(params)
      rescue Exception => e
        BrowserStack.logger.error(
          "[FIRE_CMD_ERROR] [APP_AUTOMATE_#{params[:browserstack_framework].upcase}_ERROR] "\
          "Custom media download and push failed: #{e.message} backtrace: #{e.backtrace.join('\n')}."
        )

        update_feature_usage("customMedia", false, "custom media download and push failed")
        return 500, { error: "Custom Media Files download/update failed: #{e.message}" }.to_json
      ensure
        mark_event_end('custom_media_download_and_push_time', params[:event_hash])
      end
    end

    unless enable_android_watcher
      browserstack_watcher_manager =
        WatcherHelper.new(@device, params["automate_session_id"], params[:genre], BrowserStack.logger)
      browserstack_watcher_manager.stop
    end

    copy_file("/tmp/duplicate_session_#{@device}", "#{STATE_FILES_DIR}/session_#{@device}")

    t1 = Time.now.to_i
    if params["deviceLogs"].to_s == "true"
      device_logger_helper.start

      capture_devicelogs = true
      BrowserStack.logger.info(
        "[App Automate #{params[:browserstack_framework].capitalize}] device log "\
        "enable: #{Time.now.to_i - t1}"
      )
    else
      update_feature_usage("deviceLogs", "disabled", "")
    end
    save_logs_params(@device, params, "device")

    # percy_cli_port = 4{device_port}
    params[:percy_cli_port] = CLIManager.cli_port(@devices_json[@device]['port']) if params['app_percy']

    mark_event_start('local_tunnel_setup', params[:event_hash])
    tunnel_setup(params) if !hosts.nil? && !hosts.empty? && (params[:allowDeviceMockServer].to_s != "true")
    mark_event_end('local_tunnel_setup', params[:event_hash])

    t1 = Time.now.to_i
    mark_event_start('custom_vpn_tunnel_setup_time', params[:event_hash])
    tun_counter = default_port.to_i - 8078
    tun_ip = calc_usb_tunnel_ip(default_port)

    vpn_disabled = false
    apps_string = ''
    if (params[:allowDeviceMockServer].to_s != "true") && internet_over_usb != "true"
      vpn_disabled = true
      script_logger = script_logger_args("#{File.basename(SCRIPT)}_stop_vpn")
      BrowserStack.logger.info "Stopping VPN for App "\
        "Automate(#{params[:browserstack_framework].capitalize}) Session because local "\
        "is set to true and internet_over_usb is not enabled"
      cmd = "bash #{SCRIPT} stop_vpn #{@device} #{tun_counter} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\" >> /var/log/browserstack/selenium_#{@device}.log; done"
      system(cmd)
      push_to_cls(
        params, "tunnel_setup_done", '',
        {
          "test_data" => params.to_json,
          "device_name" => @device_name,
          "instance_id" => params[:device]
        }
      )
    else
      script_logger = script_logger_args("#{File.basename(SCRIPT)}_redovpn")
      apps_string = app_bundle_ids.join("/")
      BrowserStack.logger.info "Re establishing tunnel for #{apps_string}"

      # touching the file, to reset vpn in cleanup
      system("touch /tmp/app_internet_via_usb_#{@device}") if internet_over_usb == "true"
      system("touch /tmp/internet_via_usb_#{@device}")

      cmd = "bash #{SCRIPT} redovpn #{@device} #{tun_ip} #{apps_string} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\" >> /var/log/browserstack/selenium_#{@device}.log; done"
      system(cmd)
    end
    mark_event_end('custom_vpn_tunnel_setup_time', params[:event_hash])
    check_and_log_usb_internet(params, vpn_disabled)
    BrowserStack.logger.info "[App Automate #{params[:browserstack_framework].capitalize}] "\
      "Tunnel time: #{Time.now.to_i - t1}"

    if params[:networkSimulation].to_s == "true" || params[:limit_network_usage].to_s == "true"
      mark_event_start('network_simulation_time', params[:event_hash])
      setup_network_simulation(params, @device, params["automate_session_id"], apps_string)
      mark_event_end('network_simulation_time', params[:event_hash])
    end

    write_rtc_params(params)
    if params[:video].to_s == "true"
      mark_event_start('start_video', params[:event_hash])
      capture_video = true
      width = params[:video_width].nil? ? VIDEO_DEFAULT_WIDTH : params[:video_width]
      height = params[:video_height].nil? ? VIDEO_DEFAULT_HEIGHT : params[:video_height]
      res = "#{width}x#{height}"
      save_video_params(@device, params)
      mark_event_end('start_video', params[:event_hash])
    end

    begin
      # perform_google_login step should always be done before start_session since we set the orientation
      # in start_session and this step reverts the orientation back to portrait
      perform_google_login(params, device_name, device_version)
    rescue GoogleLoginException => e
      BrowserStack.logger.error(
        "[FIRE_CMD_ERROR] [App Automate #{params[:browserstack_framework].capitalize}] Error "\
        "while google login, error: #{e.message} backtrace: #{e.backtrace.join('\n')}."
      )

      update_feature_usage("playstoreLogin", false, e.message[0, 100])
      return 500, { error: e.message.to_s, kind: e.kind, type: e.type.to_s }.to_json
    rescue Exception => e
      BrowserStack.logger.error "[FIRE_CMD_ERROR] "\
        "[App Automate #{params[:browserstack_framework].capitalize}] Error while google login, "\
        "error: #{e.message} backtrace: #{e.backtrace.join('\n')}"
      update_feature_usage("playstoreLogin", false, e.message[0, 100])
      return 500, { error: "fire cmd failed. Error: Start Failed: #{e.message}", kind: "google_login_failure",
                    type: BROWSERSTACK_ERROR_STRING }.to_json
    end

    language_or_locale = params[:language] || params[:localization]
    # We don't want to set locale before starting video to avoid media projection popup in non english language
    if language_or_locale && params[:use_rtc_app] != "v2"
      BrowserStack.logger.info "Setting device language to #{language_or_locale}"
      success = set_device_locale(@device, language_or_locale, BrowserStack.logger, { async: false })
      params[:language] ? update_feature_usage("language", success) : update_feature_usage("locale", success)
    elsif language_or_locale && params[:use_rtc_app] == "v2"
      params[:language] ? @feature_usage.delete('language') : @feature_usage.delete('locale')
    end

    # if media could not be inserted into the mediastore by this time we will throw firecmd error
    if params["custom_media"]
      begin
        custom_media_manager.verify_insertion_to_mediastore(params)
      rescue StandardError => e
        BrowserStack.logger.error("[FIRE_CMD_ERROR] CustomMediaManager could not be inserted into Android mediastore: "\
                                  "#{e.message} backtrace: #{e.backtrace.join('\n')}.")
        update_feature_usage("customMedia", false, "custom media insertion to mediastore failed")
        kind = e.kind.nil? ? "custom_media_verification_failed" : e.kind
        return 500, { error: e.message.to_s, kind: kind,
                      type: e.type.to_s }.to_json
      ensure
        FileUtils.rm_rf("/tmp/custom_media_insertion_status_#{@device}.txt", secure: true)
      end
    end

    if is_app_testing && !disable_app_profiling?(params)
      PerformanceStatistics.new(@device, BrowserStack.logger).start(params[:automate_session_id])
    end
    #v2 logic for app profiling
    begin
      if is_app_testing && params[:app_profiling].to_s == "true"
        mark_event_start('start_mcspt_session', params[:event_hash])
        code, response = MCSPT.start_session_async(
          params[:automate_session_id].to_s,
          params["user_id"].to_s,
          @device.to_s,
          params[:app_details_bundle_id].to_s,
          false,
          nil,
          app_automate: true
        )
        BrowserStack.logger.info("Started CSPT session for espresso, code: #{code} response: #{response.to_json}")
        mark_event_end('start_mcspt_session', params[:event_hash])
      end
    rescue Exception => e
      BrowserStack.logger.error("An error occurred while starting CSPT session in espresso: #{e.message}")
      BrowserStack.logger.debug(e.backtrace.join("\n"))
    end
    if  params[:browserstack_framework] == "fluttertest" && params[:autoGrantPermissions].to_s.downcase == "true" &&
      Gem::Version.new(device_version) >= Gem::Version.new(6)
      #Permissions require granting only from Android os 6
      start_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)
      grant_permissions_for_flutter(app_bundle_id, params["automate_session_id"])
      end_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)
      BrowserStack.logger.info("Autogranting flutter permissions took #{end_time - start_time} seconds")
    end

    # if app_percy_params pasing throws any error send an event to pager
    if params['app_percy']
      begin
        app_percy_params = hash_inspect_to_hash(params['app_percy'])
      rescue StandardError => e
        zombie_push(
          'android',
          'app_percy_espresso_parse_params_0',
          "App Percy Espresso Failed: #{redact_percy_token(e.message, 'PERCY_TOKEN')}",
          '',
          {
            "team" => 'app-percy',
            "app_percy_params" => redact_percy_token(params['app_percy'], 'PERCY_TOKEN')
          },
          @device,
          get_session_id_from_params(params)
        )
      end
    end

    if params[:latitude] && params[:longitude]
      set_gpslocation(
        params[:latitude], params[:longitude], params["genre"], params["automate_session_id"]
      )
    end

    if params[:test_params]
      parsed_test_params = begin
        JSON.parse(params[:test_params])
      rescue StandardError
        {}
      end
      if params[:is_session_data_enabled].to_s.downcase == 'true' ||
        (parsed_test_params["cucumberOptions"] && parsed_test_params["cucumberOptions"]["plugins"])
        FileUtils.mkdir_p("/tmp/#{@device}/assets_#{params['automate_session_id']}")
        if Gem::Version.new(device_version) <= Gem::Version.new(10)
          adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
          begin
            adb.grant_permission(app_bundle_id, STORAGE_PERMISSIONS)
          rescue AndroidToolkit::ADB::ExecutionError => e
            BrowserStack.logger.error "Exception while granting permissions: #{e.message}"
          end
        end
      end
    end

    update_feature_usage("networkLogs", "disabled", "") if params[:networkLogs].to_s != "true"
  end
rescue Timeout::Error => e
  BrowserStack.logger.error("FireCMD could not complete within #{espresso_setup_time} seconds.")
  push_to_cls(
    params, "framework_firecmd_timeout",
    e.message, { framework: 'espresso', time_available: espresso_setup_time }
  )

  return 500, {
    error: "FireCMD failed to complete within #{espresso_setup_time} seconds. Error: Start Failed: #{e.message}",
    kind: "firecmd_timeout",
    type: BROWSERSTACK_ERROR_STRING
  }.to_json
else
  BrowserStack.logger.info "Espresso setup complete within #{espresso_setup_time}. Triggering espresso session now."

  # espresso_trigger_time = time for triggering espresso_actions.sh and minor post-tasks
  # Can the minor post-tasks (like fetch timezome and orientation) be moved to espresso setup?
  # Why do we need to do them after triggering the script?
  #
  # espresso_trigger_time = session_start + minor post-tasks
  mark_event_start('espresso_trigger_time', params[:event_hash])
  mark_event_start('session_start', params[:event_hash])

  script_logger = script_logger_args_v2(
    "#{File.basename(ESPRESSO_SCRIPT)}_start_#{params[:browserstack_framework]}_session"
  )
  BrowserStack.logger.info "value of script_logger_args_v2: #{script_logger}"

  action = "start_session"

  cmd = [
    "bash", ESPRESSO_SESSION_TRIGGER, ESPRESSO_SCRIPT, action, @device.to_s, params[:build_id].to_s, \
    params[:automate_session_id].to_s, stream_width.to_s, device_orientation.to_s, \
    timezone.to_s, test_params.to_s, res.to_s, params[:rails_host].to_s, \
    params[:rails_api_host].to_s, device_name_without_downcase.to_s, device_version.to_s, \
    params[:use_scrcpy_for_video_recording].to_s, \
    params[:browserstack_framework].to_s, test_suite_download_path.to_s, \
    app_percy_params.to_json, \
    params[:use_rtc_app].to_s, params[:testhub_jwt].to_s, \
    params[:testhub_build_hashed_id].to_s, \
    params[:test_observability].to_s, \
    params[:accessibility].to_s, \
    params[:testhub_accessibility_token].to_s,  \
    params[:testhub_polling_timeout].to_s, \
    params[:testhub_scanner_version].to_s, \
    params[:includeTagsInTestingScope].to_s, \
    params[:excludeTagsInTestingScope].to_s, \
    params[:is_session_data_enabled].to_s, \
    params["app_automate_custom_params"]["session_asset_max_size_limit"].to_s, \
    params["app_automate_custom_params"]["session_asset_max_file_count"].to_s, \
    { err: %i[child out] }
  ]

  cmd_out = "while read line; do datetime=$(date -u '+%Y-%m-%d %H:%M:%S'); "\
            "echo [\"$datetime\"]#{script_logger} \"$line\"; done"

  # Run in a different scope to avoid sharing scope with Puma
  if FEDORA_VERSION >= 35
    cmd = [
      "sudo",
      "systemd-run",
      "--property=User=ritesharora",
      "--working-directory=#{BS_DIR}/mobile",
      "--unit=espresso_#{action}_#{@device}_#{params[:automate_session_id]}"
    ] + cmd
  end

  cmd_string = "#{cmd.inspect} | #{cmd_out} &"

  begin
    Open3.pipeline_start(cmd, cmd_out)
  rescue Exception => e
    BrowserStack.logger.error "Command #{cmd_string} failed with #{e.message}"
  end

  if is_automate_or_app_automate(params["genre"])
    push_to_cls(params, "session_start_done", "",
                { "device" => @device.to_s, "instance_id" => params[:device], "stream_width" => stream_width,
                  "device_orientation" => device_orientation, "session" => params[:automate_session_id],
                  "resolution" => res })
  end

  mark_event_end('session_start', params[:event_hash])
  BrowserStack.logger.info "[App Automate #{params[:browserstack_framework].capitalize}] "\
    "Starting test with command: #{cmd_string}"

  # TODO: Check the impact and fix the race condition
  # There may be a race condition here. Are we sure that timezone and orientation will be set when we read them here?
  if params["timezone"]
    # timezone is set in start_session
    device_timezone = get_timezone(@device)
    BrowserStack.logger.info "[App Automate] timezone: #{timezone} device_timezone: #{device_timezone}"
    success = (timezone == device_timezone)
    update_feature_usage("timezone", success)
  end

  if params["orientation"]
    # orientation is set in start_session
    current_orientation = get_orientation_from_settings(@device)
    success = (current_orientation == device_orientation.to_s.downcase)
    update_feature_usage("orientation", success)
  end

  monitor_device_logger_metric(@device, get_session_id_from_params(params))

  mark_event_end('firecmd_time', params[:event_hash])
  mark_event_end('espresso_trigger_time', params[:event_hash])

  BrowserStack.logger.info "[APP AUTOMATE FRAMEWORK TIME STATS #{params[:event_hash]}"
  send_to_eds(params, EdsConstants::APP_AUTOMATE_TEST_SESSIONS) if params[:edsHost] &&
                                                                   params[:edsPort] && params[:edsKey]

  return {}.to_json
end

get '/timeout_session' do
  params[:browserstack_framework] ||= 'espresso'
  clean_in_session(@device)
  stop_proxy_checker(@device)
  device_name_without_downcase = params[:device_name].to_s.split("-")[0].gsub("__", " ")
  device_version = @devices_json[@device]["device_version"]
  script_logger = script_logger_args("#{File.basename(ESPRESSO_SCRIPT)}_timeout_session")
  action = "timeout_session"

  # Running the command in foreground, as the command_status always returns true when run in background.
  # Timeout to avoid blocking. PIPESTATUS[@] is an array, that contains exit code of piped commands.
  cmd = [
    "timeout", "10", "bash", ESPRESSO_SESSION_TRIGGER, ESPRESSO_SCRIPT, action, @device.to_s,
    params[:build_id].to_s, params[:automate_session_id].to_s, device_name_without_downcase.to_s,
    device_version.to_s, params[:browserstack_framework].to_s, { err: %i[child out] }
  ]
  cmd_out = "while read line; do echo #{script_logger} \"$line\"; done"

  # Run in a different scope to avoid sharing scope with Puma
  if FEDORA_VERSION >= 35
    cmd = [
      "sudo",
      "systemd-run",
      "--property=User=ritesharora",
      "--working-directory=#{BS_DIR}/mobile",
      "--unit=espresso_#{action}_#{@device}_#{params[:automate_session_id]}"
    ] + cmd
  end

  cmd_string = "#{cmd.inspect} | #{cmd_out}"
  begin
    cmd_exitstatus = Open3.pipeline(cmd, cmd_out)[0].exitstatus
  rescue Exception => e
    BrowserStack.logger.error "App Live command #{cmd_string} failed with #{e.message}"
    cmd_exitstatus = ""
  end
  if cmd_exitstatus == 0
    BrowserStack.logger.info "[App Automate #{params[:browserstack_framework].capitalize}] "\
      "Stopped test with command: #{cmd_string}"
    return 200, {}.to_json
  end
  BrowserStack.logger.info "[App Automate #{params[:browserstack_framework].capitalize}] "\
    "Could not timeout session with command: #{cmd_string}. ExitStatus: #{cmd_exitstatus}"
  return 500, { error: "Could not timeout session. Command failed." }.to_json
end

get(%r{/inform_(?<test_fw>espresso|fluttertest|maestro)_done/(?<device>\w+)}) do
  device = params[:device]
  params[:browserstack_framework] = params[:test_fw]

  session_data = JSON.parse(File.read("/tmp/#{params[:browserstack_framework]}_callback_#{params[:device]}"))
  rails_endpoint = session_data["rails_callback"]
  uri = URI.parse(rails_endpoint)
  cls_params = { genre: "app_automate", automate_session_id: session_data["session_id"], user_id: nil }
  MCSPT.stop_session_running_on_device_async(device.to_s, cancelled: false)
  clean_in_session(device)
  stop_proxy_checker(device)

  BrowserStack.logger.info "Informing Rails about #{params[:browserstack_framework]} session completed"
  BrowserStack.logger.info "Posting to #{rails_endpoint}"
  BrowserStack.logger.info "Data is #{session_data}"
  response = BrowserStack::HttpUtils.send_post(rails_endpoint, session_data, nil, true,
                                               { retry_count: 3, retry_interval: 5 })
  BrowserStack.logger.info "Response code: #{response.code}, Body: #{response.body}"

  push_to_cls(
    cls_params,
    "#{params[:browserstack_framework]}_session_inform_done", "",
    {
      "response" => response.code.to_s,
      "session_data" => session_data.to_json,
      "device" => device
    }
  )
  if response.code.to_i != 200
    zombie_push(
      "android", "#{params[:browserstack_framework]}-post-error",
      "Post unsuccessful with code #{response.code}", "",
      { "data" => uri.host.to_s }, device
    )
  end
  begin
    FileUtils.rm("/tmp/#{params[:browserstack_framework]}_callback_#{device}")
  rescue StandardError
    nil
  end
rescue Exception => e
  BrowserStack.logger.info "Received error: #{e.message}"
  zombie_push('android', "#{params[:browserstack_framework]}-done-error", '', '', e.inspect[0, 300].to_s, device)
  push_to_cls(
    cls_params, "#{params[:browserstack_framework]}_session_inform_failed", e.message.to_s,
    {
      "error_data" => e.inspect.to_s,
      "response" => response.code.to_s,
      "session_data" => session_data.to_json,
      "device" => device
    }
  )
end

def perform_google_login(params, device_name, device_version)
  return unless params["app_store_username"] && params["app_store_password"]

  username = params["app_store_username"].downcase
  password = URI.unescape(params["app_store_password"]) # FIXME: URI.unescape is obsolete and should not be used.
  base64_encoded_pass = Base64.strict_encode64(password).strip

  BrowserStack.logger.info '[App Automate] Starting google login'
  mark_event_start('play_store_login_time', params[:event_hash])

  FileUtils.touch("/tmp/google_login_#{params[:automate_session_id]}")
  # force stopping playstore as we have playstore automation to switch off auto updates of app in play-store and
  # after that we press home button which was breaking the automation of play-store login
  BrowserStack.logger.info "[App Automate] google login Force stopping Playstore"
  force_stop_cmd = `timeout 15 adb -s #{@device} shell am force-stop com.android.vending`
  BrowserStack.logger.info "Force stopping Playstore done with status code: #{$CHILD_STATUS.exitstatus}"
  @try = 0
  begin
    @try += 1
    BrowserStack.logger.info "[App Automate] Playstore login attempt: #{@try}"
    cmd_str = if Gem::Version.new(device_version) >= Gem::Version.new(11)
                "timeout 180 adb -s #{@device} shell \"am instrument -w -r "\
                " -e debug false "\
                " -e class 'com.browserstack.uiautomation.GooglePlayStoreLoginTest' "\
                " -e 'username' '#{username}' "\
                " -e 'password64' '#{base64_encoded_pass}' "\
                " -e 'device_name' '#{device_name.gsub(/\s+/, '')}'"\
                " -e 'os_version' '#{device_version}'"\
                " com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner\""
              else
                "timeout 150 adb -s #{@device} shell "\
                " uiautomator runtest /data/local/tmp/google-play-store-login.jar "\
                " -c com.browserstack.googleplaystore.GooglePlayStoreLogin "\
                " -e 'username' '#{username}'"\
                " -e 'password64' '#{base64_encoded_pass}'"\
                " -e 'device_name' '#{device_name.gsub(/\s+/, '')}'"\
                " -e 'os_version' '#{device_version}'"
              end
    cmd = `#{cmd_str}`
    instrument_automation("Google_PlayStore_Login_Test", cmd, @device)

    BrowserStack.logger.info "[App Automate] Output From Playstore Automation for Session "\
                             "#{params[:automate_session_id]}: #{cmd}"
    if $CHILD_STATUS.exitstatus == 124
      GooglePlaystoreLoginOutput.new("command timeout")
    else
      GooglePlaystoreLoginOutput.new(cmd.to_s)
    end

    verification_try = 0
    mark_event_start('play_store_login_verification_time', params[:event_hash])
    begin
      verification_try += 1
      BrowserStack.logger.info "[App Automate] verify_google_login attempt: #{verification_try}"
      verify_google_login(username)
    rescue GoogleLoginException => e
      retry if verification_try < 2 && ["failed to read DB", "user not logged in"].include?(e.message)
      raise e
    ensure
      mark_event_end('play_store_login_verification_time', params[:event_hash])
      BrowserStack.logger.info "[App Automate] verify_google_login time: "\
                               "#{params[:event_hash]['play_store_login_verification_time']}"
    end
  rescue GoogleLoginException => e
    retry if e.message == "user not logged in" && @try < 2
    BrowserStack.logger.error "[App Automate] perform google login error #{e.message} #{e.kind}"
    mark_event_end('play_store_login_time', params[:event_hash]) # This is put here to mark it end in case of exception
    if is_automate_or_app_automate(params["genre"])
      push_to_cls(params, "google-login-error", e.message.to_s,
                  { "data" => params.to_json, "error_kind" => e.kind.to_s, "device" => @device.to_s,
                    "instance_id" => params[:device] })
    end
    update_feature_usage("playstoreLogin", false, e.kind)
    raise e
  end

  mark_event_end('play_store_login_time', params[:event_hash])
  update_feature_usage("playstoreLogin", true)
  BrowserStack.logger.info "[App Automate] google login done"
end

def get_recipient_name_from_email(email_string)
  # username can be <NAME_EMAIL> or <EMAIL> or
  # random.user.name@gmail.<NAME_EMAIL>
  # Hence we will apply rules as per : https://browserstack.atlassian.net/browse/AA-4705?focusedCommentId=197944
  recipient_name = email_string.to_s.strip.split("@")[0]
  recipient_name = recipient_name.to_s.strip.split("+")[0]
  recipient_name.to_s.strip.delete(".")
end

def verify_google_login(username)
  device = BrowserStack::AndroidDevice.new(@device, "Server.rb", BrowserStack.logger)
  #TODO: use the method in account_helper if it gives same result everytime as rooted method
  account_helper = AccountHelper.new(@device, BrowserStack.logger, BrowserStack.logger.params)
  account_present = account_helper.specific_google_account_present?(username)
  if device.rooted?
    begin
      verify_google_login_rooted(username)
      #rooted method passed
      zombie_push('android', "google-account-check", "", "", "account_present_rooted: true, "\
                                                             "account_present_non_rooted: #{account_present}", @device)
    rescue GoogleLoginException => e
      # rooted failed
      zombie_push('android', "google-account-check", "", "", "account_present_rooted: false, "\
                                                             "account_present_non_rooted: #{account_present}", @device)
      raise e
    end
  else
    verify_google_login_non_rooted(username)
  end
end

def verify_google_login_rooted(username)
  # the accounts.db can be present on any of the below two paths, therefore, trying to copying it from both the paths
  command = "cp /data/system_ce/0/accounts_ce.db /sdcard/accounts.db || "\
            "cp /data/system/users/0/accounts.db /sdcard/accounts.db"
  bsrun_output = `sh #{SCRIPT} run_as_root #{@device} "#{command}"`
  exit_code = $CHILD_STATUS.exitstatus
  BrowserStack.logger.info "[verify_google_login] #{@device} exit code of run_as_root : #{exit_code} and output : "\
                           "#{bsrun_output}"
  raise GoogleLoginException.new("bsrun failed", "google-play-store-login", BROWSERSTACK_ERROR_STRING) if exit_code != 0

  output = `timeout 15 adb -s #{@device} pull /sdcard/accounts.db /tmp/accounts_#{@device}.db`
  exit_code = $CHILD_STATUS.exitstatus
  BrowserStack.logger.info "[verify_google_login] #{@device} exit code after pulling the db from device : "\
                           "#{exit_code} and output : #{output}"
  if exit_code != 0
    raise GoogleLoginException.new(
      "adb DB pull failed", "google-play-store-login", BROWSERSTACK_ERROR_STRING
    )
  end

  input_recipient = get_recipient_name_from_email(username)
  BrowserStack.logger.info "[verify_google_login] input username : #{username} input recipient name "\
                           ": #{input_recipient}"
  logged_in_usernames = `timeout 15 /usr/bin/sqlite3 /tmp/accounts_#{@device}.db "select name from accounts"`
  BrowserStack.logger.info "[verify_google_login] The Following emails were found to be logged in: "\
                           "#{logged_in_usernames}"
  exit_code = $CHILD_STATUS.exitstatus

  logged_in_usernames = logged_in_usernames.to_s.strip.split
  BrowserStack.logger.info "[verify_google_login] #{@device} exit code after reading from DB : #{exit_code} output : "\
                           "#{logged_in_usernames} and input recipient : #{input_recipient}"
  if exit_code != 0
    raise GoogleLoginException.new(
      "failed to read DB", "google-play-store-login", BROWSERSTACK_ERROR_STRING
    )
  end
  unless logged_in_usernames.any? do |name|
    name && input_recipient && get_recipient_name_from_email(name) == input_recipient
  end
    raise GoogleLoginException.new("user not logged in", "google-play-store-login", BROWSERSTACK_ERROR_STRING)
  end
ensure
  output = `adb -s #{@device} shell rm -f /sdcard/accounts.db`
  exit_code = $CHILD_STATUS.exitstatus
  BrowserStack.logger.info "[verify_google_login] #{@device} exit code after deleting the db from device : "\
                           "#{exit_code} and output : #{output}"
  output = `sudo rm -f /tmp/accounts_#{@device}.db*`
  exit_code = $CHILD_STATUS.exitstatus
  BrowserStack.logger.info "[verify_google_login] #{@device} exit code after deleting the db from machine : "\
                           "#{exit_code} and output : #{output}"
end

def verify_google_login_non_rooted(email)
  account_helper = AccountHelper.new(@device, BrowserStack.logger, BrowserStack.logger.params)
  unless account_helper.specific_google_account_present?(email)
    raise GoogleLoginException.new(
      "user not logged in", "google-play-store-login", BROWSERSTACK_ERROR_STRING
    )
  end
end

def send_network_simulation_cls_log(params)
  puts "simulation_params #{params}, #{params.class}"
  json_to_send = if params['network_simulation_profile_name'] == "custom_profile"
                   { "network_simulation" => params['network_simulation_profile_name'],
                     "values" => { "upload" => params['network_bw_upld'], "download" => params['network_bw_dwld'],
                                   "latency" => params['network_latency'], "pk_loss" => params['network_pk_loss'] } }
                 else
                   { "network_simulation" => params['network_simulation_profile_name'] }
                 end
  session_params = begin
    JSON.parse(File.read("#{CONFIG_DIR}/rtc_service_#{@device}"), symbolize_names: true)
  rescue StandardError
    {}
  end

  if ["app_live_testing", "live_testing"].include?(params["genre"])
    BrowserStack.logger.info "Sending data to EDS"
    eds_table = (
      if params["genre"] == "app_live_testing"
        EdsConstants::APP_LIVE_TEST_SESSIONS
      else
        EdsConstants::LIVE_TEST_SESSIONS
      end
    )
    @eds_obj.push_logs(eds_table, {
      session_id: session_params["live_session_id"] || session_params["app_live_session_id"],
      product: {
        network_simulation: json_to_send
      }
    })
  else
    push_to_cls(session_params, "network_simulation", '', json_to_send)
  end
end

def setup_network_simulation(params, device, session_id = nil, apps_string = "", is_api = false) # rubocop:todo Style/OptionalBooleanParameter
  simulation_error = []
  begin
    feature_name = params["network_airplane_mode"].to_s == "true" ? "airplaneMode" : "customNetwork"
    config = JSON.parse(read_with_lock(CONFIG_FILE))
    default_port = config["devices"][device]["port"]

    if params[:limit_network_usage].to_s == "true"
      BrowserStack.logger.info "setup_network_simulation:limit_network_usage: Setting up limited network "\
                               "usage for #{session_id}"
      feature_name = "limit_network_usage" unless params[:networkSimulation].to_s == "true"
      FileUtils.touch("#{BrowserStack::ALLOW_LIMITED_NETWORK_FILE}_#{device}")
    end

    ns = NetworkSimulator.new(device, default_port, 'server', params["genre"], session_id)

    simulation_error.concat(ns.reset_simulation(apps_string))
    simulation_error.concat(ns.setup_simulation(params)) if params["network_reset"].nil? ||
                                                            params["network_reset"].to_s == "false"

    wifi_disabled = ns.is_device_wifi_disabled? || ns.is_device_airplane_mode_enabled?

    # set proxy for android if wifi was reset, as it deletes proxy set by ProxyActivity. refer ProxyActivity.java
    set_device_proxy(device, calculate_privoxy_port(default_port)) if params["automate_session_id"] &&
                                                                      params["network_wifi"].to_s != "false" &&
                                                                      wifi_disabled

    File.open("/tmp/network_simulation_#{device}", 'w') { |f| f.write("NetworkSimulation is set") }

    # Block for Tracking Metrics
    send_network_simulation_cls_log(params) if params["app_live_session_id"] || params["live_session_id"]
  rescue StandardError => e
    session_id = get_session_id_from_params(params)
    zombie_push('android', 'network-simulation-error', '', '', e.inspect[0, 300].to_s, device, session_id)
    if params["app_live_session_id"] || params["live_session_id"]
      `#{COMMON}/push_to_cls.rb #{CONFIG_DIR}/rtc_service_#{@device} 'network-simulation-error' "#{e.inspect}" '' &`
    end
    simulation_error << e.inspect
  ensure
    if is_automate_or_app_automate(params["genre"])
      unless is_api
        feature_stability = (
          if feature_name == "airplaneMode"
            !simulation_error.include?("enable_airplane_mode failed")
          else
            simulation_error.empty?
          end
        )
        update_feature_usage(feature_name, feature_stability)
      end
      unless simulation_error.empty?
        push_to_cls(params, "network-simulation-error", simulation_error.join(",").to_s,
                    { "data" => params.to_json, "instance_id" => params[:device] })
      end
    end
  end
end

def setup_device_preferences(params, device, session_id)
  initial_device_preferences_file = "#{STATE_FILES_DIR}/initial_device_preferences_#{device}"

  if File.exist?(initial_device_preferences_file)
    BrowserStack.logger.info "Unexpected initial device preferences file found for #{device}."
    zombie_push('android', 'device-pref-setup-error', 'ADB startup initial device preferences file found', '', '',
                device, session_id)
    raise FireCMDException, "Unexpected initial device preferences file found for #{device}."
  end

  FileUtils.touch(initial_device_preferences_file)
  # This file should be deleted in cleanup now if any values set are reset successfully.
  # This file will also be deleted if there is any error while reading the initial values.
  begin
    commands = JSON.parse(params["device_preferences"])["runnable_adb_commands"]
  rescue JSON::ParserError => e
    raise FireCMDException, "error in parsing runnable_adb_commands"
  end
  raise FireCMDException, "Runnable ADB commands not in a hash format" unless commands.is_a?(Hash)

  begin
    device_preferences_manager = DevicePreferencesManager.new(device, commands, "app_automate", session_id)
    device_preferences_manager.run_write_access_cmds(initial_device_preferences_file)
    update_feature_usage('devicePreferences', true, '')
  rescue UserADBCommandException => e
    # if the file still exists, it means initial value commands ran correctly
    message = File.exist?(initial_device_preferences_file) ? 'ADB command error' : 'ADB initial command error'
    zombie_push('android', 'device-pref-setup-error', message, '', e.inspect[0, 300].to_s, device, session_id)
    update_feature_usage('devicePreferences', false, e.message[0, 100])
    raise FireCMDException, "Setting device preferences ADB error with : #{e.message}"
  end

end

get '/update_network' do
  mid_session_network_simulation_file = "#{STATE_FILES_DIR}/network_simulation_mid_session_#{@device}"
  session_id = get_session_id_from_params(params)

  if File.exist?(mid_session_network_simulation_file)
    BrowserStack.logger.info "Network Simulation for sessionId #{session_id} already running, Skipping..."
    return {}.to_json
  end

  FileUtils.touch(mid_session_network_simulation_file)
  params["internet_via_usb"] = File.exist?("/tmp/app_internet_via_usb_#{@device}")
  apps_string = ""

  mobile_session_info = MobileSessionInfo.read(params[:device])
  BrowserStack.logger.info "update_network: mobile_session_info - #{mobile_session_info}"

  is_running_session = (mobile_session_info['session_id'] == session_id)
  if params["internet_via_usb"]
    apps_string = begin
      get_bundle_ids_from_session_info(mobile_session_info)
    rescue StandardError
      ""
    end
  end
  if mobile_session_info["session_type"] == "app_automate"
    params["genre"] = mobile_session_info["session_type"]
    params["automate_session_id"] = session_id
  end

  interactive_session_file = "#{STATE_FILES_DIR}/interactive_session_file_#{@device}"
  if (params["network_airplane_mode"].to_s == "true" || params["network_wifi"].to_s == "false") &&
     File.exist?(interactive_session_file)
    automate_funnel = AutomateFunnel.new
    automate_funnel.mark_block_start("StopStreamingAndInteraction")
    stop_streaming_and_interaction(params)
    automate_funnel.mark_block_end("StopStreamingAndInteraction")
  end

  BrowserStack.logger.info "Update network sessionId #{session_id}. Running session check - #{is_running_session}"
  setup_network_simulation(params, params[:device], session_id, apps_string, true) if is_running_session
  FileUtils.rm_f(mid_session_network_simulation_file)

  return {}.to_json
end

def  get_xml_for_webview(device)
  port = @devices_json[device]["webview_port"]
  # sometimes the port doesn't get forwarded at the begining hence opening
  # at the time of getting xml as well
  start_webview_tcp_port
  socket = TCPSocket.open('localhost', port)
  socket.print('GETXML: true:')
  response = socket.read.force_encoding("UTF-8")
  response
ensure
  socket&.close
end

# add xpath attribute to every node in xml tree
#
# @param xmltree [String]
# @return [Nokogiri::XML::Node] new xml tree with xpath attribute
def add_xpath_to_xml_tree(xmltree)
  return xmltree if xmltree.empty?

  xmltree = xmltree.gsub(/\n\t/, " ").gsub(/>\s*</, "><")
  xmlDoc = Nokogiri::XML(xmltree)
  root = xmlDoc.root
  add_xpath_to_xml_node(root)
rescue StandardError => e
  BrowserStack.logger.error("Unable to parse xml tree to add xpath", e)
end

# add xpath attribute to the node and its children recursively
#
# @param node [Nokogiri::XML::Node]
# @return [Nokogiri::XML::Node] xml node having xpath attribute
def add_xpath_to_xml_node(node)
  node.set_attribute('xpath', node.path)
  node.children.each do |child_node|
    add_xpath_to_xml_node(child_node)
  end
  node
end

def get_snapshot_details(device, params, adb_timeout)
  if params[:only_screenshot] || params[:webview_flow]
    screenshot_file = "/tmp/snapshot_#{device}/screenshot.png"
    FileUtils.mkdir("/tmp/snapshot_#{device}")
    adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
    adb.shell("screencap /sdcard/screenshot.png; exit")
    adb.pull("/sdcard/screenshot.png", "/tmp/snapshot_#{device}/")
    system("sudo chown -R ritesharora:ritesharora /tmp/snapshot_#{device}/")
    adb.shell("rm -rf /sdcard/screenshot.png; exit")
  else
    script_logger = script_logger_args("#{File.basename(SCRIPT)}_get_snapshot_details")
    driver_actions_kind = "generate_screenshot_and_source"
    screenshot_file = "/tmp/snapshot_#{device}/screencap.png"
    cmd = "bash #{SCRIPT} #{driver_actions_kind} #{device} #{adb_timeout} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\"; done"
    system(cmd)
  end

  if params[:orientation] == "landscape" && @devices_json[device]["device_version"].to_i < 6
    # Android screencap gives landcape screenshot in a portrait size for Android 4 and 5
    system("/usr/bin/mogrify -rotate '-90' #{screenshot_file}")
  end

  snapshot_base64 = Base64.strict_encode64(File.read(screenshot_file))
  if params[:webview_flow].nil?
    source = File.read("/tmp/snapshot_#{device}/window_dump.xml") if params[:only_screenshot].nil?
  else
    source = get_xml_for_webview(device)
    webviewFlow = "true"
  end

  # Add xpath-attribute to the xml tree
  start_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)
  xmltree_with_xpath = add_xpath_to_xml_tree(source).to_s
  xmltree_with_xpath.delete!("\n")
  end_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)

  BrowserStack.logger.info("Adding xpath to xml source tree took #{end_time - start_time} seconds")

  {
    screenshot: snapshot_base64,
    xmltree: xmltree_with_xpath.to_s,
    isWebViewFlow: webviewFlow
  }
end

def get_snapshot_details_uiautomator2(device, _params, adb_timeout)
  config = @devices_json || JSON.parse(read_with_lock(CONFIG_FILE))["devices"]
  script_logger = script_logger_args("#{File.basename(SCRIPT)}_get_snapshot_details")
  driver_actions_kind = "generate_screenshot_and_source_uiautomator2"
  uiinspect_port = config[device]["uiinspect_port"]
  BrowserStack.logger.info "get_snapshot_details_uiautomator2: uiinspect_port: #{uiinspect_port}, device: #{device}"
  cmd = "bash #{SCRIPT} #{driver_actions_kind} #{device} #{adb_timeout} #{uiinspect_port} 2>&1 | "\
        "while read line; do echo #{script_logger} \"$line\"; done"
  system(cmd)
  screenshot_file_path = "/tmp/snapshot_#{device}/encoded_screenshot"
  source_file_path = "/tmp/snapshot_#{device}/window_dump.xml"

  if !File.file?(screenshot_file_path) || !File.file?(source_file_path)
    raise StandardError, "UI Inspect failed using appium's uiautomator2"
  end

  snapshot_base64 = File.open(screenshot_file_path, "rt").read
  snapshot_base64.delete!("\n")
  source = `xmllint --noblanks #{source_file_path}`

  # Add xpath-attribute to the xml tree
  start_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)
  xmltree_with_xpath = add_xpath_to_xml_tree(source).to_s
  xmltree_with_xpath.delete!("\n")
  end_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)

  BrowserStack.logger.info("Adding xpath to xml source tree took #{end_time - start_time} seconds")

  response = {
    screenshot: snapshot_base64.to_s,
    xmltree: xmltree_with_xpath.to_s
  }
end

def start_interactions

  driver_actions_kind = "start_interactions_server_and_uiautomator"
  script_logger = script_logger_args("#{File.basename(SCRIPT)}_get_snapshot_details")
  cmd = "bash #{SCRIPT} #{driver_actions_kind} #{@device} 2>&1 | "\
    "while read line; do echo #{script_logger} \"$line\"; done"
  spawn_process(cmd)
rescue StandardError => e
  BrowserStack.logger.error "Exception in starting interactions server #{@device}: #{e.message} \n"\
                            "#{e.backtrace.join("\n")}"

end

def get_snapshot_details_wrapper(device, params, uiautomator2_enabled)
  try = 0
  adb_timeout = 5
  begin
    try += 1
    FileUtils.rm_rf("/tmp/snapshot_#{device}", secure: true)
    if uiautomator2_enabled && @devices_json[device]["device_version"].to_i >= 5
      get_snapshot_details_uiautomator2(device, params, adb_timeout)
    else
      get_snapshot_details(device, params, adb_timeout)
    end
  rescue StandardError => e
    adb_timeout *= 2
    retry if try < 3
    raise e
  end
end

get '/get_snapshot_details' do
  session_id = get_session_id_from_params(params)
  uiautomator2_enabled = params["uiautomator2_enabled"].to_s == "true"
  begin
    resp = get_snapshot_details_wrapper(@device, params, uiautomator2_enabled)
    resp_code = 200
    cmd = "/usr/local/.browserstack/mobile/common/push_to_zombie.rb "\
          "'android' 'get-snapshot-details-success' '' '#{@device_name}' "\
          "'uiautomator2_enabled:#{uiautomator2_enabled}' '#{@device}' #{session_id}"
    `#{cmd}`
  rescue Exception => e
    cmd = "/usr/local/.browserstack/mobile/common/push_to_zombie.rb "\
          "'android' 'get-snapshot-details-error' '' '#{@device_name}' "\
          "'uiautomator2_enabled:#{uiautomator2_enabled}' '#{@device}' #{session_id}"
    `#{cmd}`
    BrowserStack.logger.error "Exception in /get_snapshot_details #{@device}: #{e.message} \n"\
                              "#{e.backtrace.join("\n")}"
    resp = { error: "Error getting snapshot details #{e.message}" }
    resp_code = 500
  end
  start_interactions
  return resp_code, resp.to_json
end

get '/is_device_rooted' do
  return 200, { error: false, rooted: bsrun_device?(@device) }.to_json
rescue Exception => e
  BrowserStack.logger.error "Exception in /get_snapshot_details #{e.message}, session_id #{session_id}"
  return 500, { error: true }.to_json
end

post '/app_live/get_snapshot_details' do
  begin
    request_body = JSON.parse(request.body.read , symbolize_names: true)
    params.merge!(request_body)
    session_id = params[:app_live_session_id]
    session_details_file = "#{STATE_FILES_DIR}/al_session_#{session_id}"

    # No App-Live running sessions found.
    halt 404, {}, { error: true, message: "No Terminal found" }.to_json unless File.exist?(session_details_file)

    session_details = JSON.parse(File.read(session_details_file))

    @device = session_details["device"]
    config = JSON.parse(read_with_lock(CONFIG_FILE))
    @devices_json = config["devices"]
    @device_name = @devices_json[@device]["device_name"]
    params[:webview_flow] = params[:webviewInject].to_s == 'true'

    # Feature: Native Screenshots
    # Created Endpoint for returning a Base64 encoded String for the screenshot captured
    # Differs to /get_snapshot_details, as this doesn't need the UI Xml elements and just the screenshot
    # (Also need '/get_screenshot' naming convention).
    # Disable uiautomator2_enabled flow to get html content instead of xml content
    uiautomator2_enabled =
      session_details["uiautomator2_enabled"].to_s == "true" &&
      !(params[:webview_flow] || params[:only_screenshot])

    resp = get_snapshot_details_wrapper(@device, params, uiautomator2_enabled)
    resp_code = 200
    zombie_push(
      'android', 'get-snapshot-details-success', '', @device_name,
      "uiautomator2_enabled:#{uiautomator2_enabled}", @device, session_id
    )
  rescue Exception => e
    zombie_push(
      'android', 'get-snapshot-details-error', '',
      @device_name, "uiautomator2_enabled:#{uiautomator2_enabled}", @device, session_id
    )
    BrowserStack.logger.error "Exception in /get_snapshot_details #{@device}: #{e.message} \n"\
                              "#{e.backtrace.join("\n")}"
    resp = { error: true, message: "Error getting snapshot details." }
    resp_code = 500
  end
  start_interactions
  return resp_code, resp.to_json
end

post '/app_live/adb_command' do
  request_body_hash = JSON.parse(request.body.read , symbolize_names: true)
  params.merge!(request_body_hash)
  session_id = params[:app_live_session_id]
  session_details_file_path = "#{STATE_FILES_DIR}/al_session_#{session_id}"

  unless File.exist?(session_details_file_path)
    halt 404, {}, {
      success: false,
      message: "No Session found"
    }.to_json
  end

  # Bad request if the command is not present
  if params[:command].nil?
    halt 401, {}, {
      success: false,
      message: "No Command Present"
    }.to_json
  end

  session_details = JSON.parse(File.read(session_details_file_path))

  device = session_details["device"]

  command = params[:command]

  initial_device_preferences_file = "#{STATE_FILES_DIR}/initial_device_preferences_#{device}"
  # These are Device Preferences commands
  if command.match?("setprop|settings put")
    arr = command.split(" ")
    arr = [arr[0..-2].join(" "), arr[-1]]
    key = arr[0]
    value = arr[1]
    command_hash = {}
    command_hash[key] = value
    device_preferences_manager = DevicePreferencesManager.new(device, command_hash, APP_LIVE, session_id)
    device_preferences_manager.run_write_access_cmds(initial_device_preferences_file)
    return 200, { success: true, message: "" }.to_json
  end

  user_exposed_adb = UserExposedADB.new(device, command, APP_LIVE)
  adb_cmd_result = user_exposed_adb.run_cmd

  return 200, { success: true, response: adb_cmd_result }.to_json
rescue UserADBCommandException => e
  message = if e.message.include?("Blacklisted ADB command")
              "Blacklisted Command"
            elsif e.message.include?("timedout for")
              "Command Timeout"
            else
              e.message
            end
  return 200, { success: false, message: message }.to_json
rescue Exception => e
  # Want generic exceptions in a specific format so that we can log it to EDS via railsApp
  return 500, { success: false, message: e.message }.to_json
end

post '/app_live/inject_pfx_file' do
  request.body.rewind
  request_body_hash = JSON.parse(request.body.read, symbolize_names: true)
  params.merge!(request_body_hash)
  session_id = params[:app_live_session_id]
  session_details_file_path = "#{STATE_FILES_DIR}/al_session_#{session_id}"

  unless File.exist?(session_details_file_path)
    halt 404, {}, {
      success: false,
      message: "No Session found"
    }.to_json
  end

  # Bad request if certificate details are not present
  if params[:certificate_details].nil? || params[:password].nil?
    halt 401, {}, {
      success: false,
      message: "No Command Present"
    }.to_json
  end

  session_details = JSON.parse(File.read(session_details_file_path))
  device = session_details["device"]
  certificate_details = params[:certificate_details]

  # Bad request if certificate details are not present
  if certificate_details[:media_s3_url].nil? ||
    certificate_details[:filename].nil? ||
    certificate_details[:filetype].nil?
    halt 401, {}, {
      success: false,
      message: "Certificate Details Invalid"
    }.to_json
  end

  certificate_hash = { s3_url: certificate_details[:media_s3_url], filename: certificate_details[:filename],
                       filetype: certificate_details[:filetype], password: params[:password] }.to_json
  custom_certificate_helper = CustomCertificate.new(device, session_id, certificate_hash,
                                                    BrowserStack.logger)
  custom_certificate_helper.install_pfx_certificate_for_applive
  return 200, { success: true }.to_json
rescue Exception => e
  # Want generic exceptions in a specific format so that we can log it to EDS via railsApp
  return 500, { success: false, message: e.message }.to_json
end

post '/app_live/install_ca_certs' do
  request_body_hash = JSON.parse(request.body.read , symbolize_names: true)
  params.merge!(request_body_hash)
  session_id = params[:app_live_session_id]
  session_details_file_path = "#{STATE_FILES_DIR}/al_session_#{session_id}"

  unless File.exist?(session_details_file_path)
    halt 404, {}, {
      success: false,
      message: "No Session found"
    }.to_json
  end

  # Bad request if the command is not present or is not an array
  if params[:certificate_details].nil? || !params[:certificate_details].is_a?(Array)
    halt 401, {}, {
      success: false,
      message: "Invalid certificates"
    }.to_json
  end

  session_details = JSON.parse(File.read(session_details_file_path))

  device = session_details["device"]

  certificates = params[:certificate_details]

  certificates.each do |certificate|
    certificate_hash = { s3_url: certificate[:media_s3_url], filename: certificate[:filename],
                         filetype: certificate[:filetype] }.to_json
    custom_certificates = CustomCertificate.new(device, session_id, certificate_hash, BrowserStack.logger)
    custom_certificates.install_ca_cert
  end
  return 200, { success: true }.to_json
rescue Exception => e

  # Want generic exceptions in a specific format so that we can log it to EDS via railsApp
  return 500, { success: false, message: e.message }.to_json
end

get '/get_dedicated_state_files' do
  get_static_files_list = OSUtils.execute("ls #{STATE_FILES_DIR} | grep 'dedicated'")
  BrowserStack.logger.info("checking dedicated state file #{get_static_files_list}")
  return 200, { get_static_files_list: get_static_files_list }.to_json
end

get '/get_gpay_state_files' do
  get_static_files_list = OSUtils.execute("ls #{STATE_FILES_DIR} | grep 'google_pay'")
  BrowserStack.logger.info("checking dedicated state file #{get_static_files_list}")
  return 200, { get_static_files_list: get_static_files_list }.to_json
end

get '/get_state_file' do
  state_file_name = params[:state_file_name]
  get_static_files_list = OSUtils.execute("ls #{STATE_FILES_DIR} | grep '#{state_file_name}'")
  BrowserStack.logger.info("checking dedicated state file #{get_static_files_list}")
  return 200, { get_static_files_list: get_static_files_list }.to_json
end

# Feature: Native Screenshots
# Created Endpoint for returning a Base64 encoded String for the screenshot captured
# Differs to /get_snapshot_details, as this doesn't need the UI Xml elements and just the screenshot
# (Also need '/get_screenshot' naming convention).
get '/get_screenshot' do
  session_id = get_session_id_from_params(params)
  begin
    resp = get_snapshot_details_wrapper(@device, params, false)
    resp_code = 200
    zombie_push('android', 'get-screenshot-success', '', @device_name, '', @device.to_s, session_id)
  rescue StandardError => e
    zombie_push('android', 'get-screenshot-error', '', @device_name, '', @device.to_s, session_id)
    BrowserStack.logger.error "Exception in /get_screenshot #{@device}: #{e.message} \n#{e.backtrace.join("\n")}"
    resp = { error: "Error taking screenshot #{e.message}" }
    resp_code = 500
  end
  return resp_code, resp.to_json
end

get '/reset_app' do
  session_id = get_session_id_from_params(params)
  BrowserStack.logger.info "Got request for /reset_app with params : #{params} device: #{@device} "\
                           "device_name: #{@device_name} session_id : #{session_id}"
  begin
    mobile_session_info = MobileSessionInfo.read(params[:device])
    BrowserStack.logger.info "reset_app mobile_session_info :#{mobile_session_info}"

    unless mobile_session_info && !mobile_session_info["packages"].to_a.empty?
      raise "Packages not found in mobile session info"
    end

    package = mobile_session_info["packages"][0]["name"]

    # this endpoint is called if fullReset is true and reset only main App not other Apps even if it is there
    # This is default behaviour of Appium as well
    session_details = JSON.parse(File.read("/tmp/duplicate_session_#{@device}"))
    download_path = session_details["main_app_download_path"]
    BrowserStack.logger.info "reset_app package: #{package} session_details: #{session_details}"
    t1 = Time.now.to_i
    is_app_automate = params["automate_session_id"] ? true : false
    # Get package, here it will be true, also check for fourth parameter consent
    cmd = "bash #{APP_LIVE_SCRIPT} full_reset_app \"#{@device}\" \"#{package}\" \"-d -r -t\" 0 \"#{download_path}\"\
          \"#{is_app_automate}\""
    output = `#{cmd}`
    BrowserStack.logger.info "Apk reinstallation output: #{output}"
    raise "App reinstallation Failed: #{output.to_s.strip.split("\n")[-1]}" if $CHILD_STATUS.exitstatus != 0

    BrowserStack.logger.info "[App Automate] Reinstall time: #{Time.now.to_i - t1}"
  rescue Exception => e
    BrowserStack.logger.info "Apk reinstallation failed: device : #{@device} \n sessionid : #{session_id} \n "\
                             "error : #{e} \n errorinspect : #{e.inspect}"
    zombie_push('android', 'reset-app-failed', '', @device_name, e.inspect[0, 300].to_s, @device.to_s, session_id)
  end
end

#this is called from hub on install_app appium command.
# required params -
# automate_session_id : session_id
# app_hashed_id : app id to be installed
# This app_hashed_id should have been sent in firecmd in otherApps, and app is already downloaded in firecmd.
# We store these app's details in session file while installing otherApps in firecmd.
# We do not intercept the app install command when user passes public url it will be given to appium
get '/install_app' do
  session_id = params['automate_session_id']
  app_hashed_id = params['app_hashed_id']
  BrowserStack.logger.info "Got request for /install_app with params : #{params} device: #{@device} device_name: "\
                           "#{@device_name} session_id : #{session_id}"

  begin
    if File.exist?("/tmp/duplicate_session_#{@device}")
      session_details = JSON.parse(File.read("/tmp/duplicate_session_#{@device}"))
      BrowserStack.logger.info "Session Detail file #{session_details}"

      installed_other_apps = session_details['installed_other_apps_details']

      #If user sends an app from other apps which does not have same bundle id as main app, then just show a message
      # that app is already installed
      if installed_other_apps&.key?(app_hashed_id)
        response = { message: "The app_url 'bs://#{app_hashed_id}' passed in the installApp command is already "\
                    "installed on the device." }
        return 200, response.to_json
      end

      app_details = ""
      downloaded_other_apps = session_details['downloaded_other_apps_details']
      app_details = downloaded_other_apps[app_hashed_id] if downloaded_other_apps

      if app_details.nil? || app_details.empty? || !File.exist?(app_details['app_path'].to_s)
        raise AppInstallCommandFailedException, "An unknown server-side error occurred while processing the command. "\
                                                "Original error: The app_url value 'bs://#{app_hashed_id}' is invalid."
      end

      download_params = { app_type: "dependent", zip_align: downloaded_other_apps[app_hashed_id]["zip_align"],
                          enable_apksigner: downloaded_other_apps[app_hashed_id]["enable_apksigner"] }
      output, exit_status_code = install_apps(app_details['bundle_id'], download_params,
                                              session_id, app_details['app_path'])
      raise AppInstallCommandFailedException, output if exit_status_code != 0

      return 200, {}.to_json
    end

    raise "Something went wrong with install app Command"
  rescue AppInstallCommandFailedException => e
    BrowserStack.logger.info "Apk installation failed: device : #{@device} \n sessionid : #{session_id} \n error : "\
                             "#{e} \n errorinspect : #{e.message}"
    zombie_push('android', 'app-install-command-failed', '', @device_name, e.message.to_s, @device.to_s, session_id)

    return 500, { error: e.message }.to_json
  rescue StandardError => e
    BrowserStack.logger.info "Apk installation failed: device : #{@device} \n sessionid : #{session_id} \n error : "\
                             "#{e} \n errorinspect : #{e.message}"
    zombie_push('android', 'app-install-command-failed', '', @device_name, e.message.to_s, @device.to_s, session_id)

    return 500, { error: "An unknown server-side error occurred while processing the command. Original error: "\
            "Something went wrong with install app Command. Please try again or contact BrowserStack Support" }.to_json
  end
end

get(%r{/stop_(espresso|fluttertest|maestro)_session}) do
  params[:browserstack_framework] = params[:captures].first # The capture group from the endpoint

  session_id = get_session_id_from_params(params)
  clean_in_session(@device)
  stop_proxy_checker(@device)

  if session_id.nil? || session_id.empty?
    zombie_push(
      'android', "#{params[:browserstack_framework]}_stop_session_fail",
      "automate_session_id not found", '',
      '', @device, nil, nil
    )
    BrowserStack.logger.error(
      "[APP_AUTOMATE_#{params[:browserstack_framework].upcase}_ERROR] Got error in "\
      "stop_#{params[:browserstack_framework]}_session : automate_session_id not found for "\
      "params #{params}."
    )
    return 500, {
      error: "Got error in stop_#{params[:browserstack_framework]}_session "\
             ": automate_session_id not found."
    }.to_json
  end
  cls_params = { genre: "app_automate", automate_session_id: session_id, user_id: nil }
  begin
    BrowserStack.logger.info "Processing stop_#{params[:browserstack_framework]}_session session_id : #{session_id}"
    session_file = "/tmp/#{params[:browserstack_framework]}_summary_#{@device}"
    is_running_session = File.read(session_file).include?(session_id)
    BrowserStack.logger.info "Stop #{params[:browserstack_framework]} session sessionId #{session_id}. "\
                             "Running session check - #{is_running_session}"

    if is_running_session
      framework_helper = if params[:browserstack_framework] == 'maestro'
                           MaestroHelper.new(session_file)
                         else
                           EspressoHelper.new(session_file)
                         end
      framework_helper.force_stop_session(@device, params[:browserstack_framework], session_id)
      push_to_cls(cls_params, "stop_#{params[:browserstack_framework]}_session_done", "", { "device" => @device })
    else
      BrowserStack.logger.info "Session was not running for session_id : #{session_id}"
      raise "stop_#{params[:browserstack_framework]}_session : Session #{session_id} not running"
    end
  rescue StandardError => e
    zombie_push(
      'android', "#{params[:browserstack_framework]}_stop_session_fail",
      e.message, '', '', @device, session_id, nil
    )
    BrowserStack.logger.error "[APP_AUTOMATE_#{params[:browserstack_framework].upcase}_ERROR] "\
      "stop_#{params[:browserstack_framework]}_session failed: device : "\
      "#{@device} \n sessionid : #{session_id} \n error : #{e} \n errorinspect : #{e.inspect}"
    push_to_cls(
      cls_params, "stop_#{params[:browserstack_framework]}_session_error", e.message,
      { "error_data" => e.inspect.to_s, "device" => @device }
    )
    return 500, { error: "Got error in stop_#{params[:browserstack_framework]}_session #{e.message}" }.to_json
  end
  return {}.to_json
end

def check_basic_auth(static_conf)
  return if authorized?(static_conf)

  headers['WWW-Authenticate'] = 'Basic realm="Restricted"'
  halt 401, "Not authorized\n"
end

def authorized?(static_conf)
  @auth ||= Rack::Auth::Basic::Request.new(request.env)
  @auth.provided? && @auth.basic? && @auth.credentials &&
    @auth.credentials == [static_conf['sinatra_auth_user'], static_conf['sinatra_auth_pass']]
end

post '/adb' do
  check_basic_auth(static_conf)
  BrowserStack.logger.info("Executing adb command: adb #{params[:arguments]}")
  adb = AndroidToolkit::ADB.new(path: BrowserStack::ADB)
  raw_arguments_array = params[:arguments].split(" ")
  adb_response = adb.execute(*raw_arguments_array)
  [200, { command_output: adb_response }.to_json]
rescue StandardError => e
  BrowserStack.logger.error("Error executing adb command #{params}, #{e.message}")
  [500, { error: e.message }.to_json]
end

post '/app_percy/screenshot' do
  data = JSON.parse(request.body.read)
  config = JSON.parse(read_with_lock(CONFIG_FILE))
  devices_json = config["devices"]
  BrowserStack.logger.info("data = #{data}")
  screenshot_manager = AppPercy::ScreenshotManager.new(params[:device], devices_json)
  AppPercyGCSManager.project_id = data['project_id']
  tiles_array = screenshot_manager.take_screenshot(data['appium_session_id'], data['screenshot_type'], data['build_id'],
                                                   data['scale_factor'], data['options'])
  BrowserStack.logger.info("tiles = #{tiles_array}")
  zombie_push(
    'android',
    'app_percy_screenshot_1',
    '',
    '',
    {
      "team" => 'app-percy',
      "tiles" => tiles_array
    },
    @device,
    data['appium_session_id']
  )
  return 200, tiles_array.to_json

rescue StandardError => e
  zombie_push(
    'android',
    'app_percy_screenshot_0',
    "Percy Screenshot Failed: #{e.backtrace.join("\n")}",
    '',
    {
      "team" => 'app-percy'
    },
    @device,
    data['appium_session_id']
  )
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
  return 500, e.message
end

post '/app_percy/disable_animation' do
  data = JSON.parse(request.body.read)
  sess = AppPercySession.new(@device, data['appium_session_id'])
  return sess.disable_animation
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
  return 500, e.message
end

post '/percy/start_server' do
  data = JSON.parse(request.body.read)
  sess = Percy::PercySession.new(@device, @devices_json)
  params = {}
  params['proxy_type'] = 'privoxy'
  params['device'] = @device
  params['hosts'] = '/'
  params[:genre] = 'percy'
  params[:tunnelPorts] = "4#{@devices_json[@device]['port']}"
  params[:tunnelHostServer] = '127.0.0.1'
  params[:session_id] = Time.now.utc.iso8601

  tunnel_setup(params)
  return sess.start_server(data["env"])
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
  return 500, e.message
end

post '/percy/stop_server' do
  FileUtils.rm_f("#{STATE_FILES_DIR}/#{@device}_env")
  sess = Percy::PercySession.new(@device, @devices_json)
  return sess.stop_server
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
  return 500, e.message
end

post '/percy/health_check' do
  sess = Percy::PercySession.new(@device, @devices_json)
  status = sess.health_check?
  return 200 if status

  return 500
end

post '/percy/start_jackproxy' do
  data = JSON.parse(request.body.read)
  sess = Percy::PercySession.new(@device, @devices_json, data['image_tag'])
  return sess.start_jackproxy(
    data['params'],
    data['proxy_map'],
    data['debug_data'],
    data['proxy_map_url'],
    data['url_suffix'],
    renderer_key: data['renderer_key']
  )
rescue RendererKeyError => e
  BrowserStack.logger.error("#{e.message} \n")
  return 403, e.message
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
  return 500, e.message
end

post '/percy/stop_jackproxy' do
  sess = Percy::PercySession.new(@device, @devices_json)
  data = JSON.parse(request.body.read)
  return sess.stop_jackproxy(data['debug_data'], renderer_key: data['renderer_key'])
rescue RendererKeyError => e
  BrowserStack.logger.error("#{e.message} \n")
  return 403, e.message
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
  return 500, e.message
end

post '/percy/capture_tile' do
  data = JSON.parse(request.body.read)

  sess = Percy::PercySession.new(@device, @devices_json)
  GCSManager.project_id = data['percy_project']
  sess.tiles_manager.capture_tile(data['appium_session_id'], data['build_id'], data['seq_no'],
                                  data['strip'], data['bucket'], renderer_key: data['renderer_key'])
  return 200
rescue RendererKeyError => e
  BrowserStack.logger.error("#{e.message} \n")
  return 403, e.message
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
  return 500, e.message
end

get '/percy/capture_finalize' do
  sess = Percy::PercySession.new(@device, @devices_json)
  return sess.tiles_manager.finalize(
    params[:expected_tiles].to_i, renderer_key: params['renderer_key']
  ).map(&:to_h).to_json
rescue RendererKeyError => e
  BrowserStack.logger.error("#{e.message} \n")
  return 403, e.message
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
  return 500, e.message
end

get '/percy/is_running' do
  return Percy::PercySession.running?(@device)
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
  return 500, e.message
end

get '/percy/keep_alive' do
  res = Percy::PercySession.keep_alive(@device)
  return 422, { 'status' => 'Could not process since session file is absent' }.to_json unless res

  return 200, { 'status' => 'Successfully touched session file' }.to_json
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
  return 500, e.message
end

get '/percy/clean_mobile' do
  sess = Percy::PercySession.new(@device, @devices_json)
  return sess.clean_mobile(renderer_key: params['renderer_key'])
rescue RendererKeyError => e
  BrowserStack.logger.error("#{e.message} \n")
  return 403, e.message
end

post '/percy/screenshot' do
  sess = Percy::PercySession.new(@device, @devices_json)
  data = JSON.parse(request.body.read)
  return sess.percy_screenshot_handler(data)
end

post '/percy/setup_automate_session' do
  sess = Percy::PercySession.new(@device, @devices_json)
  data = JSON.parse(request.body.read)
  return sess.percy_setup_automate_session_handler(data)
end

post '/percy/get_metadata' do
  sess = Percy::PercySession.new(@device, @devices_json)
  data = JSON.parse(request.body.read)
  return sess.percy_get_metadata_handler(data)
end

post '/percy/dom_metadata_upload' do
  sess = Percy::PercySession.new(@device, @devices_json)
  data = JSON.parse(request.body.read)
  return sess.percy_dom_metadata_upload_handler(data)
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
  return 500, e.message
end

get '/percy/dom_metadata_finalize' do
  sess = Percy::PercySession.new(@device, @devices_json)
  data = {}
  data['project_id'] = params['project_id']
  data['build_id'] = params['build_id']
  return sess.percy_dom_metadata_finalize_handler(data)
rescue StandardError => e
  BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
  return 500, e.message
end

get '/toggle_voice_over' do
  params = JSON.parse(request.body.read)
  device = params["device"]
  enable_voice_over = params["enabled"].to_s == "true"
  talkback_helper = TalkbackHelper.new(device)

  enable_voice_over ? talkback_helper.start_talkback : talkback_helper.stop_talkback
end

post '/set_device_state' do
  params = JSON.parse(request.body.read)
  device_id = params["device"]
  requested_device_state = params["device_state"]
  current_device_state = get_device_state(device_id)

  if !device_id || current_device_state == requested_device_state || !["0", "3"].include?(requested_device_state)
    return 422, { error: "Invalid Parameters" }.to_json
  end

  device_obj = BrowserStack::AndroidDevice.new(device_id, "server.rb - set_device_state", BrowserStack.logger)
  return 422, { error: "#{device_id}: is not a foldable device" }.to_json unless device_obj.foldable_device?

  device_name = device_obj.model

  # toggle_device_screen does 3 things
  # 1. Update the Streaming width and height
  # 2. Update the Device State
  # 3. Restart the interaction server
  toggle_device_screen(device_id, requested_device_state, device_name)

  current_device_state = get_device_state(device_id)
  if current_device_state != requested_device_state
    return 500, {
      error: "#{device_id}: is not updated to state #{requested_device_state}," \
        " current device state #{current_device_state}"
    }.to_json
  end

  [200, "completed"]
end

before %r{/driver_actions/?\w*} do
  halt 404 unless ["localhost", "127.0.0.1", "0.0.0.0"].include? request.host
end

get '/driver_actions' do
  [200, "OK"]
end

get '/driver_actions/:device_id/notify_cleanup_done' do
  device_driver = DeviceDriver.new(static_conf, params[:device_id])
  response = device_driver.notify_cleanup_done
  {
    status: 200,
    data: { rails_response_status: response.code, rails_response_body: response.body }
  }.to_json
rescue StandardError => e
  BrowserStack.logger.error("Error local driver actions device #{params[:device_id]} 'send_cleanup_done' #{e}")
  [500, { status: 500, error: e.message }.to_json]
end

delete '/recycle' do
  if OSUtils.virtual_machine?
    OSUtils.shutdown_machine(time: "+1", message: 'Receive "Recycle Virtual Machine" command')

    status 200
  else
    status 422

    { error: "Can't recycle, it's not a virtual machine" }.to_json
  end
end

def validate_speedlab_network_profile(network_profile)
  network_profile.key?("network_bw_dwld") &&
    network_profile.key?("network_bw_upld") &&
    network_profile.key?("network_latency") &&
    network_profile.key?("network_pk_loss")
end

def get_tunnel_params(device_id, job_id)
  {
    "device" => device_id,
    "hosts" => "*:*:50005",
    "proxy_type" => "privoxy",
    "speedlab_session_id" => job_id,
    :hosts => "*:*:50005"
  }
end

def throttle_for_speedlab(device_id, job_config, job_id, network_profile)
  is_throttled = false
  if network_profile && device_id && job_id
    # validate network_profile
    BrowserStack.logger.info "network_profile #{network_profile} for #{job_id}"
    valid_params = validate_speedlab_network_profile(network_profile)
    if valid_params
      begin
        BrowserStack.logger.info "network_profile #{network_profile} for #{job_id} is valid"
        config = JSON.parse(read_with_lock(CONFIG_FILE))
        @device = device_id
        @devices_json = config["devices"]
        default_port = config["devices"][@device]["port"]

        tunnel_params = get_tunnel_params(device_id, job_id)
        tunnel_setup(tunnel_params)

        ns = NetworkSimulator.new(@device, default_port, 'server', 'speedlab', job_id)
        ns.reset_simulation
        ns.setup_simulation(job_config["network_profile"])

        File.open("/tmp/network_simulation_#{device_id}", 'w') { |f| f.write("NetworkSimulation is set") }
        is_throttled = true
      rescue StandardError => e
        zombie_push('android', "speedlab-throttling-failed", "Throttling failed, #{job_id}, #{e.message}",
                    '', '', device_id)
      end
    else
      BrowserStack.logger.info "Improper network_profile #{job_config['network_profile']} for #{job_id}"
    end
  end
  is_throttled
end

post "/lighthouse/start_test" do
  data = JSON.parse(request.body.read)
  job_id = data["job_id"]
  device_id = data["device_id"]
  job_config = data["job_config"]
  network_profile = job_config["network_profile"]

  lighthouse = Lighthouse.new(BrowserStack.logger, data)

  is_throttled = throttle_for_speedlab(device_id, job_config, job_id, network_profile)

  status_code, message = lighthouse.initialize_lighthouse
  message[:is_throttled] = is_throttled
  [status_code, message.to_json]
end

get '/media/push_to_device' do
  BrowserStack.logger.info("/media/push_to_device called for device #{@device} and session #{params[:session_id]}")
  # Since we are using pusher, we can then spawn this code so that railsApp doesn't have to wait for \
  # injection to complete
  # This is done for maintaing backwards compatability
  if params[:use_pusher].to_s == 'true'
    device_injection_media_dir = File.join(BrowserStack::INJECTION_MEDIA_DIR, @device)
    FileUtils.mkdir_p(device_injection_media_dir)
    File.write("#{device_injection_media_dir}/params.json", params.to_json)
    pid = Process.fork do
      cmd = "ruby #{DIR_HOME}/android/helpers/file_injector.rb 'inject_file' #{@device}"
      exec(cmd)
    end
    Process.detach(pid)
  else
    FileInjector.inject_file(@device, params)
  end
  status 200
rescue StandardError => e
  BrowserStack.logger.error("Failed to inject file for device: #{@device} with params: #{params}, " \
                            "error: #{e.message}, #{e.backtrace.join("\n")}")
  status 500
end

# Endpoint for injecting image it the app for app automate
get '/inject_image' do
  BrowserStack.logger.info("/inject_image called for device #{@device} and session #{params[:session_id]}")
  start_at = Time.now
  data_hash = {
    'inject_image' => [
      {
        'timestamp' => start_at
      }
    ]
  }
  if params[:product] && params[:product] == "live"
    BrowserStack::LiveMediaInjector.inject_media(@device, "user", params)
  else
    CameraMediaInjector.inject_media(@device, params, push_to_zombies: false)
  end
  BrowserStack.logger.info("Inject app: Update_app_patching_data_to_state_file: called for device #{@device} "\
                           "and session #{params[:session_id]}")
  data_hash["inject_image"][0]["status"] = 'success'
  zombie_push('android', "inject-image-success", "", "", "", @device, params[:session_id])
  status 200
rescue StandardError => e
  data_hash["inject_image"][0]["status"] = 'failure'
  zombie_push('android', 'inject-image-failure', e.message.to_s, '', '', @device, params[:session_id])
  status 500
  BrowserStack.logger.error("Inject app: Exception in /inject_image #{@device}: #{e.message} \n"\
                            "#{e.backtrace.join("\n")}")
  { error: "Got Error in inject_image #{e.message}" }.to_json
ensure
  data_hash["inject_image"][0]["time_taken"] = Time.now - start_at
  AppPatchingUtil.update_app_patching_data_to_state_file(@device, data_hash) if params[:product].nil? ||
                                                                                params[:product] != "live"
end

# Endpoint for injecting camera media
get '/inject_camera_media' do
  BrowserStack.logger.info("/inject_camera_media called for device #{@device} and session #{params[:session_id]}")
  async_flow = params[:format].downcase == '.mp4'
  CameraMediaInjector.inject_media(@device, params, async_flow: async_flow)
  BrowserStack.logger.info("Inject app: Update_app_patching_data_to_state_file: called for device #{@device} "\
                           "and session #{params[:session_id]}")
  status 200
rescue StandardError => e
  BrowserStack.logger.error("Inject app: Exception in /inject_camera_media #{@device}: #{e.message} \n"\
                            "#{e.backtrace.join("\n")}")
  { error: "Got Error in inject_media #{e.message}" }.to_json
end

# Audio Injection Endpoints
get '/inject_audio' do
  method_tag = '/inject_audio'
  AudioInjector.log_info("#{method_tag} called for device: #{@device} and session: #{params[:session_id]}")
  begin
    AudioInjector.inject_audio(@device, params[:file_url], params[:media_hashed_id], params[:format].downcase,
                               params[:session_id], params[:product], send_to_bq: true)
    AudioInjector.log_info("#{method_tag} returned for device: #{@device} and session: #{params[:session_id]}")
    return 200, { message: "Audio Injected Successfully" }.to_json
  rescue StandardError => e
    AudioInjector.log_error(
      "#{method_tag} failed for device: #{@device} and session: #{
        params[:session_id]}, #{e.message} #{e.backtrace.join("\n")}"
    )

    code = :AL000
    message = "Failed to Inject Audio"
    http_status = 500
    if e.is_a? AudioInjectionException
      code = e.code
      message = e.message
      http_status = e.status
    end
    return http_status, { code: code, message: message }.to_json
  end
end

get '/play_audio' do
  method_tag = '/play_audio'
  AudioInjector.log_info("#{method_tag} called for device: #{@device} and session: #{params[:session_id]}")
  begin
    AudioInjector.play_audio(@device, params[:session_id], params[:product], send_to_bq: true)
    AudioInjector.log_info("#{method_tag} returned for device: #{@device} and session: #{params[:session_id]}")
    return 200, { message: 'Audio Played Successfully' }.to_json
  rescue StandardError => e
    AudioInjector.log_error(
      "#{method_tag} failed for device: #{@device} and session: #{
        params[:session_id]}, #{e.message} #{e.backtrace.join("\n")}"
    )

    code = :AL000
    message = "Failed to Play Audio"
    http_status = 500
    if e.is_a? AudioInjectionException
      code = e.code
      message = e.message
      http_status = e.status
    end
    return http_status, { code: code, message: message }.to_json
  end
end

get '/stop_audio' do
  method_tag = '/stop_audio'
  AudioInjector.log_info("#{method_tag} called for device: #{@device} and session: #{params[:session_id]}")
  begin
    AudioInjector.stop_audio(@device, params[:session_id], params[:product], send_to_bq: true)
    AudioInjector.log_info("#{method_tag} returned for device: #{@device} and session: #{params[:session_id]}")
    return 200, { message: "Audio Stopped Successfully" }.to_json
  rescue StandardError => e
    AudioInjector.log_error(
      "#{method_tag} failed for device: #{@device} and session: #{
        params[:session_id]}, #{e.message} #{e.backtrace.join("\n")}"
    )

    code = :AL000
    message = "Failed to Stop Audio"
    http_status = 500
    if e.is_a? AudioInjectionException
      code = e.code
      message = e.message
      http_status = e.status
    end
    return http_status, { code: code, message: message }.to_json
  end
end

get '/download_files' do
  method_tag = '/download_files'
  DownloadFiles.log(:info, "#{method_tag} called for device: #{@device} and session: #{params[:session_id]}")
  begin
    pid = Process.fork do
      DownloadFiles.fetch_and_download_files(@device, params, send_to_bq: true)
      DownloadFiles.log(:info,
                        "#{method_tag} returned success for device: #{@device} and session: #{params[:session_id]}")
    end

    Process.detach(pid)
    return 200, {}.to_json

  rescue StandardError => e
    DownloadFiles.log(:error,
                      "#{method_tag} failed for device: #{@device} and session: #{params[:session_id]},
#{e.message} #{e.backtrace.join("\n")}")

    return 500, { message: "Download Files Failed" }.to_json
  end
end

post '/update_android_settings' do
  json_data = request.body.read
  data = json_data.nil? || json_data.empty? ? '{}' : json_data
  params = JSON.parse(data)
  session_id = params['app_live_session_id'] || params['live_session_id'] || params['app_automate_session_id']
  product = params['product']
  device_id = params['device']

  cumulative_response =
    { 'date_time':
        { 'status': nil,
          'message': nil } }

  return 400, { message: 'invalid or missing params' }.to_json if session_id.nil? || device_id.nil? || product.nil?

  BrowserStack.logger.info("Setting android__setting: #{params['android_setting']} for #{@device}, called by product#{
    params['product']}, for session #{session_id} with params #{params}")

  settings = params['updateAndroidSettings']

  settings.each do |setting|
    case setting
    when 'date_time'
      begin
        date_time_helper = DateTimeHelper.new(device_id, session_id, BrowserStack.logger, product, true)
        if params["date_time"]["hourFormat12"].to_s != ''
          date_time_helper.change_time_format(params["date_time"]["hourFormat12"])
        else
          date_time_helper.change_date_time(params["date_time"]["date"], params["date_time"]["time"])
        end
        cumulative_response[:date_time][:status] = 200
        cumulative_response[:date_time][:message] = 'Date Time Update Success'
      rescue StandardError => e
        BrowserStack.logger.error("UPDATE_ANDROID_SETTINGS_SET_DATE_TIME: Exception in updateAndroidSettings #{
          setting} #{@device}  #{session_id}: #{e.message} \n#{e.backtrace.join("\n")}")
        cumulative_response[:date_time][:status] = 500
        cumulative_response[:date_time][:message] = 'Date Time Update Failed!'
      end

    end
  end

  return 200, cumulative_response.to_json

rescue StandardError => e
  BrowserStack.logger.error("Exception in updateAndroidSettings #{@device}  #{
    session_id}: #{e.message} \n#{e.backtrace.join("\n")}")

  return 500, { error: "Update Android Settings Failed!" }.to_json
end

get '/execute_adb_command' do
  response = {}
  command = params[:command]
  device = params[:device]
  product = params[:product]
  adb_command_execution_file = "#{STATE_FILES_DIR}/adb_command_execution_#{device}"
  BrowserStack.logger.info "[ADB custom executor] Adb request for device #{device} "\
    "command #{command} #{product} received"
  if File.exist?(adb_command_execution_file)
    BrowserStack.logger.info "[ADB custom executor] Adb command for device #{device} already running, Skipping..."
    return 429, response.to_json
  end
  FileUtils.touch(adb_command_execution_file)
  begin
    BrowserStack.logger.info("[ADB custom executor] Executing adb command: adb #{params[:command]}")
    adb = UserExposedADB.new(device, command, product)
    output = adb.run_cmd
    BrowserStack.logger.info("[ADB custom executor] #{output}")
    response["executed_command"] = "adb -s #{device} shell #{command}"
    response["output"] = output.to_s
    FileUtils.rm_f(adb_command_execution_file)
    return 200, response.to_json
  rescue UserADBCommandException => e
    BrowserStack.logger.info("[ADB custom executor] #{e.message}")
    return e.code, {}.to_json
  ensure
    FileUtils.rm_f(adb_command_execution_file)
  end
end

get '/cleanup_stats' do
  device = params[:device]
  halt 404, {}, "device not found" if device.nil?
  last_cleanup_stats_file = "#{STATE_FILES_DIR}/last_cleanup_stats_#{device}.json"
  unless File.exist?(last_cleanup_stats_file)
    return 400, {
      error: "Last clean up stats for device #{device} not found"
    }.to_json
  end

  last_cleanup_stats = JSON.parse(File.read(last_cleanup_stats_file))
  return 200, last_cleanup_stats.to_json
end

get '/run_qa_cleanup_suite' do
  response = {}
  device = params[:device]
  suite_type = params[:suite_type]
  subsuite_type = params[:subsuite_type]
  record_video = params[:record_video]
  device_name = params[:device_name]
  session_id = params[:session_id]
  # 0. touch session file to indirectly disable device check
  FileUtils.touch("#{STATE_FILES_DIR}/session_#{device}")
  sleep 30
  begin
    adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
    response = download_and_install_qa_cleanup_test_apks(response, adb)
    response = run_qa_cleanup_tests(response, adb, device, suite_type, subsuite_type, record_video, device_name)
    response = fetch_and_upload_test_video(response, adb, device, record_video, session_id) if record_video.eql? 'true'
  ensure
    # 10. rm session file, installed apps, recorded video
    FileUtils.rm("#{STATE_FILES_DIR}/session_#{device}")
    FileUtils.rm("/tmp/cleanup.apk") if File.exist?("/tmp/cleanup.apk")
    FileUtils.rm("/tmp/cleanupTests.apk") if File.exist?("/tmp/cleanupTests.apk")
  end
  return 200, response.to_json
end

get '/get_device_injected_file' do
  device = params[:device]
  testType = params[:testType]
  adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
  download_path = DOWNLOAD_FILES_DEVICE_DOWNLOAD_PATH
  download_path = DOWNLOAD_FILES_OBB_DOWNLOAD_PATH if testType == 'obb'
  response = adb.shell("ls #{download_path}")
  return 200, response
end

get '/foldable_screen' do
  response = {}
  screen_state = params[:screen_state]
  device = params[:device]

  device_obj = BrowserStack::AndroidDevice.new(device, "server.rb - foldable_screen", BrowserStack.logger)
  return 400, { error: "#{device}: is not a foldable device" }.to_json unless device_obj.foldable_device?

  begin
    adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
    output = adb.shell("cmd device_state state #{screen_state}")
    response["output"] = "state changed to #{screen_state}. #{output}"
    return 200, response.to_json
  rescue StandardError => e
    BrowserStack.logger.info("[device_state state command failed #{e.message}")
    return 500, { error: e.message.to_s }.to_json
  end
end

get '/run_ui_test' do
  device = params[:device]
  suite = params[:suite]
  classname = params[:class]
  device_name = params[:device_name]
  phone_number = params[:phone_number]
  adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
  begin
    BrowserStack.logger.info 'Starting to install app'
    download_and_install_qa_ui_automation_test(adb)
    BrowserStack.logger.info 'Installation completed'
    BrowserStack.logger.info 'Killing Existing uiautomator process'
    begin
      process_kill_response = adb.shell("ps -A | grep uiautomator | awk '{print $2}' | xargs kill -9")
      BrowserStack.logger.info "Killed Existing uiautomator process #{process_kill_response}"
    rescue StandardError => e
      BrowserStack.logger.info("[ No such process found #{e.message}")
    end
    ui_test_cmd = "-e suiteType '#{suite}' " \
    "-e deviceName '#{device_name}' " \
    "-e phoneNumber '#{phone_number}' " \
    "-e class 'com.browserstack.qa.test.#{classname}' "
    response = adb.shell("am instrument #{ui_test_cmd} -w #{BrowserStack::QA_TEST_RUNNER}")
  ensure
    FileUtils.rm(BrowserStack::QA_TEMP_PATH_APP) if File.exist?(BrowserStack::QA_TEMP_PATH_APP)
    FileUtils.rm(BrowserStack::QA_TEMP_PATH_TEST_SUITE) if File.exist?(BrowserStack::QA_TEMP_PATH_TEST_SUITE)
  end
  return 200, response
end

get '/trigger_interaction_sync_script' do
  BrowserStack.logger.info("Triggering test #{params['test_id']} with params #{params.inspect}")
  Thread.bs_run do
    InteractionSyncStabilityTester::InteractionSyncTestManager.new(
      'Android',
      params['device_name'],
      params['device_version'],
      params['browser'],
      params['automation_name'],
      params['selenium_port'],
      params['url'],
      params['test_id']
    ).execute_script
  rescue StandardError => e
    return 500, { message: "Script Trigger Failed: #{e.message}" }.to_json
  end
  return 200, { message: "Script Trigger Successful" }.to_json
end

get '/drain_nomad_allocations' do
  BrowserStack.logger.info("Draining Nomad Allocations For This Host")
  lock_file = '/tmp/nomad_drain.lock'
  wait_min = 2
  time_window = wait_min * 60

  if File.exist?(lock_file)
    last_run_time = File.mtime(lock_file)
    if Time.now - last_run_time < time_window
      BrowserStack.logger.info("Another Drain Happened in Last #{wait_min} Minutes. Skipping execution")
      return 429, { status: "Nomad Drain Skipped", message: "Drain Happened in Last #{wait_min} Minutes" }.to_json
    else
      BrowserStack.logger.info("Nomad Allocations Drain Allowed")
      drain_nomad_allocations(static_conf)
      FileUtils.touch(lock_file)
      return 202, { status: "Nomad Allocations Drain Request Accepted", message: "Drain Will Happen Async" }.to_json
    end
  else
    BrowserStack.logger.info("Lock File Doesn't Exist. Nomad Allocations Drain Allowed")
    drain_nomad_allocations(static_conf)
    FileUtils.touch(lock_file)
    return 202, { status: "Nomad Allocations Drain Request Accepted", message: "Drain Will Happen Async"  }.to_json
  end
rescue StandardError => e
  BrowserStack.logger.error("Exception in /drain_nomad_allocations : #{e.message} \n#{e.backtrace.join("\n")}")
  return 500, { status: "Exception while draining : #{e.message}", message: e.backtrace.join("\n") }.to_json
end

def force_stop_bundle_id(device_id, bundle_id)
  adb = AndroidToolkit::ADB.new(udid: device_id, path: BrowserStack::ADB)
  adb.shell("am force-stop #{bundle_id}")
  true
rescue AndroidToolkit::ADB::ADBError => e
  BrowserStack.logger.error "ADBError occurred while terminating app with bundle_id: #{bundle_id}\
       message: #{e.message}"
  false
rescue StandardError => e
  BrowserStack.logger.error "Error occurred while force terminating app with bundle_id: #{bundle_id}\
       message: #{e.message}"
  false
end

def drain_nomad_allocations(static_conf)
  ENV['NOMAD_TOKEN'] = static_conf['nomad_drain_token']
  system("(timeout 180 nomad node drain -self -enable; nomad node eligibility -enable -self) &")
  BrowserStack.logger.info("Nomad Allocations Drain & Religibility Completed")
rescue StandardError => e
  BrowserStack.logger.error("Exception in triggering Nomad commands: #{e.message} \n#{e.backtrace.join("\n")}")
end

post '/add_device_to_private_cloud' do
  device = params["device"]&.strip
  return [400, { error: '"device" not provided or empty' }.to_json] if device.nil? || device.empty?

  BrowserStack.logger.info("Device Addition Request :: Device: #{device}")

  # List of steps to be executed for a device to be added to the private cloud
  device_addition_steps = ["create_dedicated_file", "allow_install_unknown_sources", "dedicated_restriction_helper"]

  device_addition_steps.each do |step|
    case step
    when "create_dedicated_file"
      dedicated_device_file = "#{STATE_FILES_DIR}/dedicated_device_#{device}"
      adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
      unless File.exist?(dedicated_device_file)
        FileUtils.touch(dedicated_device_file)
        BrowserStack.logger.info("Dedicated device file created for #{device}")
      end
      begin
        adb.shell("touch #{DEDICATED_DEVICE_FILE_IN_DEVICE_PATH}")
        BrowserStack.logger.info("Created #{DEDICATED_DEVICE_FILE_IN_DEVICE_PATH} file for #{device}")
      rescue StandardError => e
        error_string = "Error creating #{DEDICATED_DEVICE_FILE_IN_DEVICE_PATH} file for #{device}: #{e.message}"
        zombie_push('android', 'dedicated-device-events', error_string, '', '', device)
        BrowserStack.logger.error(error_string)
        return [500, { error: "Internal server error during #{step}" }.to_json]
      end
    when "allow_install_unknown_sources"
      device_owner_manager = DeviceOwnerManager.new(device, BrowserStack.logger)
      device_owner_manager.allow_install_unknown_sources
      BrowserStack.logger.info("Allowed install of unknown sources for #{device}")
    when "dedicated_restriction_helper"
      restriction_manager = RestrictionScriptsManager.new(device, BrowserStack.logger)
      restriction_manager.ensure_install
      BrowserStack.logger.info("Installed dedicated restriction for #{device}")
    else
      return [400, { error: "Unhandled step: #{step}" }.to_json]
    end
  rescue StandardError => e
    BrowserStack.logger.error("Error during #{step} for device #{device}: #{e.message}")
    return [500, { error: "Internal server error during #{step}" }.to_json]
  end

  [200, { message: "Successfully completed all actions for device #{device}" }.to_json]
rescue StandardError => e
  BrowserStack.logger.error("Error adding device to private cloud: #{e.message}")
  [500, { error: 'Internal Server Error in adding device to private cloud' }.to_json]
end

get '/sim_details' do
  device = params["device"]&.strip
  return 400, { success: false, message: 'Device parameter is missing.', sim_details: {} }.to_json unless device

  sim_slot_param = params["sim_slot"]&.strip
  if sim_slot_param && ![1, 2].include?(sim_slot_param.to_i)
    return 400, { success: false, message: 'Invalid sim_slot. It must be 1 or 2.', sim_details: {} }.to_json
  end

  sim_details = {}

  # Fetch details for both slots
  (1..2).each do |slot|
    sim_detail = {
      'imei' => DeviceSIMHelper.fetch_sim_prop(device, "imei-#{slot - 1}", true, 4, slot),
      'iccid' => DeviceSIMHelper.fetch_sim_prop(device, "iccid-#{slot - 1}", true, 12, slot)
    }
    sim_details[slot.to_s] = sim_detail unless sim_detail.values.all?(&:empty?)
  end

  # If sim_slot_param is provided, filter the results
  if sim_slot_param
    sim_slot = sim_slot_param.to_i

    sim_details = sim_details[sim_slot.to_s] ? { sim_slot.to_s => sim_details[sim_slot.to_s] } : {}
  end

  message = sim_details.empty? ? "No SIM card found" : "SIM details retrieved successfully."
  return { success: true, message: message, sim_details: sim_details }.to_json
rescue StandardError => e
  BrowserStack.logger.error("Error fetching sim details for device '#{device}': #{e.message}")
  return 500, { success: false, message: "Error fetching sim details for device: #{device}" }.to_json
end

post '/sim_details' do
  device = params["device"]&.strip
  return 400, { success: false, message: 'Device parameter is missing.' }.to_json unless device

  sim_details = JSON.parse(request.body.read)
  DeviceSIMHelper.validate_sim_details(sim_details)

  sim_info_file = "#{STATE_FILES_DIR}/sim_info_#{device}"

  if File.exist?(sim_info_file)
    DeviceSIMHelper.update_existing_sim_details(sim_info_file, sim_details, device)
  else
    DeviceSIMHelper.create_new_sim_details(sim_info_file, sim_details, device)
  end

  return 200, { success: true, message: "Sim details added for device #{device}" }.to_json
rescue SimValidationError => e
  BrowserStack.logger.error("SimValidationError for #{device}: #{e.message}")
  return 400, { success: false, message: "SimValidationError for device #{device}: #{e.message}" }.to_json
rescue StandardError => e
  BrowserStack.logger.error("Error writing sim details file for device #{device}: #{e.message}")
  return 500, { success: false, message: "Error updating Sim details for device #{device}: #{e.message}" }.to_json
end

def delete_sim_info(sim_info_file, device)
  FileUtils.rm(sim_info_file)
  BrowserStack.logger.info("Sim details file deleted for device #{device}")
  [200, { success: true, message: "Sim details deleted for #{device}" }.to_json]
end

def delete_slot_specific_sim_info(sim_info_file, updated_sim_details, sim_slot, device)
  if updated_sim_details.empty?
    FileUtils.rm(sim_info_file)
  else
    File.write(sim_info_file, updated_sim_details.to_json)
  end

  BrowserStack.logger.info("Sim details for slot #{sim_slot} deleted for device #{device}")
  [200, { success: true, message: "Sim details for slot #{sim_slot} deleted for #{device}" }.to_json]
end

delete '/sim_details' do
  device = params["device"]&.strip
  sim_slot = params["sim_slot"]&.strip
  return 400, { success: false, message: 'Device parameter is missing.' }.to_json unless device

  sim_info_file = "#{STATE_FILES_DIR}/sim_info_#{device}"

  if File.exist?(sim_info_file)
    if sim_slot
      sim_details = JSON.parse(File.read(sim_info_file))
      updated_sim_details = sim_details.reject { |detail| detail["sim_slot"].to_s == sim_slot }

      if updated_sim_details.size < sim_details.size
        response = delete_slot_specific_sim_info(sim_info_file, updated_sim_details, sim_slot, device)
        return response[0], response[1]
      else
        BrowserStack.logger.info("No sim details found for slot #{sim_slot} on device #{device}")
        return 400, { success: false, message: "No sim details found for slot #{sim_slot} on device #{device}" }.to_json
      end
    else
      response = delete_sim_info(sim_info_file, device)
      return response[0], response[1]
    end
  else
    BrowserStack.logger.info("Sim details file not found for device #{device}")
    return 400, { success: false, message: "Sim details does not exist for #{device}" }.to_json
  end
rescue StandardError => e
  BrowserStack.logger.error("Error deleting sim details file for device #{device}: #{e.message}")
  return 500, { success: false, message: "Error deleting Sim details for #{device}: #{e.message}" }.to_json
end

get '/get_latest_sms' do
  content_type :json

  device_id = params['device_id']

  if device_id.nil? || device_id.strip.empty?
    return [400, { success: false, message: "'device_id' is required" }.to_json]
  end

  adb = AndroidToolkit::ADB.new(udid: device_id, path: BrowserStack::ADB)

  val = adb.shell(
    'content query --uri content://sms --projection address,body,type ' \
    '--where "body LIKE \'%Ally Dealer Services security code%\'" | ' \
    'awk \'/^Row: 0/{flag=1} /^Row: 1/{flag=0} flag\''
  )

  if val.nil? || val.strip.empty? || val.chomp.eql?("No result found.")
    return [200, { success: false, message: "No SMS present on the device" }.to_json]
  end

  return [200, { success: true, message: "SMS Details Found For #{device_id}", sms: val }.to_json]
rescue StandardError => e
  BrowserStack.logger.error("Failed to fetch SMS for device '#{device}': #{e.message}")
  return [500, { success: false, message: "Failed to fetch SMS", error: e.message }.to_json]
end

def parse_log_timestamp(line, offset_in_seconds)
  # Match Android logcat timestamp format: MM-dd HH:mm:ss.SSS
  match = line.match(/(\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})/)

  if match
    timestamp_str = match[1]
    begin
      # Assume current year for MM-dd format
      current_year = Time.now.year
      full_timestamp = "#{current_year}-#{timestamp_str}"
      parsed_time = Time.strptime(full_timestamp, "%Y-%m-%d %H:%M:%S.%L")
      adjusted_time = parsed_time - offset_in_seconds
      return (adjusted_time.to_f * 1000).to_i
    rescue ArgumentError => e
      BrowserStack.logger.warn("Failed to parse timestamp '#{timestamp_str}': #{e.message}")
      # Fall through to default if parsing fails
    end
  end

  # If no timestamp found or parsing failed, use current time
  (Time.now.to_f * 1000).to_i
end

def read_device_logs_from_position(file_path, start_pos, num_lines, timezone_offset)
  logs = []
  actual_start_pos = start_pos
  actual_end_pos = start_pos
  lines_read = 0
  offset = timezone_offset.to_i
  offset_in_seconds = offset.abs.divmod(100).then { |h, m| (h * 3600) + (m * 60) } * (offset.negative? ? -1 : 1)
  BrowserStack.logger.info("Offset in seconds: #{offset_in_seconds}")
  return { logs: logs, start_pos: actual_start_pos, end_pos: actual_end_pos } unless File.exist?(file_path)

  File.open(file_path, 'r') do |file|
    file.seek(start_pos)
    actual_start_pos = file.pos

    while lines_read < num_lines && (line = file.gets)
      timestamp = parse_log_timestamp(line, offset_in_seconds)
      level = "ALL"
      message = line.strip

      logs << {
        timestamp: timestamp,
        level: level,
        message: message
      }

      lines_read += 1
      actual_end_pos = file.pos
    end
  end

  { logs: logs, start_pos: actual_start_pos, end_pos: actual_end_pos }
rescue StandardError => e
  BrowserStack.logger.error("Error reading device logs from #{file_path}: #{e.message}")
  { logs: [], start_pos: start_pos, end_pos: start_pos }
end

get '/device_logs' do
  content_type :json
  # Validate required parameters
  session_id = params['session_id']
  device = params['device']

  return [400, { error: "session_id parameter is required" }.to_json] if session_id.nil? || session_id.strip.empty?
  return [400, { error: "device parameter is required" }.to_json] if device.nil? || device.strip.empty?

  start_pos = [params['start_pos'].to_i, 0].max
  num_lines = 10000

  begin
    log_file_path = "/var/log/browserstack/app_log_#{device}.log"
    return [404, { error: "Device log file not found for device #{device}" }.to_json] unless File.exist?(log_file_path)

    adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    timezone_offset = adb.shell("date +%z").to_s.to_i
    BrowserStack.logger.info("timezone_offset=#{timezone_offset}")
    result = read_device_logs_from_position(log_file_path, start_pos, num_lines, timezone_offset)

    response = {
      meta: {
        start_pos: result[:start_pos],
        end_pos: result[:end_pos]
      },
      value: result[:logs]
    }

    BrowserStack.logger.info("start_pos=#{result[:start_pos]}, end_pos=#{result[:end_pos]}")

    return [200, response.to_json]

  rescue StandardError => e
    BrowserStack.logger.error("Error retrieving device logs for #{device}: #{e.message} \n#{e.backtrace.join("\n")}")
    return [500, { error: "Internal server error while retrieving device logs" }.to_json]
  end
end
