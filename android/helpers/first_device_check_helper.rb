require 'json'
require 'net/http'
require 'timeout'
require 'uri'
require 'fileutils'

require_relative '../models/device_state'
require_relative './http_utils'
require_relative '../constants'
require_relative '../../common/helpers'

module BrowserStack
  module FirstDeviceCheckHelper
    class << self
      def first_device_check?(device_id)
        !DeviceState.new(device_id).first_device_thread_done_file_present?
      end

      def fetch_device_details_from_terminal_details_api(device_id, static_conf)
        rails_endpoint = static_conf.device_rails_endpoint(device_id)
        url = construct_terminal_details_url(device_id, rails_endpoint)
        credentials = get_api_credentials(rails_endpoint, static_conf)

        make_api_request(url, credentials.slice(:username, :password))
      rescue StandardError => e
        BrowserStack.logger.error("Error in fetch_device_details for #{device_id}: #{e.message}")
        {}
      end

      def perform_first_device_check_setup(device_id, device_config, static_conf, zombie)
        @zombie = zombie
        @static_conf = static_conf

        device_config ||= {}
        device_specific_details = fetch_device_details_from_terminal_details_api(device_id, static_conf)[device_id]

        if device_specific_details.nil? || device_specific_details.empty?
          BrowserStack.logger.warn("No details found from API for #{device_id}: First run setup may be incomplete.")
          return
        end

        @device_state = DeviceState.new(device_id)
        assign_device_details_attributes(device_specific_details)
        setup_sim_details_for_device
        setup_dedicated_device_state_for_device

      rescue StandardError => e
        BrowserStack.logger.error("Error during first run for #{device_id}: #{e.message} #{e.backtrace.join("\n")}")
      end

      private

      def assign_device_details_attributes(device_details)
        @sim_details = device_details['terminal_sim_details']
        private_terminal_mapping = device_details['private_terminal_mapping']
        @dedicated_device = private_terminal_mapping&.any?
        @dedicated_cleanup = @dedicated_device && private_terminal_mapping&.dig('cleanup_type') == 'dedicated'
      end

      def setup_sim_details_for_device
        @device_state.write_to_sim_info_file(JSON.generate(@sim_details)) if @sim_details&.any? && @dedicated_device
      end

      def setup_dedicated_device_state_for_device
        @device_state.remove_dedicated_device_file if @device_state.dedicated_device_file_present? && !@dedicated_device
        if @device_state.dedicated_cleanup_file_present? && !@dedicated_cleanup
          @device_state.remove_dedicated_cleanup_file
        end
        @device_state.touch_dedicated_device_file if !@device_state.dedicated_device_file_present? && @dedicated_device
        if !@device_state.dedicated_cleanup_file_present? && @dedicated_cleanup
          @device_state.touch_dedicated_cleanup_file
        end
      rescue StandardError => e
        BrowserStack.logger.error("Error in dedicated state for #{device_id}: #{e.message} #{e.backtrace.join("\n")}")
      end

      def construct_terminal_details_url(device_id, rails_endpoint)
        rails_endpoint = "#{rails_endpoint}.bsstag.com" unless
          rails_endpoint =~ /^localhost(:[0-9]+)?$/ ||
          rails_endpoint.include?('.') ||
          rails_endpoint.include?(BrowserStack::PRODUCTION_SITE)

        rails_endpoint = rails_endpoint.sub(%r{^http:[/\\]+}, '')
        uri = URI("https://#{rails_endpoint}/admin/mobile_terminals/details")
        uri.query = URI.encode_www_form({ instance_ids: [device_id] })
        uri.to_s
      rescue StandardError => e
        BrowserStack.logger.error("Error in constructing URL for #{device_id}: #{e.message}")
        nil
      end

      def get_api_credentials(rails_endpoint, static_conf)
        is_staging = !rails_endpoint.include?(BrowserStack::PRODUCTION_SITE)
        username_key = is_staging ? "staging_rails_user" : "admin_terminals_user"
        password_key = is_staging ? "staging_rails_pass" : "admin_terminals_pass"

        unless static_conf[username_key] && static_conf[password_key]
          BrowserStack.logger.error("Missing credentials in static_conf: Required: #{username_key}, #{password_key}")
          return nil
        end

        { username: static_conf[username_key], password: static_conf[password_key], is_staging: is_staging }
      rescue StandardError => e
        BrowserStack.logger.error("Error in getting API credentials for #{device_id}: #{e.message}")
        nil
      end

      def make_api_request(url, auth)
        response_body = {}
        response = HttpUtils.send_get(url, auth)

        return {} unless response.code == "200"

        parsed_resp = JSON.parse(response.body)
        return parsed_resp['details'] if parsed_resp['success'] && parsed_resp['details']

        error_message = "terminal details API call was not successful for #{@device_id}: #{parsed_resp['message']}"
        log_and_push_error("terminal_api_request_failed", error_message, { device_id: @device_id, url: url })
        {}
      rescue StandardError => e
        error_message = "Error making API request for #{@device_id}: #{e.message}"
        log_and_push_error("terminal_api_request_failed", error_message, { device_id: @device_id, url: url })
        {}
      end

      def log_and_push_error(kind, message, data)
        BrowserStack.logger.error(message)
        @zombie.push_logs(kind, message, data)
      end
    end
  end
end
