<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<map>
    <boolean name="SETTINGS_DEFAULT_VOICE_INPUT" value="true" />
    <string name="SETTINGS_DEFAULT_HWR_RECOGNITION_TYPE">1</string>
    <boolean name="SETTINGS_DEFAULT_KEYPAD_FLICK_UMLAUT" value="false" />
    <boolean name="0x68720000" value="false" />
    <boolean name="0x73760000" value="false" />
    <boolean name="0x66690000" value="false" />
    <boolean name="0x6b610000" value="false" />
    <boolean name="0x64650000" value="false" />
    <boolean name="0x656c0000" value="false" />
    <boolean name="0x70740000" value="false" />
    <boolean name="SETTINGS_DEFAULT_HWR_ON" value="true" />
    <boolean name="is_one_hand_right_set" value="true" />
    <boolean name="SETTINGS_DEFAULT_USE_ADDTO_NUMBER_KEY_FIRST_LINE" value="true" />
    <boolean name="0x756b0000" value="false" />
    <boolean name="0x6e620000" value="false" />
    <boolean name="smt_init_fail_flag" value="false" />
    <boolean name="is_last_mode_was_dex" value="false" />
    <boolean name="SETTINGS_DEFAULT_AUTO_PERIOD" value="false" />
    <string name="SETTINGS_DEFAULT_KEYPAD_TYPE">0</string>
    <boolean name="0x6c760000" value="false" />
    <boolean name="SETTINGS_DEFAULT_PREDICTION_ON" value="true" />
    <boolean name="0x656e5553" value="false" />
    <int name="KEY_INPUT_MODE" value="0" />
    <boolean name="0x63610000" value="false" />
    <boolean name="0x68790000" value="false" />
    <boolean name="0x656e4742" value="false" />
    <boolean name="0x736b0000" value="false" />
    <int name="input_language" value="1701726018" />
    <boolean name="SETTINGS_DEFAULT_SUPPORT_KEY_VIBRATE" value="true" />
    <boolean name="SETTINGS_KEYBOARD_TRANSLITERATION_ENGLISH_ENABLED" value="true" />
    <boolean name="sticker_need_show_badge" value="false" />
    <boolean name="SETTINGS_DEFAULT_USE_ALTERNATIVE_CHARACTERS" value="false" />
    <boolean name="0x73720000" value="false" />
    <boolean name="SETTINGS_DEFAULT_KEYPAD_SWEEPING" value="false" />
    <boolean name="0x6e6c0000" value="false" />
    <boolean name="0x67610000" value="false" />
    <boolean name="0x6b6b0000" value="false" />
    <boolean name="0x69740000" value="false" />
    <boolean name="SETTINGS_DEFAULT_USE_PREVIEW" value="true" />
    <boolean name="SETTINGS_DEFAULT_PEN_DETECTION" value="false" />
    <boolean name="0x676c0000" value="false" />
    <boolean name="SETTINGS_DEFAULT_SPACE_LANGUAGE_CHANGE" value="true" />
    <boolean name="0x64610000" value="false" />
    <boolean name="0x6d6b0000" value="false" />
    <boolean name="0x72750000" value="false" />
    <int name="SETTINGS_HIGH_CONTRAST_KEYBOARD_THEME" value="0" />
    <boolean name="SETTINGS_DEFAULT_SUPPORT_KEY_SOUND" value="true" />
    <string name="SETTINGS_DEFAULT_NUMBER_AND_SYMBOLS_KEYPAD_TYPE">0</string>
    <boolean name="SETTINGS_ADD_NUMBER_ROW_SETTING" value="true" />
    <boolean name="SETTINGS_DEFAULT_AUTO_CAPS" value="true" />
    <int name="input_mode_type_for_dex" value="0" />
    <boolean name="0x736c0000" value="false" />
    <boolean name="0x62670000" value="false" />
    <boolean name="0x63730000" value="false" />
    <string name="omc_path">/odm/omc/single/BTU/conf</string>
    <boolean name="0x726f0000" value="false" />
    <boolean name="key_universal_switch_pref" value="false" />
    <boolean name="0x706c0000" value="false" />
    <int name="0x656e4742order" value="0" />
    <boolean name="SETTINGS_DEFAULT_TRACE" value="false" />
    <boolean name="SETTINGS_DEFAULT_KEYPAD_POINTING" value="false" />
    <boolean name="0x65730000" value="false" />
    <boolean name="SETTINGS_DEFAULT_USE_SYSTEM_LANGUAGE" value="false" />
    <string name="support_languages">EN_GB;EN_US;AZ;CA;CS;DA;DE;ET;ES;EU;EL;FR;GA;GL;KA;HR;IT;IS;KK;LV;LT;HU;NB;NL;PL;PT;RU;RO;FI;SR;SK;SL;SV;TR;UK;KO;HY;BG;MK;</string>
    <string name="latest_emoticon_list">😊 😄 😉 😆 😋 😁 😂 </string>
    <boolean name="SETTINGS_DEFAULT_ONE_HAND" value="false" />
    <boolean name="0x74720000" value="false" />
    <boolean name="0x6b6f0000" value="false" />
    <boolean name="SETTINGS_DEFAULT_USE_INPUT_METHOD_TYPE_ON_KOR" value="false" />
    <boolean name="dlm_init_fail_flag" value="false" />
    <boolean name="0x65740000" value="false" />
    <boolean name="0x66720000" value="false" />
    <boolean name="0x68750000" value="false" />
    <string name="latest_symbol_list">? ! @ - ~ ^ ♡ </string>
    <string name="SETTINGS_DEFAULT_HWR_RECOGNIZE_DELAY">300</string>
    <boolean name="0x65750000" value="false" />
    <boolean name="0x69730000" value="false" />
    <boolean name="0x6c740000" value="false" />
    <boolean name="0x617a0000" value="false" />
    <boolean name="SETTINGS_ON_BOARDING_STATE" value="true" />
    <boolean name="SETTINGS_ON_BOARDING_BACK_PRESSED" value="false" />
</map>