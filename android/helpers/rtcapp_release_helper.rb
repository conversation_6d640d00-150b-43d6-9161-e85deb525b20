# WARNING: Do not add gems. This helper is used in bash scripts without bundle exec.

require_relative '../constants'

require 'nokogiri'

class RTCAppReleaseHelper

  def get_version_to_download(use_rtc_app)
    manifest_file = use_rtc_app == "v2" ? BrowserStack::RTCAPP_2_MANIFEST_FILE : BrowserStack::RTCAPP_MANIFEST_FILE
    manifest = Nokogiri::XML(File.read(manifest_file))
    version_name = manifest.at_xpath('//manifest').attr('android:versionName')
  end

  def should_install_rtc_app?(use_rtc_app, device_name, os_version)
    return true if use_rtc_app == "v1"
    return false if use_rtc_app != "v2" || BrowserStack::RTCAPP_2_NON_SUPPORTED_DEVICES.include?(device_name)
    return true if os_version.to_f >= 5.0 # enable for all devices >= 5

    false
  end

  def mp_already_provisioned?(device_id)
    rtc2_service = "fr.pchab.AndroidRTC2/.RTCService"
    `adb -s #{device_id} shell am startservice --user 0 -n #{rtc2_service} --es action is-mp-provisioned`
    file_output = `adb -s #{device_id} shell ls /sdcard/mp_already_provisioned 2>&1`
    !file_output.include?("No such file or directory")
  end
end

if $PROGRAM_NAME == __FILE__
  method = ARGV[0]
  raise 'No method given' if method.nil?

  helper = RTCAppReleaseHelper.new

  case method
  when 'get_version_to_download'
    use_rtc_app = ARGV[1]
    puts helper.get_version_to_download(use_rtc_app)
    exit
  when 'should_install_rtc_app'
    use_rtc_app = ARGV[1]
    device_name = ARGV[2]
    os_version = ARGV[3]
    if use_rtc_app.nil? || device_name.nil? || os_version.nil?
      raise "missing params for should_install_rtc_app?(#{use_rtc_app},#{device_name},#{os_version})"
    end

    puts helper.should_install_rtc_app?(use_rtc_app, device_name, os_version)
    exit
  when 'mp_already_provisioned'
    device_id = ARGV[1]
    puts helper.mp_already_provisioned?(device_id)
    exit
  end

end
