#!/bin/bash
set -x
TODO=$1
DEVICEID=$2

ALL_COMMANDS_TIME=$( date +%s%3N )
DEFINE_VARIABLE_TIME=$( date +%s%3N )
STATE_FILES_DIR="/usr/local/.browserstack/state_files"
DRIVER_ACTIONS="/usr/local/.browserstack/mobile/android/driver_actions.sh"
LIVE_SCRIPT="/usr/local/.browserstack/mobile/android/live/scripts/live_actions.sh"
CONFIG="/usr/local/.browserstack/config"
FILE_UPLOAD_DIRECTORY="/sdcard/automate_uploads/"
TEMP_STATUSBAR_FILE="/data/local/tmp/check_statusbar"
SESSION_START_DIR="$STATE_FILES_DIR/session_start"
DATA_LOCAL_TMP="/data/local/tmp"
DEVICELOGS_FILE="/var/log/browserstack/app_log_$DEVICEID.log"
DUP_DEVICELOGS_FILE="/tmp/app_log_$DEVICEID.log"
APP_SESSION_FILE="/tmp/app_session_$DEVICEID"
MOBILE_PACKAGE_FILE="/sdcard/app_package_name"
FLOW_TO_HAR="/usr/local/.browserstack/mobile/android/scripts/har_dump.py"
INTERACTION_CONFIG_FILE="/usr/local/.browserstack/mobile/android/live/interaction_server/device_config.json"

mkdir -p /usr/local/.browserstack/automate_screenshot

source /usr/local/.browserstack/mobile/android/common.sh
source /usr/local/.browserstack/mobile/android/check_status_bar_management.sh
source /usr/local/.browserstack/mobile/android/helpers/version_comparison.sh

kill_mitm() {
  MITMPROXY_LISTEN_PORT=`cat /tmp/mitmdump_port_$DEVICEID`
  if [[ ! -z $MITMPROXY_LISTEN_PORT ]]; then
    MITM_PIDS=`ps aux | grep mitmdump | grep $MITMPROXY_LISTEN_PORT | grep -v grep | awk '{print $2}' | xargs kill -9`
  fi
}

log() {
  echo "`date -u`: "$@ >> /var/log/browserstack/selenium_$DEVICEID.log
}

perform_wifi_check() {
  wifi_check=`$ADB -s $DEVICEID shell dumpsys wifi | grep mNetworkInfo`
  log "Wifi check: $wifi_check"
}

start_minicap() {
  STREAMING_ARGS=$(cat $CONFIG/streaming_params_$DEVICEID)

  if [ -f "$INTERACTION_CONFIG_FILE" ] && jq -e ".deviceConfigurations[\"$device_model\"]" "$INTERACTION_CONFIG_FILE" > /dev/null; then
    stream_width=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_width" "$INTERACTION_CONFIG_FILE")
    stream_height=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_height" "$INTERACTION_CONFIG_FILE")
  else
    echo "$device_model not found in $INTERACTION_CONFIG_FILE or file absent. Falling back to STREAMING_ARGS."
    stream_width=$(echo "$STREAMING_ARGS" | grep -o 'streaming_width=[^ ]*' | cut -d '=' -f2)
    stream_height=$(echo "$STREAMING_ARGS" | grep -o 'streaming_height=[^ ]*' | cut -d '=' -f2)
  fi

  width_height="${stream_width}x${stream_height}"
  real_size=$(adb -s $DEVICEID shell 'wm size' | grep 'Physical size' | awk -F ': ' '{ print $2 }' | tr -d "\n\r")
  adb -s $DEVICEID shell "cd /data/local/tmp; CLASSPATH=/data/local/tmp/minicap-debug.apk app_process /system/bin io.devicefarmer.minicap.Main -P $real_size@$width_height/0 -t &"  >> /var/log/browserstack/screencap_$DEVICEID.log &
}

start_screencap() {
  if [[ -z $device_model ]]; then
      warn "No device_model. Trying to fetch..."
      get_device_model
  fi

  STREAMING_ARGS=$(cat $CONFIG/streaming_params_$DEVICEID)

  if [ -f "$INTERACTION_CONFIG_FILE" ] && jq -e ".deviceConfigurations[\"$device_model\"]" "$INTERACTION_CONFIG_FILE" > /dev/null; then
    stream_width=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_width" "$INTERACTION_CONFIG_FILE")
    stream_height=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_height" "$INTERACTION_CONFIG_FILE")
  else
    echo "$device_model not found in $INTERACTION_CONFIG_FILE or file absent. Falling back to STREAMING_ARGS."
    stream_width=$(echo "$STREAMING_ARGS" | grep -o 'streaming_width=[^ ]*' | cut -d '=' -f2)
    stream_height=$(echo "$STREAMING_ARGS" | grep -o 'streaming_height=[^ ]*' | cut -d '=' -f2)
  fi

  adb -s $DEVICEID shell <<__EOF &
    cd $DATA_LOCAL_TMP
    sh start_binary.sh $DATA_LOCAL_TMP/bs_screencap "$interaction_ags debug" &
    cd /
__EOF
}

set_device_orientation(){
  adb -s $DEVICEID shell am startservice --user 0 -n com.android.browserstack/.services.OrientationService --es "orientation" "$1"
}

set_timezone(){
  if device_is_a bsrun_device; then
    run_as_root "setprop persist.sys.timezone $1"
  else
    adb -s $DEVICEID shell service call alarm 3 s16 $1
  fi
}

get_device_orientation(){
  # String echoed is written to file
  get_os_version
  if vers_lt "$OS_VERSION" "13"; then
    rotation_angle=`timeout 10 adb -s $DEVICEID shell dumpsys input | grep 'SurfaceOrientation' | awk '{ print $2 }' | tr -d '[[:space:]]'`
  else
    rotation_angle=`timeout 10 adb -s $DEVICEID shell dumpsys input | grep -Eoi -m 1  'viewport internal.*orientation=[0-9]' | awk '{print $NF}' | tr -d -c 0-9 |  tr -d '[[:space:]]'`
  fi

  if [ $(( $rotation_angle % 2 )) = 0 ]
  then
    echo "portrait"
  else
    echo "landscape"
  fi
}

archive_and_truncate_appium_logs() {
  DEVICE=$1
  APPIUM_LOGS="/var/log/browserstack/appium_${DEVICEID}.log"
  APPIUM_LOGS_ARCHIVE="/var/log/browserstack/appium_${DEVICEID}.log.archive"
  cat $APPIUM_LOGS >> $APPIUM_LOGS_ARCHIVE
  truncate -s 0 $APPIUM_LOGS
}

upload_network_logs_chrome_har() {
  HAR_FILE="/tmp/har_file_${DEVICEID}_${SESSION_ID}.har"
  echo "Trying to upload network logs, present in $HAR_FILE with SESSION_ID ${SESSION_ID}, REGION $AWS_REGION, BUCKET $AWS_BUCKET, GENRE $GENRE"

  if [ -f $HAR_FILE ]; then
    echo "Network logs found."
    s3_url="https://s3$AWS_REGION.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$SESSION_ID-har-logs.txt"
    FILE_SIZE_KB=$(expr $(ls -l $HAR_FILE  | awk '{print $5}') / 1024)
    if [[ "$session_type" == "app_automate" ]]; then
      EDS_KIND="app_automate_test_sessions"
    else
      EDS_KIND="automate_test_sessions"
    fi
    response=`timeout 101 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "text/json" "$HAR_FILE" "public-read" "$s3_url" "$AWS_REGION" 100 "$SESSION_ID" "$GENRE"`
    # timeout 100 /usr/local/bin/s3curl --id=$AWS_KEY --key=$AWS_SECRET --contentType "text/json" --put=$HAR_FILE -- -H "x-amz-acl: public-read" $s3_url
    if [ $? -ne 0 ]
    then
      eds_data='{"feature_usage":{"networkLogs":{"success":"false","size":"'$FILE_SIZE_KB'","exception":"'$response'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb $EDS_KIND $eds_data
    else
      eds_data='{"feature_usage":{"networkLogs":{"success":"true","size":"'$FILE_SIZE_KB'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb $EDS_KIND $eds_data
    fi
    rm $HAR_FILE
  else
    echo "Network logs NOT found."
  fi
}

function log_start_time() {
  START=""
  END=""
  START=$(date +%s%N)
}
function log_end_time() {
  END=$(date +%s%N)
  LOG_TIME=`echo "($END - $START)/1000000" | bc`
  echo $LOG_TIME
}

function install_settings_and_unlock_apks() {
  bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$APPIUM_SETTINGS_PATH "io.appium.settings" "false" "false" "false" &
  settings_installation_pid="$!"
  bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$APPIUM_UNLOCK_PATH "io.appium.unlock" "false" "false" "false" &
  unlock_installation_pid="$!"
  bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$APPIUM_UNICODE_IME_PATH "io.appium.android.ime" "false" "false" "false" &
  unicode_ime_installation_pid="$!"
  wait "$settings_installation_pid" "$unlock_installation_pid" "$unicode_ime_installation_pid"
}

function install_settings_apk() {
  bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$APPIUM_SETTINGS_PATH "io.appium.settings" "false" "false" "false" &
  settings_installation_pid="$!"
  wait "$settings_installation_pid"
}

function handle_appium() {
    DEFAULT_APPIUM_VERSION=$(get_default_appium_version $OS_VERSION)

    PRE_APPIUM_COMMANDS_TIME=$(bc <<< "`date +%s%3N` - $PRE_APPIUM_COMMANDS_TIME")
    # Restart appium only after session-touch to avoid device check
    SHOULD_WAIT_FOR_APPIUM=1
    APPIUM_CREATE_TIME=$( date +%s%3N )
    if [[ ${APPIUM_VERSION} != ${DEFAULT_APPIUM_VERSION} ]] || [[ ! -z "$CHROME_DRIVER_VERSION" ]]; then
      sudo bash /usr/local/.browserstack/mobile/android/helpers/create_appium_service.sh "$DEVICEID" "$APPIUM_PORT" "$CHROME_DRIVER_PORT" "$ANDROID_BOOTSTRAP_PORT" "$APPIUM_VERSION" "create_kill_and_wait" "$CHROME_DRIVER_VERSION" "$AUTOMATION_NAME" "$AUTOMATION_VERSION" &
      APPIUM_CREATION_PID="$!"
    else
      # We dont need to kill/restart appium as we ensure in /stop that the appium being used is default one. Create only creates the RUN_FILE if it doesn't already exist
      sudo bash /usr/local/.browserstack/mobile/android/helpers/create_appium_service.sh "$DEVICEID" "$APPIUM_PORT" "$CHROME_DRIVER_PORT" "$ANDROID_BOOTSTRAP_PORT" "$APPIUM_VERSION" "create_and_wait" "$CHROME_DRIVER_VERSION" &
      APPIUM_CREATION_PID="$!"
      touch "/tmp/avoid_appium_apk_install_$DEVICEID"
    fi
    APPIUM_CREATE_TIME=$(bc <<< "`date +%s%3N` - $APPIUM_CREATE_TIME")

    APPIUM_APKS_TIME=$( date +%s%3N )

    get_appium_apks_path "$APPIUM_VERSION" "$AUTOMATION_NAME" "$AUTOMATION_VERSION"
    if [ ${APPIUM_VERSION} != ${DEFAULT_APPIUM_VERSION} ]; then
      if ( vers_lt "$APPIUM_VERSION" "1.10.1" && vers_gte "$APPIUM_VERSION" "1.6.5" ); then
        install_settings_and_unlock_apks &
        SETTING_AND_UNLOCK_INSTALLATION_PID="$!"
      elif [ "$APPIUM_VERSION" == "1.4.16" ] || [ "$APPIUM_VERSION" == "1.5.3" ]; then
        adb -s $DEVICEID uninstall io.appium.settings
        adb -s $DEVICEID uninstall io.appium.unlock
      else
        install_settings_apk &
        SETTING_AND_UNLOCK_INSTALLATION_PID="$!"
      fi

      # TODO: For AED sessions, Default UIA2 apks will remain installed. Can be uninstalled.
      if (vers_gte "$OS_VERSION" "4.4") && [[ "$AUTOMATION_NAME" != "espresso" ]]; then
        bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$UIAUTOMATOR2_APKS_PATH/$UIAUTOMATOR2_SERVER_APK "io.appium.uiautomator2.server" "false" "false" "false" &
        uiautomator2_server_installation_pid="$!"
        bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$UIAUTOMATOR2_APKS_PATH/$UIAUTOMATOR2_SERVER_TEST_APK "io.appium.uiautomator2.server.test" "false" "false" "false" &
        uiautomator2_server_test_installation_pid="$!"
        wait "$uiautomator2_server_installation_pid" "$uiautomator2_server_test_installation_pid"
      fi

      #bash $DRIVER_ACTIONS grant_permissions_and_launch_appium_settings $DEVICEID
      APKS_INSTALLATION_WAIT_TIME=$( date +%s%3N )
      wait "$SETTING_AND_UNLOCK_INSTALLATION_PID"
      APKS_INSTALLATION_WAIT_TIME=$(bc <<< "`date +%s%3N` - $APKS_INSTALLATION_WAIT_TIME")
      timeout 2 $ADB -s $DEVICEID shell pm grant io.appium.settings android.permission.READ_PHONE_STATE &
      grant_permission_read_phone_pid="$!"
      timeout 2 $ADB -s $DEVICEID shell pm grant io.appium.settings android.permission.ACCESS_FINE_LOCATION &
      grant_permission_access_fine_location_pid="$!"
      timeout 2 $ADB -s $DEVICEID shell am start -n io.appium.settings/io.appium.settings.Settings &
      start_appium_settings_pid="$!"
      bash $DRIVER_ACTIONS grant_permissions $DEVICEID &
      grant_common_permissions_pid="$!"
      wait "$grant_permission_read_phone_pid" "$grant_permission_access_fine_location_pid" "$start_appium_settings_pid" "$grant_common_permissions_pid"
    fi

    APPIUM_APKS_TIME=$(bc <<< "`date +%s%3N` - $APPIUM_APKS_TIME")

}

case $TODO in
  start )
    DEFINE_VARIABLE_TIME=$(bc <<< "`date +%s%3N` - $DEFINE_VARIABLE_TIME")
    PRE_APPIUM_COMMANDS_TIME=$( date +%s%3N )
    DEBUG_PORT=$3
    WIDTH=$4
    ORIENTATION=$5
    APPIUM_VERSION=$6
    APPIUM_PORT=$7
    CHROME_DRIVER_PORT=$8
    ANDROID_BOOTSTRAP_PORT=$9
    TIMEZONE=${10}
    APP_TESTING=${11}
    APP_TESTING_BUNDLEID=${12}
    DEVICELOGS_ENABLED=${13}
    session_id=${14}
    CHROME_DRIVER_VERSION=${15}
    ENABLE_CONTACTS_APP_ACCESS="${16}"
    AUTOMATION_NAME=${17}
    AUTOMATION_VERSION=${18}

    log "Start request for selenium"
    touch $STATE_FILES_DIR/session_$DEVICEID
    perform_wifi_check &
    PERFORM_WIFI_CHECK_PID="$!"
    touch_in_session $session_id
    get_os_version
    handle_appium

    if [ "$?" = "0" ]; then
      SCREEN_OFF_TIME=$( date +%s%3N )
      adb -s $DEVICEID shell settings put system screen_off_timeout 86400000

      start_logcat_capture &
      START_LOGCAT_CAPTURE_PID="$!"

      log_gms_version &
      LOG_GMS_VERSION_PID="$!"
      SCREEN_OFF_TIME=$(bc <<< "`date +%s%3N` - $SCREEN_OFF_TIME")

      SCREEN_UNLOCK_TIME=$( date +%s%3N )
      ensure_screen_is_unlocked
      SCREEN_UNLOCK_TIME=$(bc <<< "`date +%s%3N` - $SCREEN_UNLOCK_TIME")
      SET_ORIENTATION_TIME=$( date +%s%3N )
      if [[ $ORIENTATION != "portrait" ]]; then
        # Set orientation "portrait" is also called in ensure_screen_is_unlocked
        set_device_orientation $ORIENTATION &
        ORIENTATION_SETTING_PID="$!"
      fi
      SET_ORIENTATION_TIME=$(bc <<< "`date +%s%3N` - $SET_ORIENTATION_TIME")

      log_app_anr &

      SET_TIMEZONE_TIME=$( date +%s%3N )
      if [[ $TIMEZONE != "" ]]; then
        set_timezone $TIMEZONE &
        TIMEZONE_SETTING_PID="$!"
      fi
      SET_TIMEZONE_TIME=$(bc <<< "`date +%s%3N` - $SET_TIMEZONE_TIME")

      PORT_FORWARD_TIME=$( date +%s%3N )
      adb -s $DEVICEID forward tcp:$DEBUG_PORT tcp:2020
      PORT_FORWARD_TIME=$(bc <<< "`date +%s%3N` - $PORT_FORWARD_TIME")

      SCREENCAP_START_TIME=$( date +%s%3N )
      if vers_gte "$OS_VERSION" "12"; then
        start_minicap
      else
        start_screencap
      fi
      SCREENCAP_START_TIME=$(bc <<< "`date +%s%3N` - $SCREENCAP_START_TIME")

      PRODUCT="automate"

      APP_AUTOMATE_CODE_TIME=$( date +%s%3N )
      if [ "$APP_TESTING" == "true" ]; then
        PRODUCT="app_automate"
        dismiss_crash_popup &
        disable_chrome_welcome_screen
        play_services $DEVICEID "enable" &

        # Allow user's app and appium apps in check_status_bar
        echo $APP_TESTING_BUNDLEID > $APP_SESSION_FILE
        echo 'io.appium.unlock' >> $APP_SESSION_FILE
        echo 'io.appium.settings' >> $APP_SESSION_FILE

        # Allow other apps in check_status_bar
        if [[ `cat /tmp/duplicate_session_$DEVICEID | jq -r ".other_app_bundle_ids"` != "null" ]]; then
          for other_app in `cat /tmp/duplicate_session_$DEVICEID | jq -r ".other_app_bundle_ids[]"`; do
            echo `echo "$other_app" | tr -d "\r\n"` >> $APP_SESSION_FILE
          done
        fi

        if [[ `cat /tmp/duplicate_session_$DEVICEID | jq -r ".testPackageName"` != "null" ]]; then
          echo `cat /tmp/duplicate_session_$DEVICEID | jq -r ".testPackageName" | tr -d "\r\n"` >> $APP_SESSION_FILE
        fi

        adb -s $DEVICEID push $APP_SESSION_FILE $MOBILE_PACKAGE_FILE

        adb -s $DEVICEID shell "mkdir -p $SESSION_DIR; touch $SESSION_DIR/bs-session-app-automate.txt"

        if [[ `cat /tmp/duplicate_session_$DEVICEID | jq -r ".mid_session_app_bundle_ids"` != "null" ]]; then
          for mid_session_app in `cat /tmp/duplicate_session_$DEVICEID | jq -r ".mid_session_app_bundle_ids[]"`; do
            echo `echo "$mid_session_app" | tr -d "\r\n"` >> $APP_SESSION_FILE
          done
        fi
        # Post mid_session_app and other_app processsing, possible that `MOBILE_PACKAGE_FILE` may contain
        # duplicate bundle_ids. Even possible that the bundle_id is NOT YET installed on device.

        if [[ $DEVICELOGS_ENABLED == "true" ]]; then
          truncate -s 0 $DEVICELOGS_FILE
          adb -s $DEVICEID logcat -c
          touch $DEVICELOGS_FILE
        fi
      fi
      APP_AUTOMATE_CODE_TIME=$(bc <<< "`date +%s%3N` - $APP_AUTOMATE_CODE_TIME")

      RESTART_CHECK_STATUS_TIME=$( date +%s%3N )
      enable_contacts_param=""
      if [ $ENABLE_CONTACTS_APP_ACCESS == "true" ]; then
        enable_contacts_param='--enable-contacts'
      fi
      restart_check_status_bar $PRODUCT "${enable_contacts_param}"
      RESTART_CHECK_STATUS_TIME=$(bc <<< "`date +%s%3N` - $RESTART_CHECK_STATUS_TIME")

      WIFI_CHECK_WAIT_TIME=$( date +%s%3N )
      wait $PERFORM_WIFI_CHECK_PID
      WIFI_CHECK_WAIT_TIME=$(bc <<< "`date +%s%3N` - $WIFI_CHECK_WAIT_TIME")

      # Ensuring appium kill(for non-default appium version) and status logs gets archived before session start
      APPIUM_LOGS_TRUNCATION_TIME=$( date +%s%3N )
      wait "$APPIUM_CREATION_PID"
      if [ "$?" != "0" ]; then
        exit 1
      fi
      archive_and_truncate_appium_logs $DEVICEID
      APPIUM_LOGS_TRUNCATION_TIME=$(bc <<< "`date +%s%3N` - $APPIUM_LOGS_TRUNCATION_TIME")

      ORIENTATION_SETTING_WAIT_TIME=$( date +%s%3N )
      wait "$ORIENTATION_SETTING_PID"
      ORIENTATION_SETTING_WAIT_TIME=$(bc <<< "`date +%s%3N` - $ORIENTATION_SETTING_WAIT_TIME")

      TIMEZONE_SETTING_WAIT_TIME=$( date +%s%3N )
      wait "$TIMEZONE_SETTING_PID"
      TIMEZONE_SETTING_WAIT_TIME=$(bc <<< "`date +%s%3N` - $TIMEZONE_SETTING_WAIT_TIME")

      LOG_GMS_VERSION_WAIT_TIME=$( date +%s%3N )
      wait $LOG_GMS_VERSION_PID
      LOG_GMS_VERSION_WAIT_TIME=$(bc <<< "`date +%s%3N` - $LOG_GMS_VERSION_WAIT_TIME")

      LOGCAT_CAPTURE_WAIT_TIME=$( date +%s%3N )
      wait $START_LOGCAT_CAPTURE_PID
      LOGCAT_CAPTURE_WAIT_TIME=$(bc <<< "`date +%s%3N` - $LOGCAT_CAPTURE_WAIT_TIME")

      ALL_COMMANDS_TIME=$(bc <<< "`date +%s%3N` - $ALL_COMMANDS_TIME")
      INSTRUMENTATION_LOG_FILE=/tmp/$DEVICEID-instrumentation.log

      echo "Log was generated on:" `date` > $INSTRUMENTATION_LOG_FILE

      for time_var in DEFINE_VARIABLE_TIME PRE_APPIUM_COMMANDS_TIME APPIUM_CREATE_TIME SCREEN_OFF_TIME SCREEN_UNLOCK_TIME SET_ORIENTATION_TIME SET_TIMEZONE_TIME PORT_FORWARD_TIME OS_VERSION APPIUM_VERSION DEFAULT_APPIUM_VERSION APPIUM_APKS_TIME SCREENCAP_START_TIME APP_AUTOMATE_CODE_TIME RESTART_CHECK_STATUS_TIME WIFI_CHECK_WAIT_TIME APPIUM_LOGS_TRUNCATION_TIME APKS_INSTALLATION_WAIT_TIME ORIENTATION_SETTING_WAIT_TIME TIMEZONE_SETTING_WAIT_TIME LOG_GMS_VERSION_WAIT_TIME LOGCAT_CAPTURE_WAIT_TIME ALL_COMMANDS_TIME; do
        echo $time_var ${!time_var}
        echo $time_var ${!time_var} >> $INSTRUMENTATION_LOG_FILE
      done
      exit 0
    else
      exit 1
    fi;;
  minified_start )
    DEFINE_VARIABLE_TIME=$(bc <<< "`date +%s%3N` - $DEFINE_VARIABLE_TIME")
    PRE_APPIUM_COMMANDS_TIME=$( date +%s%3N )
    DEBUG_PORT=$3
    WIDTH=$4
    ORIENTATION=$5
    APPIUM_VERSION=$6
    APPIUM_PORT=$7
    CHROME_DRIVER_PORT=$8
    ANDROID_BOOTSTRAP_PORT=$9
    TIMEZONE=${10}
    APP_TESTING=${11}
    APP_TESTING_BUNDLEID=${12}
    DEVICELOGS_ENABLED=${13}
    session_id=${14}
    CHROME_DRIVER_VERSION=${15}
    AUTOMATION_NAME=${17}
    AUTOMATION_VERSION=${18}

    log "MINIFIED Start request for selenium"
    touch $STATE_FILES_DIR/session_$DEVICEID
    perform_wifi_check &
    PERFORM_WIFI_CHECK_PID="$!"
    touch_in_session $session_id
    get_os_version

    PRE_APPIUM_COMMANDS_TIME=$(bc <<< "`date +%s%3N` - $PRE_APPIUM_COMMANDS_TIME")
    if [[ -f "$STATE_FILES_DIR/dedicated_minimized_cleanup_reserved_$DEVICEID" ]]; then
      handle_appium
    fi
    if [ "$?" = "0" ]; then
      start_logcat_capture &
      START_LOGCAT_CAPTURE_PID="$!"

      log_gms_version &
      LOG_GMS_VERSION_PID="$!"

      SCREEN_UNLOCK_TIME=$( date +%s%3N )
      ensure_screen_is_unlocked
      SCREEN_UNLOCK_TIME=$(bc <<< "`date +%s%3N` - $SCREEN_UNLOCK_TIME")

      SET_ORIENTATION_TIME=$( date +%s%3N )
      if [[ $ORIENTATION != "portrait" ]]; then
        # Set orientation "portrait" is also called in ensure_screen_is_unlocked
        set_device_orientation $ORIENTATION &
        ORIENTATION_SETTING_PID="$!"
      fi
      SET_ORIENTATION_TIME=$(bc <<< "`date +%s%3N` - $SET_ORIENTATION_TIME")

      log_app_anr &

      /usr/local/bin/adb -s $DEVICEID forward tcp:$DEBUG_PORT tcp:2020

      SCREENCAP_START_TIME=$( date +%s%3N )
      start_screencap
      SCREENCAP_START_TIME=$(bc <<< "`date +%s%3N` - $SCREENCAP_START_TIME")

      PRODUCT="automate"

      APP_AUTOMATE_CODE_TIME=$( date +%s%3Ns )
      if [ "$APP_TESTING" == "true" ]; then
        PRODUCT="app_automate"
        dismiss_crash_popup &
        disable_chrome_welcome_screen
        play_services $DEVICEID "enable" &

        if [[ $DEVICELOGS_ENABLED == "true" ]]; then
          truncate -s 0 $DEVICELOGS_FILE
          adb -s $DEVICEID logcat -c
          touch $DEVICELOGS_FILE
        fi
      fi
      APP_AUTOMATE_CODE_TIME=$(bc <<< "`date +%s%3N` - $APP_AUTOMATE_CODE_TIME")

      WIFI_CHECK_WAIT_TIME=$( date +%s%3N )
      wait $PERFORM_WIFI_CHECK_PID
      WIFI_CHECK_WAIT_TIME=$(bc <<< "`date +%s%3N` - $WIFI_CHECK_WAIT_TIME")

      APPIUM_LOGS_TRUNCATION_TIME=$( date +%s%3N )
      archive_and_truncate_appium_logs $DEVICEID
      APPIUM_LOGS_TRUNCATION_TIME=$(bc <<< "`date +%s%3N` - $APPIUM_LOGS_TRUNCATION_TIME")

      ORIENTATION_SETTING_WAIT_TIME=$( date +%s%3N )
      wait "$ORIENTATION_SETTING_PID"
      ORIENTATION_SETTING_WAIT_TIME=$(bc <<< "`date +%s%3N` - $ORIENTATION_SETTING_WAIT_TIME")

      LOG_GMS_VERSION_WAIT_TIME=$( date +%s%3N )
      wait $LOG_GMS_VERSION_PID
      LOG_GMS_VERSION_WAIT_TIME=$(bc <<< "`date +%s%3N` - $LOG_GMS_VERSION_WAIT_TIME")

      LOGCAT_CAPTURE_WAIT_TIME=$( date +%s%3N )
      wait $START_LOGCAT_CAPTURE_PID
      LOGCAT_CAPTURE_WAIT_TIME=$(bc <<< "`date +%s%3N` - $LOGCAT_CAPTURE_WAIT_TIME")

      ALL_COMMANDS_TIME=$(bc <<< "`date +%s%3N` - $ALL_COMMANDS_TIME")
      INSTRUMENTATION_LOG_FILE=/tmp/$DEVICEID-instrumentation.log

      echo "Minified Flow Log was generated on:" `date` > $INSTRUMENTATION_LOG_FILE

      for time_var in DEFINE_VARIABLE_TIME PRE_APPIUM_COMMANDS_TIME SCREEN_UNLOCK_TIME SET_ORIENTATION_TIME SCREENCAP_START_TIME APP_AUTOMATE_CODE_TIME WIFI_CHECK_WAIT_TIME APPIUM_LOGS_TRUNCATION_TIME ORIENTATION_SETTING_WAIT_TIME LOG_GMS_VERSION_WAIT_TIME LOGCAT_CAPTURE_WAIT_TIME ALL_COMMANDS_TIME; do
        echo $time_var ${!time_var}
        echo $time_var ${!time_var} >> $INSTRUMENTATION_LOG_FILE
      done
      exit 0
    else
      exit 1
    fi;;
esac
