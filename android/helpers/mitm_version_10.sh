LOG_FILE=/data/local/tmp/mitm.log
exec 1<>$LOG_FILE
exec 2>&1

CA_CERT_FILE_HASH="efb15d7d.0"
CA_CERT_FILE_MD5SUM="d5d9f142ec09c0c05c6db467acae8b23"
CA_CERTS_LOCATION=$1
CA_CERTS_BIND_LOCATION="/data/cacerts/"

CA_CERT_LOCATION="${CA_CERTS_LOCATION}/${CA_CERT_FILE_HASH}"
CA_CERT_TMP_LOCATION="/data/local/tmp/${CA_CERT_FILE_HASH}"
CA_CERT_BIND_LOCATION="${CA_CERTS_BIND_LOCATION}/${CA_CERT_FILE_HASH}"
MOUNT_BIN_PATH="/system/bin/mount"

MITM_MOVE_EXIT_CODE_FILE="/data/local/tmp/ca_cert_move_exit_code.log"

exit_code=0

fail(){
    exit_code=1
    echo "FAILURE: " $@
    echo "Aborting MITM CA Cert installation with exit code $exit_code"
    echo $exit_code > $MITM_MOVE_EXIT_CODE_FILE
    chmod 644 $MITM_MOVE_EXIT_CODE_FILE
    chmod 644 $LOG_FILE
    chown shell:shell $LOG_FILE
    exit 1
}

create_bind_dir(){
    echo "creating the bind directory @ $CA_CERTS_BIND_LOCATION"
    mkdir -p $CA_CERTS_BIND_LOCATION
    chmod 755 $CA_CERTS_BIND_LOCATION
    cp -a $CA_CERTS_LOCATION/* $CA_CERTS_BIND_LOCATION/

    [ -d $CA_CERTS_BIND_LOCATION ] || fail "can't create bind location"

    local num_ca_certs=`ls /$CA_CERTS_LOCATION | wc -l`
    local num_ca_certs_in_bind=`ls /$CA_CERTS_BIND_LOCATION | wc -l`

    echo "Copied ${num_ca_certs_in_bind}/${num_ca_certs} certificates"

    [ $num_ca_certs -le $num_ca_certs_in_bind ] || fail "some certs failed to copy"
}

copy_mitm_cert(){
    echo "copying the cert!"
    if [ -f $CA_CERT_TMP_LOCATION ]; then
        cp $CA_CERT_TMP_LOCATION $CA_CERT_BIND_LOCATION || fail "can't copy certificate"
        chmod 644 $CA_CERT_BIND_LOCATION
    else
        fail "can't find cert to copy it!"
    fi
}

bind_dir(){
    echo "bind mounting the certs..."
    $MOUNT_BIN_PATH --bind $CA_CERTS_BIND_LOCATION $CA_CERTS_LOCATION

    # For debugging
    echo "(debug) Checking if mount was created:"
    local mount=`mount | grep /etc/security/cacerts`
    local mount=`mount | grep /etc/security/cacerts`
    [[ -z $mount ]] && fail "/etc/security/cacerts not appearing on mount output"
}

ca_cert_md5=$(md5sum $CA_CERT_LOCATION | cut -d ' ' -f 1)

if [[ ! -f $CA_CERT_LOCATION ]] || [[ "$ca_cert_md5" != "$CA_CERT_FILE_MD5SUM" ]]; then
    create_bind_dir
    copy_mitm_cert
    bind_dir

    [[ -f $CA_CERT_LOCATION ]] || fail "Certificate is not there after script run"

    local num_certificates_installed=`ls $CA_CERTS_LOCATION | wc -l`
    [[ $num_certificates_installed -gt 1 ]] || fail "Only 1 cert installed after running!"
else
    echo "Nothing to do, cert already there"
fi

echo "Success! final exit code is $exit_code"
echo $exit_code > $MITM_MOVE_EXIT_CODE_FILE
chmod 644 $MITM_MOVE_EXIT_CODE_FILE

chmod 644 $LOG_FILE
chown shell:shell $LOG_FILE

rm $CA_CERT_TMP_LOCATION
rm /data/local/tmp/mitm.sh
