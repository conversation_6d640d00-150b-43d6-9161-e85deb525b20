require 'android_toolkit'
require 'logger'

require_relative "./upgrade_popup_helper"
require_relative "../constants"

class SamsungBrowserHelper

  def initialize(device, logger, logger_params = {}, session_id = "", cleanup_type = "")
    raise "Device cannot be empty" if device.nil? || device == ""

    @device = device
    @logger = logger
    @logger_params = logger_params
    @session_id = session_id
    @cleanup_type = cleanup_type
    AndroidToolkit::Log.logger = @logger
    @adb = AndroidToolkit::ADB.new(udid: @device)
  end

  def clean_and_setup_browser
    log :info, "Cleaning and setting up samsung browser"
    return unless samsung_browser_launched?

    start_time = Time.now

    begin
      @adb.shell("pm clear com.sec.android.app.sbrowser", timeout: 30)
    rescue StandardError => e
      log :info, "Error while clearing samsung browser data: #{e.message} - #{e.backtrace}"
    end

    dismiss_upgrade_popups = DismissUpgradePopups.new(@device, @logger)
    dismiss_upgrade_popups.dismiss

    log :info, "Samsung browser cleanup done. Duration: #{Time.now - start_time}"
  end

  private

  def log(level, msg)
    @logger.send(level.to_sym, "#{self.class} #{msg}")
  end

  def device_logger_unreliable?
    File.exist?("#{BrowserStack::STATE_FILES_DIR}/device_logger_unreliable_#{@device}")
  end

  def samsung_browser_used_file_exist?
    File.exist?("#{BrowserStack::STATE_FILES_DIR}/samsung_browser_used_#{@device}")
  end

  def samsung_browser_detected_by_check_status_bar?
    opened_browsers_file = BrowserStack::OPENED_BROWSERS_FILE.gsub("device_id", @device)
    begin
      @adb.pull(BrowserStack::TEMP_OPENED_BROWSERS_FILE, opened_browsers_file)
      @adb.shell("rm -f #{BrowserStack::TEMP_OPENED_BROWSERS_FILE}")
    rescue StandardError => e
      log :info, "Error in fetching or deleting TEMP_OPENED_BROWSERS_FILE: #{e.message} - #{e.backtrace}"
      return false
    end

    content = ""
    content = File.read(opened_browsers_file) if File.exist?(opened_browsers_file)

    content.include?("com.sec.android.app.sbrowser")
  end

  def samsung_browser_launched?
    return true if @session_id.to_s == ""

    return true if @cleanup_type != "quick_cleanup"

    return true if samsung_browser_detected_by_check_status_bar?

    return true if samsung_browser_used_file_exist?

    return true if device_logger_unreliable?

    launched_apps_file = "/tmp/android_launched_apps/#{@session_id}"

    launched_apps = ""
    launched_apps = File.read(launched_apps_file).strip.split(",") if File.exist?(launched_apps_file)

    launched_apps.include?("com.sec.android.app.sbrowser")
  end
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase.to_sym
  device_id = ARGV[1].to_s.strip
  session_id = ARGV[2].to_s.strip
  cleanup_type = ARGV[3].to_s.strip
  helper = SamsungBrowserHelper.new(device_id, Logger.new($stdout), {} , session_id, cleanup_type)
  helper.send(command)
end
