require 'English'
require 'json'
require 'net/http'
require 'uri'

require_relative '../constants'
require 'browserstack_logger'

module B<PERSON>erStack
  # Contains utils methods for posting data to and endpoint and downloading a file
  class HttpUtils
    @logger_params = {
      progname: File.basename(__FILE__)
    }

    class << self
      def send_get(url_string, options = {})
        @logger_params[:component] = __method__.to_s
        url_string = "http://#{url_string}" unless url_string.start_with?('http://', 'https://')
        options = {
          username: nil, password: nil, params: nil,
          retry_count: 0, retry_interval: 0, redirect_count: 10
        }.merge(options)
        url = URI(url_string)
        url.query = URI.encode_www_form(options[:params]) if options[:params]
        http = Net::HTTP.new(url.host, url.port)
        http.use_ssl = true if url.scheme == 'https'
        http.read_timeout = options[:read_timeout] if options[:read_timeout]
        request = Net::HTTP::Get.new(url)
        request.basic_auth(options[:username], options[:password]) if options[:username] && options[:password]
        response = http.request(request)
        return response if response.code.to_i == 200

        if is_redirect?(response)
          BrowserStack.logger.info("HTTP GET redirect response #{response.code}, following #{options[:redirect_count]}")
          options[:redirect_count] -= 1
          return send_get(response['location'], options)
        end

        return response if options[:retry_count] <= 0

        BrowserStack.logger.info(
          "HTTP GET non 200 response: #{response.code}, retry_count: #{options[:retry_count]}. " \
          "Retrying after #{options[:retry_interval]} seconds",
          @logger_params
        )
        options[:retry_count] -= 1
        sleep(options[:retry_interval])
        send_get(url, options)
      rescue StandardError => e
        raise e if options[:retry_count] <= 0

        BrowserStack.logger.info(
          "Exception Occurred in GET: URL: #{url}, Options: #{options},  #{e.inspect}, retry_count: "\
          "#{options[:retry_count]}. Retrying after #{retry_interval} seconds",
          @logger_params
        )
        options[:retry_count] -= 1
        sleep(options[:retry_interval])
        send_get(url, options)
      end

      def send_post(url, data_hash, basic_auth=nil, json=nil, options={})
        @logger_params[:component] = __method__.to_s
        retry_count = options[:retry_count] || 0
        retry_interval = options[:retry_interval] || 0
        read_timeout = options[:read_timeout] || 60 # 60 is the default value in ruby anyway
        url = URI(url)
        http = Net::HTTP.new(url.host, url.port)
        http.use_ssl = true if url.scheme == 'https'
        http.read_timeout = read_timeout
        request = Net::HTTP::Post.new(url)
        request.basic_auth(basic_auth[:username], basic_auth[:password]) if basic_auth
        request["content-type"] = 'application/json' if json
        if data_hash && data_hash[:app_upload_identifier].to_s != ""
          request["x-chitragupta-log-id"] = data_hash[:app_upload_identifier]
        end
        request.body = data_hash.to_json if data_hash
        response = http.request(request)
        return response if response.code.to_i == 200 || retry_count <= 0

        BrowserStack.logger.info(
          "HTTP Post non 200 response: #{response.code}, retry_count: #{retry_count}. " \
          "Retrying after #{retry_interval} seconds",
          @logger_params
        )
        options[:retry_count] -= 1
        sleep(retry_interval)
        send_post(url, data_hash, basic_auth, json, options)
      rescue StandardError => e
        raise e if retry_count <= 0

        BrowserStack.logger.info(
          "Exception Occurred in POST: URL: #{url}, Data: #{data_hash}, options: #{options},  "\
          "#{e.inspect}, retry_count: #{retry_count}. " \
          "Retrying after #{retry_interval} seconds",
          @logger_params
        )
        options[:retry_count] -= 1
        sleep(retry_interval)
        send_post(url, data_hash, basic_auth, json, options)
      end

      def download_file(url, filename, filetype, options = {})
        @logger_params[:component] = __method__.to_s
        timeout = options[:timeout] || 30
        retry_count = options[:retry_count] || 0
        kill_in_cleanup = options[:kill_in_cleanup] || false
        device_id = options[:device_id] || ''

        if options[:is_app_testing]
          # Handle request and response headers
          header_options = ""
          header = options[:header]
          dump_headers_file = options[:dump_headers_to_file]
          header_options += " -D #{dump_headers_file}" unless dump_headers_file.nil? || dump_headers_file.empty?
          header_options += " --header '#{header}'" unless header.nil? || header.empty?
        end

        if kill_in_cleanup
          read_io, write_io = IO.pipe
          cmd = "curl --max-time #{timeout} -s -S -o '#{filename}' -w '%{http_code}' '#{url}'"
          cmd += header_options if options[:is_app_testing] && header_options != ""
          pid = Process.spawn(cmd, out: write_io)
          file_path = "#{APP_DOWNLOAD_PID_PREFIX_PATH}_#{device_id}"
          File.open(file_path, 'w') { |f| f.write(pid.to_s) }
          pid, status = Process.wait2(pid)
          write_io.close
          http_code = read_io.read
          exit_code = status.exitstatus
          term_code = status.termsig
          read_io.close
          File.delete(file_path)
        else
          http_code = if options[:is_app_testing]
                        `curl --max-time #{timeout} -s -S -o "#{filename}" -w "%{http_code}" "#{url}" #{header_options}`
                      else
                        `curl --max-time #{timeout} -s -S -o "#{filename}" -w "%{http_code}" "#{url}"`
                      end
          exit_code = $CHILD_STATUS.exitstatus
        end
        if exit_code != 0 && retry_count != 0 && term_code.nil?
          BrowserStack.logger.info(
            "#{filetype} download failed. Retrying. " \
            "exit_code: #{exit_code}, http_code: #{http_code}, retry_count: #{retry_count}",
            @logger_params
          )
          options[:retry_count] -= 1
          download_file(url, filename, filetype, options)
        else
          if !term_code.nil?
            raise "Download process terminated in cleanup"
          elsif http_code.to_s != "200"
            BrowserStack.logger.info "Command: head -5 #{filename}"
            error = File.exist?(filename) ? IO.foreach(filename).take(5).join : 'File not downloaded'
            BrowserStack.logger.info "Error (first 5 lines): #{error}. Status: #{http_code}"
            raise "S3 #{filetype} Download Failed"
          end
          raise "File not downloaded" unless File.exist?(filename)

          unless exit_code == 0
            raise "Non zero exit code #{exit_code}, downloaded file size #{File.size(filename)} bytes"
          end
        end
      end

      def download_from_s3_with_presigned_url(presigned_url, local_file_path)

        begin
          FileUtils.mkdir_p(File.dirname(local_file_path))
        rescue StandardError
          nil
        end

        uri = URI.parse(presigned_url)

        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = (uri.scheme == 'https')
        http.read_timeout = 60

        request = Net::HTTP::Get.new(uri.request_uri)

        response = http.request(request)

        if response.code.to_i == 200
          File.open(local_file_path, 'wb') do |file|
            file.write(response.body)
          end

          BrowserStack.logger.info("Successfully downloaded file to #{local_file_path}")
          true
        else
          BrowserStack.logger.error("Download failed with response code 2: #{response.code}")
          BrowserStack.logger.error("Response body: #{response.body[0..500]}")
          false
        end
      rescue StandardError => e
        BrowserStack.logger.error("Error downloading file: #{e.message}")
        BrowserStack.logger.error(e.backtrace.join("\n"))
        false

      end

      def upload_to_s3_with_presigned_url(presigned_url, local_file_path)

        unless File.exist?(local_file_path)
          BrowserStack.logger.error("File does not exist: #{local_file_path}")
          return false
        end

        file_content = File.read(local_file_path)

        uri = URI.parse(presigned_url)

        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = (uri.scheme == 'https')
        http.read_timeout = 60

        request = Net::HTTP::Put.new(uri.request_uri)
        request.body = file_content

        content_type = case File.extname(local_file_path).downcase
                       when '.json'
                         'application/json'
                       when '.txt'
                         'text/plain'
                       when '.png'
                         'image/png'
                       when '.jpg', '.jpeg'
                         'image/jpeg'
                       else
                         'application/octet-stream'
                       end

        request['Content-Type'] = content_type
        request['Content-Length'] = file_content.bytesize.to_s

        response = http.request(request)

        if response.code.to_i == 200
          BrowserStack.logger.info("Successfully uploaded file from #{local_file_path} to S3")
          true
        else
          BrowserStack.logger.error("Upload failed with response code: #{response.code}")
          BrowserStack.logger.error("Response body: #{response.body[0..500]}")
          false
        end
      rescue StandardError => e
        BrowserStack.logger.error("Error uploading file: #{e.message}")
        BrowserStack.logger.error(e.backtrace.join("\n"))
        false

      end

      private

      def is_redirect?(response)
        (300...400).cover?(response.code.to_i) && !response['location'].nil?
      end
    end
  end
end
