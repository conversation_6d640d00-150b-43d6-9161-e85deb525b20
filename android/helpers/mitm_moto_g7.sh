LOG_FILE=/data/local/tmp/mitm.log
exec 1<>$LOG_FILE
exec 2>&1

MITMPROXY_CA_CERT_FILE_HASH="efb15d7d.0"
CA_CERT_FILE_MD5SUM="d5d9f142ec09c0c05c6db467acae8b23"
MITMPROXY_CA_CERT_LOCATION="/etc/security/cacerts/$MITMPROXY_CA_CERT_FILE_HASH"

TOUCHABLE_TEST_FILE="/etc/security/cacerts/touchable"

MITMPROXY_CA_CERT_TMP_LOCATION="/data/local/tmp/$MITMPROXY_CA_CERT_FILE_HASH"
MITM_MOVE_EXIT_CODE_FILE="/data/local/tmp/ca_cert_move_exit_code.log"
MOUNT_BIN_PATH="/system/bin/mount"

mount_dirs(){
    echo "mounting endpoints..."
    $MOUNT_BIN_PATH -o remount,rw /sbin
    $MOUNT_BIN_PATH -o remount,rw /sbin/charger
    $MOUNT_BIN_PATH -o remount,rw /sbin/.magisk/block/system_root
    $MOUNT_BIN_PATH -o remount,rw /

    # For debugging
    mount | grep charger
    mount | grep magisk


    echo "(debug) touching the ca cert dir:"
    touch $TOUCHABLE_TEST_FILE

    if [ -f $TOUCHABLE_TEST_FILE ]; then
        echo "The directory is touchable"
        rm $TOUCHABLE_TEST_FILE
    else
        "The directory can't be touched!"
    fi
}

copy_cert(){
    echo "copy the cert!.."
    cp $MITMPROXY_CA_CERT_TMP_LOCATION $MITMPROXY_CA_CERT_LOCATION
    exit_code=$?
    echo "Exit code was $exit_code"
    chmod 644 $MITMPROXY_CA_CERT_LOCATION
}

umount_dirs(){
    echo "Unmounting stuff"
    $MOUNT_BIN_PATH -o remount,ro /sbin/.magisk/block/system_root
    $MOUNT_BIN_PATH -o remount,ro /sbin/charger
}

ca_cert_md5=$(md5sum $MITMPROXY_CA_CERT_LOCATION | cut -d ' ' -f 1)

if [[ ! -f $MITMPROXY_CA_CERT_LOCATION ]] || [[ "$ca_cert_md5" != "$CA_CERT_FILE_MD5SUM" ]]; then
    mount_dirs
    copy_cert
    umount_dirs

    if [ -f $MITMPROXY_CA_CERT_LOCATION ]; then
        exit_code=0
        echo "File copied succesfully!"
    else
        exit_code=1
        echo "File failed to copy"
    fi
else
    echo "Nothing to do, cert already there"
    exit_code=0
fi

echo "Final exit code: $exit_code"
echo $exit_code > $MITM_MOVE_EXIT_CODE_FILE
chmod 644 $MITM_MOVE_EXIT_CODE_FILE

chmod 644 $LOG_FILE

# rm $MITMPROXY_CA_CERT_TMP_LOCATION
# rm /data/local/tmp/mitm.sh
