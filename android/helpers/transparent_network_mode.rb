require_relative '../constants'
require 'browserstack_logger'

class TransparentNetworkMode
  class << self
    def touch_state_file(device_id)
      FileUtils.touch("#{BrowserStack::STATE_FILES_DIR}/network_transparent_mode_#{device_id}.json")
      true
    end

    def remove_state_file(device_id)
      FileUtils.rm_rf("#{BrowserStack::STATE_FILES_DIR}/network_transparent_mode_#{device_id}.json")
      true
    end

    def enabled?(device_id)
      File.exist?("#{BrowserStack::STATE_FILES_DIR}/network_transparent_mode_#{device_id}.json")
    end

    def enable(device_obj)
      if device_obj.uses_bstack_internet_app?
        brt_controller = BStackReverseTetherController.new(device_obj, BrowserStack.logger)
        brt_controller.toggle_transparent_mode(stop: false)
        log_info("Enabled for #{device_obj.id}.json")
        touch_state_file(device_obj.id)
      else
        log_info("Could not enable as no bstack internet app #{device_obj.id}.json")
      end
    end

    def disable(device_obj)
      # TODO
    end

    def log_info(message)
      BrowserStack.logger.info("[TransparentNetworkMode] #{message}")
    end

  end
end
