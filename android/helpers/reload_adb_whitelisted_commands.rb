require 'socket'
require 'timeout'

begin
  Timeout.timeout(10) do
    sock = TCPSocket.new('127.0.0.1', 5041)
    sock.write 'shell:RELOAD_WHITELISTED_ADB_COMMANDS'
    response = sock.read(4)
    puts "Response: #{response}"
    sock.close
  end
rescue Timeout::Error => e
  puts "Timeout Exceeded of 10 seconds. Exception Details #{e.inspect}"
rescue StandardError => e
  puts "Exception occurred while reloading whitelisted adb commands. Exception Details #{e.inspect}"
end
