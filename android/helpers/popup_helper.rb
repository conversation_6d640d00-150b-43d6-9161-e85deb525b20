#!/usr/bin/env ruby

require 'json'
require 'dotenv/load'
require 'android_toolkit'
require 'browserstack_logger'
require 'nokogiri'

require_relative '../constants'
require_relative '../../common/push_to_zombie'

module BrowserStack
  class PopupHelper

    def initialize(device_id:, os_version:, session_id: nil, mp_popup_coords: nil)
      @device_id = device_id
      @os_version = os_version
      @session_id = session_id
      @mp_popup_coords = mp_popup_coords
      @adb = AndroidToolkit::ADB.new(udid: @device_id, path: BrowserStack::ADB)
    end

    def dismiss_crash_popup
      m_current = current_window

      if crash_detected?(m_current)
        BrowserStack.logger.info("Crash Popup detected")
        crashed_app = /com[.\w*]*/.match(current_window).to_s
        run_popup_handler('ClickAnrPopup')
        zombie_push('android', 'crashed_app', '', '', crashed_app, @device, @session_id)
      else
        BrowserStack.logger.info("Crash Popup not detected")
      end
    end

    def dismiss_deprecated_app_popup
      m_current = current_window

      if current_window.include?('DeprecatedTargetSdkVersionDialog')
        BrowserStack.logger.info("Deprecated App Popup detected")
        run_popup_handler('MainAutomation')
      else
        BrowserStack.logger.info("Deprecated App Popup not detected")
      end
    end

    #toggling location to reproducing the popup and dismissing it
    #mobpe-1152
    def dismiss_location_warning_popup
      @adb.shell('settings put secure location_mode 0')
      @adb.shell('input keyevent KEYCODE_BACK')
      @adb.shell('settings put secure location_mode 3')
      BrowserStack.logger.info("location_warning_popup is dismissed")
    end

    def dismiss_esim_popup
      m_current = current_window

      #if current window ends with com.samsung.android.app.telephonyui
      if current_window.include?('com.samsung.android.app.telephonyui}')
        BrowserStack.logger.info("eSim Popup detected")
        run_popup_handler('MainAutomation')
      else
        BrowserStack.logger.info("eSim Popup not detected")
      end
    end

    def run_main_automation(exit_on_failure: true)
      run_popup_handler('MainAutomation')
    rescue StandardError => e
      BrowserStack.logger.info("Popup handler failed with exception #{e.message}")
      raise if exit_on_failure
    end

    def handle_media_projection_popup_via_coordinates
      BrowserStack.logger.info("Tapping element using coordinates")
      begin
        coordinates_hash = JSON[File.read(BrowserStack::MEDIA_PROJECTION_COORDINATES_JSON)]
        model = @adb.model
        version_key = @os_version.to_s.to_f.to_s
        if coordinates_hash[model] && coordinates_hash[model][version_key]
          coordinates = coordinates_hash[model][version_key]
          BrowserStack.logger.info("Coordinates found in JSON for #{model} : OS #{version_key}: #{coordinates}")
          if coordinates.is_a?(Array)
            coordinates.each do |coordinate|
              sleep 0.5
              BrowserStack.logger.info("Tapping #{coordinate}")
              @adb.shell("input tap #{coordinate}")
            end
          else
            BrowserStack.logger.info("Tapping at #{coordinates}")
            @adb.shell("input tap #{coordinates}")
          end
          return true if ["Pixel 9", "Pixel 9 Pro XL"].include?(model) || @os_version.to_s.to_f >= 15.0

          return true
        end
      rescue StandardError => e
        BrowserStack.logger.info("Failed to read JSON coordinates: #{e.message}")
        BrowserStack.logger.info("Back trace: #{e.backtrace}")
      end
      # Fallback: Try using @mp_popup_coords from Rails param
      if @mp_popup_coords && !@mp_popup_coords.strip.empty? && @mp_popup_coords != "null"
        BrowserStack.logger.info("Using @mp_popup_coords from session param: #{@mp_popup_coords}")
        @mp_popup_coords.split(",").map(&:strip).each do |coordinate|
          sleep 0.5
          BrowserStack.logger.info("Tapping #{coordinate}")
          @adb.shell("input tap #{coordinate}")
        end
        return true
      else
        BrowserStack.logger.info("@mp_popup_coords Not found !!")
        zombie_push('android', 'mp_popup_coords_missing', '', '', '', @device, @session_id)
      end
      # Final fallback: Check if popup is still present using dumpsys and dismiss via uiautomator
      begin
        perm_window = "adb -s #{@device_id} shell dumpsys window "\
        " | grep -E 'mCurrentFocus|mFocusedApp|ActivityRecord' | grep \"MediaProjectionPermissionActivity\""
        cmd_output = `#{perm_window}`
        if cmd_output.include?("MediaProjectionPermissionActivity")
          BrowserStack.logger.info("Popup still present")
          if @os_version.to_s.to_f >= 15.0
            tap_element_via_adb("text", "A single app")
            sleep 0.5
            tap_element_via_adb("text", "Entire screen")
            sleep 0.5
            tap_element_via_adb("text", "Start")
            sleep 0.5
            tap_element_via_adb("text", "OK")
          else
            tap_element_via_adb("text", "Start now")
          end
        else
          BrowserStack.logger.info("Popup handled successfully")
        end
      rescue StandardError => e
        BrowserStack.logger.info("Failed to tap on popup: #{e.message}. Full error: #{e}")
        BrowserStack.logger.info("Back trace: #{e.backtrace}")
        return false # rubocop:disable Style/RedundantReturn
      end
    end

    def dismiss_celia_popup
      sleep 2
      tap_element_via_adb("text", "CANCEL")
    end

    def tap_element_via_adb(attribute, value)
      # this function taps by finding coordinates of element using uiautomator dump and then use adb to tap
      # this is cheap way and do not use it replace UI automation
      # each tap takes around 2-3 seconds, bottleneck is uiautomator dump command time
      dump_location = @adb.shell('uiautomator dump').gsub('UI hierchary dumped to: ', '')
      dumped_xml = @adb.cat(dump_location)
      xml_doc = Nokogiri::XML(dumped_xml)
      element = xml_doc.at_css("node[@#{attribute}='#{value}']")
      if element.nil?
        BrowserStack.logger.info("Cannot find element with attribute #{attribute} and value #{value}")
        return false
      end
      # format of bounds: [xStart,yStart][xEnd,yEnd]
      bounds = element['bounds'].match("\\[(.*),(.*)\\]\\[(.*),(.*)\\]")
      if bounds.nil?
        BrowserStack.logger.info("Cannot found bounds for element")
        return false
      end

      x_center = ((bounds[1].to_i + bounds[3].to_i) / 2).to_i
      y_center = ((bounds[2].to_i + bounds[4].to_i) / 2).to_i
      BrowserStack.logger.info("Tapping on element at coordinates #{x_center},#{y_center}")
      @adb.shell("input tap #{x_center} #{y_center}")
      true
    rescue StandardError => e
      BrowserStack.logger.info("Failed to tap on popup: #{e.message}. Full error: #{e}")
      BrowserStack.logger.info("Back trace: #{e.backtrace}")
      if attribute == 'text' && value == 'Start now'
        # handle media projection popup by hardcoded values - MOBPL-2023
        # the coordinates are hardcoded currently to solve a customer issue till we find a proper fix
        coordinates_hash = JSON[File.read(BrowserStack::MEDIA_PROJECTION_COORDINATES_JSON)]
        model = @adb.model
        unless coordinates_hash[model].nil?
          coordinates = coordinates_hash[model][@os_version.to_s.to_f.to_s]
          return false if coordinates.nil?

          BrowserStack.logger.info("Tapping at #{coordinates}, fetched from json")
          @adb.shell("input tap #{coordinates}")
          return true
        end
      end
      false
    end

    private

    def current_window
      output = @adb.dumpsys("window", timeout: 5)
      output.scan(/.*mCurrent.*/).to_s
    end

    def crash_detected?(current_window)
      current_window.include?('Application Error: ')
    end

    def run_popup_handler(class_name)
      BrowserStack.logger.info("Running popup handler")
      # TODO: make naming consistent across .jar and .apk so that we can have this function without these kind of checks
      class_name = 'PopUpHandler' if class_name == 'MainAutomation' && @os_version.segments[0] >= 11

      cmd = if @os_version.segments[0] >= 11
              "am instrument -w -r"\
               " -e debug false"\
               " -e class 'com.browserstack.uiautomation.#{class_name}Test'"\
               " com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner"
            else
              "uiautomator runtest PopupHandlerAutomation.jar"\
               " -c com.browserstack.popupHandler.#{class_name}"
            end

      @adb.shell(cmd, timeout: 5)
    rescue StandardError => e
      BrowserStack.logger.info("Popup handler failed with exception #{e.message}")
      zombie_push('android', 'popup-handler-failure', '', '', e.message, @device, @session_id)
      raise
    end
  end
end

if __FILE__ == $PROGRAM_NAME
  method = ARGV[0].to_s.strip.downcase.to_sym
  raise 'No method given' if method.nil?

  device_id = ARGV[1]
  os_version = Gem::Version.new(ARGV[2])
  session_id = ARGV[3]
  mp_popup_coords = ARGV[4].to_s

  logfile = File.join(BrowserStack::LOGGING_DIR, "popup_helper.log")
  logger_params = { device: device_id, session: session_id, component: 'popup_helper.rb' }
  BrowserStack.init_logger(logfile, logger_params)

  helper = BrowserStack::PopupHelper.new(
    device_id: device_id, os_version: os_version,
    session_id: session_id, mp_popup_coords: mp_popup_coords
  )
  helper.send(method)
end
