<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<map>
    <boolean name="transliteration_enabled_0x490000" value="false" />
    <int name="settings_moakey_drag_length" value="1" />
    <boolean name="transliteration_enabled_0x1220031" value="false" />
    <boolean name="allow_network_access_permission" value="true" />
    <boolean name="transliteration_enabled_0x570000" value="false" />
    <int name="key_prev_handwriting_sub_type" value="0" />
    <boolean name="transliteration_enabled_0x650000" value="false" />
    <string name="flick_angle_multi">38</string>
    <boolean name="settings_speak_keyboard_input_aloud_on" value="false" />
    <int name="pref_last_input_mode_type" value="0" />
    <string name="SETTINGS_DEFAULT_HWR_SCH_TO_TCH_SWITCH">0</string>
    <float name="onehand_keyboard_height" value="1.0" />
    <float name="keyboard_transparency_alpha" value="1.0" />
    <int name="bee_user_set_main_primary_count" value="5" />
    <boolean name="transliteration_enabled_0x670000" value="false" />
    <boolean name="is_one_hand_right_set" value="true" />
    <boolean name="use_developer_options" value="false" />
    <boolean name="SETTINGS_DEFAULT_USE_ADDTO_NUMBER_KEY_FIRST_LINE" value="true" />
    <boolean name="TOOLBAR_SMART_TIPS_ADJUST_KEYBOARD_SIZE_EXECUTE_COMPLETE" value="false" />
    <boolean name="SETTINGS_DEFAULT_SUGGEST_STICKER_AREMOJI" value="true" />
    <boolean name="setting_fuzzy_pinyin_ch_c_key" value="false" />
    <float name="normal_keyboard_bias_left" value="0.5" />
    <boolean name="SETTINGS_DEFAULT_AUTO_PERIOD" value="false" />
    <boolean name="mushroom" value="false" />
    <boolean name="integrated_pp_executed" value="false" />
    <boolean name="setting_fuzzy_pinyin_uang_uan_key" value="false" />
    <boolean name="transliteration_enabled_0x500000" value="false" />
    <boolean name="japanese_input_word_learning" value="true" />
    <int name="KEY_INPUT_MODE" value="0" />
    <int name="SETTINGS_HANDWRITING_CANDIDATE_TYPE" value="0" />
    <boolean name="sticker_pp_mojitok_agreement_accepted" value="false" />
    <string name="predictive_text_lines">2</string>
    <float name="normal_keyboard_inner_width_landscape" value="1.6" />
    <float name="onehand_keyboard_width" value="0.75" />
    <int name="COVER_DISPLAY_SETTINGS_KEYBOARD_THEMES_P_OS_INDEX" value="822149120" />
    <boolean name="transliteration_enabled_0x630000" value="false" />
    <boolean name="KEY_TOOLBAR_SMART_TIPS_STICKER_SE__EXECUTE_COMPLETE" value="false" />
    <float name="normal_keyboard_inner_height" value="1.0" />
    <long name="big_data_weekly_time_stored_in_milliseconds" value="1614943136641" />
    <boolean name="SETTINGS_DEFAULT_SUGGEST_STICKER_BITMOJI" value="false" />
    <boolean name="transliteration_enabled_0x590000" value="false" />
    <string name="settings_shuangpin_type">2</string>
    <int name="last_used_mm_symbol_key_code_for_korean_vega_and_naratgul" value="44" />
    <boolean name="KEY_SPACE_SMART_TIPS_FUNCTION_EXECUTE_COMPLETE" value="false" />
    <boolean name="setting_fuzzy_pinyin_zh_z_key" value="false" />
    <int name="TOOLBAR_SMART_TIPS_FUNCTION_EXECUTE_COUNT" value="4" />
    <string name="SETTINGS_DEFAULT_HWR_WRITING_STYLE">0</string>
    <int name="TOOLBAR_SMART_TIPS_STICKER_SE_EXECUTE_COUNT" value="3" />
    <int name="LAST_USED_COMMA_KEYCODE" value="44" />
    <int name="swiftkey_prelearning_version" value="2" />
    <boolean name="settings_keyboard_swipe_none" value="true" />
    <boolean name="sogou_pp_dialog_executed" value="false" />
    <boolean name="japanese_wildcard_prediction" value="false" />
    <int name="pref_keyboard_front_view_type_land" value="4" />
    <boolean name="setting_fuzzy_pinyin_ing_in_key" value="false" />
    <boolean name="sogou_pp_agreement_accepted" value="false" />
    <float name="floating_keyboard_width_landscape" value="0.75" />
    <boolean name="SETTINGS_DEFAULT_SPACE_LANGUAGE_CHANGE" value="true" />
    <int name="custom_vibrate_duration_setting" value="-1" />
    <boolean name="SETTINGS_COVER_DISPLAY_DEFAULT_USE_ALTERNATIVE_CHARACTERS" value="false" />
    <boolean name="gif_pp_agreement_accepted" value="false" />
    <string name="SETTINGS_DEFAULT_NUMBER_AND_SYMBOLS_KEYPAD_TYPE">qwerty</string>
    <float name="floating_keyboard_height" value="0.75" />
    <boolean name="cloud_sync" value="true" />
    <boolean name="USE_CHINESE_PHONETIC_SPELL_EFFECT" value="false" />
    <string name="SETTINGS_DEFAULT_SOGOU_HOTWORD_AUTO_UPDATE">2</string>
    <boolean name="google_pp_dialog_executed" value="false" />
    <boolean name="SETTINGS_DEFAULT_KEYPAD_POINTING" value="false" />
    <boolean name="transliteration_enabled_0x620000" value="false" />
    <boolean name="SETTINGS_DEFAULT_RARE_WORD_INPUT" value="true" />
    <float name="normal_keyboard_inner_height_landscape" value="0.64" />
    <int name="pref_keyboard_front_view_type" value="0" />
    <int name="SPACE_LANGUAGE_CHANGING_THRESHOLD" value="85" />
    <int name="settings_moakey_drag_angle" value="0" />
    <boolean name="KEY_TOOLBAR_SMART_TIPS_FUNCTION_EXECUTE_COMPLETE" value="true" />
    <boolean name="SETTINGS_DEFAULT_SUGGEST_EMOJI" value="true" />
    <string name="settings_keyboard_swipe">settings_keyboard_swipe_none</string>
    <boolean name="google_pp_agreement_accepted" value="false" />
    <boolean name="gif_pp_dialog_executed" value="false" />
    <boolean name="SETTINGS_COVER_DISPLAY_DEFAULT_USE_ADDTO_NUMBER_KEY_FIRST_LINE" value="false" />
    <int name="pref_prev_keyboard_front_view_type_land" value="4" />
    <string name="auto_cursor_movement">1200</string>
    <boolean name="key_is_full_handwriting" value="false" />
    <float name="floating_keyboard_width" value="0.75" />
    <int name="data_migration_status" value="2" />
    <float name="previous_keyboard_size_version" value="30.09" />
    <int name="CURRENT_KEYBOARD_THEME_STYLE_VALUE" value="2" />
    <boolean name="transliteration_enabled_0x600000" value="false" />
    <int name="SETTINGS_KEYBOARD_FONT_SIZE" value="1" />
    <boolean name="EXTRACT_UI" value="true" />
    <int name="settings_speak_keyboard_input_aloud_mode" value="0" />
    <int name="SETTINGS_KEYBOARD_THEMES_P_OS_INDEX" value="285212672" />
    <boolean name="settings_period_key_popup_multi_tap" value="false" />
    <boolean name="setting_fuzzy_pinyin_h_f_key" value="false" />
    <boolean name="download_list_execution" value="true" />
    <int name="KEY_INPUT_MODE_LAND" value="4" />
    <boolean name="SETTINGS_USE_TOOLBAR" value="true" />
    <boolean name="HOME_THEME_LAST_USED" value="false" />
    <boolean name="first_predictive_text_execution" value="true" />
    <boolean name="KEY_TOOLBAR_SMART_TIPS_TRANSLITERATION_EXECUTE_COMPLETE" value="false" />
    <boolean name="KEY_AUTO_REPLACEMENT_SMART_TIPS_FUNCTION_EXECUTE_COMPLETE" value="false" />
    <float name="normal_keyboard_height" value="1.0" />
    <boolean name="transliteration_enabled_0x580000" value="false" />
    <int name="need_to_show_restore_unigram_dialog" value="0" />
    <int name="pref_prev_keyboard_front_view_type" value="0" />
    <boolean name="is_last_mode_was_dex" value="false" />
    <boolean name="transliteration_enabled_0x660000" value="false" />
    <boolean name="sticker_pp_bitmoji_dialog_executed" value="false" />
    <string name="SETTINGS_DEFAULT_XT9_HWR_MODE">0</string>
    <float name="onehand_keyboard_inner_width" value="0.75" />
    <boolean name="SETTINGS_DEFAULT_PREDICTION_ON" value="false" />
    <boolean name="SETTINGS_DEFAULT_NEXT_WORD_WITH_SPACE" value="false" />
    <int name="last_used_mm_symbol_key_code" value="44" />
    <int name="KEY_STAND_ALONE_DEX_VIEW" value="2" />
    <boolean name="SETTINGS_DEFAULT_LINK_TO_CONTACTS" value="false" />
    <boolean name="flick_toggle_input" value="true" />
    <boolean name="sticker_pp_bitmoji_agreement_accepted" value="false" />
    <int name="rts_show_candidate_cue_count" value="0" />
    <int name="last_used_mm_key_code" value="-121" />
    <boolean name="SETTINGS_DEFAULT_SUPPORT_KEY_VIBRATE" value="true" />
    <boolean name="first_tips_all_execution" value="true" />
    <boolean name="setting_fuzzy_pinyin_n_l_key" value="false" />
    <boolean name="SUPPORT_SPACE_LANGUAGE_CHANGE" value="true" />
    <float name="floating_keyboard_height_landscape" value="0.75" />
    <string name="SETTINGS_DEFAULT_CLOUD_LINK">2</string>
    <boolean name="use_keyboard_size_smart_tip" value="true" />
    <boolean name="sticker_pp_mojitok_dialog_executed" value="false" />
    <boolean name="SETTINGS_DEFAULT_USE_ALTERNATIVE_CHARACTERS" value="false" />
    <boolean name="first_auto_replacement_tap_execution" value="true" />
    <boolean name="settings_speak_keyboard_input_aloud_phonetic_alphabet" value="false" />
    <boolean name="setting_fuzzy_pinyin_iang_ian_key" value="false" />
    <boolean name="transliteration_enabled_0x640000" value="false" />
    <boolean name="SETTINGS_SUGGEST_STICKER_METHOD_POPUP" value="true" />
    <boolean name="setting_fuzzy_pinyin_eng_en_key" value="false" />
    <string name="bee_user_set_main">{&quot;id&quot;:&quot;emoticon&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;com.samsung.android.icecone.sticker&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;com.samsung.android.icecone.gif&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;voice_input&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;kbd_setting&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;search&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;translation&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;com.samsung.android.authfw.samsungpass&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;com.samsung.android.icecone.spotify&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;com.samsung.android.icecone.youtube&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;com.samsung.android.clipboarduiservice.plugin_clipboard&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;text_editing&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;kbd_view_type&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;kbd_adjust_size&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;com.samsung.android.icecone.sticker#Bitmoji&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;com.samsung.android.icecone.sticker#ArEmoji&quot;,&quot;isNew&quot;:false};{&quot;id&quot;:&quot;com.samsung.android.icecone.sticker#Mojitok&quot;,&quot;isNew&quot;:false}</string>
    <float name="onehand_keyboard_inner_height" value="1.0" />
    <boolean name="SETTINGS_FONT_SIZE_CHANGE_EXECUTED" value="false" />
    <boolean name="SETTINGS_DEFAULT_USE_PREVIEW" value="true" />
    <boolean name="USE_TOGGLE_NUMBER_INPUT" value="false" />
    <boolean name="SETTINGS_MULTILINGUAL_TYPING" value="false" />
    <boolean name="settings_use_phonepad_in_landscape" value="false" />
    <boolean name="need_to_backup_and_restore_unigram" value="false" />
    <boolean name="SETTINGS_DEFAULT_PEN_DETECTION" value="false" />
    <boolean name="custom_theme_preference" value="false" />
    <float name="standard_split_keyboard_inner_width_landscape" value="0.8333333" />
    <float name="normal_keyboard_height_landscape" value="0.64" />
    <int name="SETTINGS_HIGH_CONTRAST_KEYBOARD_THEME" value="0" />
    <boolean name="first_xt9_custom_ldb_added" value="true" />
    <boolean name="setting_fuzzy_pinyin_r_l_key" value="false" />
    <boolean name="SETTINGS_DEFAULT_SUPPORT_KEY_SOUND" value="true" />
    <boolean name="transliteration_enabled_0x610000" value="false" />
    <boolean name="setting_fuzzy_pinyin_ang_an_key" value="false" />
    <boolean name="SETTINGS_DEFAULT_SUGGEST_STICKER_MOJITOK" value="false" />
    <boolean name="SETTINGS_DEFAULT_AUTO_CAPS" value="true" />
    <float name="normal_keyboard_inner_width" value="1.0" />
    <string name="key_handwriting_on_list"></string>
    <boolean name="spotify_pp_dialog_executed" value="false" />
    <boolean name="settings_keyboard_swipe_continuous_input" value="false" />
    <boolean name="SETTINGS_HIGH_CONTRAST_KEYBOARD" value="false" />
    <float name="current_keyboard_size_version" value="30.09" />
    <boolean name="transliteration_enabled_0x550000" value="false" />
    <boolean name="transliteration_enabled_0x680000" value="false" />
    <float name="normal_keyboard_bias_left_landscape" value="0.5" />
    <boolean name="sticker_rts_ftu_executed" value="false" />
    <boolean name="SETTINGS_SUGGEST_STICKER_METHOD_PREDICTION" value="false" />
    <int name="settings_touch_and_hold_delay" value="300" />
    <boolean name="half_width_input" value="false" />
    <boolean name="PREF_SETTINGS_SWITCHING_KEY" value="false" />
    <string name="period_key_custom_symbols_list">^ % $ # @ ' ! ? ,</string>
    <boolean name="setting_fuzzy_pinyin_sh_s_key" value="false" />
    <boolean name="setting_fuzzy_pinyin_k_g_key" value="false" />
    <int name="pref_last_input_mode_type_land" value="4" />
    <int name="TOOLBAR_SMART_TIPS_ADJUST_KEYBOARD_SIZE_EXECUTE_COUNT" value="3" />
    <boolean name="SETTINGS_DEFAULT_TRADITIONAL_CHINESE_INPUT" value="false" />
    <string name="SETTINGS_DEFAULT_XT9_HWR_RECOG_TYPE">0</string>
    <string name="latest_symbol_list">? ! @ - ~ ^ </string>
    <string name="settings_touch_and_hold_space_bar">cursor_control</string>
    <boolean name="SETTINGS_DEFAULT_SUGGEST_STICKER" value="true" />
    <boolean name="spotify_pp_agreement_accepted" value="false" />
    <string name="SETTINGS_DEFAULT_HWR_RECOGNIZE_DELAY">500</string>
    <int name="settings_backspace_delete_speed" value="100" />
    <string name="voice_input_ja">1</string>
</map>
