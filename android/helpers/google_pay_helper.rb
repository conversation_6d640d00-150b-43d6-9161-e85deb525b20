require 'android_toolkit'
require 'browserstack_logger'
require_relative '../constants'
require_relative '../../common/push_to_zombie'
require_relative '../models/android_device'
require_relative './utils'

class GooglePay
  MACHINE_IP = "/usr/local/.browserstack/whatsmyip".freeze
  def initialize(params)
    @params = params
    @device_id = params[:device]
    @session_id = params[:session_id]
    @logger = BrowserStack.logger
    @product = params[:product]
    @device_obj = BrowserStack::AndroidDevice.new(@device_id, self.class.to_s, @logger)
    @adb = AndroidToolkit::ADB.new(udid: @device_id, path: BrowserStack::ADB)
    @eds_obj = EDS.new({}, @logger)
    @eds_event_type = "web_events"
    @machine_ip = begin
      File.read(MACHINE_IP)
    rescue StandardError
      ""
    end
  end

  def extract_session_params
    parsed_session_params = JSON.parse(File.read("/usr/local/.browserstack/config/rtc_service_#{@device_id}"))
    log(:info, "Session Params : #{parsed_session_params}")
    parsed_session_params
  rescue StandardError => e
    raise "#{BrowserStack::GOOGLE_PAY_TAG} Failed to extract session params #{@device_id}"
  end

  def send_data_to_eds(event_name, time, event_status)
    params = {
      product: @product,
      team: "device_features",
      event_json: {
        session_id: @session_id,
        device_id: @device_id,
        device_version: @device_obj.os_version,
        device_name: @device_obj.common_name,
        machine_ip: @machine_ip,
        time: time,
        event_status: event_status
      },
      event_name: event_name
    }
    @eds_obj.push_logs(@eds_event_type, params).join
  end

  def log(level, message)
    params = { subcomponent: BrowserStack::GOOGLE_PAY_TAG }
    BrowserStack.logger.send(level.to_sym, message, params)
  end

  def disable_google_play_services
    start_disable_google_play_services = Time.now
    touch_state_file
    begin
      log(:info, "Disabling Google Play services to enable google pay for #{@device_id}, #{@session_id}, #{@product}")
      google_play_services_state = @adb.shell("pm disable-user #{BrowserStack::GOOGLE_PLAY_SERVICES_PACKAGE}")
      @adb.shell("settings put global heads_up_notifications_enabled 0")
      time_taken = Time.now - start_disable_google_play_services
      if google_play_services_state.include? "new state: disabled-user"
        send_data_to_eds("disable-google-service", time_taken, "success")
        notify_to_pusher(extract_session_params , BrowserStack::GOOGLE_PAY_PUSHER_SUCCESS_MESSAGE, nil)
        true
      else
        raise "#{BrowserStack::GOOGLE_PAY_TAG} Failed to disable Google Play services #{@device_id}"
      end
    rescue StandardError => e
      log(:error, "Error occured during running pm disable command: #{e.message}, #{@device_id}, #{@session_id},
      #{e.message}, #{e.backtrace.join("\n")}")
      time_taken = Time.now - start_disable_google_play_services
      send_data_to_eds("disable-google-service", time_taken, "failure")
      zombie_push("android", "disable-google-service", e.message, "", "", @device_id, @session_id)
      notify_to_pusher(extract_session_params , BrowserStack::GOOGLE_PAY_PUSHER_FAILURE_MESSAGE, nil)
      raise e
    end
  end

  def google_play_services_disabled?
    File.exist?("#{BrowserStack::STATE_FILES_DIR}/#{BrowserStack::GOOGLE_PAY}_#{@device_id}")
  end

  def notify_to_pusher(params, message, other_params_to_pusher = nil)
    log(:info, "notify_to_pusher called with params: #{params} with message: #{message}")
    params["session_id"] = params["live_session_id"]
    notify_pusher_live(message, params, other_params_to_pusher)
  end

  def cleanup
    start_enable_google_play_services = Time.now
    return false unless google_play_services_disabled?

    begin
      log(:info, "Enabling Google Play services to disable google pay for #{@device_id}, #{@session_id}, #{@product}")
      @adb.shell("settings put global heads_up_notifications_enabled 1")
      google_play_services_state = @adb.shell("pm enable #{BrowserStack::GOOGLE_PLAY_SERVICES_PACKAGE}")
      time_taken = Time.now - start_enable_google_play_services
      if google_play_services_state.include? "new state: enabled"
        send_data_to_eds("enable-google-service", time_taken, "success")
        remove_state_file
        true
      else
        raise "#{BrowserStack::GOOGLE_PAY_TAG} Failed to enable Google Play services #{@device_id}"
      end
    rescue StandardError => e
      log(:error, "Error occured on pm enable command: #{e.message},
        #{@device_id},
        #{@session_id},
        #{e.message},
        #{e.backtrace.join("\n")}")
      time_taken = Time.now - start_enable_google_play_services
      zombie_push(
        "android",
        "enable-google-service-failure",
        e.message,
        "", "",
        @device_id, @session_id
      )
      send_data_to_eds("enable-google-service-failure", time_taken, "failure")
      false
    end
  end

  def self.google_pay_device?(device_id)
    BrowserStack::GOOGLE_PAY_DEVICES.include?(device_id)
  end

  def touch_state_file
    FileUtils.touch("#{BrowserStack::STATE_FILES_DIR}/#{BrowserStack::GOOGLE_PAY}_#{@device_id}")
    true
  end

  def remove_state_file
    FileUtils.rm_rf("#{BrowserStack::STATE_FILES_DIR}/#{BrowserStack::GOOGLE_PAY}_#{@device_id}")
    true
  end

end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase
  device_id = ARGV[1].to_s.strip
  session_id = ARGV[2].to_s.strip
  product = ARGV[3].to_s.strip
  helper = GooglePay.new({ device: device_id, session_id: session_id, product: product })
  helper.send(command)
end
