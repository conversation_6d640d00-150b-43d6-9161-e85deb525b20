require 'yaml'

require_relative '../constants'

# Module for retrieving version information from component_versions.yml
module VersionConfig
  # Holds the supported versions and variants of all components
  CONFIG = YAML.load_file(BrowserStack::COMPONENT_VERSION_CONFIG_PATH)

  # Return array of all supported versions for <component>
  def supported_versions(component)
    CONFIG[component.upcase]["VERSIONS"]
  end

  # Return array of all supported variants for <component>
  def supported_variants(component)
    CONFIG[component.upcase]["VARIANTS"]
  end

  # Define class methods for each component like `webview_versions`, `play_services_variants`
  CONFIG.each_key do |component|
    define_method(:"#{component.downcase}_versions") do
      supported_versions(component.upcase)
    end

    define_method(:"#{component.downcase}_variants") do
      supported_variants(component.upcase)
    end
  end
end
