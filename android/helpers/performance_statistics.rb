require_relative '../models/android_device'
require_relative '../lib/root_command'
require_relative '../constants'

require 'android_toolkit'
require 'logger'

require "#{BrowserStack::MOBILE_COMMON_HOME}/mobile_session_info/lib/mobile_session_info"

class PerformanceStatistics

  PERFORMANCE_STATISTICS_FILE_PATH = File.expand_path('../live/scripts/performance_statistics.sh', __dir__)

  def initialize(device, logger, logger_params = {})
    @device = device
    @package_list_file = "/tmp/#{@device}_package_list_profiling.txt"

    @device = device
    @logger = logger
    @logger_params = logger_params
    AndroidToolkit::Log.logger = @logger
    @adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    @device_obj = BrowserStack::AndroidDevice.new(@device, "PerformanceStatistics", @logger)
    @root_command = RootCommand.new(@device_obj, @logger, @logger_params)
  end

  def start(session_id)

    push_performance_statistics_script_to_device
    write_session_header_to_log(session_id)
    start_performance_statistics_sh_script
  rescue StandardError => e
    @logger.error("Exception in PerformanceStatistics Start for #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
    raise "Exception in PerformanceStatistics Start for #{@device}: #{e.message} \n#{e.backtrace.join("\n")}"

  end

  def update_app(new_packages = nil)

    packages = read_packages_from_file

    add_package = false
    new_packages&.each do |package|
      unless packages.include?(package.strip)
        packages.push(package)
        add_package = true
      end
    end
    return unless add_package

    stop_performance_script
    write_packages_to_file(packages)
    push_performance_statistics_script_to_device(packages)
    start_performance_statistics_sh_script
  rescue StandardError => e
    raise "Exception #{e.message}\n#{e.backtrace.join("\n")}"

  end

  private

  def push_performance_statistics_script_to_device(packages = nil)
    file = modified_script(packages)
    system("adb -s #{@device} push #{file.path} /data/local/tmp/performance_statistics.sh")
  ensure
    file.close
    file.unlink
  end

  def start_performance_statistics_sh_script
    @logger.info("starting performance script")
    spawn_process("adb -s #{@device} shell 'sh /data/local/tmp/performance_statistics.sh &> "\
                  "#{BrowserStack::PERFORMANCE_STATS_LOGS_DEVICE_PATH}' &")
  end

  def modified_script(packages)
    performance_script = File.read(PERFORMANCE_STATISTICS_FILE_PATH)

    packages = if !packages.nil?
                 "'#{packages.map(&:strip).join("' '")}'"
               else
                 installed_packages
               end

    performance_script.gsub!('%REPLACE_UDP_HOST%', udp_host)
    performance_script.gsub!('%REPLACE_UDP_PORT%', udp_port)
    performance_script.gsub!('%REPLACE_PACKAGES%', packages)

    file = Tempfile.new('performance_statistics_script')
    file.write(performance_script)
    file.rewind

    file
  end

  def udp_host

    File.read("/usr/local/.browserstack/whatsmyip")
  rescue StandardError
    ""

  end

  def udp_port
    "33333"
  end

  def installed_packages
    packages_array = MobileSessionInfo.read(@device)["packages"]
    packages_array.map! { |package| package["name"] }
    write_packages_to_file(packages_array)
    packages_array = "'#{packages_array.join("' '")}'"
  end

  def read_packages_from_file
    packages = File.readlines(@package_list_file)
    packages.map(&:strip)
  end

  def write_packages_to_file(packages)
    File.open(@package_list_file, "w+") do |f|
      packages.each { |package| f.puts(package) }
    end
  end

  def stop_performance_script
    if @device_obj.rooted?
      @logger.info("stopping performance script")
      kill_cmd = "kill -9 \`/data/local/tmp/busybox ps aux | grep performance_statistics.sh | "\
               "/data/local/tmp/busybox awk '{print \$1}'\`"
      @root_command.run(kill_cmd)
    else
      @logger.info("Cannot stop script, device is not rooted")
    end
  end

  def write_session_header_to_log(session_id)
    File.open(performance_stats_logs_path, 'a') do |current_log_file|
      current_log_file.puts "---------- Session: #{session_id} ----------"
    end
  end

  def pull_performance_stats_logs
    @logger.info("Pulling performance stats logs")
    # pull log file from device and append to logs on host machine
    @adb.pull(BrowserStack::PERFORMANCE_STATS_LOGS_DEVICE_PATH, tmp_performance_stats_logs_path)
    if File.exist?(tmp_performance_stats_logs_path)
      log_to_append = File.read(tmp_performance_stats_logs_path)
      File.open(performance_stats_logs_path, 'a') do |current_log_file|
        current_log_file.puts log_to_append
      end
      File.delete(tmp_performance_stats_logs_path)
      @adb.shell("rm #{BrowserStack::PERFORMANCE_STATS_LOGS_DEVICE_PATH}")
    end
  end

  def performance_stats_logs_path
    "#{BrowserStack::LOGGING_DIR}/performance_stats_#{@device}.log"
  end

  def tmp_performance_stats_logs_path
    "#{BrowserStack::TMP_DIR}/performance_stats_#{@device}.log"
  end
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase.to_sym
  device_id = ARGV[1].to_s.strip
  helper = PerformanceStatistics.new(device_id, Logger.new($stdout))
  puts helper.send(command)
end
