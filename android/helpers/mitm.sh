device=`getprop ro.product.model`
version=`getprop ro.build.version.release`
version=${version:0:1}
exit_code=0

# TODO: Parametrize the below hash as it maybe possible that if
# server is not restarted, we may push old cert with the new name.
MITMPROXY_CA_CERT_FILE_HASH="efb15d7d.0"
CA_CERT_FILE_MD5SUM="d5d9f142ec09c0c05c6db467acae8b23"
MITMPROXY_CA_CERT_TMP_LOCATION="/data/local/tmp/$MITMPROXY_CA_CERT_FILE_HASH"
MITM_MOVE_EXIT_CODE_FILE="/data/local/tmp/ca_cert_move_exit_code.log"
MOUNT_BIN_PATH="/system/bin/mount"
MITM_LOG_FILE="/data/local/tmp/mitm_cert_logs"

if [[ $version == 7 && ($device == "Pixel" || $device == "Pixel XL") ]]; then
  MOUNT_PATH="/system_root"
elif [[ $device == "SM-A105FN" || $device == "SM-A115M" ]] || [[ $device == "SM-G980F" ]] || [[ $device == "SM-G991B" ]] || [[ $device == "SM-G998B" ]] || [[ $device == "SM-G996B" ]] || [[ $device == "SM-T870" ]] || [[ $device == "SM-T875" ]] || [[ $device == "SM-T876B" ]] || [[ $device == "SM-A105M" ]]; then
  MOUNT_PATH="/"
else
  MOUNT_PATH="/system"
fi

if [[ $version == 7 && ($device == "Pixel" || $device == "Pixel XL") ]]; then
  SYSTEM_PATH="/system_root"
else
  SYSTEM_PATH="/system"
fi

MITMPROXY_CA_CERT_LOCATION="$SYSTEM_PATH/etc/security/cacerts/$MITMPROXY_CA_CERT_FILE_HASH"

delete_ringtones() {
  rm /system/media/audio/ringtones/*
}

ca_cert_md5=$(md5sum $MITMPROXY_CA_CERT_LOCATION | cut -d ' ' -f 1)

if ([[ ! -s $MITMPROXY_CA_CERT_LOCATION ]] || [[ "$ca_cert_md5" != "$CA_CERT_FILE_MD5SUM" ]]) && [[ $version != 5 ]]
then
  echo "Mitm cert is empty, starting copy for $device: `date`" > $MITM_LOG_FILE

  # Make system mount path read-write
  $MOUNT_BIN_PATH -o remount,rw $MOUNT_PATH
  echo "return code of remount,rw : $?" >> $MITM_LOG_FILE
  $MOUNT_BIN_PATH -o rw,remount $MOUNT_PATH
  echo "return code of rw,remount : $?" >> $MITM_LOG_FILE

  # Deleting ringtones to ensure system partition has some free space for oneplus 6t, MOB-6052
  if [[ "$device" == "ONEPLUS A6010" ]] || [[ "$device" == "ONEPLUS A6013" ]]; then
    delete_ringtones
  fi

  # Copy our mitm cert to cacerts directory
  cp $MITMPROXY_CA_CERT_TMP_LOCATION $MITMPROXY_CA_CERT_LOCATION >> $MITM_LOG_FILE
  exit_code=$?
  echo "cp exit_code: $exit_code" >> $MITM_LOG_FILE
  chmod 644 $MITMPROXY_CA_CERT_LOCATION
  echo "return code chmod : $?" >> $MITM_LOG_FILE
  file_info=`ls -lah $MITMPROXY_CA_CERT_TMP_LOCATION $MITMPROXY_CA_CERT_LOCATION`
  echo "File info: $file_info" >> $MITM_LOG_FILE

  # Make system mount path read-only again
  $MOUNT_BIN_PATH -o remount,ro $MOUNT_PATH
  echo "return code remount,ro : $?" >> $MITM_LOG_FILE
  $MOUNT_BIN_PATH -o ro,remount $MOUNT_PATH
  echo "return code ro,remount : $?" >> $MITM_LOG_FILE
fi

echo $exit_code > $MITM_MOVE_EXIT_CODE_FILE # Exit code file is used elsewhere to check for success

# Change permission of log files so they dont need root user to access/read, because mitm.sh is run_as_root
chmod 644 $MITM_MOVE_EXIT_CODE_FILE
chmod 644 $MITM_LOG_FILE

# Cleanup
rm $MITMPROXY_CA_CERT_TMP_LOCATION
rm /data/local/tmp/mitm.sh

