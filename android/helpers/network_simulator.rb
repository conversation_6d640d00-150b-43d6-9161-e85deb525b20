#!/usr/bin/ruby

require 'English'
require 'fileutils'
require 'json'
require 'dotenv/load'
require "/usr/local/.browserstack/mobile/common/push_to_zombie"
require "/usr/local/.browserstack/mobile/common/helpers"
require "/usr/local/.browserstack/mobile/android/helpers/utils"
require "/usr/local/.browserstack/mobile/android/helpers/http_utils"
require "/usr/local/.browserstack/mobile/android/lib/model_database"

require_relative "../lib/os_utils"
require_relative '../lib/android_influxdb_client'
require_relative '../lib/usb_vpn'
require_relative '../lib/bstack_reverse_tether_controller'
require_relative '../models/android_device'

# To run reset_simulation from bash
require 'browserstack_logger'
NETWORK_SIMULATION_RESET_STANDALONE_LOG = "/var/log/browserstack/network_simulation_reset_standalone.log".freeze

class NetworkSimulator # rubocop:todo Metrics/ClassLength
  include BrowserStack

  MAX_DOWNLOAD = "50Mbit".freeze
  MAX_UPLOAD = "50Mbit".freeze
  USB_MAX_DOWNLOAD = "200Mbit".freeze
  USB_MAX_UPLOAD = "200Mbit".freeze
  USB_LIMITED_MAX_DOWNLOAD = BrowserStack::LIMITED_NETWORK_CAP[:max_download][:usb]
  USB_LIMITED_MAX_UPLOAD = BrowserStack::LIMITED_NETWORK_CAP[:max_upload][:usb]
  WIFI_LIMITED_MAX_DOWNLOAD = BrowserStack::LIMITED_NETWORK_CAP[:max_download][:wifi]
  WIFI_LIMITED_MAX_UPLOAD = BrowserStack::LIMITED_NETWORK_CAP[:max_upload][:wifi]

  DIR_HOME = "/usr/local/.browserstack".freeze
  STATIC_CONF = "#{DIR_HOME}/config/static_conf.json".freeze
  CONFIG_JSON = "#{DIR_HOME}/config/config.json".freeze
  DRIVER_ACTIONS = "#{DIR_HOME}/mobile/android/driver_actions.sh".freeze

  def initialize(device, port, component, genre = nil, session_id = nil)
    @device = device
    @port = calculate_privoxy_port(port).to_i
    @tunnel_ip = calc_usb_tunnel_ip(port)
    @tun_counter = calc_tun_counter(port)
    @config_json = JSON.parse(File.read(CONFIG_JSON))
    @component = component
    @chromedriver_port = calculate_chromedriver_port(port).to_i
    @chromedriver_session_id = get_chrome_driver_session if genre == "selenium"
    @influxdb_client = BrowserStack::AndroidInfluxdbClient.new(BrowserStack.logger)
    @session_id = session_id
    @usb_vpn = UsbVPN.new(device)
    @allow_limited_network_file = "#{BrowserStack::ALLOW_LIMITED_NETWORK_FILE}_#{@device}"
    @device_obj = BrowserStack::AndroidDevice.new(@device, self.class.to_s, BrowserStack.logger)
    setup_interface
  end

  def brt_controller
    @brt_controller ||= BStackReverseTetherController.new(@device_obj, BrowserStack.logger)
  end

  def log(message)
    puts "#{Time.now} #{self.class}: #{@device} ID: #{object_id} session: #{@session_id} message: #{message}"
  end

  def script_logger_args(progname='')
    BrowserStack::ScriptFormatter.new.call(Thread.current[:logger_params].merge({ progname: progname }))
  end

  def execute(command)
    log "Executing #{command}"
    result = `#{command}`.strip
    log "Output of execution: #{result}. Status: #{$CHILD_STATUS}"
    result
  end

  def is_usb_interface?(interface)
    @device_usb_interface && (interface == @device_usb_interface)
  end

  def max_upload_speed(interface)
    if is_usb_interface?(interface)
      allow_limited_network? ? USB_LIMITED_MAX_UPLOAD : USB_MAX_UPLOAD
    else
      (allow_limited_network? ? WIFI_LIMITED_MAX_UPLOAD : MAX_UPLOAD)
    end
  end

  def max_download_speed(interface)
    if is_usb_interface?(interface)
      allow_limited_network? ? USB_LIMITED_MAX_DOWNLOAD : USB_MAX_DOWNLOAD
    else
      (allow_limited_network? ? WIFI_LIMITED_MAX_DOWNLOAD : MAX_DOWNLOAD)
    end
  end

  def limited_upload_speed(interface)
    if is_usb_interface?(interface)
      BrowserStack::LIMITED_NETWORK_CAP[:network_bw_upld][:usb]
    else
      BrowserStack::LIMITED_NETWORK_CAP[:network_bw_upld][:wifi]
    end
  end

  def limited_download_speed(interface)
    if is_usb_interface?(interface)
      BrowserStack::LIMITED_NETWORK_CAP[:network_bw_dwld][:usb]
    else
      BrowserStack::LIMITED_NETWORK_CAP[:network_bw_dwld][:wifi]
    end
  end

  def get_interface_list
    execute("ls /sys/class/net/ | grep -v tun | grep -v lo | grep -v ifb0").split
  end

  def get_tun_interface_list
    execute("ls /sys/class/net/ | grep tun").split
  end

  def is_interface_main?(interface, ip)
    execute("ifconfig #{interface} | grep \"#{ip}\" | wc -l") == "1"
  end

  def get_machine_ethernet_interface
    result = ""
    ip = begin
      File.read("/usr/local/.browserstack/whatsmyip").strip
    rescue StandardError
      nil
    end
    get_interface_list.each do |i|
      if is_interface_main?(i, ip)
        result = i
        break
      end
    end
    result
  end

  def get_device_usb_interface
    return 'lo' if @device_obj.uses_gnirehtet_vpn_app? # gnirehtet vpn relay on localhost (or loopback)

    "tun#{@device_obj.tun_counter}"
  end

  def setup_interface
    @machine_interface = get_machine_ethernet_interface
    @device_usb_interface = get_device_usb_interface
    @virtual_interface = "ifb0"
  end

  def get_chrome_driver_session
    data = JSON.parse(HttpUtils.send_get("127.0.0.1:#{@chromedriver_port}/wd/hub/sessions").body)
    if data["status"] != 0
      BrowserStack.logger.warn(
        "NetworkSimulator: Chrome Driver Response /sessions - #{data.inspect}", @logger_params
      )
    end
    data["value"][0]["id"]
  rescue StandardError => e
    BrowserStack.logger.warn("NetworkSimulator: Chrome Driver Request Error - #{e.inspect}", @logger_params)
  end

  def flowid
    "1:1#{@port % 10}"
  end

  def is_queuing_setup?(interface, rule)
    done = execute("tc qdisc show dev #{interface} | grep \"qdisc #{rule}\" | wc -l") == "1"
    log "Queuing rule #{rule} not setup for interface #{interface}" unless done
    done
  end

  def is_ingress_queuing_setup?(interface)
    done = is_queuing_setup?(interface, "ingress")
    log "Queuing rule ingress not setup for interface #{interface}" unless done
    done
  end

  def is_htb_queuing_setup?(interface)
    done = is_queuing_setup?(interface, "htb")
    log "Queuing htb handle not setup for interface #{interface}" unless done
    done
  end

  def is_htb_class_setup?(interface, flow_id)
    done = execute("sudo tc class show dev #{interface} | grep -w \"class htb #{flow_id}\" | wc -l") == "1"
    log "Queuing htb class #{flow_id} not setup for interface #{interface}" unless done
    done
  end

  def is_htb_filter_setup?(interface, flow_id)
    done = execute("sudo tc filter show dev #{interface} | grep -w \"#{flow_id}\" | wc -l") == "1"
    log "Queuing htb filter #{flow_id} not setup for interface #{interface}" unless done
    done
  end

  def is_virtual_interface_setup?
    Dir.exist?("/sys/class/net/#{@virtual_interface}")
  end

  def setup_virtual_interface(interface)
    unless is_virtual_interface_setup?
      execute("sudo modprobe ifb numifbs=1")
      execute("sudo ip link set dev #{@virtual_interface} up")
    end

    unless is_ingress_queuing_setup?(interface)
      execute("sudo tc qdisc add dev #{interface} handle ffff: ingress")
      execute("sudo tc filter add dev #{interface} parent ffff: protocol ip u32 match u32 0 0 action mirred "\
              "egress redirect dev #{@virtual_interface}")
    end
    log "Setting up virtual_interface completed"
  end

  def setup_queuing_rules(interface)
    execute("sudo tc qdisc add dev #{interface} root handle 1: htb default 30") unless is_htb_queuing_setup?(interface)
    setup_virtual_interface(interface)
    unless is_htb_queuing_setup?(@virtual_interface)
      execute("sudo tc qdisc add dev #{@virtual_interface} root handle 1: htb default 30")
    end
    log "Setting up Queuing rules completed"
  end

  def setup_parent_class_rules(interface)
    unless is_htb_class_setup?(interface, "1:1")
      execute("sudo tc class add dev #{interface} parent 1:1 classid 1:1 htb rate #{max_download_speed(interface)}"\
              " ceil #{max_download_speed(interface)}")
    end
    setup_virtual_interface(interface)
    unless is_htb_class_setup?(@virtual_interface, "1:1")
      execute("sudo tc class add dev #{@virtual_interface} parent 1:1 classid 1:1 htb rate "\
              "#{max_upload_speed(interface)} ceil #{max_upload_speed(interface)}")
    end
    log "Setting up Parent class 1:1 rules completed"
  end

  def is_netem_setup?(flow_id, interface)
    done = execute("sudo tc qdisc show dev #{interface} | grep \"qdisc netem\" | grep -w \"#{flow_id}\" | wc -l") == "1"
    log "Queuing netem rule is not setup for #{interface}" unless done
    done
  end

  def setup_download_rules(params, interface)
    bw_dwld = params["network_bw_dwld"] || limited_download_speed(interface)
    filter_rule = "tc filter add dev #{interface} protocol ip parent 1: prio 1 u32"
    action = is_htb_class_setup?(interface, flowid) ? "change" : "add"
    execute("sudo tc class #{action} dev #{interface} parent 1:1 classid #{flowid} htb rate #{bw_dwld}kbit")
    unless is_htb_filter_setup?(interface, flowid)
      execute(
        "sudo #{filter_rule} match ip protocol 6 0xff match ip sport #{@port} 0xffff flowid #{flowid}"
      )
    end
    log "#{@device}: Setting up download rules completed"
  end

  def setup_upload_rules(params, interface)
    # upload rules are setup on the virtual interface which is common to the device_usb_interface and main interface
    bw_upld = params["network_bw_upld"] || limited_upload_speed(interface)
    action = is_htb_class_setup?(@virtual_interface, flowid) ? "change" : "add"
    execute("sudo tc class #{action} dev #{@virtual_interface} parent 1:1 classid #{flowid} htb rate #{bw_upld}kbit")
    unless is_htb_filter_setup?(@virtual_interface, flowid)
      execute("sudo tc filter add dev #{@virtual_interface} protocol ip parent 1: prio 1 u32 match ip protocol 6 0xff "\
              "match ip dport #{@port} 0xffff flowid #{flowid}")
    end
    log "Setting up upload rules completed"
  end

  def setup_netem_rules(params, interface)
    latency = params["network_latency"] || BrowserStack::LIMITED_NETWORK_CAP[:network_latency]
    pk_loss = params["network_pk_loss"] || BrowserStack::LIMITED_NETWORK_CAP[:network_pk_loss]
    action = is_netem_setup?(flowid, interface) ? "change" : "add"
    execute("sudo tc qdisc #{action} dev #{interface} parent #{flowid} netem delay #{latency}ms loss #{pk_loss}%")
    log "Setting up netem lattency rules completed"
  end

  def verify_download_rules(interface)
    done = is_htb_class_setup?(interface, flowid) && is_htb_filter_setup?(interface, flowid) &&
      is_netem_setup?(flowid, interface)
    log "Setting up download rules not set" unless done
    done
  end

  def verify_upload_rules
    # upload rules are setup on the virtual interface which is common to the device_usb_interface and main interface
    done = is_htb_class_setup?(@virtual_interface, flowid) && is_htb_filter_setup?(@virtual_interface, flowid)
    log "Setting up upload rules not set" unless done
    done
  end

  def throttling_setup?(interface)
    (verify_download_rules(interface) && verify_upload_rules)
  end

  def setup_throttling_rules(params, interface)
    setup_queuing_rules(interface)
    setup_parent_class_rules(interface)
    setup_download_rules(params, interface)
    setup_upload_rules(params, interface)
    setup_netem_rules(params, interface)
  end

  def reset_throttling_rules(interface)
    if is_htb_queuing_setup?(interface)
      action = is_htb_class_setup?(interface, flowid) ? "change" : "add"
      execute("sudo tc class #{action} dev #{interface} parent 1:1 classid #{flowid} htb rate "\
              "#{max_download_speed(interface)}")
      action = is_htb_class_setup?(@virtual_interface, flowid) ? "change" : "add"
      execute("sudo tc class #{action} dev #{@virtual_interface} parent 1:1 classid #{flowid} htb rate "\
              "#{max_upload_speed(interface)}")
      action = is_netem_setup?(flowid, interface) ? "change" : "add"
      execute("sudo tc qdisc #{action} dev #{interface} parent #{flowid} netem delay 0ms loss 0%")
      log "Interface: #{interface} Queuing rules are reset"
    elsif allow_limited_network?
      log "Interface: #{interface} Setting up throttling rules to allow limited network"
      setup_throttling_rules({}, interface)
    else
      log "Interface: #{interface} Queuing rules are not setup"
    end
  end

  def push_and_run_bsrun
    if File.exist? "/tmp/#{@device}_bsrun.sh"
      execute("adb -s #{@device} push /tmp/#{@device}_bsrun.sh /data/local/tmp/bsrun.sh")
      execute("adb -s #{@device} shell setprop log.tag.bsrun 1")
      execute("adb -s #{@device} shell setprop log.bsrun 1")
    end
    FileUtils.rm_rf "/tmp/#{@device}_bsrun.sh"
  end

  def get_wifi_credentials
    static_conf = JSON.parse(File.read(STATIC_CONF))
    ssid = static_conf['ssid']
    ssid = "BLT iOS" if wifi_experiment_enabled?(device_model, device_region)
    password = static_conf['wifi_password']
    [ssid, password]
  end

  def device_region
    @config_json['devices'][@device]['sub_region']
  end

  def device_model
    @config_json['devices'][@device]['device_name']
  end

  def device_wifi_disabled_file
    "/tmp/wifi_disabled_#{@device}"
  end

  def is_device_wifi_disabled?
    File.exist? device_wifi_disabled_file
  end

  def push_disable_wifi_file
    # create a disable wifi file and push to the device, so Browserstack app will not enable wifi automatically
    disable_wifi_file = "/tmp/disable_wifi_#{@device}"
    FileUtils.touch(disable_wifi_file)
    push_disable_wifi_file_cmd = "timeout 20 adb -s #{@device} push '#{disable_wifi_file}' '/sdcard/disable_wifi'"
    OSUtils.execute(push_disable_wifi_file_cmd)
  end

  def update_wifi_state(required_state)
    # Do not sun svc wifi command for Samsung Galaxy S20 and S21
    if @device_obj.rooted? && !["SM-G991B", "SM-G998B", "SM-G996B",
                                "SM-G981B", "SM-G980F"].include?(@config_json['devices'][@device]['device_name'])
      OSUtils.execute("sh #{DRIVER_ACTIONS} run_as_root #{@device} 'svc wifi #{required_state} &> /sdcard/ok'")
    elsif ["BON-AL00"].include?(@config_json['devices'][@device]['device_name'])
      OSUtils.execute("adb -s #{@device} shell 'svc wifi #{required_state}'")
    else
      OSUtils.execute(
        "adb -s #{@device} shell am startservice --user 0 "\
        "-n com.android.browserstack/.services.WifiHandlerService --es action \"#{required_state}\""
      )
    end
  end

  def disable_wifi
    push_disable_wifi_file
    execute("adb -s #{@device} shell 'am stopservice com.android.browserstack/.services.GraphitePushService'")
    execute("adb -s #{@device} shell 'pm disable com.android.browserstack/.receiver.WifiBroadcastReceiver'")
    update_wifi_state("disable")
    sleep 2
    File.open(device_wifi_disabled_file, 'w') { |f| f.write("Wifi is Disabled") }
    log "Wifi disabled"
  end

  def enable_wifi
    return unless is_device_wifi_disabled?

    ssid, password = get_wifi_credentials
    update_wifi_state("enable")
    sleep 2
    execute("adb -s #{@device} shell 'am startservice --user 0 -n "\
        "com.android.browserstack/.services.GraphitePushService --es ssid #{ssid} --es pass #{password} &> /sdcard/ok'")
    File.delete(device_wifi_disabled_file)
    log "Command to enable wifi executed"
  end

  def device_airplane_mode_toggled_file
    "/tmp/airplane_mode_toggled_#{@device}"
  end

  def is_device_airplane_mode_enabled?
    File.exist? device_airplane_mode_toggled_file
  end

  def is_airplane_mode_enabled?
    execute("adb -s #{@device} shell settings get global airplane_mode_on").strip.to_s == "1"
  end

  def enable_airplane_mode
    OSUtils.execute("sh #{DRIVER_ACTIONS} run_as_root #{@device} 'settings put global airplane_mode_on 1; "\
                    "am broadcast -a android.intent.action.AIRPLANE_MODE'")
    File.open(device_airplane_mode_toggled_file, 'w') { |f| f.write("AirPlane is Toggled") }
    log "Airplane mode enabled"
  end

  def disable_airplane_mode
    return unless is_device_airplane_mode_enabled?

    OSUtils.execute("sh #{DRIVER_ACTIONS} run_as_root #{@device} 'settings put global airplane_mode_on 0; "\
                    "am broadcast -a android.intent.action.AIRPLANE_MODE'")
    File.delete(device_airplane_mode_toggled_file)
    log "Airplane mode disabled"
  end

  def setup_chrome_driver_network_profile(params)
    return if @chromedriver_session_id.nil?

    data = {
      network_conditions: {
        offline: params["network_airplane_mode"].to_s == "true" || params["network_wifi"].to_s == "false",
        latency: params["network_latency"].to_i,
        download_throughput: params["network_bw_dwld"].to_i * 128, # Byets/Second
        upload_throughput: params["network_bw_upld"].to_i * 128 # Bytes/Second
      }
    }
    res = HttpUtils.send_post("http://127.0.0.1:#{@chromedriver_port}/wd/hub/session/#{@chromedriver_session_id}/chromium/network_conditions",
                              data)
    succeeded = JSON.parse(res.body)["status"] == 0
    unless succeeded
      BrowserStack.logger.warn(
        "NetworkSimulator: Chrome Driver Response /network_conditions - #{res.body}", @logger_params
      )
    end
    succeeded
  rescue StandardError => e
    BrowserStack.logger.warn("NetworkSimulator: Network Profile Setup Failed due to #{e.inspect}", @logger_params)
  end

  def is_app_internet_over_usb_file_present?
    File.exist?("/tmp/app_internet_via_usb_#{@device}")
  end

  def is_vpn_active?
    is_app_internet_over_usb_file_present? && @usb_vpn.vpn_tunnel_exists?
  end

  def allow_limited_network?
    File.exist?(@allow_limited_network_file)
  end

  def stop_vpn_for_device
    return unless is_app_internet_over_usb_file_present?

    BrowserStack.logger.info "Stopping VPN for device: #{@device} and session_id: #{@session_id}"
    script_logger = script_logger_args("#{File.basename(DRIVER_ACTIONS)}_stop_vpn")

    cmd = "bash #{DRIVER_ACTIONS} stop_vpn #{@device} #{@tun_counter} 2>&1 | "\
      "while read line; do echo #{script_logger} \"$line\" >> /var/log/browserstack/selenium_#{@device}.log; done"

    OSUtils.execute(cmd)
  end

  def redovpn_for_device(apps_string)
    BrowserStack.logger.info "Redoing VPN for device: #{@device} and session_id: #{@session_id}"
    script_logger = script_logger_args("#{File.basename(DRIVER_ACTIONS)}_redovpn")

    cmd = "bash #{DRIVER_ACTIONS} redovpn #{@device} #{@tunnel_ip} #{apps_string} 2>&1 | "\
          "while read line; do echo #{script_logger} \"$line\" >> /var/log/browserstack/selenium_#{@device}.log; done"

    OSUtils.execute(cmd)
  end

  def setup_simulation(params)
    # Disabling wifi/Enabling Airplane Mode involves turning device internet off.
    # Since we allow network to pass through usb, we need to disable wifi + stop vpn.
    log "Setting up simulation with params: #{params}"
    error = []
    excluded_error = []
    if params["genre"] == "selenium"
      succeeded = setup_chrome_driver_network_profile(params)
      error << "setup_chrome_driver_network_profile failed" unless succeeded
    elsif params["network_wifi"].to_s == "false"
      stop_vpn_for_device
      disable_wifi
      error << "disable_wifi failed" unless is_wifi_down?(@device)
      error << "stop_vpn failed" if is_vpn_active?
    elsif params["network_airplane_mode"].to_s == "true"
      if @device_obj.rooted?
        stop_vpn_for_device
        disable_wifi
        enable_airplane_mode
        error << "enable_airplane_mode failed" unless is_airplane_mode_enabled?
        error << "disable_wifi failed" unless is_wifi_down?(@device)
        error << "stop_vpn failed" if is_vpn_active?
      else
        excluded_error << "Attempt to enable Airplane Mode on non-rooted device #{@device} failed feature not supported"
      end
    else
      # will be setting throttling rules for both interfaces since there are devices where the vpn app wont be present.
      setup_throttling_rules(params, @machine_interface) # for internet over wifi
      excluded_error << "throttling_setup failed #{@machine_interface}" unless throttling_setup?(@machine_interface)

      setup_throttling_rules(params, @device_usb_interface) # for internet over usb
      unless throttling_setup?(@device_usb_interface)
        excluded_error << "throttling_setup failed #{@device_usb_interface}"
      end

      if @device_obj.uses_gnirehtet_for_app_automate?
        setup_throttling_rules(params, 'lo') # set here too to be sure every interface is set
        excluded_error << "throttling_setup failed lo" unless throttling_setup?('lo')
      end
    end
    session = params["automate_session_id"] || params["app_live_session_id"] || params["live_session_id"]
    if ( error + excluded_error ).any?
      zombie_push(
        'android', 'network-simulation-setup-failed',
        '', '', (error + excluded_error).join(","), @device, session
      )
    end
    error += excluded_error if (params["network_bw_dwld"] && params["network_latency"] && params["network_bw_upld"]) ||
                               allow_limited_network?
    error
  end

  def reset_simulation(apps_string = "", params = {})
    log "Resetting simulation"
    error = []
    excluded_error = []
    enable_wifi
    error << "enable_wifi failed" if is_wifi_down?(@device)

    if @device_obj.rooted?
      disable_airplane_mode
      error << "disable_airplane_mode failed" if is_airplane_mode_enabled?
    else
      excluded_error << "Attempt to disable Airplane Mode on non-rooted device #{@device} feature not supported"
    end

    # Redovpn if the device is internet_over_usb but usb vpn is not up start/mid session.
    # We already redovpn at the start of cleanup.
    if !apps_string.empty? && is_app_internet_over_usb_file_present? && !vpn_running?
      if @device_obj.uses_bstack_internet_app?
        brt_controller.ensure_running
        execute("adb -s #{@device} shell settings put system screen_off_timeout 86400000")
      else
        redovpn_for_device(apps_string)
      end
      error << "redovpn/brt-app failed" unless vpn_running?
    elsif !vpn_running?
      #to check if redo vpn was triggered post no network and airplane mode.
      excluded_error << "Redovpn not triggered as #{apps_string} "\
                        "#{is_app_internet_over_usb_file_present?} #{vpn_running?}"
    end

    # for internet over wifi
    reset_throttling_rules(@machine_interface)
    excluded_error << "reset_throttling_rules failed #{@machine_interface}" if throttling_setup?(@machine_interface)
    # for internet over usb
    reset_throttling_rules(@device_usb_interface)
    if throttling_setup?(@device_usb_interface)
      excluded_error << "reset_throttling_rules failed #{@device_usb_interface}"
    end
    # also checking and removing if dual vpn is used
    if @device_obj.uses_gnirehtet_for_app_automate?
      reset_throttling_rules('lo')
      excluded_error << "reset_throttling_rules failed #{@device_usb_interface}" if throttling_setup?('lo')
    end

    # Metrics and Tracking
    if ( error + excluded_error ).any?
      zombie_push(
        'android', 'network-simulation-reset-failed',
        '', '', (error + excluded_error).join(","), @device, @session_id,
        is_app_accessibility: params[:is_app_accessibility]
      )
    end
    unless error.empty?
      @influxdb_client.track_event(
        'network-simulation-reset-failed', @component, 'network_simulator', @device, 'true'
      )
    end
    error
  end

  def vpn_running?
    if @device_obj.uses_bstack_internet_app?
      brt_controller.running?(full_check: false)
    elsif @device_obj.uses_gnirehtet_vpn_app?
      @usb_vpn.send(:gnirehtet_vpn_running?) && @usb_vpn.gnirehtet_relay_running?
    else
      @usb_vpn.jni_forwarder_up?
    end
  end
end

if __FILE__ == $PROGRAM_NAME
  task = ARGV[0]
  device_id = ARGV[1]
  default_port = ARGV[2]
  component = ARGV[3]

  logger_params = {}
  logger_params[:device] = device_id
  logger_params[:component] = 'NetworkSimulator_bash'

  BrowserStack.init_logger(NETWORK_SIMULATION_RESET_STANDALONE_LOG, logger_params)
  ns = NetworkSimulator.new(device_id, default_port, component)
  ns.reset_simulation if task.to_s.casecmp("reset").zero?
end
