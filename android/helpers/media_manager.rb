require_relative '../constants'
require_relative '../exit_file'
require_relative '../../common/push_to_zombie'
require_relative '../models/android_device'

require 'browserstack_logger'
require 'shellwords'
require 'android_toolkit'
require 'set'

# This class manages device media files (pictures/movies)
class MediaManager # rubocop:disable Metrics/ClassLength
  include BrowserStack

  CUSTOM_MEDIA_DIR_ON_DEVICE = '/data/local/tmp/custom_media'.freeze
  UITEST_APP = 'com.browserstack.uiautomation'.freeze
  CONTACTS_APP = 'com.android.browserstack'.freeze

  def initialize(device, log_file, logger, logger_params = {})
    @device = device
    @log_file = log_file # output of shell logs
    @logger = logger
    @logger_params = logger_params
    @device_obj = BrowserStack::AndroidDevice.new(device, 'media_manager.rb', logger, logger_params)
    @recently_delete_file = nil
    AndroidToolkit::Log.logger = @logger
  end

  # True if there is unwanted media, or is missing preloaded media
  def media_cleanup_required?
    if dedicated_cleanup?
      log :info, 'Media cleanup not required as it is dedicated cleanup flow'
      return false
    end

    log :info, 'Checking locations where media files are frequently saved'
    return true unless common_locations_empty?

    log :info, 'Checking that all preloaded images and videos are present in the sdcard'
    return true unless sdcard_pictures_and_movies_clean?

    log :info, 'Checking that all preloaded images and videos are present in the media provider db'
    return true unless media_db_clean?

    log :info, 'Checking whether custom media are present in the sdcard'
    return true unless custom_documents_clean?

    log :info, 'Checking whether recently deleted media are clean'
    return true unless recently_deleted_clean?

    log :info, 'Device media is clean'
    false
  end

  def clean_media
    # Delete unwanted update folder in OPPO A96
    adb.shell("rm -rf /sdcard/Download/update") if ['CPH2333', 'RMX3785', 'RMX3867'].include?(model)

    log :info, 'Cleaning media using UITest app'
    cleanup_type = 'uitest app media cleanup'
    error = ''

    remove_uitest_app_tmp_files

    # TODO: can remove this when we switch to using the adb gem.
    # It uses Open3 instead of backticks - the command can be long.
    begin
      uitest_app_remove_media
    rescue StandardError
      # This handles case where there are thousands of media files to remove
      # Removing via uitest app will fail due to the length of the command,
      # so we fall back on the old media cleanup instead.
      log :error, 'Too many media files to remove - argument list too long'
    end

    if Gem::Version.new(os_version) >= Gem::Version.new('10.0')
      push_preloaded_media_to_custom_media_dir
    else
      push_media_to_sdcard
    end

    uitest_app_insert_media(images: images, videos: videos)

    # Wait for the activity to insert the images
    sleep 5

    # Check uitest app cleanup worked
    log :info, 'Checking if media cleanup was successful'
    return unless media_cleanup_required?

    cleanup_type = 'old media cleanup'

    log :info, 'UITest app failed to clean media. Using to old approach instead.'
    remove_media
    clear_media_provider unless clear_media_provider_fails?
    push_media_to_sdcard
    broadcast_media_intent unless broadcast_fails?
    clear_recently_deleted_files

    reboot_and_wait('media-cleanup - reboot after cleaning media') if reboot_required?
  rescue StandardError => e
    error = e.message
    raise
  ensure
    push_to_zombie(kind: 'media-cleanup', data: cleanup_type, error: error)
  end

  def check_and_clean_screen_recording
    return if Gem::Version.new(os_version) < Gem::Version.new('10.0')

    screenrecord_sdcard = adb.ls('/sdcard/screenrecord')
    screenrecord_storage = adb.ls('/storage/emulated/0/screenrecord')

    unless screenrecord_sdcard.empty? || screenrecord_storage.empty?
      remove_screen_recording
      push_to_zombie(kind: 'media-cleanup-screenrecord-file', data: screenrecord_sdcard + screenrecord_storage)
    end

    screenrecord_db = []

    db_media_paths('video').each do |path|
      screenrecord_db << path if path.include?('screenrecord')
    end

    unless screenrecord_db.empty?
      uitest_app_remove_media(images: [], videos: screenrecord_db)
      push_to_zombie(kind: 'media-cleanup-screenrecord-db', data: screenrecord_db)
    end

  end

  # Sometimes screenrecording files are not deleted after session ends
  # See more in this ticket: MOB-6849
  def remove_screen_recording
    log :info, 'Removing screen recording'
    adb.shell('rm -rf /sdcard/screenrecord')
    adb.shell('rm -rf /storage/emulated/0/screenrecord')
  end

  def common_locations_empty?
    # Oppos devices store photos & screenshots here
    # Google Pixel 6 has an empty Camera folder inside /sdcard/DCIM, which is marking device to offline intermittently

    dcim_dir = adb.ls('/sdcard/DCIM')
    log :info, "Directory present inside /sdcard/DCIM : #{dcim_dir}"

    # Check if DCIM contains only the Camera folder and if Camera is empty
    unless dcim_dir.empty?
      if dcim_dir == "Camera"
        camera_dir = adb.ls('/sdcard/DCIM/Camera')
        return false unless camera_dir.empty?
      else
        log :info, 'Too many directories present inside /sdcard/DCIM'
        return false
      end
    end

    # Huawei devices store screenshots here
    screenframe_dir = adb.ls('/sdcard/screenframes')
    return false unless screenframe_dir.empty?

    # Sometimes screenrecording files are not deleted after session ends
    # See more in this ticket: MOB-6849
    screenrecord_dir = adb.ls('/sdcard/screenrecord')
    return false unless screenrecord_dir.empty?

    # Documents directory can sometimes have user stored files - MOBPE-1188
    documents_dir = adb.ls('/sdcard/Documents')
    return false unless documents_dir.empty?

    # Sdcard directory sometimes has user specific folder - MOBPE-1315
    confirm_dir = adb.ls('/sdcard/Confirm')
    return false unless confirm_dir.empty?

    true
  end

  def custom_media_dir_clean?
    # Custom media is pushed here on android 10+
    custom_media_dir = adb.ls(CUSTOM_MEDIA_DIR_ON_DEVICE)
    return true if custom_media_dir.empty?

    preloaded_media = images + videos
    device_media = adb.ls(CUSTOM_MEDIA_DIR_ON_DEVICE)

    return false unless device_media.split("\n").count == preloaded_media.count

    preloaded_media.each { |f| return false unless device_media.include?(File.basename(f)) }

    true
  end

  def recently_deleted_clean?
    # on deleting media files, huawei stores them as temp files in gallery so that it can be recovered.
    # due to this data of previous session can be visible to next user
    # Removing it using this func

    return true unless ["BON-AL00"].include?(model)

    # Huawei stores recently deleted media under /sdcard/Pictures/.Gallery2/recycle/bins/
    all_files = adb.ls('-a /sdcard/Pictures').split("\n")

    hidden_file_exists = false
    all_files.each do |file|
      next unless file.match(/Gallery/)

      recycle_bin = adb.ls("-a /sdcard/Pictures/#{file}/recycle/bins")
      next if recycle_bin == ""

      hidden_file_exists = true
      @recently_delete_file = "/sdcard/Pictures/#{file}/recycle/bins"
      log :info, "Recently deleted files found #{@recently_delete_file}"
      break
    end

    !hidden_file_exists
  end

  # Returns true if the /sdcard/Pictures and /sdcard/Movies dirs contain only preloaded media.
  def sdcard_pictures_and_movies_clean?
    device_images = adb.ls('/sdcard/Pictures')
    if ['CPH2585', 'RMX3867', 'CPH2531'].include?(model)
      device_images = device_images.gsub("Screenshots\n", "") # OnePlus 12R: additional Screenshots folder
    end

    return false unless device_images.split("\n").count == images.count

    images.each { |f| return false unless device_images.include?(File.basename(f)) }

    device_videos = adb.ls('/sdcard/Movies')
    return false unless device_videos.split("\n").count == videos.count

    videos.each { |f| return false unless device_videos.include?(File.basename(f)) }

    true
  end

  #only get called if there is some extra cached media present
  def query_media_provider_without_cache(media)
    adb.shell("content query --uri content://media/external/#{media}/media --projection _data --where '_size>0'")
  end

  def update_media_database(media)
    adb.shell("am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file:///storage/emulated/0/#{media}")
  end

  def check_media?(media, media_array)
    media_query = query_media_provider_db(media)
    media_files = get_unique_filenames(media_query)

    if media_files.count != media_array.count
      log :info, "Forcing media scan to update the media database"
      case media
      when "video"
        update_media_database("Movies")
      when "images"
        update_media_database("Pictures")
      end
      log :info, "Checking media query again without cache data as the count of media files are not same"
      media_query = query_media_provider_without_cache(media)
      media_files = get_unique_filenames(media_query)
      log :info, "Media files are: #{media_files}"
    end
    return false unless media_files.count == media_array.count

    log :info, "Media file counts are same as preload media"

    media_array.each { |f| return false unless media_files.include?(File.basename(f)) }
    log :info, "Media file synced to the preload media"

    true
  end

  # Returns true if the media provider database contains only preloaded media.
  def media_db_clean?
    log :info, "Checking media db"

    check_media?('images', images) && check_media?('video', videos)
  end

  def custom_documents_clean?
    output_custom_media = adb.shell("#{BrowserStack::BUSYBOX} ls -A /sdcard/Download")
    if output_custom_media.include?('Nearby Share')
      #if 'Nearby Share' folder is present
      is_only_nearby_share = output_custom_media.strip == 'Nearby Share'

      if is_only_nearby_share
        log :info, 'Nearby share directory is present in the sdcard'
        #Check if 'Nearby Share' folder is empty
        files_inside_nearby_share = adb.shell("#{BrowserStack::BUSYBOX} ls -A \"/sdcard/Download/Nearby Share\"")
        return true if files_inside_nearby_share.empty?
      end
    end
    return false unless output_custom_media.include?('No such file or directory') || output_custom_media.empty?

    true
  rescue AndroidToolkit::ADB::ExecutionError => e
    return true if e.message.include?('No such file or directory') && ["BON-AL00"].include?(model)

    raise e
  end

  def remove_uitest_app_tmp_files
    uitest_app_files = "/sdcard/Android/data/#{UITEST_APP}/files"

    begin
      adb.shell("rm -f #{uitest_app_files}/custom_media_insertion_status.txt")
    rescue StandardError => e
      log :error, "Failed to remove custom_media_insertion_status.txt: #{e.message}"
    end

    custom_media_file = "/tmp/custom_media_files_#{@device}.json"
    FileUtils.rm_f(custom_media_file) if File.exist?(custom_media_file)
  end

  def uitest_app_remove_media(images: db_media_paths('images'), videos: db_media_paths('video'))
    images_to_remove = escape_and_join_filenames(images)
    videos_to_remove = escape_and_join_filenames(videos)

    cmd = 'am start'
    cmd += %( -n "#{UITEST_APP}/#{UITEST_APP}.CustomMediaActivity")
    cmd += %( --es action "remove")
    cmd += %( --esa image "#{images_to_remove}") unless images_to_remove.empty?
    cmd += %( --esa video "#{videos_to_remove}") unless videos_to_remove.empty?

    adb.shell(cmd)
  end

  def uitest_app_insert_media(images: [], videos: [], documents: [], session_id: '')
    images_to_insert = escape_and_join_filenames(images)
    videos_to_insert = escape_and_join_filenames(videos)
    docs_to_insert = escape_and_join_filenames(documents)

    cmd = 'am start'
    cmd += %( -n "#{UITEST_APP}/#{UITEST_APP}.CustomMediaActivity")
    cmd += %( --es action "insert")
    cmd += %( --esa image "#{images_to_insert}") unless images.empty?
    cmd += %( --esa video "#{videos_to_insert}") unless videos.empty?
    cmd += %( --esa document "#{docs_to_insert}") unless documents.empty?
    cmd += %( --es session_id "#{session_id}") unless session_id.empty?

    adb.shell(cmd)
  end

  def app_load_custom_contacts(contacts, session_id)
    contacts_to_insert = escape_and_join_filenames(contacts)

    cmd = 'am start'
    cmd += %( -n "#{CONTACTS_APP}/#{CONTACTS_APP}.main.PreloadContactsActivity")
    cmd += %( --es action "insert")
    cmd += %( --es clean "false")
    cmd += %( --esa contact "#{contacts_to_insert}")
    cmd += %( --es session_id "#{session_id}") unless session_id.empty?

    adb.shell(cmd)
  end

  def total_contacts_count
    cmd = 'content query --uri content://com.android.contacts/data/phones/ --projection _id | wc -l'
    output = adb.shell(cmd)
    output.nil? ? 0 : output.to_i
  end

  # Escapes spaces and special characters in filenames. Commas in filenames are escaped with "\,"
  # All the files are then joined into a comma separated string.
  def escape_and_join_filenames(media)
    escaped_media_filenames = media.map do |path|
      Shellwords.escape(File.basename(path)).gsub(/,/, '\,')
    end

    escaped_media_filenames.join(",")
  end

  def clean_pictures_for_huawei
    # cleaning pictures fails due to hidden screenshots in /sdcard/Pictures/Screenshots
    # launching the photos app and then cleaning, helps fix the issue
    return unless ["BON-AL00"].include?(model)

    log :info, "Removing Pictures for Huawei"
    adb.shell("am start -n com.huawei.photos/com.huawei.gallery.app.SinglePhotoActivity")
    adb.shell('rm -rf /sdcard/Pictures/*')
  end

  def remove_media
    log :info, 'Removing everything in /sdcard/Pictures and /sdcard/Movies'
    begin
      adb.shell('rm -rf /sdcard/Pictures/*')
    rescue AndroidToolkit::ADB::ExecutionError => e
      log :error, "Failed in /sdcard/Pictures/ #{e}"
      if ["BON-AL00"].include?(model)
        clean_pictures_for_huawei
      else
        raise e
      end
    end

    adb.shell('rm -rf /sdcard/Movies/*')

    # Location where preloaded media is pushed on android 10+
    adb.shell('rm -rf /data/local/tmp/custom_media/*')

    # Oppos devices store photos & screenshots here
    dcim_dir = adb.ls('/sdcard/DCIM')
    log :info, "Removing #{dcim_dir} inside /sdcard/DCIM directory"
    adb.shell('rm -rf /sdcard/DCIM/*')

    # Huawei devices store screenshots here
    adb.shell('rm -rf /sdcard/screenframes')

    # Delete documents dir contents - MOBPE-1188
    adb.shell('rm -rf /sdcard/Documents/*')

    # Deleting Confirm folder in /sdcard (temp fix MOBPE-1315)
    log :info, 'Removing Confirm Folder'
    adb.shell('rm -rf /sdcard/Confirm')

    remove_screen_recording

    # TODO: Only run this if the above commands failed to remove images
    log :info, 'Deleting any unexpected media found outside Pictures & Movies'
    unexpected_db_media.each do |media|
      #escaped_path = Shellwords.escape(media)
      adb.shell("rm -rf '#{media}'")
    end

    # Remove custom media
    log :info, 'Removing Custom media'
    adb.shell("cd /sdcard/Download && " \
    "#{BrowserStack::BUSYBOX} ls -A1 | while read -r dir; do #{BrowserStack::BUSYBOX} rm -rf \"$dir\"; done")
  end

  def clear_recently_deleted_files
    # Delete hidden files on Huawei
    if !@recently_delete_file.nil? || !recently_deleted_clean?
      log :info, 'Removing recently delete files'
      adb.shell("rm -rf #{@recently_delete_file}/*")
    end
  end

  # Remove any images currently in the gallery
  def clear_media_provider
    log :info, 'Removing existing images in gallery'
    adb.pm_clear('com.android.providers.media')
  end

  def push_preloaded_media_to_custom_media_dir
    adb.shell("mkdir -p #{CUSTOM_MEDIA_DIR_ON_DEVICE}")
    adb.push((images + videos), CUSTOM_MEDIA_DIR_ON_DEVICE)
  end

  def push_media_to_sdcard
    adb.shell('mkdir -p /sdcard/Pictures')
    adb.shell('mkdir -p /sdcard/Movies')
    adb.shell('mkdir -p /sdcard/Download')

    log :info, 'Adding default images/videos back'
    adb.push(images, '/sdcard/Pictures')
    adb.push(videos, '/sdcard/Movies')
  end

  # Broadcasts the media to be added to the media provider db
  def broadcast_media_intent
    log :info, 'Sending broadcast intent to add each image/video to gallery'
    pids = []
    media = {
      'Pictures' => images,
      'Movies' => videos
    }

    media.each do |dir, files|
      files.each do |f|
        action = 'android.intent.action.MEDIA_SCANNER_SCAN_FILE'
        data_uri = "file:///sdcard/#{dir}/" + File.basename(f)

        # activity manager broadcast:
        # https://developer.android.com/studio/command-line/adb#am
        broadcast = "am broadcast -a #{action} -d #{data_uri}"

        timeout = 60 * 10 # 10 min timeout
        cmd = "timeout #{timeout} #{BrowserStack::ADB} -s #{@device} shell #{broadcast}"

        # Spawn subproccess. The broadcast intent can sometimes take several
        # minutes to be acted on and closed - longer than the cleanup.
        pid = Process.spawn(cmd, %i[out err] => [@log_file, 'a'])
        pids << pid
      end
    end

    # Detatch subprocesses from parent so that they are
    # automatically reaped on termination
    pids.each do |pid|
      Process.detach(pid)
    end
  end

  # All images/videos listed the the media provider db
  def db_media_paths(type)
    media = query_media_provider_db(type).split("\n")

    media_paths = []

    media.each do |entry|
      _, path = entry.split('_data=')

      # Handles case where query returns 'No result found.'
      next if path.nil?

      media_paths << path
    end

    media_paths
  end

  # images/videos listed the media provider db, which are not in either:
  # /sdcard/Pictures/ or /sdcard/Movies/ or /sdcard/screenrecord/
  def unexpected_db_media
    unexpected_media = []

    all_media = db_media_paths('images') + db_media_paths('video')

    all_media.each do |path|
      next if path.include?('/storage/emulated/0/Pictures/') ||
        path.include?('/storage/emulated/0/Movies/') ||
        path.include?('/storage/emulated/0/screenrecord/')

      unexpected_media << path
    end
    unexpected_media
  end

  # Query the db used by android media provider
  def query_media_provider_db(media)
    adb.shell("content query --uri content://media/external/#{media}/media --projection _data")
  end

  def reboot_required?
    # On android 10, the file picker does not get updated after broadcast.
    # See this ticket for more: MOB-6942
    if Gem::Version.new(os_version) == Gem::Version.new('10.0')
      log :info, 'Reboot required to refresh file picker on android 10 devices'
      return true
    end

    if broadcast_fails?
      log :info, 'Broadcast intent does not work on this device. Rebooting to load gallery.'
      return true
    end

    if clear_media_provider_fails?
      log :info, 'Clearing the media provider fails on this device. Reboot required to refresh.'
      return true
    end

    false
  end

  # On the following devices the broadcast intent fails due to a permissions issue
  # ModernMediaScanner: Failed to visit ... java.nio.file.AccessDeniedException
  # See this ticket for more: MOB-6638
  # SM-G981B, SM-G980F = Samsung Galaxy S20
  # SM-G991B = Samsung Galaxy S21
  # SM-G998B = Samsung Galaxy S21 Ultra
  def broadcast_fails?
    return false unless Gem::Version.new(os_version) >= Gem::Version.new('11.0')
    return false unless ['SM-G981B', 'SM-G980F', 'SM-G991B', 'SM-G998B'].include?(model)

    true
  end

  # On Oppo Reno 3 Pro, the command to clear the media provider fails with:
  # Security exception: adb clearing user data is forbidden.
  def clear_media_provider_fails?
    @device_obj.pm_clear_command_fails?
  end

  def dedicated_cleanup?
    File.exist?(File.join(STATE_FILES_DIR, "dedicated_cleanup_#{@device}"))
  end

  # See more in this ticket: MOBPL-186
  def get_unique_filenames(filenames_string)
    unique_filenames = Set.new

    filenames_string.split(/\r\n|\n/).each do |path|
      unique_filenames << File.basename(path)
    end
    unique_filenames
  end

  private

  def reboot_and_wait(reason = nil)
    log :info, 'Rebooting device and waiting for reboot to complete'
    system(
      "sh #{BrowserStack::PATH}/driver_actions.sh reboot_and_wait #{@device} \"#{reason}\" " \
      ">> #{@log_file} 2>&1"
    )
  end

  def images
    Dir["#{media_dir}/images/*"]
  end

  def videos
    Dir["#{media_dir}/videos/*"]
  end

  def media_dir
    "#{BrowserStack::BS_DIR}/media"
  end

  def os_version
    @os_version ||= adb.os_version.to_s.to_i
  end

  def model
    @model ||= adb.model
  end

  def push_to_zombie(kind: '', error: '', data: '')
    zombie_key_value(
      platform: 'android',
      kind: kind,
      error: error,
      browser: model,
      data: data,
      device: @device,
      os_version: os_version
    )
  end

  def adb
    @adb ||= AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
  end

  def log(level, msg)
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, msg)
    else
      logger_params = @logger_params.merge({ subcomponent: self.class.to_s })
      @logger.send(level.to_sym, msg, logger_params)
    end
  end
end

def run_from_bash
  device_id = ARGV[0]

  raise 'Device id required' if device_id.nil?

  log_file = "#{BrowserStack::LOGGING_DIR}/cleanup_#{device_id}.log"
  logger_params = { device: device_id, component: 'cleanup' }
  logger = BrowserStack.init_logger(log_file, logger_params)

  manager = MediaManager.new(device_id, log_file, logger, logger_params)
  manager.check_and_clean_screen_recording
  manager.clean_media if manager.media_cleanup_required?
rescue StandardError => e
  ExitFile.write(e.message[0..200])
  raise e
end

run_from_bash if $PROGRAM_NAME == __FILE__
