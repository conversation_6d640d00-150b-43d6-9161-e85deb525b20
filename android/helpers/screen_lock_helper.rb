#!/usr/bin/env ruby

require 'android_toolkit'

require_relative '../exit_file'
require_relative '../constants'
require_relative '../../common/push_to_zombie'
require 'browserstack_logger'

module BrowserStack
  class ScreenLockHelper
    def initialize(params = {})
      @device_id = params[:device_id]
      @os_version = params[:os_version]
      @manufacturer = params[:manufacturer]
      @session_id = params[:session_id]

      @adb = AndroidToolkit::ADB.new(udid: @device_id)
      @logger = params[:logger]
      AndroidToolkit::Log.logger = @logger
    end

    def screen_lock_enabled?
      return false if @os_version < Gem::Version.new(8) # this method only works on android 8 and above

      # issue on Samsung Galaxy S9 Plus - 8.0 where dumpsys values are not correct unless
      # we run this first
      attempt_clear_lock
      output = lock_settings
      return false if output.nil? || output.empty?

      if @manufacturer == "samsung" && @os_version >= Gem::Version.new('10')
        # If CredentialType is in output, use this to determine status
        result = output.scan(/CredentialType: [a-zA-Z]*/)[0]

        if result.nil? # CredentialType not present, use isFbeSecure instead
          result = output.scan(/isFbeSecure = [a-zA-Z]*/)[0]
          return false if result.nil?

          result.include?("true")
        else
          !result.downcase.include?("none")
        end
      else
        result = output.scan(/SID = [0-9a-zA-Z]*/)[0]
        return false if result.nil?

        result.split(" = ")[1] != "0"
      end
    end

    private

    def lock_settings
      @adb.dumpsys("lock_settings", timeout: 5)
    end

    def attempt_clear_lock
      @adb.shell("locksettings clear")
    end
  end
end

if __FILE__ == $PROGRAM_NAME
  begin
    method = ARGV[0].to_s.strip.downcase.to_sym
    raise 'No method given' if method.nil?

    device_id = ARGV[1]
    os_version = Gem::Version.new(ARGV[2])
    manufacturer = ARGV[3]
    session_id = ARGV[4]

    logger_params = {}
    logger_params[:device] = device_id
    logger_params[:component] = 'ScreenLockHelper_bash'
    logger = BrowserStack.init_logger(File.join(BrowserStack::LOGGING_DIR, "cleanup_#{device_id}.log"), logger_params)

    helper = BrowserStack::ScreenLockHelper.new(device_id: device_id, os_version: os_version,
                                                manufacturer: manufacturer, session_id: session_id, logger: logger)
    ExitFile.write(helper.send(method))
  rescue StandardError => e
    ExitFile.write(e.message)
    raise e
  end
end
