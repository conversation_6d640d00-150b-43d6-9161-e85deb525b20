require_relative '../constants'
require_relative '../../common/push_to_zombie'

require 'android_toolkit'
require 'browserstack_logger'

module BrowserStack
  # This is a class that will handle anything related to the Fingerprint Sensor
  class FingerprintSensorHelper
    def initialize(device, os_version, device_model, logger)
      raise "Device cannot be empty" if device.nil? || device == ""

      @device = device
      @os_version = Gem::Version.new(os_version)
      @device_model = device_model
      @logger = logger

      AndroidToolkit::Log.logger = @logger
    end

    def check_fingerprint_sensor
      return if fingerprints_working?

      log(:info, "fingerprint_sensor_failure")
      zombie_push('android', 'fingerprint_sensor_failure', '', '', '', @device)
    end

    private

    def fingerprints_working?
      if @device_model == "Pixel 3" && @os_version == Gem::Version.new(10)
        adb_response = adb.getprop("vendor.fps.init.succeed")
        log(:info, adb_response)
        !adb_response.include?("false")
      else
        log(:info, "Skipping fingerprint sensor check")
        true
      end
    end

    def adb
      @adb ||= AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    end

    def log(level, msg)
      @logger.send(level.to_sym, "#{self.class} #{msg}")
    end
  end
end

if $PROGRAM_NAME == __FILE__
  begin
    raise StandardError, "Not enough arguments" if ARGV.size < 4

    command = ARGV[0].to_s.strip
    device_id = ARGV[1].to_s.strip
    os_version = ARGV[2].to_s.strip
    device_model = ARGV[3].to_s.strip

    logger_params = {}
    logger_params[:device] = device_id
    logger_params[:component] = 'FingerPrintSensorHelper_bash'
    logger = BrowserStack.init_logger(File.join(BrowserStack::LOGGING_DIR, "cleanup_#{device_id}.log"), logger_params)
    helper = BrowserStack::FingerprintSensorHelper.new(device_id, os_version, device_model, logger)
    helper.send(command)
  rescue StandardError => e
    puts e.message
  end
end
