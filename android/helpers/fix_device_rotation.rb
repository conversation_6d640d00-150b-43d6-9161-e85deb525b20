require 'android_toolkit'
require_relative '../constants'
require_relative '../lib/custom_exceptions'
require "#{BrowserStack::BS_DIR}/mobile/common/push_to_zombie.rb"
require "#{BrowserStack::BS_DIR}/mobile/common/helpers"
require 'browserstack_logger'

class FixRotationManager
  def initialize(device_id, logger, session_id = nil, product = nil, logger_params = {})
    @device = device_id
    @session = session_id
    @product = product
    @logger = logger
    @logger_params = logger_params
    @state_file_path = "#{BrowserStack::STATE_FILES_DIR}/fix_device_rotation_#{device_id}.txt"
    @eds = EDS.new({}, logger)

    @adb = AndroidToolkit::ADB.new(udid: device_id, path: BrowserStack::ADB)
    unless @adb.os_version >= Gem::Version.new(BrowserStack::FIX_ROTATION_MIN_OS)
      raise FixRotationNotSupportedError, BrowserStack::FIX_ROTATION_OS_NOT_SUPPORTED
    end

    AndroidToolkit::Log.logger = @logger
  end

  def command_to_fix_rotation(value)
    if @adb.os_version.to_s.to_i >= 12
      @adb.shell("wm fixed-to-user-rotation #{value}")
    elsif @adb.os_version.to_s.to_i >= 10
      @adb.shell("wm set-fix-to-user-rotation #{value}")
    end
  end

  def enable
    log :info, "Enable fix rotation request for #{@device}, #{@session}"
    type = 'enable'

    begin
      touch_state_file
      start = Time.now
      command_to_fix_rotation("enabled")
      time_taken = Time.now - start

      log :info, "Fix rotation enabled successfully"
      push(type, "success", "Fix rotation enabled successfully", time_taken)
      return true

    rescue StandardError => e
      log :error,  "Unknown error in enabling fix rotation: #{e.message} \n backtrace: #{e.backtrace}"
      push(type, "failed", "Unknown error in enabling fix rotation", 0, e.message)
    end

    false
  end

  def disable
    log :info, "Disable fix rotation request for #{@device}"
    type = 'disable'

    begin
      unless fix_rotation_state_file_exists?
        log :info, "Returning as rotation is not fixed"
        return true
      end

      start = Time.now
      command_to_fix_rotation("disabled")
      time_taken = Time.now - start

      log :info, "Fix rotation disabled successfully"
      remove_state_file
      push(type, 'success', "Fix rotation disabled successfully", time_taken)
      return true

    rescue StandardError => e
      log :error,  "Unknown error in disabling fix rotation: #{e.message}  \n backtrace: #{e.backtrace}"
      push(type, "failed", "Unknown error in disabling fix rotation", 0, e.message)
    end

    false
  end

  def touch_state_file
    FileUtils.touch @state_file_path
  end

  def remove_state_file
    FileUtils.rm @state_file_path
  end

  def push(type, result, msg, time, error = nil)
    data = {
      "result" => result,
      "time" => time
    }
    data["product"] = @product unless @product.nil?
    data["error"] = error unless error.nil?

    zombie_push('android', "fix-rotation-#{type}", msg, "", data, @device, @session) unless error.nil?
    eds_push if type == 'enable' && @product.to_s.downcase == "app_live"
  end

  def eds_push
    @eds.push_logs(EdsConstants::APP_LIVE_TEST_SESSIONS, {
      session_id: @session,
      product: {
        android_fix_device_orientation_toggled: true
      }
    }).join
  end

  def log(level, msg)
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, msg)
    else
      @logger.send(level.to_sym, msg, @logger_params)
    end
  end

  def fix_rotation_state_file_exists?
    File.exist?(@state_file_path)
  end
end
