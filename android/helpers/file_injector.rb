require_relative './http_utils'
require_relative './utils'
require_relative '../constants'
require 'fileutils'
require_relative '../lib/os_utils'
require 'shellwords'
require "#{BrowserStack::BS_DIR}/mobile/common/helpers"
require "#{BrowserStack::BS_DIR}/mobile/common/push_to_zombie"

class FileInjector
  class << self
    # should return boolean true on success else false with error
    def inject_file(device, params)
      filename = params[:file_name]
      url = params[:file_url]
      session_id = params[:session_id]

      eds_data = {
        event_name: "file-injection",
        device_id: device,
        session_id: session_id,
        product: params[:product],
        team: "device_features",
        media_id: params[:media_hashed_id],
        injection_status: true
      }

      raise "Filename or URL not provided" if filename.nil? || filename.empty? || url.nil? || url.empty?

      push_file = "[push-file]"
      custom_media = 'CUSTOM_FILES_DOWNLOAD'
      custom_files_dir = File.join(BrowserStack::INJECTION_MEDIA_DIR, device)

      BrowserStack.logger.info("#{push_file} device: #{device}, session: #{session_id}, file: #{filename}, url: #{url}")
      extension = File.extname(filename)
      base_file_name = File.basename(filename, extension)

      # set up folder
      FileUtils.mkdir_p(custom_files_dir)
      file_path = File.join(custom_files_dir, filename)

      # download file
      BrowserStack::HttpUtils.download_file(url, file_path, custom_media, { retry_count: 3, timeout: 60 })

      destination_file_path = "#{BrowserStack::DEVICE_INJECTION_MEDIA_BASE_DIR}/#{base_file_name}_" \
                              "#{(Time.now.to_f * 1000).to_i}#{extension}"

      if extension == BrowserStack::ZIP
        file_unzip_directory = File.join(custom_files_dir, base_file_name)
        FileUtils.mkdir_p(Shellwords.escape(file_unzip_directory))
        unzip_result, unzip_status =
          OSUtils.execute("timeout 30 unzip \"#{file_path}\" -d \"#{file_unzip_directory}\"", true)
        if unzip_status != 0
          raise "Failed to push file to device #{device}, Reason: Failed to unzip file at path: " \
                "#{file_path} to directory: #{file_unzip_directory}, result: #{unzip_result}, " \
                "status: #{unzip_status}"
        end

        file_path = file_unzip_directory
        destination_file_path = "#{BrowserStack::DEVICE_INJECTION_MEDIA_BASE_DIR}/#{base_file_name}_" \
                                "#{(Time.now.to_f * 1000).to_i}"
      end

      # make sure Download folder exists
      exit_code, output = adb_shell_cmd('mkdir -p', device, BrowserStack::DEVICE_INJECTION_MEDIA_BASE_DIR,
                                        { tag: custom_media })
      raise "Failed to mkdir on device, output: #{output}" if exit_code != 0

      # push file to android device
      exit_code, output = adb_file_cmd(
        'push', device, file_path, destination_file_path, { tag: custom_media, timeout: 120 }
      )
      if exit_code != 0
        BrowserStack.logger.info(
          "#{push_file} device: #{device}, session: #{session_id} cannot push to device exit code: #{exit_code}, " \
          "output: #{output}"
        )
        raise "Failed to push file/folder on device: #{output}"
      end

      BrowserStack.logger.info(
        "#{push_file} device: #{device}, session: #{session_id} successfully pushed file to device"
      )
      notify_pusher(BrowserStack::FILE_INJECTION_SUCCESS, params.transform_keys(&:to_s), nil, params[:product])
      true
    rescue StandardError => e
      # exception will only be raised for download failure
      BrowserStack.logger.error("#{push_file} session: #{session_id}, exception raised: #{e.backtrace.join('\n')}")
      eds_data[:injection_status] = true
      eds_data[:error] = e.message

      zombie_push('android', 'inject-file-failure', e.message, '', '', device, params[:session_id])
      notify_pusher(BrowserStack::FILE_INJECTION_FAILED, params.transform_keys(&:to_s), nil, params[:product])
      raise e
    ensure
      eds_obj = EDS.new({}, BrowserStack.logger)
      eds_obj.push_logs(EdsConstants::APP_LIVE_WEB_EVENTS, eds_data)

      if !filename.nil? && File.extname(filename) == BrowserStack::ZIP
        FileUtils.rm_rf(File.join(custom_files_dir, File.basename(filename, File.extname(filename))))
      end
    end
  end
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip
  case command
  when "inject_file"
    device_id = ARGV[1].to_s.strip
    device_injection_media_dir = File.join(BrowserStack::INJECTION_MEDIA_DIR, device_id)
    params = begin
      JSON.parse(File.read("#{device_injection_media_dir}/params.json"), symbolize_names: true)
    rescue StandardError
      {}
    end
    FileUtils.rm("#{device_injection_media_dir}/params.json")
    FileInjector.inject_file(device_id, params)
  end
end
