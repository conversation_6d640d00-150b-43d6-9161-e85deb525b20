require 'browserstack_logger'
require_relative '../constants'
require_relative '../../common/push_to_zombie'
require_relative './utils'
require_relative './socat_helper'
require 'json'

class LocalTestingChromeExtension
  LOCAL_CHROME_EXTENSION = "local_chrome_extension".freeze
  STATE_FILE_PATH_PREFIX = "#{BrowserStack::STATE_FILES_DIR}/#{LOCAL_CHROME_EXTENSION}".freeze

  def initialize(device_id, session_id)
    @device_id = device_id
    @session_id = session_id
    @eds_obj = EDS.new({}, BrowserStack.logger)
    @eds_event_type = "web_events"
    @product = "live"
  end

  def self.get_state_file_path(device)
    "#{STATE_FILE_PATH_PREFIX}_#{device}"
  end

  def state_file
    "#{STATE_FILE_PATH_PREFIX}_#{@device_id}"
  end

  def send_data_to_eds(event_name, event_json)
    params = {
      product: @product,
      os: "Android",
      team: "live",
      event_json: event_json,
      event_name: event_name
    }
    @eds_obj.push_logs(@eds_event_type, params).join
  end

  def local_testing_chrome_extension_session?
    File.exist?(state_file)
  end

  # MITMProxy python script writes following JSON data to the state file:
  # {"P1": <x>, "P25": <x>, "P50": <x>, "P75": <x>, "P99": <x>, "total_requests": <x>, "total_failed_requests": <x>}
  def request_stats
    json_data = File.read(state_file)
    BrowserStack.logger.info("LocalTestingChromeExtension :: request_stats read data #{json_data}")
    JSON.parse(json_data)
  rescue StandardError => e
    BrowserStack.logger.error("LocalTestingChromeExtension :: Error reading JSON file: #{e.message}")
    {}
  end

  def cleanup
    return unless local_testing_chrome_extension_session?

    start_time = Time.now
    BrowserStack.logger.info("LocalTestingChromeExtension :: Starting cleanup..")

    # Send SIGTERM to mitmproxy process to save instrumentation stats to the state file
    OSUtils.execute("ps aux | grep -v grep | grep mitmdump | grep #{@device_id} | awk '{print $2}' | xargs -n 1 -I {} timeout 5 kill {}", true) # rubocop:disable Layout/LineLength

    # Push request time distribution instrumentation to web_events
    stats = request_stats
    event_json = {
      session_id: @session_id,
      device_id: @device_id,
      request_response_time_P1: stats["P1"],
      request_response_time_P25: stats["P25"],
      request_response_time_P50: stats["P50"],
      request_response_time_P75: stats["P75"],
      request_response_time_P99: stats["P99"],
      total_requests: stats["total_requests"],
      total_failed_requests: stats["total_failed_requests"]
    }
    send_data_to_eds("LCE_requests_stats", event_json)
    BrowserStack.logger.info("LocalTestingChromeExtension :: Pushed data: #{event_json} to web_events")

    # Cleanup socat ports and state files
    SocatHelper.cleanup(@device_id)
    remove_state_file

    time_taken = Time.now - start_time
    BrowserStack.logger.info("LocalTestingChromeExtension clean took #{time_taken} seconds")
  rescue StandardError => e
    BrowserStack.logger.info("LocalTestingChromeExtension cleanup failed with error: #{e.message}, #{e.backtrace}")
  end

  def touch_state_file
    FileUtils.touch(state_file)
    true
  end

  def remove_state_file
    FileUtils.rm_rf(state_file)
    true
  end
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase
  device_id = ARGV[1].to_s.strip
  session_id = ARGV[2].to_s.strip
  helper = LocalTestingChromeExtension.new(device_id, session_id)
  helper.send(command)
end
