#!/usr/bin/ruby
require 'open3'
require 'browserstack_logger'
require 'json'

require_relative '../constants'
require_relative 'http_utils'
require_relative 'utils'

class MetadataExtraction
  include BrowserStack

  def initialize(app_path, device_id, session_id)
    @app_path = app_path
    @device_id = device_id
    @session_id = session_id

    @device = BrowserStack::AndroidDevice.new(@device_id, "metadata_extraction", BrowserStack.logger)

    @bundle_id = nil
    @min_sdk_version = nil
    @launcher_activity = nil

    @params = begin
      JSON.parse(File.read("#{STATE_FILES_DIR}/al_session_#{@session_id}"))
    rescue StandardError
      {}
    end

    @app_auth = @params["app_bs_auth"]
    @app_check_endpoint = @params["app_check_endpoint"]
    @app_filename = @params["app_filename"]
    @device_api_level = @params["device_api_level"]
  end

  def extract_validate_and_get_app_params
    result = { "success" => false, "app_data" => {} }
    return result unless validate_app_path

    begin
      extract_app_metadata

      return result unless validate_extracted_metadata

      verify_app
    rescue StandardError => e
      log(:error, "Error during extraction and validation for async metadata flow for #{@session_id}: #{e.message}")
      result
    end
  end

  def verify_app
    launcher_activity = begin
      @launcher_activity.detect { |la| la[1] == 0 || la[1] == 2 }.first
    rescue StandardError
      nil
    end
    result = { "success" => false,
               "app_data" => { "launcher_activity" => launcher_activity, "package" => @bundle_id } }

    if @min_sdk_version.to_f > @device_api_level.to_f
      log(
        :info,
        "session started on incompatible device. " \
        "Device version: #{@device_api_level} and " \
        "min app supported version: #{@min_sdk_version}"
      )

      notify_min_os_version
      return result
    end

    payload = {
      package: @bundle_id,
      os: "android",
      user_id: @params["user_id"],
      bs_auth: @app_auth
    }

    begin
      response = HttpUtils.send_post(@app_check_endpoint, payload, nil, true, { read_timeout: 61 })

      if response.code.to_i == 200
        response_body = JSON.parse(response.body)
        log(:info, "App Check API response body: #{response_body}")

        result["app_data"].merge!(response_body['app_params']) if response_body['app_params'].is_a?(Hash)
        result["success"] = true
      else
        notify_close_session
      end

    rescue StandardError => e
      log(:error, "unable to run app check via API: #{@session_id} with error: #{e.message}")
    end

    result
  end

  def validate_app_path
    if @app_path.nil? || @app_path.empty?
      log(:error, "App path is nil or empty")
      return false
    end

    true
  end

  def parse_app_manifest
    manifest = {}

    output, err, status = Open3.capture3(
      BrowserStack::AAPT2,
      'dump',
      'badging',
      @app_path
    )

    if !err.nil? && !err.strip.empty?
      BrowserStack.logger.error("AAPT2 badging error: #{err}")
      return manifest
    end

    output.each_line do |line|
      line.strip!
      kv = line.split(':')

      manifest[kv[0]] = kv[1] if kv.length == 2
    end

    manifest
  end

  def extract_app_metadata
    manifest = parse_app_manifest

    @min_sdk_version = begin
      manifest["sdkVersion"].split("\'")[1].to_f
    rescue StandardError
      nil
    end
    @bundle_id = begin
      manifest["package"].match(/name='[a-zA-Z0-9._-]*'/)[0].split("'")[1]
    rescue StandardError
      nil
    end

    launcher_activity_regex = /name='([a-zA-Z0-9._-]+)'/
    match = manifest["launchable-activity"].match(launcher_activity_regex)
    activity_name = match[1] if match
    @launcher_activity = [[activity_name, 0]]
  end

  def validate_extracted_metadata
    # empty launcher activity is allowed. In such case, user would manually need to launch the app
    log(:info, "Launcher activity is nil or empty") if @launcher_activity.nil? || @launcher_activity.empty?

    if @bundle_id.nil? || @bundle_id.empty?
      log(:error, "Bundle ID is nil or empty")
      return false
    end

    if @min_sdk_version.nil? || !@min_sdk_version.is_a?(Float)
      log(:error, "Minimum OS version is nil or empty")
      return false
    end

    log(:info, "Validation checks passed for async metadata flow on device: #{@device_id} for session: #{@session_id}")
    true
  end

  def log(level, msg)
    BrowserStack.logger.send(level.to_sym, msg, { subcomponent: self.class.to_s, device_id: @device_id })
  end

  def notify_min_os_version
    min_os_version = @device.os_version
    message = "in_compatible_os,#{min_os_version},#{@app_filename}"
    notify_pusher_app_live(message, @params)
  end

  def notify_close_session
    notify_pusher_app_live("close_session", @params)
  end
end

if __FILE__ == $PROGRAM_NAME
  app_path = ARGV[0]
  device_id = ARGV[1]
  session_id = ARGV[2]

  response = MetadataExtraction.new(app_path, device_id, session_id).extract_validate_and_get_app_params
  File.write("/tmp/app_live_async_metadata_response_#{device_id}", JSON.pretty_generate(response))
end
