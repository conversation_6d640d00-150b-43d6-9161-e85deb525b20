require_relative '../../common/push_to_zombie'
require_relative '../constants'
require_relative 'popup_helper'

require 'android_toolkit'
require 'browserstack_utils'
require 'fileutils'

class EnableLocation

  def initialize(device_obj, logger, logger_params = {})
    @device = device_obj
    @logger = logger
    @logger_params = logger_params
    @logger_params[:subcomponent] = self.class.to_s
  end

  def run
    BrowserStackUtils::LogCleanupFlow.log(@device.id, Time.now.to_i, 'Ensuring location enabled')

    # 2201117TI = Xiaomi Redmi Note 11
    # RMX3085 = Realme 8
    if @device.os_version.to_i >= 12 || ['2201117TI', 'RMX3085'].include?(@device.model)
      current_location_mode = adb.get_setting('secure', 'location_mode').to_s
      return if current_location_mode == '3'

      log(:info, "Updating location mode from #{current_location_mode} to 3")
      adb.shell('settings put secure location mode 3')
    else
      current_location_providers = adb.get_setting('secure', 'location_providers_allowed')
      log(:info, "Location providers: #{current_location_providers}")

      return if current_location_providers.include?('gps') &&
                current_location_providers.include?('network')

      # SM-G973F = Samsung Galaxy S10
      # SM-N970F = Samsung Galaxy Note 1
      if ['SM-G973F', 'SM-N970F'].include?(@device.model)
        enable_location_providers_looped
      else
        enable_location_providers
      end
    end

    popup_helper.run_main_automation(exit_on_failure: false)
    check_for_popups
  end

  def enable_location_providers
    # XT1068 = Motorola Moto G 2nd Gen
    if @device.os_version.to_i >= 6 || @device.model == "XT1068"
      adb.shell(
        "settings put secure location_providers_allowed +gps; "\
        "settings put secure location_providers_allowed +network"
      )
    else
      adb.shell("settings put secure location_providers_allowed gps,network")
    end

    sleep 1
  end

  def enable_location_providers_looped
    success = false
    5.times do
      location_providers = adb.get_setting('secure', 'location_providers_allowed')

      log(:info, "Location providers: #{location_providers}")
      if location_providers.include?('gps') && location_providers.include?('network')
        success = true
        break
      end

      cmd = 'settings put secure location_providers_allowed +gps; '\
            'settings put secure location_providers_allowed +network'

      adb.shell(cmd)
      sleep 1
    end

    return if success

    BrowserStackUtils::LogCleanupFlow.log(
      @device.id,
      Time.now.to_i,
      'Sending S10/Note10-Location Disabled Stats to Zombie'
    )

    zombie_key_value(
      platform: 'android',
      kind: 'location-not-enabled',
      device: @device.id
    )
  end

  def check_for_popups
    current_focus = adb.shell('dumpsys window | grep mCurrentFocus')
    case current_focus
    when /StatusBar/
      log(:info, 'All popups handled')
    when /NexusLauncherActivity/
      log(:info, 'At Nexus Launcher activity')
    else
      log(:info, 'Popups not handled - logging UIAutomator dump')

      adb.shell('uiautomator dump /data/local/tmp/dump.xml')
      adb.pull("/data/local/tmp/dump.xml", "/tmp/dump_#{@device.id}.xml")

      log(:info, File.read("/tmp/dump_#{@device.id}.xml"))

      FileUtils.rm_rf("/tmp/dump_#{@device.id}.xml")
      adb.shell('rm -rf /data/local/tmp/dump.xml')
    end
  end

  private

  def popup_helper
    @popup_helper ||= BrowserStack::PopupHelper.new(
      device_id: @device.id,
      os_version: Gem::Version.new(@device.os_version)
    )
  end

  def adb
    @adb ||= AndroidToolkit::ADB.new(udid: @device.id, path: BrowserStack::ADB)
  end

  def log(level, msg)
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, msg)
    else
      @logger.send(level.to_sym, msg, @logger_params)
    end
  end
end
