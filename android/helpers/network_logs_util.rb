require 'socket'
require 'base64'
require 'digest/sha1'
require 'fileutils'
require 'json'
require_relative '../proxies/proxy'
require_relative './utils'

class NetworkLogsUtil
  include Proxy

  MITM_WS_READY = 'MITM_WS_READINESS_CHECK_PASS'.freeze
  MITM_WS_FAILED = 'MITM_WS_READINESS_CHECK_FAIL'.freeze
  SSL_PINNED_APP = 'APP_HAS_DISABLE_SSL_PINNED'.freeze
  APP_PATCHED_SUCCESSFUL = 'APP_PATCHED_SUCCESSFUL'.freeze
  APP_PATCHED_FAILED = 'APP_PATCHED_FAILED'.freeze
  APP_PATCHING_STARTED = 'APP_PATCHING_STARTED'.freeze

  def initialize(device, devices_json, params, eds_obj)
    @device = device
    @devices_json = devices_json
    @params = params
    @eds_obj = eds_obj
    @event_hash = {}
    @app_process_data = {}
    @network_logs_event_hash = { rooted: bsrun_device?(@device), app_hashed_id: @params[:app_hashed_id] }
    if @params[:app_hashed_id] == "play_store"
      @params[:package] = PLAY_STORE_PACKAGE_NAME
      @params[:launcher_activity] = PLAY_STORE_LAUNCHER_ACTIVITY_NAME
    end
    begin
      @state_session_data = JSON.parse(File.read("#{STATE_FILES_DIR}/session_#{@device}"))
    rescue StandardError
      @state_session_data = {}
    end

    # Store all the event for network logs flow and will store in EDS at the end of the session.
    @network_logs_event_hash_file = "#{STATE_FILES_DIR}/al_network_logs_hash_"\
            "#{@params['app_live_session_id']}"
    FileUtils.touch(@network_logs_event_hash_file)
    @params['hosts'] = []
    set_local_params if @params['tunnelPresent'].to_s == 'true'
  end

  def run
    # Setup proxy and patching app and installing app are independent to each others.
    proxy_setup_pid = fork do
      safe_execute(:set_up_mitm)
      safe_execute(
        :check_mitm_ws_readiness, @params[:mitm_ws_readiness_timeout].to_i, @params[:mitm_ws_readiness_interval].to_i
      )
      safe_execute(:persist_network_logs_hash, @network_logs_event_hash)
    end
    Process.detach(proxy_setup_pid)

    app_deployment_pid = fork do
      safe_execute(:patch_app_if_required)
      safe_execute(:handle_app_deployment)
      safe_execute(:persist_network_logs_hash, @network_logs_event_hash)
    end
    Process.detach(app_deployment_pid)

    safe_execute(:enable_transparent_mode)

  end

  def self.perform_clean_up(eds_obj, session_id)
    network_logs_hash = {}
    begin
      network_logs_event_hash_file = "#{STATE_FILES_DIR}/al_network_logs_hash_#{session_id}"
      if File.exist?(network_logs_event_hash_file)
        File.open(network_logs_event_hash_file, 'r') do |file|
          network_logs_hash = JSON.parse(file.read)
        end
        network_logs_hash.merge!(
          {
            event_name: 'network_logs_2_0_json_event',
            session_id: session_id,
            genre: 'app_live_testing',
            product: 'app_live',
            team: 'app_live',
            platform: 'android'
          }
        )
        FileUtils.rm_f(network_logs_event_hash_file)
      end
    rescue StandardError => e
      network_logs_hash[:error] = "NETWORK_LOGS_HASH_COLLECTION_FAILED"
    ensure
      eds_obj.push_logs(EdsConstants::APP_LIVE_WEB_EVENTS, network_logs_hash)
    end
  end

  private

  # Add/remove any local related params here.
  # Assuming session file has all the data since during session start we pass all the required params.
  def set_local_params
    @params['hosts'] ||= @state_session_data['hosts']
    @params['proxy_type'] ||= @state_session_data['proxy_type']
    @params['tunnelPorts'] ||= @state_session_data['tunnelPorts']
    @params['tunnelHostServer'] ||= @state_session_data['tunnelHostServer']
    @params['force_local'] ||= @state_session_data['force_local']
    if @state_session_data['startElement'] == 'local'
      @params['resolve_common_hosts_on_device'] ||= @state_session_data['resolve_common_hosts_on_device']
    end
  end

  def touch_frida_files_in_the_device
    @params[:app_data] ||= {}

    AppInjection.touch_app_injection_state_file(@device, ENABLE_NETWORK_LOGS_FRIDA)

    if @params[:app_data][:biometric_auth].to_s == 'true' &&
      @params[:biometric_new_failure_flow_restricted].to_s == 'false'
      AppInjection.touch_app_injection_state_file(@device, ENABLE_MULTIPLE_BIOMETRIC_FAILURES)
    end

    if @params[:app_data][:biometric_auth].to_s == 'true'
      AppInjection.touch_app_injection_state_file(@device, ENABLE_BIOMETRIC_FRIDA)
    end

    if @params[:is_camera_toggled].to_s == 'true'
      AppInjection.touch_app_injection_state_file(@device, ENABLE_CAMERA_FRIDA)
    end

    if @params[:app_data][:camera_preview].to_s == 'true'
      AppInjection.touch_app_injection_state_file(@device, ENABLE_CAMERA_PREVIEW_LAYER_FILE)
    end
    if @params[:app_data][:video_injection].to_s == 'true'
      AppInjection.touch_app_injection_state_file(@device, ENABLE_VIDEO_INJECTION_FILE)
    end
  rescue StandardError => e
    BrowserStack.logger.error("#{self.class}##{__method__} Exception: #{e.message}, stacktrace : #{e.backtrace}")
  end

  def safe_execute(method, *args)
    start_time = Time.now
    BrowserStack.logger.info "#{self.class}##{__method__} calling #{method}"
    send(method, *args)
  rescue StandardError => e
    BrowserStack.logger.error("#{self.class}##{method} Exception: #{e.message}, stacktrace : #{e.backtrace}")
  ensure
    @network_logs_event_hash["#{method}_time_taken"] = Time.now - start_time
  end

  def set_up_mitm
    @network_logs_event_hash[:tunnel_setup_start] = Time.now
    @params['tunnel_setup_flow'] = 'network_config_change'
    port = @devices_json[@device]['port']
    @params[:proxy_port] = calculate_privoxy_port(port)
    @params['network_logs_port'] = @devices_json[@device]['network_logs_port']
    tunnel_setup(@params, [@devices_json[@device]['mobile_ip'], calc_usb_tunnel_ip(port)])
  end

  def enable_transparent_mode
    return unless @params[:enable_transparent_mode].to_s == 'true'

    device_obj = BrowserStack::AndroidDevice.new(@device, "Server.rb - /app_start", BrowserStack.logger)
    return unless device_obj.uses_bstack_internet_app?

    BStackReverseTetherController.new(device_obj, BrowserStack.logger).toggle_transparent_mode(stop: false)
  end

  def patch_app_if_required

    # Patch not needed for
    # 1. If the user has accepted the user trust certificates in the app.
    # 2. If the device is rooted.
    @app_process_data[:patch_needed] = ( @params[:app_hashed_id] != "play_store") && !(
      @params[:user_certs_accepted]&.to_s == 'true' || @network_logs_event_hash[:rooted]
    )

    notify_pusher(SSL_PINNED_APP) if @params[:ssl_pinned_app]&.to_s == 'true'
    if @app_process_data[:patch_needed].to_s == 'true' && @params[:new_app_download_url].nil?
      notify_pusher(APP_PATCHING_STARTED)
      @app_process_data[:patched_by] = 'apk-mitm'
    end

    # Use frida only if
    # 1. App patching is needed.
    # 2. Frida patching is allowed.
    # 3. New app download url is not empty. (This is the case when is already patched)
    # 4. App patch queue length is not exceeded.
    # 5. App is not already patched via apk-mitm.
    should_use_frida_patch = @app_process_data[:patch_needed] &&
                 (@params[:frida_patch_allowed].nil? || @params[:frida_patch_allowed].to_s == 'true') &&
                 @params[:new_app_download_url].nil? &&
                 (@params[:app_patch_queue_length_exceeded].nil? ||
                  @params[:app_patch_queue_length_exceeded].to_s == 'false') &&
                 !File.exist?("/tmp/app-#{@device}-patched.apk")

    response = {}

    # Patch needed and app is already patched. Use patched app.
    if @params[:new_app_download_url].to_s.strip != ''
      @app_process_data.merge!(
        frida_app_patched_status: true,
        app_download_url: @params[:new_app_download_url], app_is_already_patched: true
      )
      touch_frida_files_in_the_device
    elsif should_use_frida_patch
      BrowserStack.logger.info("#{self.class}##{__method__} Triggering frida app patching")
      touch_frida_files_in_the_device
      @app_process_data[:patched_by] = 'frida'
      response = BrowserStack::AppActions.new(
        device_id: @device
      ).send_instrument_request_and_get_url(@params[:instrumentation_hash])
      # If fails, No action item. Let it be handle via apk-mitm patching.
      @app_process_data.merge!(
        frida_app_patched_status: response[:success] && response[:url].to_s.strip != '',
        app_download_url: response[:url]
      )
      BrowserStack.logger.info("#{self.class}##{__method__} AppPatcher response is #{response.inspect}")

    end

    notify_pusher('APP_PATCHED_QUEUE_LENGTH_EXCEEDED') if @params[:app_patch_queue_length_exceeded].to_s == 'true'
    if @app_process_data[:patch_needed]
      @app_process_data[:final_patching_status] = ensure_app_is_patched
      notify_pusher(@app_process_data[:final_patching_status] ? APP_PATCHED_SUCCESSFUL : APP_PATCHED_FAILED)
    end

    @app_process_data[:app_patch_queue_length_exceeded] = @params[:app_patch_queue_length_exceeded].to_s == 'true'
    BrowserStack.logger.info("#{self.class}##{__method__} App process data is #{@app_process_data.inspect}")
    @app_process_data[:app_data] = @params[:app_data] || {}
    @network_logs_event_hash.merge!(@app_process_data)
  end

  def check_mitm_ws_readiness(timeout = 30, check_interval = 1)
    start_time = Time.now
    event_hash = {
      mitm_ws_readiness_time_taken: 0, mitm_ws_readiness_timeout_value: timeout,
      mitm_ws_readiness_interval: check_interval , mitm_ws_readiness_status: false
    }

    while Time.now - start_time < timeout
      break if (event_hash[:mitm_ws_readiness_status] = mitm_ready_for_ws?('localhost', @params['network_logs_port']))

      sleep check_interval
      event_hash[:mitm_ws_readiness_time_taken] += check_interval
    end

    notify_pusher(event_hash[:mitm_ws_readiness_status] ? MITM_WS_READY : MITM_WS_FAILED)
    persist_network_logs_hash(event_hash)
    @network_logs_event_hash[:tunnel_setup_time_taken] = Time.now - @network_logs_event_hash[:tunnel_setup_start]
    @network_logs_event_hash.delete(:tunnel_setup_start)
  end

  def mitm_ready_for_ws?(host, port , path = '/updates', timeout = 1)
    key = Base64.strict_encode64(Random.new.bytes(16))
    request = <<~HTTP
      GET #{path} HTTP/1.1
      Host: #{host}:#{port}
      Upgrade: websocket
      Connection: Upgrade
      Sec-WebSocket-Key: #{key}
      Sec-WebSocket-Version: 13

    HTTP

    socket = nil
    begin
      socket = Socket.tcp(host, port, connect_timeout: timeout)
      socket.write(request)
      response = socket.readpartial(1024)

      response.include?("101 Switching Protocols")
    rescue Errno::ECONNREFUSED, Errno::ETIMEDOUT
      false
    ensure
      socket&.close
    end
  end

  def handle_app_deployment
    # Not required to uninstall the app if app patching is failed.
    # Bad user experience if the app is disappeared from the device.
    return unless @app_process_data[:final_patching_status]

    app_download_url = @app_process_data[:app_download_url] || @params[:new_app_download_url]
    proxy_port = calculate_privoxy_port(@devices_json[@device]['port'])
    app_download_timeout = @params[:app_download_timeout] || 90
    cmd = "bash #{APP_LIVE_SCRIPT} handle_app_launch_for_network_logs " \
          "#{@params[:app_hashed_id]} #{@device} #{@params[:launcher_activity]} " \
          "#{@params[:package]} #{@params['app_live_session_id']} #{proxy_port} " \
          "#{@app_process_data[:patch_needed]} #{@params[:network_logs_2_0_enabled]} " \
          "#{@app_process_data[:frida_app_patched_status]} \"#{app_download_url}\" " \
          "#{app_download_timeout} #{@network_logs_event_hash[:apk_mitm_patch_status]}"
    output = `#{cmd}`
    BrowserStack.logger.info("handle_app_deployment script output: #{output}")
  end

  def persist_network_logs_hash(hash)
    File.open(@network_logs_event_hash_file, 'r+') do |file|
      file.flock(File::LOCK_EX)
      begin
        existing_data = JSON.parse(file.read)
      rescue StandardError => e
        existing_data = {}
      end
      merged_data = existing_data.merge(hash)
      file.rewind
      file.truncate(0)
      file.write(JSON.pretty_generate(merged_data))
      file.flock(File::LOCK_UN)
    end
  end

  def ensure_app_is_patched
    return true if @app_process_data[:patch_needed] && @app_process_data[:frida_app_patched_status].to_s == 'true'

    # Wait for APK MITM patching status.
    patching_tracking_file = "/tmp/patching_app_#{@device}"
    users_patched_app = "/tmp/app-#{@device}-patched.apk"
    elapsed_time = 0
    @params[:apk_mitm_patch_timeout] = @params[:apk_mitm_patch_timeout].to_i
    @params[:apk_mitm_patch_retry_interval] = @params[:apk_mitm_patch_retry_interval].to_i

    notify_pusher('APK_MITM_PATCHING_RETRY_STARTED')
    while File.exist?(patching_tracking_file) && elapsed_time < @params[:apk_mitm_patch_timeout]
      BrowserStack.logger.info "#{__method__} Waiting for patched app to be available..."
      sleep @params[:apk_mitm_patch_retry_interval]
      elapsed_time += @params[:apk_mitm_patch_retry_interval]
    end
    notify_pusher('APK_MITM_PATCHING_RETRY_END')
    @network_logs_event_hash[:apk_mitm_patch_status] = File.exist?(users_patched_app)
    @network_logs_event_hash[:apk_mitm_patch_waiting_time] = elapsed_time
    BrowserStack.logger.info "#{__method__} APKMITM patching status #{@network_logs_event_hash[:apk_mitm_patch_status]}"
    @network_logs_event_hash[:apk_mitm_patch_status]
  end

  def notify_pusher(message, extra_hash = {})
    BrowserStack.logger.info("#{__method__} Notifying app live pusher message : #{message}")
    notify_pusher_app_live(
      message,
      {
        "pusher_auth" => @state_session_data["pusher_auth"],
        "pusher_channel" => @state_session_data["pusher_channel"],
        "session_id" => @params["app_live_session_id"],
        "pusher_url" => @state_session_data["pusher_url"]
      }.merge!(@params),
      extra_hash
    )
  end

end
