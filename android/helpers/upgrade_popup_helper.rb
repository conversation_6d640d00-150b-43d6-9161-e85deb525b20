require 'android_toolkit'
require 'logger'

class DismissUpgradePopups

  def initialize(device, logger, logger_params = {})
    raise "Device cannot be empty" if device.nil? || device == ""

    @device = device
    @logger = logger
    @logger_params = logger_params
    AndroidToolkit::Log.logger = @logger
    @adb = AndroidToolkit::ADB.new(udid: @device)
  end

  def dismiss
    class_name = "DismissUpgradePopup"
    start_time = Time.now
    log :info, "Starting UI Automation to dismiss upgrade popup. Start Time: #{start_time}"
    begin
      @adb.shell("am instrument -w -r -e debug false -e class 'com.browserstack.uiautomation.#{class_name}' "\
                 "com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner", timeout: 35)
    rescue StandardError => e
      log :info, "Error while dismissing upgrade popup: #{e.message} - #{e.backtrace}"
    end
    log :info, "Upgrade popup dismissed. Duration: #{Time.now - start_time}"
  end

  private

  def log(level, msg)
    @logger.send(level.to_sym, "#{self.class} #{msg}")
  end
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase.to_sym
  device_id = ARGV[1].to_s.strip
  helper = DismissUpgradePopups.new(device_id, Logger.new($stdout))
  helper.send(command)
end