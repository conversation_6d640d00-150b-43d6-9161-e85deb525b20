require_relative '../constants'

require 'browserstack_logger'
require 'json'

# This class contains methods to match a device udid with a usb bus/port and associated information.
#
# --- Overview --
# When a device is first connected to usb, device check records the bus/port info with cmd: adb devices -l
# This data persists in the config even if the device later goes off adb (e.g. into download mode)
# The bus/port data can be used to match a device's udid to its usb device file (e.g. /dev/bus/usb/001/006),
# This file name can be used to reveal usb related information with the command:
# udevadm info --name=/dev/bus/usb/001/006
#
# --- Dev Notes ---
# * The "udevadm info" cmd also lists the property ID_SERIAL_SHORT, which is the device udid. However,
# this property is missing on devices in download mode.
# ---
module UsbInfo
  extend self

  def write_udid_usb_info_json
    unless File.exist?(BrowserStack::CONFIG_JSON_FILE)
      log(:info, 'Not updating usb info because config json does not exist')
      return
    end

    log(:info, 'Updating usb info json')
    File.open(BrowserStack::USB_INFO_FILE, "w") do |f|
      f.write(udid_usb_info.to_json)
    end
  end

  # Iterates through udids listed in config.json
  # For each device udid, it uses the bus/port info recorded during device check
  # to match it with a device listed in lsusb. Returns:
  # { "<udid 1>" => {<usb info>}, "<udid 2>" => {<usb info}, ... }
  def udid_usb_info
    devices_config = JSON.parse(File.read(BrowserStack::CONFIG_JSON_FILE))['devices']

    usb_devices_info = connected_devices_usb_details
    output = {}
    devices_config.each do |udid, config|
      unless config.key?('adb_info')
        log(:info, "Skipping #{udid} as it does not have adb info in config")
        next
      end

      # Bus/port number info is added to config in device check (adb_device_info_updater)
      # It is included in the output of the command: adb devices -l
      device_bus_port = config['adb_info']['usb']

      output[udid] = usb_devices_info[device_bus_port] if usb_devices_info.key?(device_bus_port)
    end

    output
  end

  # Returns hash of bus/device number mapping like:
  # { "<bus 001>" => ["<device 013>", "<device 127>" ...], "<bus 002>" => [...] }
  def bus_device_map
    buses = `ls /dev/bus/usb/`.split("\n")

    bus_device_hash = {}
    buses.each do |bus|
      devices = `ls /dev/bus/usb/#{bus}/`.split("\n")
      bus_device_hash[bus] = devices
    end

    bus_device_hash
  end

  # Iterates through bus and device numbers
  # For each usb device, it reads the device file and parses the output
  # Returns hash of hashes like:
  # { "2-5" => {<device 1 usb info>}, 2-7.2 => {<device 2 usb info>}, ...]}
  def connected_devices_usb_details
    output = {}

    bus_device_map.each do |bus, device_numbers|
      device_numbers.each do |device_number|
        device_file = device_file_path(bus, device_number)
        unless File.exist?(device_file)
          log(:info, "USB device file not found: #{device_file}")
          next
        end

        raw = `udevadm info --name=#{device_file}`
        usb_info_hash = parse_udevadm_output(raw)
        usb_info_hash = add_usb_metadata(usb_info_hash)
        output[usb_info_hash[:bus_port]] = usb_info_hash
      end
    end

    output
  end

  # Parses output of udevadm command like:
  # udevadm info --name=/dev/bus/usb/001/006
  #
  # Returns hash like:
  # {
  #   bus_port: "2-7.2",
  #   device_number: "013"
  #   vendor: "SAMSUNG"
  #   vendor_id: "04e8"
  #   vendor_from_database: "Samsung Electronics Co., Ltd"
  #   model: "SAMSUNG_USB"
  #   model_id: "685d"
  #   model_from_database: "GT-I9100 Phone [Galaxy S II] (Download mode)"
  # }
  def parse_udevadm_output(raw)
    raw = raw.split("\n")
    output = {}

    output[:bus_port] = raw.grep(/DEVPATH/)[0].split("=")[1].split("/")[-1]
    output[:device_number] = raw.grep(/DEVNAME/)[0].split("=")[1].split("/")[-1]
    output[:vendor] = raw.grep(/ID_VENDOR/)[0].split("=")[1]
    output[:vendor_id] = raw.grep(/ID_VENDOR_ID/)[0].split("=")[1]

    if raw.grep(/ID_VENDOR_FROM_DATABASE/).any?
      output[:vendor_from_database] = raw.grep(/ID_VENDOR_FROM_DATABASE/)[0].split("=")[1]
    end

    output[:model] = raw.grep(/ID_MODEL/)[0].split("=")[1]
    output[:model_id] = raw.grep(/ID_MODEL_ID/)[0].split("=")[1]

    if raw.grep(/ID_MODEL_FROM_DATABASE/).any?
      output[:model_from_database] = raw.grep(/ID_MODEL_FROM_DATABASE/)[0].split("=")[1]
    end

    output
  end

  # /dev/bus/usb/<bus>/<device>
  def device_file_path(bus, device_number)
    padded_bus = bus.to_s.rjust(3, "0") # convert "1" to "001"
    padded_dev = device_number.to_s.rjust(3, "0") # convert "32" to "032"

    "/dev/bus/usb/#{padded_bus}/#{padded_dev}"
  end

  # Adding more USB metadata, which can be read from the files at:
  # /sys/bus/usb/devices/bus-port/(version|speed|idProduct...)
  def add_usb_metadata(usb_info_hash)
    bus_port = usb_info_hash[:bus_port]

    metadata_values_files = {
      usb_version: 'version',
      usb_speed: 'speed',
      interface_count: 'bNumConfigurations',
      product_id: 'idProduct',
      max_power: 'bMaxPower'
    }

    metadata_values_files.each do |key, value|
      file = "/sys/bus/usb/devices/#{bus_port}/#{value}"
      next unless File.exist?(file)

      usb_info_hash[key] = File.read(file).strip
    end

    usb_info_hash
  end

  def log(level, msg)
    BrowserStack.logger.send(level.to_sym, msg, { subcomponent: 'UsbInfo' })
  end
end
