require 'android_toolkit'
require 'date'
require 'logger'

require_relative '../constants'
require_relative '../exit_file'
require_relative '../lib/custom_exceptions'
require_relative '../models/android_device'
require_relative '../version_managers/device_owner_manager'

class SystemUpdateBlockHelper
  def self.run_from_bash
    device_id = ARGV[0].to_s.strip
    command = ARGV[1].to_s.strip
    args = ARGV[2..]

    logger = Logger.new($stdout)

    system_update_block_manager = SystemUpdateBlockHelper.new(device_id, logger)
    system_update_block_manager.send(command, *args)
  rescue StandardError => e
    ExitFile.write(e.message)
    raise e
  end

  def initialize(device, logger, logger_params = {})
    device_obj = BrowserStack::AndroidDevice.new(device, "SystemUpdateBlockHelper", logger)
    @supports_device_owner = device_obj.supports_device_owner?
    @device = device
    @os_version = device_obj.os_version.to_f
    @logger = logger
    @logger_params = logger_params

    @adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    AndroidToolkit::Log.logger = @logger
  end

  def block_updates
    raise DeviceOwnerNotSupportedError unless @supports_device_owner

    return unless system_updates_blocked_file_outdated?

    FileUtils.rm_f system_updates_blocked_file
    # only block updates if we actually need to block based on last update set time
    if needs_freeze_period_reset?
      unblock_updates
      @adb.shell('dpm clear-freeze-period-record')
      device_owner_manager.block_system_updates
    end
    FileUtils.touch system_updates_blocked_file
  end

  def unblock_updates
    raise DeviceOwnerNotSupportedError unless @supports_device_owner

    # removes old rules to block OTA updates
    device_owner_manager.remove_blocked_system_update_policy
    FileUtils.rm_f system_updates_blocked_file
  end

  def needs_freeze_period_reset?
    return false unless @supports_device_owner

    # returns boolean indicating if updates need to be blocked again
    period = device_owner_manager.system_updates_freeze_period.match(/Freeze period: (.*)/)[1]
    freeze_start = Date.parse(period.split("-")[0])
    current_date = DateTime.now.to_date
    difference = (current_date - freeze_start).ceil
    # next line hack is because device owner output only returns month and year
    # example: if freeze start is 10 Dec, current date is 10 Jan, and year is 2022 (freeze was set in 2021)
    # ruby will calculate 10 January 2022 - 10 December 2022 and give output as -334, adding 365 to it gives 31
    difference += 365 if difference < 0
    @logger.info "Days passed since last freeze was set: #{difference}"
    difference > 53 # reset every 53 days, keeping 1 week safe distance from potential freeze period expiry
  rescue DeviceOwnerError
    true
  end

  def system_updates_blocked_file_outdated?
    return false unless @supports_device_owner

    return false if @os_version < 9 # command to block requires Android P and above

    return true unless File.exist?(system_updates_blocked_file)

    # check if file is older than 5 days
    days = 5
    File.mtime(system_updates_blocked_file) < (Time.now - (60 * 60 * 24 * days))
  end

  private

  def device_owner_manager
    @device_owner_manager ||= DeviceOwnerManager.new(@device, @logger, @logger_params)
  end

  def system_updates_blocked_file
    "#{BrowserStack::STATE_FILES_DIR}/system_updates_blocked_#{@device}"
  end
end

SystemUpdateBlockHelper.run_from_bash if $PROGRAM_NAME == __FILE__
