require_relative "device_sim_helper"
require_relative "../models/device_state"
require 'json'

class DedicatedDeviceSIMHelper < DeviceSIMHelper

  def self.device_state(device)
    DeviceState.new(device)
  end

  def self.get_sim_data_from_statefile(device)
    sim_details = JSON.parse(device_state(device).read_sim_info_file)

    BrowserStack.logger.info "Fetched sim data from statefile"
    sim_details
  rescue StandardError => e
    BrowserStack.logger.error "Failed to get existing dedicated sim details, error: #{e.message}"
    raise e
  end

  def self.get_sim_data_from_device(device)
    adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)

    device_data = []

    # Get the carrier name for each SIM slot, keep empty array if both sims don't show up
    sim_carriers = adb.getprop("gsm.sim.operator.alpha").strip.split(",", -1)

    sim_carriers.each_with_index do |carrier, index|
      sim_slot = index + 1
      carrier_index = index
      phone_number = begin
        fetch_sim_prop(device, "msisdn-#{carrier_index}", true, 12, sim_slot)
      rescue StandardError
        next # Skip if phone number is not found
      end

      detail = {
        'sim_slot' => sim_slot,
        'carrier' => carrier,
        'phone_number' => phone_number,
        'imei' => fetch_sim_prop(device, "imei-#{carrier_index}", true, 4, sim_slot),
        'sim_id' => fetch_sim_prop(device, "iccid-#{carrier_index}", true, 12, sim_slot)
      }

      device_data << detail
    end

    BrowserStack.logger.info("Fetched sim data from device")
    device_data
  rescue StandardError => e
    BrowserStack.logger.error "Failed to get sim data from device, error: #{e.message}"
    []
  end

  def self.compare_sim_details(device)
    device_detail = get_sim_data_from_device(device)
    state_detail = get_sim_data_from_statefile(device)

    comparison = {}

    device_detail.each do |device_sim|
      state_detail.each do |state_sim|
        next if state_sim['sim_slot'] != device_sim['sim_slot']

        ['phone_number', 'imei', 'sim_id'].each do |field|
          # Normalize phone numbers for comparison to avoid issues with formatting
          if field == 'phone_number'
            state_sim[field] = state_sim[field].gsub(/[^0-9]/, '')
            device_sim[field] = device_sim[field].gsub(/[^0-9]/, '')
          end

          next if state_sim[field] == device_sim[field]

          # Skip if the field is nil or empty
          next if state_sim[field].nil? || device_sim[field].nil?
          next if state_sim[field] == "" || device_sim[field] == ""

          comparison["sim#{state_sim['sim_slot']}"] ||= {}
          comparison["sim#{state_sim['sim_slot']}"][field] = {
            'railsDB' => state_sim[field],
            'device' => device_sim[field]
          }
        end
      end
    end

    BrowserStack.logger.info "Finished comparing sim details with actual device info"
    comparison
  rescue StandardError => e
    BrowserStack.logger.error "Failed to compare sim details, error: #{e.message}"
    {}
  end

  def self.sim_info(device)

    return [] unless DedicatedDeviceSIMHelper.sim?(device)

    sim_details = get_sim_data_from_statefile(device)

    sim_details.each do |sim_detail|
      last_updated_info = check_update_signal_strength(device, sim_detail['carrier'], sim_detail['sim_slot'])
      sim_detail['signal_strength'] = last_updated_info[:signal_strength] unless last_updated_info.nil?
      sim_detail['last_updated'] = last_updated_info[:last_updated_signal_strength] unless last_updated_info.nil?
    end

    sim_details
  rescue StandardError => e
    BrowserStack.logger.error "Failed to fetch dedicated device sim info, error: #{e.message}"
    []
  end
end
