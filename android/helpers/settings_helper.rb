require_relative '../constants'
require_relative "./settings_helper"
require_relative "./timezone_helper"

require 'android_toolkit'
require 'browserstack_logger'

module BrowserStack
  # This is a class that will handle settings related checks
  class Settings<PERSON><PERSON>per
    def initialize(device, logger, session_id = nil, product = nil)
      raise "Device cannot be empty" if device.nil? || device == ""

      @device = device
      @logger = logger
      @session_id = session_id
      @product = product
      @adb = AndroidToolkit::ADB.new(udid: @device)
      AndroidToolkit::Log.logger = @logger
    end

    def apply_setting(setting, value)
      log(:info, "Applying setting: #{setting} and value: #{value} for device: #{@device} and session: #{@session_id}")
      case setting
      when 'device_passcode'
        update_passcode_state(value)
      when 'fix_device_rotation'
        update_rotation_state(value)
      when 'disable_sensors'
        update_sensors_state(value)
      when 'set_time_format'
        update_time_format(value)
      when 'always_finish_activities'
        update_always_finish_activities(value)
      when 'timezone'
        update_timezone(value)
      else
        log(:warn,
            "Failed to set setting: #{setting} and value: #{value} for device: #{@device} and session: #{@session_id}, \
            error: Invalid request sent")
        raise SettingsException.new("Invalid setting: #{setting} request sent", 400)
      end
      log(:info, "Applied setting: #{setting} and value: #{value} for device: #{@device} and session: #{@session_id}")
      true
    rescue StandardError => e
      log(:error,
          "Failed to set setting: #{setting} and value: #{value} for device: #{@device} and session: #{@session_id}, \
          error: #{e.message}, #{e.backtrace.join("\n")}")
      raise e
    end

    def disable_vibrations
      BrowserStack.logger.info("disable_vibrations")
      @adb.put_setting('system', 'SEM_VIBRATION_NOTIFICATION_INTENSITY', '0') # notification
      @adb.put_setting('system', 'media_vibration_intensity', '0') # media
      @adb.put_setting('system', 'VIB_FEEDBACK_MAGNITUDE', '0') # system
      @adb.put_setting('system', 'VIB_RECVCALL_MAGNITUDE', '0') # call
    end

    def disable_sounds
      BrowserStack.logger.info("disable_sounds")
      @adb.shell("cmd media_session volume --stream 1 --set 0") # system
      @adb.shell("cmd media_session volume --stream 2 --set 1") # ringtone
      @adb.shell("cmd media_session volume --stream 3 --set 0") # media
      @adb.shell("cmd media_session volume --stream 5 --set 0") # notification
    end

    def clear_nexus_launcher
      BrowserStack.logger.info("clear_nexus_launcher")
      @adb.shell("pm clear com.google.android.apps.nexuslauncher") # clear the nexus-launcher state
    end

    def disable_taskbar
      BrowserStack.logger.info("disabling taskbar")
      @adb.put_setting('global', 'task_bar', '0')
    end

    def disable_spen
      @adb.put_setting('system', 'air_cmd_use_minimized', '0')
    end

    def disable_notification_channel_warnings
      @adb.put_setting('global', 'show_notification_channel_warnings', '0')
    end

    def disable_nearby_device_scanning
      @adb.put_setting('system', 'nearby_scanning_enabled', '0')
    end

    def disable_double_tap_to_sleep
      @adb.put_setting('system', 'double_tap_to_sleep', '0')
    end

    def disable_touch_disable_mode
      @adb.put_setting('system', 'touch_disable_mode', '0')
    end

    def restart_location
      # Toggles the location twice - turns off and then on
      @adb.put_setting('secure', 'location_mode', '0')
      @adb.put_setting('secure', 'location_mode', '3')
    end

    def screen_brightness_mode(value)
      @adb.put_setting('system', 'screen_brightness_mode', value)
    end

    def screen_brightness(value)
      @adb.put_setting('system', 'screen_brightness', value)
    end

    def screen_off_timeout(value)
      @adb.put_setting('system', 'screen_off_timeout', value)
    end

    def screen_off_pocket(value)
      @adb.put_setting('system', 'screen_off_pocket', value)
    end

    def oem_acc_anti_misoperation_screen(value)
      @adb.put_setting('system', 'oem_acc_anti_misoperation_screen', value)
    end

    def stay_on_while_plugged_in(value)
      @adb.put_setting('global', 'stay_on_while_plugged_in', value)
    end

    def wifi_sleep_policy(value)
      @adb.put_setting('global', 'wifi_sleep_policy', value)
    end

    def aod_mode(value)
      @adb.put_setting('system', 'aod_mode', value)
    end

    def immersive_mode_confirmations(value)
      @adb.put_setting('secure', 'immersive_mode_confirmations', value)
    end

    def enabled_accessibility_services(value)
      @adb.put_setting('secure', 'enabled_accessibility_services', value)
    end

    def package_verifier_user_consent(value)
      @adb.put_setting('secure', 'package_verifier_user_consent', value)
    end

    def wifi_on(value)
      @adb.put_setting('secure', 'wifi_on', value)
    end

    def device_provisioned(value)
      @adb.put_setting('global', 'device_provisioned', value)
    end

    def user_setup_complete(value)
      @adb.put_setting('secure', 'user_setup_complete', value)
    end

    def surface_palm_swipe(value)
      @adb.put_setting('system', 'surface_palm_swipe', value)
    end

    def surface_palm_touch(value)
      @adb.put_setting('system', 'surface_palm_touch', value)
    end

    def motion_pick_up_to_call_out(value)
      @adb.put_setting('system', 'motion_pick_up_to_call_out', value)
    end

    def motion_pick_up(value)
      @adb.put_setting('system', 'motion_pick_up', value)
    end

    def motion_overturn(value)
      @adb.put_setting('system', 'motion_overturn', value)
    end

    def motion_merged_mute_pause(value)
      @adb.put_setting('system', 'motion_merged_mute_pause', value)
    end

    def adaptive_fast_charging(value)
      @adb.put_setting('system', 'adaptive_fast_charging', value)
    end

    def ota_disable_automatic_update(value)
      @adb.put_setting('global', 'ota_disable_automatic_update', value)
    end

    def update_wifi_only2(value)
      @adb.put_setting('system', 'SOFTWARE_UPDATE_WIFI_ONLY2', value)
    end

    def wifi_should_switch_network(value)
      @adb.put_setting('system', 'wifi_should_switch_network', value)
    end

    def enable_screen_on_proximity_sensor(value)
      @adb.put_setting('global', 'enable_screen_on_proximity_sensor', value)
    end

    def put_key_is_open_prevent_mode(value)
      @adb.put_setting('system', 'key_is_open_prevent_mode', value)
    end

    def timepower_config(value)
      @adb.put_setting('system', 'timepower_config', value)
    end

    def poweron_time(value)
      @adb.put_setting('system', 'poweron_time', value)
    end

    def def_timepower_config(value)
      @adb.put_setting('system', 'def_timepower_config', value)
    end

    def next_timing_boot_timestamp(value)
      @adb.put_setting('secure', 'next_timing_boot_timestamp', value)
    end

    def hidden_api_policy(value)
      @adb.put_setting('global', 'hidden_api_policy', value)
    end

    def default_input_method(value)
      @adb.put_setting('secure', 'default_input_method', value)
    end

    def clock_set_24h
      @adb.put_setting('system', 'time_12_24', '24')
    end

    def passcode_enabled?
      device_passcode_manager.passcode_state_file_exists?
    rescue StandardError
      nil
    end

    def fix_rotation_enabled?
      fix_rotation_manager.fix_rotation_state_file_exists?
    rescue StandardError
      nil
    end

    def device_sensors_disabled?
      device_sensors_manager.device_sensors_state_file_exists?
    rescue StandardError => e
      log(:info, "Some error occured while checking for state file. Error: #{e.message}, backtrace: #{e.backtrace}")
      nil
    end

    def update_rotation_state(state)
      result = nil
      case state
      when "enable"
        result = fix_rotation_manager.enable
      when "disable"
        result = fix_rotation_manager.disable
      end
      raise "Failed to fix device rotation" unless result

      true
    end

    def update_peak_refresh_rate(peak_refresh_rate)
      return if peak_refresh_rate.empty?

      if peak_refresh_rate.to_f > 0
        log(:info, "Setting peak_refresh_rate to #{peak_refresh_rate}")
        @adb.put_setting('system', 'peak_refresh_rate', peak_refresh_rate)
      else
        log(:info, "Deleting peak_refresh_rate system settings")
        @adb.delete_setting('system', 'peak_refresh_rate')
      end
    end

    def update_sensors_state(state)
      result = nil
      case state
      when "enable"
        result = device_sensors_manager.disable
      when "disable"
        result = device_sensors_manager.enable
      end
      raise "Failed to change device sensors" unless result

      true
    end

    def update_time_format(value)
      result = nil
      case value
      when "12"
        result = device_time_format_manager.change_time_format("12")
      when "24"
        result = device_time_format_manager.change_time_format("24")
      end
      raise "Failed to change time format" unless result

      true
    end

    def update_timezone(timezone_value)
      timezone_helper = TimezoneHelper.new(@device, @logger, product: @product, session_id: @session_id)
      timezone_helper.change_time_zone(timezone_value)
    end

    def update_always_finish_activities(value)
      package_path = @adb.shell("pm path com.android.browserstack").split(":")[1].chomp
      method_name = "alwaysFinishActivities"
      @adb.shell(
        "CLASSPATH=#{package_path} app_process / com.android.browserstack.Main #{method_name} #{value}"
      )

      state_file_helper = StateFileHelper.new("always_finish_activities_#{@session_id}")
      if value.to_s == "true"
        state_file_helper.touch
      else
        state_file_helper.remove
      end
    end

    def update_passcode_state(state)
      result = nil
      case state
      when 'enable'
        result = device_passcode_manager.set_passcode
      when 'disable'
        result = device_passcode_manager.clear_passcode
      end
      raise 'Failed to set device passcode' unless result

      true
    end

    def bluetooth_automatic_turn_on(value)
      @adb.put_setting('secure', 'bluetooth_automatic_turn_on', value)
    end

    def self.run_from_bash

      raise StandardError, 'Not enough arguments' if ARGV.size < 2

      function_to_call = ARGV[0].to_s.strip
      device_id = ARGV[1].to_s.strip

      logger_params = {}
      logger_params[:device] = device_id
      logger_params[:component] = 'SettingsHelper_bash'

      logger = BrowserStack.init_logger(File.join(BrowserStack::LOGGING_DIR,
                                                  "cleanup_#{device_id}.log"), logger_params)
      settings_helper = BrowserStack::SettingsHelper.new(device_id, logger)
      settings_helper.send(function_to_call)
    rescue StandardError => e
      puts e.message
      raise e

    end

    private

    def device_passcode_manager
      @device_passcode_manager ||= DevicePasscodeManager.new(@device, @logger, @session_id, @product)
    end

    def fix_rotation_manager
      @fix_rotation_manager ||= FixRotationManager.new(@device, @logger, @session_id, @product)
    end

    def device_sensors_manager
      @device_sensors_manager ||= DeviceSensorsManager.new(@device, @logger, @session_id, @product)
    end

    def device_time_format_manager
      @device_time_format_manager ||= DeviceTimeFormatManager.new(@device, @logger, @session_id, @product)
    end

    def log(level, message)
      if @logger.instance_of?(Logger)
        @logger.send(level.to_sym, message)
      else
        logger_params = { subcomponent: 'SETTINGS_HELPER' }
        @logger.send(level.to_sym, message, logger_params)
      end
    end
  end
end
BrowserStack::SettingsHelper.run_from_bash if $PROGRAM_NAME == __FILE__
