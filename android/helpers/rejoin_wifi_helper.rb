require 'android_toolkit'
require 'logger'
require_relative 'utils'

class RejoinWifi

  def initialize(device, logger, logger_params = {})
    raise "Device cannot be empty" if device.nil? || device == ""

    @device = device
    @logger = logger
    @logger_params = logger_params
    AndroidToolkit::Log.logger = @logger
    @adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
  end

  def rejoin
    class_name = "RejoinBLT"
    start_time = Time.now
    log :info, "Starting UI Automation to rejoin wifi. Start Time: #{start_time}"
    begin
      output = @adb.shell("am instrument -w -r -e debug false -e class 'com.browserstack.uiautomation.#{class_name}' "\
                 "com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner", timeout: 35)
      instrument_automation("Rejoin_BLT", output, @device)
    rescue StandardError => e
      log :info, "Error while rejoing wifi: #{e.message} - #{e.backtrace}"
    end
    log :info, "Wifi rejoined. Duration: #{Time.now - start_time}"
  end

  private

  def log(level, msg)
    @logger.send(level.to_sym, "#{self.class} #{msg}")
  end
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase.to_sym
  device_id = ARGV[1].to_s.strip
  helper = RejoinWifi.new(device_id, Logger.new($stdout))
  helper.send(command)
end
