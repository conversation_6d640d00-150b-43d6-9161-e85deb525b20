require 'dotenv/load'
require 'fileutils'
require_relative './utils'
require_relative '../constants'
require_relative './http_utils'
require_relative '../lib/custom_exceptions'
require_relative '../../common/push_to_zombie'
require_relative './camera_media_injector'

module BrowserStack
  MEDIA_INJECTION_SAMPLE_FILENAME = "SampleInjectionMedia.mjpeg".freeze
  MEDIA_INJECTION_SAMPLE_S3_BUCKET = "bs-mobile/mediainjection".freeze
  MEDIA_INJECTION_DIR_DEVICE = "/data/local/tmp/media_injection".freeze
  MEDIA_INJECTION_DEVICE_FILENAME = "injectionMedia.mjpeg".freeze
  MEDIA_INJECTION_DIR_MINI = "/tmp/live_injection_media".freeze

  class LiveMediaInjector
    MAX_ADB_ATTEMPTS = 3
    MAX_DOWNLOAD_ATTEMPTS = 3
    MAX_CONVERT_ATTEMPTS = 3
    DEFAULT_IMAGE_SCALE = "1280:720:force_original_aspect_ratio=increase".freeze
    DEFAULT_IMAGE_SCALE_PORTRAIT = "720:1280:force_original_aspect_ratio=decrease".freeze

    class << self
      def download_media(host_path, s3_url, force_dl=false) # rubocop:todo Style/OptionalBooleanParameter
        if force_dl || !File.exist?(host_path)
          FileUtils.mkdir_p(File.dirname(host_path))
          BrowserStack::HttpUtils.download_file(s3_url, host_path, 'LIVE_MEDIA_INJECTION',
                                                { retry_count: MAX_DOWNLOAD_ATTEMPTS, timeout: 30 })
        end
      end

      def push_media_to_device(device_id, host_path, device_path)
        attempts = 0
        begin
          attempts += 1
          exit_code, output = adb_file_cmd('push', device_id, host_path, device_path, { tag: 'CAMERA_IMAGE_INJECTION' })
          raise "Image #{host_path} could not be pushed to the device #{device_id}" unless exit_code == 0
        rescue StandardError => e
          retry if attempts < MAX_ADB_ATTEMPTS
          raise e
        end
      end

      def inject_media(device_id, type="sample", media_data=nil)
        device_path = File.join(MEDIA_INJECTION_DIR_DEVICE, MEDIA_INJECTION_DEVICE_FILENAME)
        case type
        when "sample"
          session_id = nil
          host_path = File.join(MEDIA_INJECTION_DIR_MINI, MEDIA_INJECTION_SAMPLE_FILENAME)
          file_url = "https://s3.amazonaws.com/#{MEDIA_INJECTION_SAMPLE_S3_BUCKET}/#{MEDIA_INJECTION_SAMPLE_FILENAME}"
          force_dl = false
        when "user"
          raise "media_data is nil for user media" if media_data.nil?

          file_url = media_data["file_url"]
          session_id = media_data["session_id"]
          host_path = File.join(MEDIA_INJECTION_DIR_MINI, media_data['media_hashed_id'],
                                "#{media_data['media_hashed_id']}#{media_data['format'].downcase}")
          force_dl = true
        end

        begin
          download_media(host_path, file_url, force_dl)
          if type == "user"
            mjpeg_path = "#{host_path.split('.')[0]}.mjpeg"
            convert_media(host_path, mjpeg_path)
            host_path = mjpeg_path
          end
          push_media_to_device(device_id, host_path, device_path) if File.exist?(host_path)
          BrowserStack.logger.info("Live media injection successful device:#{device_id}, type:#{type}")
        rescue StandardError => e
          BrowserStack.logger.error("Live media injection failed device:#{device_id}, type:#{type}")
          zombie_push('android', "live-media-injection-failed", "type:#{type}", "chrome", "", device_id)
          raise e
        end

        cleanup_files_on_mini(File.join(MEDIA_INJECTION_DIR_MINI, media_data['media_hashed_id'])) if type == "user"
      end

      def cleanup_files_on_mini(media_folder)
        FileUtils.rm_rf(media_folder) if File.exist?(media_folder)
      end

      def enable_fake_media_stream(device_id)
        begin
          media_path = File.join(MEDIA_INJECTION_DIR_DEVICE, MEDIA_INJECTION_DEVICE_FILENAME)
          is_media_present = adb(device_id).ls(media_path) != ''
        rescue StandardError => e
          is_media_present = false
          BrowserStack.logger.info "Fake media present check failed: #{e}"
        end
        is_media_present ? "with_media" : "no_media"
      end

      def convert_media(input_file_path, output_file_path)
        raise "File doesn't exist: #{input_file_path}" unless File.exist?(input_file_path)

        # Convert to mjpeg of correct scale
        scale = calculate_media_scale(input_file_path)
        convert_media_to_mjpeg(input_file_path, output_file_path, scale)
      end

      def convert_media_to_mjpeg(input_file_path, output_file_path, scale=DEFAULT_IMAGE_SCALE)
        attempts = 0
        begin
          attempts += 1
          result, status = if input_file_path.end_with?(".mp4")
                             OSUtils.execute("#{FFMPEG} -i #{input_file_path} #{output_file_path} -y", true)
                           else
                             OSUtils.execute("#{FFMPEG} -framerate 1/5 -i #{input_file_path} -r 5 -pix_fmt "\
                                           "yuvj420p -vf scale=#{scale} #{output_file_path} -y", true)
                           end
          BrowserStack.logger.info("Output of conversion of #{input_file_path} to #{output_file_path} status: "\
                                   "#{status} result: #{result} scale: #{scale}")
          raise "Image #{input_file_path} conversion to #{output_file_path} mjpeg failed" if status != 0
        rescue StandardError => e
          retry if attempts < MAX_CONVERT_ATTEMPTS
          raise e
        end
      end

      def calculate_media_scale(input_file_path)
        scale = if input_file_path.end_with?(".mp4")
                  OSUtils.execute("timeout 2 #{FFMPEG} -i #{input_file_path} 2>&1 | grep 'Video' "\
                                  "| head -n 1 | sed -E 's/.*, ([0-9]+)x([0-9]+).*/\1:\2/'")
                else
                  OSUtils.execute("timeout 2 identify -ping -format '%[width]:%[height]' #{input_file_path}")
                end
        return DEFAULT_IMAGE_SCALE if scale.nil?

        width, height = scale.split(':').map(&:to_f)
        width > height ? DEFAULT_IMAGE_SCALE : DEFAULT_IMAGE_SCALE_PORTRAIT
      end

      def run(task)
        case task
        when "inject_sample_media"
          device_id = ARGV[1]
          type = "sample"
          LiveMediaInjector.inject_media(device_id, type)
        when "download_media"
          LiveMediaInjector.download_media(*ARGV)
        when "push_media_to_device"
          LiveMediaInjector.push_media_to_device(*ARGV)
        else
          device_id = ARGV[1]
          type = ARGV[2]
          LiveMediaInjector.inject_media(device_id, type)
        end
      end
    end
  end
end

# This code path is invoked when using this script via bash
if __FILE__ == $PROGRAM_NAME
  BrowserStack.logger.params[:component] = 'live_image_injector.rb'
  BrowserStack::LiveMediaInjector.run(ARGV[0])
end
