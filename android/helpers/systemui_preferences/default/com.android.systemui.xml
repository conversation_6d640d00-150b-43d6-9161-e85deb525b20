<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<map>
    <set name="QsTileSpecsRevealed">
        <string>bt</string>
        <string>wifi</string>
        <string>airplane</string>
        <string>rotation</string>
        <string>night</string>
        <string>dnd</string>
        <string>battery</string>
        <string>cell</string>
        <string>flashlight</string>
    </set>
    <int name="OverviewOpenedCount" value="6" />
    <int name="QuickStepInteractionFlags" value="0" />
    <boolean name="DndTileVisible" value="true" />
    <boolean name="DndTileCombinedIcon" value="true" />
    <boolean name="HasSeenRecentsSwipeUpOnboarding" value="true" />
    <boolean name="HasDismissedRecentsQuickScrubOnboardingOnce" value="true" />
    <int name="OverviewOpenedFromHomeCount" value="3" />
    <int name="DismissedRecentsSwipeUpOnboardingCount" value="1000" />
</map>
