require_relative '../constants'
require_relative "./utils"
require_relative '../../common/push_to_zombie'
require 'browserstack_logger'
require_relative '../../common/helpers'
require_relative '../lib/os_utils'

class SocatHelper
  class << self
    def create_secure_tcp_channel(socat_port, domain, port)
      # First, kill all socat processes running on the specified port
      kill(socat_port)
      cmd = "#{BrowserStack::SOCAT} -d TCP-LISTEN:#{socat_port},reuseaddr,fork OPENSSL:#{domain}:#{port},verify=0"
      socat_pid = spawn_process(cmd)
      BrowserStack.logger.info("Spawned socat process with command: #{cmd}, socat_pid: #{socat_pid}")
    end

    def kill(socat_port)
      # Run the command and redirect output to /dev/null
      output = OSUtils.execute("ps aux | grep -E 'socat.*TCP-LISTEN:' | grep -v grep | awk '{print $2}'")
      if output.empty?
        BrowserStack.logger.info("No socat processes found on port #{socat_port}.")
        return
      end
      # Kill the socat processes
      output, status = OSUtils.execute("ps aux | grep -E 'socat.*TCP-LISTEN:#{socat_port}' | grep -v grep | awk '{print $2}' | xargs -n 1 -I {} timeout 5 kill -9 {}", true) # rubocop:disable Layout/LineLength
      if status.exitstatus == 0
        BrowserStack.logger.info("All socat processes on port #{socat_port} killed successfully.")
      else
        zombie_push('android', "android_socat_kill_failed", '', '', { "socat_port" => "1" }.to_json, "", "", "")
        BrowserStack.logger.error("Failed to kill socat processes on port #{socat_port}.")
      end
    end

    def cleanup(device)
      socat_port = get_device_socat_port(device)
      kill(socat_port)
    end
  end

  if $PROGRAM_NAME == __FILE__
    command = ARGV[0]
    case command
    when "cleanup"
      device_id = ARGV[1].to_s.strip
      SocatHelper.cleanup(device_id)
    else
      BrowserStack.logger.info("[SocatHelper] Invalid params")
    end
  end
end
