require_relative '../models/android_device'
require_relative '../constants'
require_relative '../version_managers/ui_automation_apps_manager'

require 'android_toolkit'
require 'logger'

module BrowserStack
  class UiAutomationRulesHelper

    def self.adb(device)
      AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
    end

    def self.get_rule_for_automation(device_id, step)
      begin
        automation_rules = JSON[File.read(BrowserStack::UI_AUTOMATION_RULES_CONFIG)]
      rescue StandardError => e
        BrowserStack.logger.error "Failed to load UI automation rules config: #{e.class} - #{e.message}"
        return ["normal_automation", nil]    # Run normal automation in case file/config read issue
      end
      device_model = adb(device_id).model
      os_version = adb(device_id).os_version
      step_config = automation_rules[step]
      rules = step_config['rules'] || []
      via = nil
      intent = nil

      # Iterate from bottom to top (reverse order)
      rules.reverse_each do |rule|
        BrowserStack.logger.info "Verifying rule: #{rule}"

        # If 'device_model' exists, make sure it includes the current device_model
        BrowserStack.logger.info "Check device_model: #{device_model}"
        next if rule.key?('device_model') && !rule['device_model'].include?(device_model)

        BrowserStack.logger.info "Passed device model check"

        # OS version must match regardless (Regex)
        BrowserStack.logger.info "Check os_version: #{Regexp.new(rule['os_version']).match?(os_version.to_s)}"
        next unless Regexp.new(rule['os_version']).match?(os_version.to_s)

        BrowserStack.logger.info "Passed os_version check : #{Regexp.new(rule['os_version']).match?(os_version.to_s)}"

        via = rule['via']
        intent = rule['intent']
        BrowserStack.logger.info "Matching rule found. via : #{via}, intent : #{intent}"
        break
      end

      via ||= step_config['default_via'] # Mostly, normal automation without intent
      intent ||= step_config['default_intent']
      [via, intent]
    end

    def self.log(level, msg)
      @logger.send(level.to_sym, "#{self.class} #{msg}")
    end
  end
end
