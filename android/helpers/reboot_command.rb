require_relative '../constants'
require_relative '../../common/push_to_zombie'

require 'android_toolkit'
require 'logger'

class RebootCommand

  def initialize(device_id, logger, device_obj)
    @device_id = device_id
    @logger = logger

    AndroidToolkit::Log.logger = @logger
    @adb = AndroidToolkit::ADB.new(udid: @device_id, path: BrowserStack::ADB)
    @device_obj = device_obj
    @device_config = device_obj.device_config
    @device_name = @device_config&.fetch('device_name', 'Unknown Device')
    @device_version = @device_config&.fetch('device_version', '0').to_i
  end

  def reboot_device(reason: "unknown", wait: false, timeout: 100, boot_to_recovery: false)
    # use DB to check if we should reboot to recovery unless forced into recovery mode
    boot_to_recovery ||= @device_obj.recovery_device?
    if @device_name == "Pixel 6 Pro" && @device_version >= 15
      log :info, "Enabling USB debugging for device: #{@device_id}"
      system("#{BrowserStack::ADB} -s #{@device_id} shell 'settings put global adb_enabled 1'")
    end
    if @device_obj.soft_reboot_device?
      log :info, "Soft Rebooting Device #{@device_id} Reason: #{reason} Wait: #{wait}"
      @adb.soft_reboot
      zombie_key_value(
        kind: 'device-soft-rebooted',
        data: reason,
        device: @device_id
      )
    else
      log :info, "Rebooting Device #{@device_id} Reason: #{reason} Wait: #{wait} Boot to recovery: #{boot_to_recovery}"
      @adb.reboot(boot_to_recovery: boot_to_recovery)
      zombie_key_value(
        kind: 'device-rebooted',
        data: reason,
        device: @device_id
      )
    end
    FileUtils.touch(@device_obj.reboot_state_file)
    # todo? push stats of reboot time when waiting
    @device_obj.wait_for_boot(timeout) if wait
    if @device_name == "Pixel 6 Pro" && @device_version >= 15
      @device_obj.wait_for_boot(timeout)
      log :info, "Enabling USB debugging again after reboot for device: #{@device_id}"
      system("#{BrowserStack::ADB} -s #{@device_id} shell 'settings put global adb_enabled 1'")
    end
  end

  def log(level, msg)
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, msg)
    else
      @logger.send(level.to_sym, msg, @logger_params)
    end
  end
end
