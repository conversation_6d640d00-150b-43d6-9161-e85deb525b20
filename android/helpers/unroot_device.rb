require_relative '../models/android_device'
require_relative '../lib/root_command'
require_relative '../constants'

require 'android_toolkit'
require 'logger'

class UnrootDevice
  attr_reader :adb, :root_command

  def initialize(device, logger, logger_params = {})
    @device = device
    @logger = logger
    @logger_params = logger_params

    AndroidToolkit::Log.logger = @logger
    @adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    @device_obj = BrowserStack::AndroidDevice.new(@device, "UnrootDevice", @logger)
    @root_command = RootCommand.new(@device_obj, @logger, @logger_params)
  end

  def self.run_from_bash
    device_id = ARGV[0].to_s.strip
    command = ARGV[1].to_s.strip.downcase.to_sym

    logger = Logger.new($stdout)

    helper = UnrootDevice.new(device_id, logger)
    helper.send(command)
  rescue StandardError => e
    ExitFile.write(e.message)
    raise e
  end

  def unroot
    return unless @device_obj.rooted?

    uninstall_magisk_manager
    remove_root_binaries
  end

  private

  def uninstall_magisk_manager
    # Uninstall the injected Magisk Manager stub
    adb.uninstall(BrowserStack::MAGISK_APP_PACKAGE_NAME)
  rescue AndroidToolkit::ADB::ExecutionError => e
    log(:error, "Unable to uninstall #{BrowserStack::MAGISK_APP_PACKAGE_NAME} #{e.message}")
  end

  def remove_root_binaries
    # Remove the su and magisk binaries
    rm_cmd = "rm -rf #{root_binaries.join(' ')}"
    root_command.run(rm_cmd)
  end

  def root_binaries
    [
      "/sbin/su",
      "/sbin/magisk",
      "/system/bin/su",
      "/system/bin/magisk"
    ]
  end

  def log(level, msg)
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, msg)
    else
      @logger.send(level.to_sym, msg, @logger_params)
    end
  end
end

UnrootDevice.run_from_bash if $PROGRAM_NAME == __FILE__
