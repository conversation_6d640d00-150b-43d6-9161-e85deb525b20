require 'socket'
require 'logger'

require_relative "../lib/os_utils"
require_relative '../lib/screenshot_instrumentation'
require_relative "../scripts/upload_to_s3"
require_relative '../constants'
require "#{BrowserStack::MOBILE_COMMON_HOME}/utils/screenshot_util"

require 'android_toolkit'
require 'browserstack_logger'

DEVICE = ARGV[0]
PORT = ARGV[1]
FILENAME = ARGV[2]
ORIENTATION = ARGV[3].to_s.downcase
KEY_ID = ARGV[4]
SECRET_KEY = ARGV[5]
DIRECT_JPG = ARGV[6]
USE_ADB_FOR_SCREENSHOT = ARGV[7].to_s.downcase
CHECK_BLACK_SCREENSHOT = ARGV[8].to_s.casecmp("true").zero?

SCREENSHOT_TIMEOUT_THRESHOLD = 120
INTERACTION_CONFIG_FILE = "/usr/local/.browserstack/mobile/android/live/interaction_server/device_config.json".freeze
CONFIG_DIR = '/usr/local/.browserstack/config'.freeze

log_file = File.open('/var/log/browserstack/hub_snapshot.log', File::WRONLY | File::APPEND | File::CREAT)
BrowserStack.init_logger(log_file)

puts "========= Args: #{ARGV.join(', ')}"

DEBUG_SCREENSHOT_FOLDER = "/usr/local/.browserstack/automate_screenshot".freeze

@@socket = nil
@@is_black_screenshot = "skipped"
@@error = []
@@error_stage = nil
@@screenshot_data = {
  stage: "capture",
  start_time: 0,
  time_taken: 0,
  total_time: 0
}
session_id = begin
  FILENAME.split("##")[1]
rescue StandardError
  ""
end
screenshot_instrumentation = BrowserStack::ScreenshotInstrumentation.new(session_id)

def calculate_time_taken(start_time)
  (Time.now - start_time).round(2)
end

def update_stage_data(req_stage, is_start)
  @@screenshot_data[:stage] = req_stage
  if is_start
    @@screenshot_data[:start_time] = Time.now
  else
    @@screenshot_data[:time_taken] = calculate_time_taken(@@screenshot_data[:start_time])
    @@screenshot_data[:total_time] += @@screenshot_data[:time_taken]
  end
end

def get_pre_conversion_filename
  return "#{FILENAME}.png" if USE_ADB_FOR_SCREENSHOT == "true"

  "#{FILENAME}.rgba"
end

def connect
  @@socket = TCPSocket.new 'localhost', PORT
  puts "TCP connect success"
end

def take_screenshot_with_adb
  # will be converting and compressing
  # this file to final jpeg
  png_file = "#{DEBUG_SCREENSHOT_FOLDER}/#{get_pre_conversion_filename}"
  system("adb -s #{DEVICE} exec-out screencap -p > #{png_file}")
  if File.size(png_file) == 0
    @@error << "png file is empty"
    @@error_stage = "capture"
  end
end

def take_screenshot_with_debug_port
  @@socket.puts "a"
  data = @@socket.read
  if DIRECT_JPG == "true"
    jpegfile = "#{DEBUG_SCREENSHOT_FOLDER}/#{FILENAME}.jpeg"
    File.write(jpegfile, data)
    puts "got direct jpeg file, bytes: #{data.length}"
    if File.size(jpegfile) == 0
      @@error << "direct jpeg file is empty"
      @@error_stage = "capture"
    end
  else
    rgbfile = "#{DEBUG_SCREENSHOT_FOLDER}/#{get_pre_conversion_filename}"
    File.write(rgbfile, data)
    puts "got rgba file, bytes: #{data.length}"
    if File.size(rgbfile) == 0
      @@error << "rgba file is empty"
      @@error_stage = "capture"
    end
  end
  begin
    @@socket.puts "a"
  rescue StandardError
    nil
  end
  @@socket.close
end

def adb
  @adb ||= AndroidToolkit::ADB.new(udid: DEVICE, path: BrowserStack::ADB)
end

def take_screenshot
  if USE_ADB_FOR_SCREENSHOT == "true"
    take_screenshot_with_adb
    return
  end

  take_screenshot_with_debug_port
end

def convert
  # TODO: Change the filename to reflect dir structure. Currently, the filename contains ## and is
  # in format: testautomation##416ca59a670429430db6b8aafe91e21b550c83c3##screenshot-e487cab177.jpeg
  device_model = adb.model
  device_model = device_model.to_s.strip
  devices_config = JSON.parse(File.read(INTERACTION_CONFIG_FILE))
  if devices_config['deviceConfigurations'].key?(device_model)
    stream_width = devices_config['deviceConfigurations'][device_model]["streaming_width"]
    stream_height = devices_config['deviceConfigurations'][device_model]["streaming_height"]
  else
    streaming_args_file = "#{CONFIG}/streaming_params_#{DEVICE}"
    if File.exist?(streaming_args_file)
      streaming_args = File.read(streaming_args_file)
      stream_width = streaming_args.match(/streaming_width=(\d+)/)[1]
      stream_height = streaming_args.match(/streaming_height=(\d+)/)[1]
    else
      raise "STREAMING_ARGS file not found: #{streaming_args_file}"
    end
  end
  width_height = "#{stream_width}x#{stream_height}"
  rotation_degree = '-rotate -90 ' if ORIENTATION == 'landscape' && USE_ADB_FOR_SCREENSHOT == 'false'
  quality = '-quality 30 -resize 50% ' if USE_ADB_FOR_SCREENSHOT == 'true'
  cmd = "cd #{DEBUG_SCREENSHOT_FOLDER}; convert #{rotation_degree}-size #{width_height} " \
        "#{quality}" "-depth 8 \"#{get_pre_conversion_filename}\" \"#{FILENAME}.jpeg\"; exit_status=$?; " \
        "openvt #{FILENAME}.jpeg; exit $exit_status"
  output, status = OSUtils.execute(cmd, true)
  File.delete "#{DEBUG_SCREENSHOT_FOLDER}/#{get_pre_conversion_filename}"
  jpeg_file = "#{DEBUG_SCREENSHOT_FOLDER}/#{FILENAME}.jpeg"
  @@error << "jpeg file is empty" if File.size(jpeg_file) == 0
  @@error << "convert command failed" if status != 0
  @@error_stage = "convert" if !@@error_stage && (File.size(jpeg_file) == 0 || status != 0)
end

def upload_to_s3
  # s3curl --id bs --put="./drony.apk" -- -H "x-amz-acl: public-read" "https://s3.amazonaws.com/bs-stag/dhimil-test/drony.apk"
  bucket_path = "#{FILENAME.gsub('##', '/')}.jpeg"
  s3url = "https://s3.amazonaws.com/#{bucket_path}"
  upload_file = "#{DEBUG_SCREENSHOT_FOLDER}/#{FILENAME}.jpeg"

  # check if its a black screenshot
  @@is_black_screenshot = CHECK_BLACK_SCREENSHOT ? ScreenshotUtil.is_black_screenshot?(upload_file).to_s : "skipped"

  status, error = UploadToS3.upload_file_to_s3(KEY_ID, SECRET_KEY, "image/jpeg", upload_file,
                                               "public-read", s3url, nil, 10)
  File.delete upload_file
  needs_reboot_file = "#{BrowserStack::STATE_FILES_DIR}/needs_reboot_#{DEVICE}"
  device_model = adb.model
  if device_model.to_s.include?('Pixel 8') && @@is_black_screenshot.to_s == "true" && !File.exist?(needs_reboot_file)
    FileUtils.touch(needs_reboot_file)
  end
  if !status || error
    @@error << "upload command failed"
    @@error_stage ||= "upload"
  end
end

update_stage_data("capture", true)
screenshot_instrumentation.update_screenshot_instrumentation_with_lock(nil, @@screenshot_data[:stage], {})
if USE_ADB_FOR_SCREENSHOT == "false"
  Logger.new(log_file).info("[#{DEVICE}] connecting to dev tools port for screenshot")
  connect
else
  Logger.new(log_file).info("[#{DEVICE}] using adb for screenshot")
end
Logger.new(log_file).info("[#{DEVICE}] taking screenshot")
take_screenshot
update_stage_data("capture", false)

update_stage_data("convert", true)
screenshot_instrumentation.update_screenshot_instrumentation_with_lock(
  "capture", @@screenshot_data[:stage],   { stage_time_taken: @@screenshot_data[:time_taken] }
)
Logger.new(log_file).info("[#{DEVICE}] converting")
convert if DIRECT_JPG == "false" || USE_ADB_FOR_SCREENSHOT == "true"
update_stage_data("convert", false)

update_stage_data("upload", true)
screenshot_instrumentation.update_screenshot_instrumentation_with_lock(
  "convert", @@screenshot_data[:stage], { stage_time_taken: @@screenshot_data[:time_taken] }
)
Logger.new(log_file).info("[#{DEVICE}] uploading to s3")
upload_to_s3
update_stage_data("upload", false)

if !@@error.empty?
  Logger.new(log_file).info("[#{DEVICE}] Screenshot Process Error: #{@@error} during stage: #{@@error_stage}.")
  screenshot_instrumentation.update_screenshot_instrumentation_with_lock(
    @@error_stage, "failed",
    {
      stage_time_taken: calculate_time_taken(@@screenshot_data[:start_time]),
      is_black_screenshot: @@is_black_screenshot
    }
  )
else
  next_stage = if @@screenshot_data[:total_time] > SCREENSHOT_TIMEOUT_THRESHOLD
                 "timeout"
               else
                 "success"
               end
  screenshot_instrumentation
    .update_screenshot_instrumentation_with_lock(
      @@screenshot_data[:stage],
      next_stage,
      {
        stage_time_taken: @@screenshot_data[:time_taken],
        total_time: @@screenshot_data[:total_time],
        is_black_screenshot: @@is_black_screenshot
      }
    )
  Logger.new(log_file).info("[#{DEVICE}] done")
end
