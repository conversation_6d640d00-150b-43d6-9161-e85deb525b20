require_relative '../lib/os_utils'

class MachineBluetoothUtils
  class << self
    def enable(timeout = 5)
      execute_command(
        "sudo rfkill unblock bluetooth && sudo systemctl start bluetooth && sudo systemctl enable bluetooth", timeout
      )
    end

    def pairable_on(agent = "NoInputNoOutput", timeout = 5)
      execute_command("bluetoothctl --agent=#{agent} pairable on", timeout)
    end

    def discoverable_on(agent = "NoInputNoOutput", timeout = 5)
      execute_command("bluetoothctl --agent=#{agent} discoverable on", timeout)
    end

    def scan(agent = "NoInputNoOutput", timeout = 5)
      execute_command("bluetoothctl --agent=#{agent} scan on", timeout)
    end

    def trust(mac_address, agent = "NoInputNoOutput", timeout = 5)
      execute_command("bluetoothctl --agent=#{agent} trust #{mac_address}", timeout)
    end

    def send_pairing_request(mac_address, agent = "NoInputNoOutput", timeout = 7)
      pid = spawn("timeout #{timeout} bluetoothctl --agent=#{agent} pair #{mac_address}")
      Process.detach(pid)
      pid
    end

    def send_connection_request(mac_address, agent = "NoInputNoOutput", timeout = 5)
      pid = spawn("timeout #{timeout} bluetoothctl --agent=#{agent} connect #{mac_address}")
      Process.detach(pid)
      pid
    end

    def stop_scan(agent = "NoInputNoOutput", timeout = 5)
      execute_command("bluetoothctl --agent=#{agent} scan off", timeout, should_raise_error: false)
    end

    def pairable_off(agent = "NoInputNoOutput", timeout = 5)
      execute_command("bluetoothctl --agent=#{agent} pairable off", timeout)
    end

    def discoverable_off(agent = "NoInputNoOutput", timeout = 5)
      execute_command("bluetoothctl --agent=#{agent} discoverable off", timeout)
    end

    def remove(mac_address, agent = "NoInputNoOutput", timeout = 5)
      execute_command("bluetoothctl --agent=#{agent} remove #{mac_address}", timeout, should_raise_error: false)
    end

    def disable(_agent = "NoInputNoOutput", timeout = 5)
      execute_command(
        "sudo systemctl disable bluetooth && sudo systemctl stop bluetooth && sudo rfkill block bluetooth", timeout
      )
    end

    def info(mac_address, agent = "NoInputNoOutput", timeout = 5)
      execute_command("bluetoothctl --agent=#{agent} info #{mac_address}", timeout)
    end

    def paired?(mac_address, agent = "NoInputNoOutput", timeout = 5)
      response = execute_command("bluetoothctl --agent=#{agent} info #{mac_address} | grep 'Paired: yes'", timeout)
      !response.nil?
    end

    def connected?(mac_address, agent = "NoInputNoOutput", timeout = 5)
      response = execute_command("bluetoothctl --agent=#{agent} info #{mac_address} | grep 'Connected: yes'", timeout)
      !response.nil?
    end

    private

    def execute_command(command, timeout = 5, should_raise_error: true)
      timeout_command = "timeout #{timeout}" if timeout > 0
      user_command = "XDG_RUNTIME_DIR=/run/user/1000 #{timeout_command} #{command}"
      result, status = OSUtils.execute(user_command, true)
      if should_raise_error && status != 0
        raise "Failed to execute command ( non-zero exit code ): #{user_command}, result: #{result}, status: #{status}"
      end

      result
    end
  end
end
