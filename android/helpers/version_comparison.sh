# functions to compare version numbers like 12.3.4 >= 9.32.1
# each function returns 0 if true, 1 if false
# works with letters: vers_gt "8.9p.10x" "6.4v" returns 0 (true)

# $1 <= $2
vers_lte() {
  [  "$1" = "`echo -e "$1\n$2" | sort -V | head -n1`" ]
}

# $1 < $2
vers_lt() {
  [ "$1" = "$2" ] && return 1 || vers_lte $1 $2
}

# $1 >= $2
vers_gte() {
  [  "$1" = "`echo -e "$1\n$2" | sort -V | tail -n1`" ]
}

# $1 > $2
vers_gt() {
  [ "$1" = "$2" ] && return 1 || vers_gte $1 $2
}

# checks if major version numbers are equal
# e.g: major_vers_eq "10.9.9" "10.1.1" returns 0 (true)
# $1 == $2
major_vers_eq() {
  major_vers_1=`echo $1 | cut -f1 -d"."` # 9.4.5 => 9
  major_vers_2=`echo $2 | cut -f1 -d"."`

  [[ "$major_vers_1" == "$major_vers_2" ]]
}

vers_major() {
  # appends major version to 'v', e.g v10, v8, v14
  major_vers=`echo $1 | cut -f1 -d"."`
  v_major_vers=v$major_vers
}
