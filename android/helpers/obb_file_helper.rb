require_relative "../constants"
require "browserstack_logger"
require_relative "./run_as_root_helper"
require_relative "../../common/push_to_zombie"
require_relative "../exit_file"
require_relative "../lib/os_utils"
require_relative "../lib/custom_exceptions"
require_relative "./http_utils"
require_relative "../lib/root_command"
require "fileutils"
require "android_toolkit"
require "json"
require "logger"

class OBBFileHelper
  STATE_FILES_DIR = BrowserStack::STATE_FILES_DIR
  MACHINE_IP = "/usr/local/.browserstack/whatsmyip".freeze
  OBB_FILE_HELPER_LOG = "[obb_file_helper_log]".freeze

  def initialize(device_id, bundle_id, logger, file_name, session_id = "")
    @device_id = device_id
    @session_id = session_id
    @bundle_id = bundle_id
    @file_name = file_name
    @logger = logger
    @adb = AndroidToolkit::ADB.new(udid: @device_id, path: BrowserStack::ADB)
    @root_helper = BrowserStack::RunAsRootHelper.new(device_id: @device_id, logger: @logger)
    @device_obj = BrowserStack::AndroidDevice.new(@device_id, "OBBFileHelper", @logger)
    @root_command = RootCommand.new(@device_obj, @logger, @logger_params)
    @eds_obj = EDS.new({}, @logger)
    @eds_event_type = "web_events"
    @machine_ip = (File.exist?(MACHINE_IP) && File.read(MACHINE_IP)) || ""
  end

  def download(file_url)
    BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} Download got called")
    file_download_total_time = 0
    host_obb_file_folder = "/tmp/obb_file_#{@device_id}"
    cls_json = {
      "host_obb_file_folder" => host_obb_file_folder,
      "device_name" => @device_id,
      "session_id" => @session_id
    }

    BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} cls json: #{cls_json}")
    FileUtils.mkdir_p(host_obb_file_folder)

    file_name = @file_name
    file_type = ".obb"

    BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} file details: #{file_url}, #{file_name}, #{file_type}")

    file_path = File.join(host_obb_file_folder, file_name)

    data_to_be_written = "/sdcard/Download/#{@file_name}\n/sdcard/Android/obb/#{@bundle_id}"
    File.open("#{STATE_FILES_DIR}/obb_file_details_#{@device_id}", 'w') { |file| file.write(data_to_be_written) }

    file_download_start_time = Time.now.to_f
    BrowserStack::HttpUtils.download_file(
      file_url,
      file_path,
      "custom_media",
      { retry_count: 3, timeout: 300 }
    )

    file_download_total_time += (Time.now.to_f - file_download_start_time)
    BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} OBB file downloaded, time taken: #{file_download_total_time}")
    FileUtils.touch("#{STATE_FILES_DIR}/obb_file_downloaded_#{@device_id}")
    send_data_to_eds("obb-file-download", file_download_total_time, "pass")
  rescue StandardError => e
    BrowserStack.logger.info("Failure occured during download and push of obb file. Error: #{e.message} #{e.backtrace}")
    zombie_push("android", "obb-download-failure", e.message, "", { team: "device-features" }, @device_id, @session_id)
    send_data_to_eds("obb-file-download", Time.now.to_f - file_download_start_time, "fail", e.message)
  end

  def push_obb_file_to_device
    file_push_start_time = Time.now.to_f
    download_path = "/sdcard/Download"
    device_file_path = "/sdcard/Android/obb/#{@bundle_id}"
    host_obb_file_folder = "/tmp/obb_file_#{@device_id}"
    file_name = @file_name
    BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} Pushing obb file #{@file_name} for device #{@device_id}")
    file_path = "#{host_obb_file_folder}/#{file_name}"
    begin
      if @root_helper.run_as_root_working?
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} Device is rooted")

        @adb.push(file_path, download_path)
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} file pushed to downloads folder")

        output = @root_command.run("mkdir -p '#{device_file_path}'")
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} created obb folder dir -- Output: #{output}")

        #mv command is giving Operation not permitted error in some devices.
        output = @root_command.run("cp '#{download_path}/#{file_name}' '#{device_file_path}'")
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} file pushed to obb folder -- Output: #{output}")

        output = @root_command.run("chmod 666 '#{device_file_path}/#{file_name}'")
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} file's permission changed -- Output: #{output}")
      else
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} Run as root not working")

        output = @adb.shell("mkdir -p '#{device_file_path}'")
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} created obb folder dir -- Output: #{output}")

        @adb.push(file_path, device_file_path)
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} file pushed to obb folder")
      end

      FileUtils.touch("#{STATE_FILES_DIR}/obb_file_injected_#{@device_id}")
      file_push_total_time = (Time.now.to_f - file_push_start_time)
      BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} OBB file pushed, time taken: #{file_push_total_time}")
      send_data_to_eds("obb-file-push", file_push_total_time, "pass")
    rescue StandardError => e
      FileUtils.touch("#{STATE_FILES_DIR}/obb_file_failure_#{@device_id}")
      File.open("#{STATE_FILES_DIR}/obb_file_failure_#{@device_id}", "w") { |file| file.write(e.message) }
      BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} OBB file not pushed error: #{e}")
      zombie_push("android", "obb-push-failure", e.message, "", { team: "device-features" }, @device_id, @session_id)
      send_data_to_eds("obb-file-push", Time.now.to_f - file_push_start_time, "fail", e.message)
    ensure
      FileUtils.rm_rf(host_obb_file_folder)
    end
  end

  def self.cleanup(device_id)
    FileUtils.rm_rf("/tmp/obb_file_#{device_id}")
    file = File.open("#{STATE_FILES_DIR}/obb_file_details_#{device_id}")
    file_data = file.readlines.map(&:chomp)
    adb = AndroidToolkit::ADB.new(udid: device_id, path: BrowserStack::ADB)
    file_data.each do |file_path|
      adb.shell("rm -rf '#{file_path}'")
    end
    file.close
    FileUtils.rm_rf("#{STATE_FILES_DIR}/obb_file_details_#{device_id}")
  end

  def self.run_from_bash
    command = ARGV[0].to_s.strip.downcase
    device_id = ARGV[1].to_s.strip
    session_id = ARGV[2].to_s.strip
    bundle_id = ARGV[3].to_s.strip
    file_url = ARGV[4].to_s.strip
    file_name = ARGV[5].to_s.strip
    BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} #{command}, #{device_id}, #{bundle_id}, #{file_url}, #{file_name}")
    logger = Logger.new($stdout)
    case command
    when "download"
      begin
        instance_obj = OBBFileHelper.new(device_id, bundle_id, logger, file_name, session_id)
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} OBB file helper got allocated")
        output = instance_obj.download(file_url)
      rescue StandardError => e
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} Failed to download obb file")
        exit 1
      end
    when "push"
      begin
        instance_obj = OBBFileHelper.new(device_id, bundle_id, logger, file_name, session_id)
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} OBB file helper got allocated")
        output = instance_obj.push_obb_file_to_device
      rescue StandardError => e
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} Failed to push obb file to downloads folder")
        exit 1
      end
    when "cleanup"
      begin
        output = OBBFileHelper.cleanup(device_id)
      rescue StandardError => e
        BrowserStack.logger.info("#{OBB_FILE_HELPER_LOG} Failed to clean obb flow")
        exit 1
      end
    end
  end

  private

  def send_data_to_eds(event_name, time, event_status, message = "")
    params = {
      product: "app_live",
      team: "device_features",
      event_json: {
        session_id: @session_id,
        device_id: @device_id,
        device_version: @device_obj.os_version,
        device_name: @device_obj.common_name,
        machine_ip: @machine_ip,
        time: time,
        message: message,
        event_status: event_status
      },
      event_name: event_name
    }
    @eds_obj.push_logs(@eds_event_type, params)
  end
end

OBBFileHelper.run_from_bash if $PROGRAM_NAME == __FILE__
