class UsbCrashReport
  CONTROLLER_CRASHED_REGEX = /\[(.*)\]\s(.*HC\sdied.*)/.freeze
  private_constant :CONTROLLER_CRASHED_REGEX

  def crashed_recently?
    usb_crash_times.any? do |time|
      time > DateTime.now - (1.0 / 24)
    end
  end

  def needs_rebind?
    crashed_recently? && !devices_on_lsusb?
  end

  def devices_on_lsusb?
    all = lsusb.split("\n")
    hubs = all.select { |l| l.match(/Linux Foundation \w.0 root hub/) }
    all != hubs
  end

  def report_summary
    crashes_in_24hrs = usb_crash_times.select { |time| time > DateTime.now - 1 }.length
    "USB controller crashed #{crashes_in_24hrs} times in last 24 hours, #{usb_crash_times.length} times since reboot"
  end

  private

  def usb_crash_times
    @usb_crash_times ||= dmesg.scan(CONTROLLER_CRASHED_REGEX).map do |time_string, _|
      DateTime.strptime(time_string, "%a %b %e %H:%M:%S %Y")
    end
  end

  def lsusb
    `lsusb`
  end

  def dmesg
    `dmesg -T`
  end
end
