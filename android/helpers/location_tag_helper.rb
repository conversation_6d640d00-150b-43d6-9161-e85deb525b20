require_relative '../models/android_device'
require_relative '../constants'
require_relative '../version_managers/ui_automation_apps_manager'

require 'android_toolkit'
require 'logger'

module BrowserStack
  class EnableLocationTags

    def initialize(device, logger, logger_params = {})
      raise "Device cannot be empty" if device.nil? || device == ""

      @device = device
      @logger = logger
      @logger_params = logger_params
      AndroidToolkit::Log.logger = @logger
      @adb = AndroidToolkit::ADB.new(udid: @device)
    end

    def enable
      class_name = "EnableLocationTag"
      start_time = Time.now
      log :info, "Starting UI Automation to Enable Location tags. Start Time: #{start_time}"
      begin
        retry_count = 0
        max_retries = 5

        while retry_count < max_retries
          output = @adb.shell("am instrument -w -r -e debug false -e class \
            'com.browserstack.uiautomation.#{class_name}' "\
            "com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner", timeout: 35)

          if output.match(/uiautomator.UiObjectNotFoundException/) || output.empty?
            log :info, "Error occurred while enabling location tags: #{output}"
            log :info, "Retrying to enable location tag"
            retry_count += 1
          else
            break  # Break the loop if the location tag is set successfully
          end
        end

        if retry_count == max_retries
          log :info, "Max retries reached. Error occurring while enabling the location tag."
          @zombie.push_logs('enable-location-tag-automation-fail', output, { 'device' => @device })
        end
      rescue StandardError => e
        log :info, "Error while enabling location tags: #{e.message} - #{e.backtrace}"
      end
      log :info, "Location tags completed. Duration: #{Time.now - start_time}"
    end

    private

    def log(level, msg)
      @logger.send(level.to_sym, "#{self.class} #{msg}")
    end
  end
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase.to_sym
  device_id = ARGV[1].to_s.strip
  helper = BrowserStack::EnableLocationTags.new(device_id, Logger.new($stdout))
  helper.send(command)
end
