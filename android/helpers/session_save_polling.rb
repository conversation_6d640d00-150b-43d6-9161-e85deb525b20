require 'browserstack_logger'
require 'json'
require 'websocket-client-simple'

require_relative '../constants'
require_relative '../lib/os_utils'
require_relative '../../common/helpers'

# currently supports device browser chrome
class SessionSavePolling
  def initialize(session_id, device_id, debugger_port, genre, setting_cookies)
    @session_id = session_id
    @device_id = device_id
    @debugger_port = debugger_port
    @genre = genre
    @eds = EDS.new({}, BrowserStack.logger)
    @history = {}
    @ws = nil
    @setting_cookies = setting_cookies
    @cookie_file_path = "/usr/local/.browserstack/state_files/#{@device_id}_cookie_data_from_s3.json"
    BrowserStack.logger.info("[SessionSavePolling] initialize #{@session_id}, #{@device_id}, #{@debugger_port}")
  end

  def setup_ws_connection

    max_retries = 60
    retry_count = 0
    page_tab = nil

    # Loop until we get a valid page tab or exceed max retries
    while retry_count < max_retries
      json_list = OSUtils.execute("curl http://localhost:#{@debugger_port}/json/list")
      BrowserStack.logger.info("[SessionSavePolling] fetching tabs: #{json_list}")

      unless json_list.empty?
        tabs = JSON.parse(json_list)
        page_tab = tabs.find { |tab| tab["type"] == "page" && tab["url"] != "chrome-native://newtab/" }

        if page_tab && page_tab["id"]
          BrowserStack.logger.info("[SessionSavePolling] found valid page tab with" \
            "ID: #{page_tab['id']} and URL: #{page_tab['url']}")
          break
        else
          BrowserStack.logger.info("[SessionSavePolling] found page tab but either missing ID" \
            " or has newtab URL")
        end
      end

      retry_count += 1
      if retry_count < max_retries
        BrowserStack.logger.info("[SessionSavePolling] no valid page tab found, " \
          "retrying (#{retry_count}/#{max_retries})...")
        sleep(1)
      else
        BrowserStack.logger.error("[SessionSavePolling] failed to find valid page tab " \
          "after #{max_retries} attempts")
        return false
      end
    end

    # Return false if we don't have a valid page tab with ID or if it's a new tab page
    return false unless page_tab && page_tab["id"] && page_tab["url"] != "chrome-native://newtab/"

    ws_url = "http://localhost:#{@debugger_port}/devtools/page/#{page_tab['id']}"
    BrowserStack.logger.info("[SessionSavePolling] setting up WebSocket connection to #{ws_url}")

    connection_established = false
    connection_error = nil

    WebSocket::Client::Simple.connect(ws_url) do |ws|
      @ws = ws

      ws.on :open do |_event|
        connection_established = true
        BrowserStack.logger.info("[SessionSavePolling] WS connection established for tab: #{page_tab['url']}")
      end

      ws.on :error do |error|
        connection_error = error
        BrowserStack.logger.error("[SessionSavePolling] WS connection error: #{error.message}")
      end

      ws.on :close do |_event|
        BrowserStack.logger.info("[SessionSavePolling] WS connection closed")
      end

      ws.on :message do |msg|
        BrowserStack.logger.debug("[SessionSavePolling] received message: #{msg.data}")
      end
    end

    # Wait for connection to be established
    timeout = Time.now + 5  # 5 second timeout
    until connection_established || connection_error
      sleep 0.1
      raise "[SessionSavePolling] WebSocket connection timeout after 5 seconds" if Time.now > timeout
    end

    raise "[SessionSavePolling] WebSocket connection failed: #{connection_error.message}" if connection_error

    raise "[SessionSavePolling] WebSocket connection not established" unless @ws

  rescue StandardError => e
    BrowserStack.logger.error("[SessionSavePolling] setup_ws_connection failed: #{e.message}")
    raise

  end

  def start
    # this is a safe check
    Timeout.timeout(MAX_TERMINAL_BLOCK_TIME) do
      Thread.bs_run do
        start_file = self.class.start_file(@device_id)
        FileUtils.touch(start_file)

        setup_ws_connection

        set_cookies if @ws&.open? && @setting_cookies

        while File.exist?(start_file)
          begin
            fetch_cookies
          rescue StandardError => e
            BrowserStack.logger.error("[SessionSavePolling] exception: #{e.message}")
            break
          end
          sleep(10)
        end

      ensure
        BrowserStack.logger.info("[SessionSavePolling] start ensure block")
        File.delete(start_file) if File.exist?(start_file)
      end
    end
  end

  class << self
    def start_file(device_id)
      "#{BrowserStack::STATE_FILES_DIR}/start_save_session_#{device_id}"
    end

    def running?(device_id)
      # check for start file, if exists then already running
      File.exist?(start_file(device_id))
    end
  end

  private

  def with_ws_connection
    if @ws.nil? || !@ws.open?
      BrowserStack.logger.info("[SessionSavePolling] WebSocket connection not active, reconnecting...")
      setup_ws_connection
    end
    yield @ws
  rescue StandardError => e
    BrowserStack.logger.error("[SessionSavePolling] WebSocket operation failed: #{e.message}")
    raise
  end

  def fetch_cookies
    with_ws_connection do |ws|
      url_received = false
      cookies_received = false
      current_url = nil
      cookie_path = @cookie_file_path

      # Set up message handlers before sending requests
      ws.on :message do |msg|
        response = JSON.parse(msg.data)

        # Handle URL response using Page.getFrameTree
        if response['id'] == 4999 && response['result'] && response['result']['frameTree']
          full_url = response['result']['frameTree']['frame']['url']
           # Strip URL to domain inline
          begin
            require 'uri'
            uri = URI.parse(full_url)
            current_url = "#{uri.scheme}://#{uri.host}"
          rescue URI::InvalidURIError => e
            BrowserStack.logger.error("[SessionSavePolling] error parsing URL: #{e.message}")
            current_url = full_url
          end
          BrowserStack.logger.info("[SessionSavePolling] current URL from DevTools: #{full_url}")
          BrowserStack.logger.info("[SessionSavePolling] stripped URL: #{current_url}")
          url_received = true
        end

        # Handle cookies response
        if response['id'] == 5000 && response['result'] && response['result']['cookies']
          if current_url
            cookies = response['result']['cookies']
            BrowserStack.logger.info("[SessionSavePolling] successfully retrieved #{cookies.length} cookies")

            existing_cookies = if File.exist?(cookie_path)
                                 JSON.parse(File.read(cookie_path))
                               else
                                 {}
                               end

            existing_cookies[current_url] = cookies

            FileUtils.mkdir_p(File.dirname(cookie_path))
            File.write(cookie_path, JSON.pretty_generate(existing_cookies))
            BrowserStack.logger.info("[SessionSavePolling] cookies saved to " \
              "#{cookie_path} for URL: #{current_url}")
          end
          cookies_received = true
        end
      rescue JSON::ParserError => e
        BrowserStack.logger.error("[SessionSavePolling] error parsing response: #{e.message}")
      end

      # First get the current URL using Page.getFrameTree
      get_url_msg = {
        id: 4999,
        method: "Page.getFrameTree"
      }.to_json

      BrowserStack.logger.info("[SessionSavePolling] sending Page.getFrameTree request")
      ws.send(get_url_msg)

      # Wait for URL with timeout
      timeout = Time.now + 5
      until url_received
        sleep 0.1
        next unless Time.now > timeout

        # Fallback to using json/list if getFrameTree fails
        json_list = OSUtils.execute("curl http://localhost:#{@debugger_port}/json/list")
        unless json_list.empty?
          tabs = JSON.parse(json_list)
          page_tab = tabs.find { |tab| tab["type"] == "page" }
          if page_tab
            full_url = page_tab["url"]
            current_url = strip_url_to_domain(full_url)
            BrowserStack.logger.info("[SessionSavePolling] current URL from json/list fallback: #{full_url}")
            BrowserStack.logger.info("[SessionSavePolling] stripped URL: #{current_url}")
            url_received = true
          end
        end
        break
      end

      return unless url_received # Don't proceed if we couldn't get the URL

      # Then get the cookies
      get_cookies_msg = {
        id: 5000,
        method: "Network.getCookies",
        params: {}
      }.to_json

      BrowserStack.logger.info("[SessionSavePolling] sending getCookies request")
      ws.send(get_cookies_msg)

      # Wait for cookies with timeout
      timeout = Time.now + 5
      until cookies_received
        sleep 0.1
        if Time.now > timeout
          BrowserStack.logger.error("[SessionSavePolling] timeout waiting for cookies response")
          break
        end
      end
    end
  rescue StandardError => e
    BrowserStack.logger.error("[SessionSavePolling] get_cookies error: #{e.message}")

  end

  # Helper method to strip URL to domain only
  def strip_url_to_domain(url)

    require 'uri'
    uri = URI.parse(url)
    "#{uri.scheme}://#{uri.host}"
  rescue URI::InvalidURIError => e
    BrowserStack.logger.error("[SessionSavePolling] error parsing URL: #{e.message}")
    url # Return original URL if parsing fails

  end

  def set_cookies
    # Check if the cookie file exists
    unless File.exist?(@cookie_file_path)
      BrowserStack.logger.info("[SessionSavePolling] no cookies file found at #{@cookie_file_path}")
      return
    end

    # Load all cookies from the file
    all_cookies_by_url = JSON.parse(File.read(@cookie_file_path))
    BrowserStack.logger.info("[SessionSavePolling] loaded cookies for " \
      "#{all_cookies_by_url.keys.length} URLs and path #{@cookie_file_path}")

    # Flatten all cookies into a single array
    all_cookies = []
    all_cookies_by_url.each do |_url, cookies|
      all_cookies.concat(cookies)
    end

    # Remove duplicate cookies (same name and domain)
    unique_cookies = {}
    all_cookies.each do |cookie|
      key = "#{cookie['name']}_#{cookie['domain']}"
      unique_cookies[key] = cookie
    end

    cookies_to_set = unique_cookies.values
    BrowserStack.logger.info("[SessionSavePolling] setting #{cookies_to_set.length} unique cookies")

    with_ws_connection do |ws|
      cookies_set = 0
      refresh_complete = false

      # Set up message handler before sending requests
      ws.on :message do |msg|
        response = JSON.parse(msg.data)

        if response['id'] >= 6000 && response['id'] < 7000
          if response['result'] && response['result']['success']
            cookies_set += 1
            BrowserStack.logger.info("[SessionSavePolling] successfully set cookie " \
              "#{cookies_set}/#{cookies_to_set.length}")
          else
            BrowserStack.logger.error("[SessionSavePolling] failed to set cookie: " \
              "#{response['error']&.fetch('message', 'Unknown error')}")
          end
        elsif response['id'] == 7000
          if response['result']
            refresh_complete = true
            BrowserStack.logger.info("[SessionSavePolling] page refresh completed successfully")
          else
            BrowserStack.logger.error("[SessionSavePolling] page refresh failed: " \
              "#{response['error']&.fetch('message', 'Unknown error')}")
          end
        end
      rescue JSON::ParserError => e
        BrowserStack.logger.error("[SessionSavePolling] error parsing set_cookies response: #{e.message}")
      end

      # Send all cookie set requests
      cookies_to_set.each_with_index do |cookie, index|
        set_cookie_msg = {
          id: 6000 + index,
          method: "Network.setCookie",
          params: cookie.transform_keys(&:to_sym).compact
        }.to_json

        ws.send(set_cookie_msg)
      end

      # Wait for all cookies to be set with timeout
      timeout = Time.now + 10
      until cookies_set >= cookies_to_set.length
        sleep 0.1
        next unless Time.now > timeout

        BrowserStack.logger.error("[SessionSavePolling] timeout waiting for cookies to be set. " \
          "Set #{cookies_set} out of #{cookies_to_set.length}")
        break
      end

      # Refresh the page after setting cookies
      refresh_msg = {
        id: 7000,
        method: "Page.reload",
        params: { ignoreCache: false }
      }.to_json

      BrowserStack.logger.info("[SessionSavePolling] refreshing page after setting cookies")
      ws.send(refresh_msg)

      # Wait for refresh to complete
      timeout = Time.now + 5  # 5 second timeout for refresh
      until refresh_complete
        sleep 0.1
        if Time.now > timeout
          BrowserStack.logger.error("[SessionSavePolling] timeout waiting for page refresh")
          break
        end
      end
    end

  rescue StandardError => e
    BrowserStack.logger.error("[SessionSavePolling] set_cookies error: #{e.message}")
    raise

  end

end
