# frozen_string_literal: true

require 'open3'

# <PERSON><PERSON> building and executing the ADB broadcast command for continuous scanning.
class BroadcastService
  def initialize(context, error_service)
    @context       = context
    @error_service = error_service
  end

  # Invokes the broadcast command to start continuous scanning session.
  #
  # @return [<PERSON><PERSON>an] true if successful, false if an error occurred
  def perform_broadcast_command(package_name, pusher, rule_engine_callback, mode, start_time)
    cmd_args = build_adb_broadcast_command(
      package_name: package_name,
      pusher: pusher,
      rule_engine_callback: rule_engine_callback,
      continuous_scanning: mode.eql?('on').to_s
    )
    # Log a joined string for debugging, but pass the array to capture3
    BrowserStack.logger.info("[APP_ACCESSIBILITY] Broadcast command is #{cmd_args.join(' ')}")

    # nosemgrep: ruby.lang.security.dangerous-exec.dangerous-exec
    # Reason: Executing 'adb' command. Arguments passed safely using array form (*cmd_args)
    # via Open3.capture3, preventing shell injection. Input validation for Android
    # broadcast intent extras is handled separately where applicable.
    stdout_str, stderr_str, status = Open3.capture3(*cmd_args) # nosem: ruby.lang.security.dangerous-exec.dangerous-exec
    !!(stdout_str =~ /Broadcast completed:\s+result=0/)
  rescue StandardError => e
    @error_service.handle_error(start_time, e.message)
    false
  end

  private

  # Constructs the adb broadcast command for continuous scanning session.
  def build_adb_broadcast_command(package_name:, pusher:, rule_engine_callback:, continuous_scanning:)
    [
      'adb', '-s', @context[:device_id], 'shell',
      'am', 'broadcast',
      '-a',  "#{AppAccessibility::WATCHER_PACKAGE}.APP_ACCESSIBILITY_SETUP_CONTINUOUS_SCANNING_SESSION",
      '--es', 'currentSessionId',           "'#{@context[:session_id]}'",
      '--es', 'currentReportId',            "'#{rule_engine_callback[:report_id]}'",
      '--es', 'foregroundPackageName',      "'#{package_name}'",
      '--es', 'pusherURL',                  "'#{pusher[:pusher_url]}'",
      '--es', 'pusherToken',                "'#{pusher[:pusher_token]}'",
      '--es', 'pusherChannel',              "'#{pusher[:pusher_channel]}'",
      '--es', 'bsUserId',                   "'#{rule_engine_callback[:bs_user_id]}'",
      '--es', 'ruleEngineSubdomain',        "'#{rule_engine_callback[:rule_engine_subdomain]}'",
      '--es', 'isContinuousScanning',       "'#{continuous_scanning}'"
    ]
  end
end

