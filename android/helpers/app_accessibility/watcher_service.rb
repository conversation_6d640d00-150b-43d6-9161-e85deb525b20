# frozen_string_literal: true

# Handles interactions with the "watcher" app on the device (e.g., checking logs, preparing data).
class WatcherService
  def initialize(context)
    @context = context
    @device_obj = @context[:device_obj]
    @adb        = @device_obj.adb
  end

  # Checks if the watcher process is running on the device.
  #
  # @return [<PERSON><PERSON><PERSON>]
  def running?
    output = @adb.shell("dumpsys activity processes | grep #{AppAccessibility::WATCHER_PACKAGE}")
    output.include?(AppAccessibility::WATCHER_PACKAGE)
  rescue StandardError => e
    BrowserStack.logger.error("watcher_running? check failed: #{e.message}")
    false
  end

  # Removes the main watcher log file.
  def remove_logs
    @adb.shell("run-as #{AppAccessibility::WATCHER_PACKAGE} rm #{AppAccessibility::WATCHER_LOG_FILE}")
  rescue StandardError => e
    BrowserStack.logger.error "Failed to remove watcher logs: #{e.message}"
  end

  # Prepares the data file structure on the device, then instructs watcher to capture DOM.
  def prepare_data
    base_path = "/data/user/0/#{AppAccessibility::WATCHER_PACKAGE}"
    ls_output = @adb.shell("run-as #{AppAccessibility::WATCHER_PACKAGE} ls #{base_path}").split("\n")
    unless ls_output.include?("files")
      @adb.shell("run-as #{AppAccessibility::WATCHER_PACKAGE} mkdir #{base_path}/files")
    end
    @adb.shell("run-as #{AppAccessibility::WATCHER_PACKAGE} touch #{base_path}/files/appa11y_session.txt")
    cmd = "am broadcast -a #{AppAccessibility::WATCHER_PACKAGE}.APP_ACCESSIBILITY_CAPTURE_DOM " \
      "--es sessionId '#{@context[:session_id]}'"
    @adb.shell(cmd)
  rescue StandardError => e
    BrowserStack.logger.error("Failed to prepare watcher data: #{e.message}")
  end

  def touch_app_a11y_session_file
    base_path = "/data/user/0/#{AppAccessibility::WATCHER_PACKAGE}"
    ls_output = @adb.shell("run-as #{AppAccessibility::WATCHER_PACKAGE} ls #{base_path}").split("\n")
    unless ls_output.include?("files")
      @adb.shell("run-as #{AppAccessibility::WATCHER_PACKAGE} mkdir #{base_path}/files")
    end
    @adb.shell("run-as #{AppAccessibility::WATCHER_PACKAGE} touch #{base_path}/files/appa11y_session.txt")
  rescue StandardError => e
    BrowserStack.logger.error("Failed to prepare watcher data: #{e.message}")
  end

  def touch_continuous_scanning_state_file
    @adb.shell("run-as #{AppAccessibility::WATCHER_PACKAGE} touch #{AppAccessibility::CS_STATE_FILE}")
  rescue StandardError => e
    BrowserStack.logger.error "Failed to touch continuous scanning file: #{e.message}"
  end

  # Fetches the watcher logs for debugging.
  # @return [String]
  def fetch_logs
    @adb.shell("run-as #{AppAccessibility::WATCHER_PACKAGE} cat #{AppAccessibility::WATCHER_LOG_FILE}")
  rescue StandardError => e
    BrowserStack.logger.error("Failed to fetch watcher logs: #{e.message}\n#{e.backtrace&.join('\n')}")
    ""
  end
end
