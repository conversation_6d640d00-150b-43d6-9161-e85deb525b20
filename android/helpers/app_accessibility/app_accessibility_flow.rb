# frozen_string_literal: true

# This module holds the flow methods for capturing and finalizing accessibility info.
# Including it in AppAccessibility reduces lines in the main class file.
module AppAccessibilityFlow
  private

  # Orchestrates capturing DOM data and finalizing the results for scanning.
  def handle_accessibility_flow(continuous_scanning, start_time)
    package_name = current_app_package_name
    app_details  = analyze_app(package_name)

    watcher.prepare_data
    return { 'success' => true } if continuous_scanning

    raw_json = dom_data_service.fetch_dom_data_and_parse
    if dom_data_service.dom_data_failure?(raw_json)
      return dom_data_service.handle_dom_data_failure(raw_json,
                                                      start_time)
    end

    finalize_accessibility_info(raw_json, package_name, app_details, start_time)
  end

  # Logs time, builds message, sends EDS data, handles async vs. sync.
  def finalize_accessibility_info(raw_json, package_name, app_details, start_time)
    end_time  = current_time_ms
    duration  = end_time - start_time
    log("Time taken to process app accessibility info: #{duration} ms")

    screen_orientations = orientation_service.screen_orientation_check
    msg = message_service.build_message(raw_json, app_details, package_name, screen_orientations)

    @context[:eds_reporter].send_data_to_eds("accessibility-info", duration, "pass", msg)

    if @context[:async_mode]
      notify_results(
        AppAccessibility::APP_A11Y_AUTOMATE_URL,
        message_service.message_with_full_data(raw_json, msg, app_details),
        @context[:automate_obj]
      )
      { 'success' => true }
    else
      message_service.message_with_full_data(raw_json, msg, app_details)
    end
  end

  # Builds the final method response hash for broadcast calls.
  def build_response(continuous_scanning, app_details, broadcast_success)
    {
      continuous_scanning: continuous_scanning,
      app_details: app_details,
      broadcast_success: broadcast_success
    }
  end

  # Logs watcher-not-running error and returns a response.
  def watcher_not_running_response(start_time, app_details)
    log("Android Watcher not running")
    error_service.handle_error(start_time, "Android Watcher not running")
    build_response(false, app_details, false)
  end

  # Returns standard response hash for older devices.
  def device_not_supported_response
    { success: 'false', message: 'device version not supported' }
  end
end
