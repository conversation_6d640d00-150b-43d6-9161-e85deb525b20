# frozen_string_literal: true

# Responsible for sending events/data to EDS (Event Delivery System).
class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def initialize(data_reporter, context)
    @data_reporter = data_reporter
    @context       = context
  end

  # Sends data to EDS.
  #
  # @param event_name   [String]
  # @param time         [Integer] elapsed time in ms
  # @param event_status [String]  e.g., "pass" or "fail"
  # @param message      [Hash,String]
  def send_data_to_eds(event_name, time, event_status, message = "")
    @data_reporter.log_to_eds(build_eds_payload(event_name, time, event_status, message))
  rescue StandardError => e
    BrowserStack.logger.error "Failed to send data to EDS: #{e.message}\n#{e.backtrace&.join("\n")}"
  end

  private

  # Builds the payload for EDS.
  #
  # @param event_name   [String]
  # @param time         [Integer]
  # @param event_status [String]
  # @param message      [String,Hash]
  #
  # @return [Hash]
  def build_eds_payload(event_name, time, event_status, message)
    {
      product: 'app-accessibility',
      team: 'app-accessibility-team',
      event_json: event_json_payload(time, message, event_status),
      event_name: event_name
    }
  end

  # Builds the event_json part of the payload.
  #
  # @return [Hash]
  def event_json_payload(time, message, event_status)
    {
      team: 'app-accessibility-team',
      time: time,
      message: message,
      is_async_mode: @context[:async_mode],
      test_run_uuid: @context[:automate_obj]['test_run_uuid'],
      th_build_uuid: @context[:automate_obj]['th_build_uuid'],
      event_status: event_status,
      watcher_performance_stats: @context[:performance_stats]
    }
  end
end
