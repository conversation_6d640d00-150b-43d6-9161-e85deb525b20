# frozen_string_literal: true

require_relative 'app_accessibility_initializer'
require_relative 'orientation_service'
require_relative 'dom_data_service'
require_relative 'error_service'
require_relative 'message_service'
require_relative 'broadcast_service'
require_relative 'app_accessibility_flow'
require_relative 'app_analyzer'
require_relative '../../constants'

require 'json'
require 'base64'
require 'open3'
require 'fileutils'
require 'tempfile'
require 'English'
require 'securerandom'

# This class orchestrates capturing and reporting accessibility info
# from the connected Android device using an android app which is granted accessibility service on Browserstack devices.
class AppAccessibility
  WATCHER_PACKAGE       = "com.browserstack.watcher" # the android app, which is granted accessibility service
  WATCHER_LOG_FILE      = "/data/user/0/#{WATCHER_PACKAGE}/files/appa11y.log"
  APP_A11Y_AUTOMATE_URL = 'https://app-accessibility-service.browserstack.com/automate/api/v1/scan/notify-results'
  DIR_HOME = '/usr/local/.browserstack'
  APP_ACCESSIBILITY_CONF_PATH = "#{DIR_HOME}/mobile/keys/app_a11y/app_a11y_auth.json"
  CS_STATE_FILE = "#{WATCHER_WORKING_DIR}/#{CONTINUOUS_SCANNING_STATE_FILE}"

  include AppAccessibilityInitializer
  include AppAccessibilityFlow

  def initialize(device_id,
                 session_id, product = "", first_scan: false,
                 automate_obj: {})

    initialize_context(device_id, session_id, product, first_scan, automate_obj)
  end

  # Captures accessibility info. Main entry point for manual scanning.
  def capture_accessibility_info(continuous_scanning: false)
    start_time = current_time_ms
    log("Getting accessibility info")

    return device_not_supported_response if device_version_unsupported?
    return watcher_not_running_response(start_time, {}) unless watcher.running?

    handle_accessibility_flow(continuous_scanning, start_time)
  rescue StandardError => e
    error_service.handle_standard_error(e, start_time)
  ensure
    watcher.remove_logs
  end

  # Broadcast setup to the watcher. Main entry point for continuous scanning.
  def broadcast_continuous_scanning_session(pusher, rule_engine_callback, mode)
    start_time = setup_continuous_scanning
    if mode.eql?('on')
      package_name, app_details = fetch_package_and_details
    else
      app_details = {}
    end

    return watcher_not_running_response(start_time, app_details) unless watcher.running?

    watcher.touch_continuous_scanning_state_file
    success = perform_broadcast(package_name, pusher, rule_engine_callback, mode, start_time)
    build_response(true, app_details, success)
  end

  # Asynchronously notifies results to an external URL using fork.
  def notify_results(url, res_json, automate_obj)
    notifier.notify_results(url, res_json, automate_obj)
  end

  private

  ##########################################################
  #                  HELPER ACCESSORS
  ##########################################################

  def watcher
    @context[:watcher]
  end

  def error_service
    @context[:error_service]
  end

  def dom_data_service
    @context[:dom_data_service]
  end

  def orientation_service
    @context[:orientation_service]
  end

  def message_service
    @context[:message_service]
  end

  def broadcast_service
    @context[:broadcast_service]
  end

  def notifier
    @context[:notifier]
  end

  # Returns current time in milliseconds
  def current_time_ms
    (Time.now.to_f * 1000).to_i
  end

  # Logs a message with a standard prefix
  def log(message)
    BrowserStack.logger.info("[APP_ACCESSIBILITY] #{message}")
  end

  # Checks if the device version is unsupported (<11).
  def device_version_unsupported?
    @context[:device_obj].os_version.to_i < 11
  end

  # Gets the device's current app package name.
  def current_app_package_name
    adb = @context[:device_obj].adb
    result = adb.shell("dumpsys window | grep -E 'mCurrentFocus|mFocusedApp' | awk -F'[ /]' '{print $5}'")
    result.split("\n").last
  rescue StandardError => e
    BrowserStack.logger.error("Failed to get current app package name: #{e.message}")
    ""
  end

  # Analyzes the app details using a custom AppAnalyzer.
  def analyze_app(package_name)
    details = AppAnalyzer.new(@context[:device_id], package_name).start_analysis
    details.is_a?(Array) ? details.first : details
  end

  def setup_continuous_scanning
    start_time = current_time_ms
    log("Setting up device for continuous accessibility scans")
    start_time
  end

  def fetch_package_and_details
    package_name = current_app_package_name
    app_details  = analyze_app(package_name)
    [package_name, app_details]
  end

  def perform_broadcast(package_name, pusher, rule_engine_callback, mode, start_time)
    broadcast_service.perform_broadcast_command(
      package_name,
      pusher,
      rule_engine_callback,
      mode,
      start_time
    )
  end
end
