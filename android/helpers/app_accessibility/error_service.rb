# frozen_string_literal: true

# Handles error logging, sending EDS data for failures, and zombie pushes.
class ErrorService
  def initialize(context, eds_reporter)
    @context      = context
    @eds_reporter = eds_reporter
  end

  # Logs the error, sends event data, and triggers a zombie push to indicate a failure.
  def handle_error(start_time_ms, message)
    end_time_ms = current_time_ms
    log("Exception: #{message}")
    truncated_msg = message.to_s[0, 500]
    @eds_reporter.send_data_to_eds("accessibility-info", end_time_ms - start_time_ms, "fail", truncated_msg)
    zombie_push("android", "accessibility-info-failure", truncated_msg[0, 250])
  end

  # Logs and handles a standard error (used in capture_accessibility_info).
  def handle_standard_error(error, start_time)
    handle_error(start_time, error.message)
    logs = @context[:watcher].fetch_logs  # or you can pass @watcher as a dependency
    handle_error(start_time, "watcher_logs = #{logs}")
    {}
  end

  private

  # Simple stub method for "zombie push" in case of failures.
  def zombie_push(platform, event_name, message)
    BrowserStack.logger.error("[ZOMBIE_PUSH] platform=#{platform}, event=#{event_name}, msg=#{message}")
  end

  # Logs a message with a standard prefix.
  def log(message)
    BrowserStack.logger.info("[ERROR_SERVICE] #{message}")
  end

  # Returns current time in milliseconds
  def current_time_ms
    (Time.now.to_f * 1000).to_i
  end
end
