# frozen_string_literal: true

require 'base64'
require 'fileutils'
require 'open3'
require 'set'
require_relative '../../constants'
require 'android_toolkit'
require 'nokogiri'
require 'zip' # To extract the icon file and list contents

class AppA<PERSON>yzer
  def initialize(device_id, package_name)
    @device = device_id
    @package_name = package_name
    @adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
  end

  # (1) Main entry point to start analyzing
  def start_analysis
    # 1. Pull all APKs for the package name
    apk_paths = pull_apks_from_device
    result = {
      os: 'android',
      deviceInfo: {
        sdkVersion: @adb.shell('getprop ro.build.version.sdk'),
        displayDensity: @adb.shell("wm density | awk '/Physical density/{print $3}'").to_f / 160
      },
      bundle_id: nil,
      app_name: nil,
      app_version: nil,
      is_flutter: false,
      is_react_native: false,
      app_icon: nil,
      supported_orientations: Set.new
    }

    # 2. Analyze each pulled APK
    apk_paths.each do |apk_path|
      r = analyze_apk(apk_path)
      result[:bundle_id] = r[:package_name] if result[:package_name].nil?
      result[:app_name] = r[:app_name] if result[:app_name].nil?
      result[:app_version] = r[:app_version] if result[:app_version].nil?
      result[:is_flutter] ||= r[:is_flutter]
      result[:is_react_native] ||= r[:is_react_native]
      result[:app_icon] = r[:app_icon] if result[:app_icon].nil?
      result[:supported_orientations].merge(r[:supported_orientations].split(','))
    end

    result[:supported_orientations] = result[:supported_orientations].to_a.join(',')
    result
  end

  private

  # (2) Pull single / multiple APKs from the device for a package
  def pull_apks_from_device
    output_dir = File.join(BrowserStack::STATE_FILES_DIR, "app_a11y")
    FileUtils.mkdir_p(output_dir)

    pm_output = @adb.shell("pm path #{@package_name}")
    remote_apk_paths = pm_output.strip.split("\n").map { |line| line.sub('package:', '').strip }

    local_apk_paths = []
    remote_apk_paths.each_with_index do |remote_path, idx|
      local_path = File.join(output_dir, "#{@package_name}-#{idx}.apk")
      system("adb -s #{@device} pull #{remote_path} #{local_path}")
      local_apk_paths << local_path
    end

    local_apk_paths
  end

  # (3) Analyze a single APK via AAPT2 and minimal Ruby tooling
  def analyze_apk(apk_path)
    # 3a) Extract package info, label, icon path from "aapt2 dump badging"
    pkg_info  = dump_badging_info(apk_path)
    p_name    = pkg_info[:package_name]
    v_name    = pkg_info[:version_name]
    app_label = pkg_info[:app_label]
    icon_path = pkg_info[:icon_path]  # e.g. "res/mipmap-xxxhdpi/ic_launcher.png"

    # 3b) If we got an icon path, extract it from the APK and base64-encode
    icon_data = icon_path ? extract_file_from_zip(apk_path, icon_path) : nil
    icon_b64  = icon_data ? Base64.strict_encode64(icon_data) : nil

    # 3c) File list for detecting Flutter / React Native
    file_list       = zip_file_list(apk_path)
    is_flutter      = flutter_detection?(file_list)
    is_react_native = react_native_detection?(file_list)

    # 3d) Orientation values from "aapt2 dump xmltree"
    orientation_vals = extract_screen_orientations(apk_path)

    {
      bundle_id: p_name,
      app_name: app_label,
      app_version: v_name,
      is_flutter: is_flutter,
      is_react_native: is_react_native,
      app_icon: icon_b64,
      supported_orientations: orientation_vals
    }
  end

  # (4) Use "aapt2 dump badging" to parse:
  #     package name, versionName, application-label, application-icon line, etc.
  #
  # Example line from aapt2 dump badging:
  #
  #  package: name='com.example.myapp' versionCode='8' versionName='1.2.3'
  #  application-label:'My App'
  #  application-icon-160:'res/mipmap-mdpi-v4/ic_launcher.png'
  #
  def dump_badging_info(apk_path)
    # nosemgrep: ruby.lang.security.dangerous-exec.dangerous-exec
    # Reason: Command path (BrowserStack::AAPT2) is a trusted internal constant.
    # Arguments are passed safely using the array form (*args) to prevent injection.
    output, err, status = Open3.capture3( # nosem: ruby.lang.security.dangerous-exec.dangerous-exec
      BrowserStack::AAPT2,
      'dump',
      'badging',
      apk_path
    )

    BrowserStack.logger.error("AAPT2 badging error: #{err}") if !err.nil? && !err.strip.empty?

    package_name = nil
    version_name = nil
    app_label    = nil
    icon_path    = nil

    output.each_line do |line|
      line.strip!

      # Parse package name and versionName
      # e.g. "package: name='com.example.myapp' versionCode='8' versionName='1.2.3'"
      if line.start_with?("package:")
        package_name = Regexp.last_match(1) if line =~ /name='([^']+)'/
        version_name = Regexp.last_match(1) if line =~ /versionName='([^']+)'/
      end

      # application-label:'My App'
      if line.start_with?("application-label:") && line =~ /application-label:'([^']+)'/
        app_label = Regexp.last_match(1)
      end

      # application-icon-...:'res/mipmap-xxxhdpi/ic_launcher.png'
      if (line.start_with?("application-icon-") ||
        line.start_with?("application: label=")) && (m = line.match(/'([^']+)'/))
        icon_path = m[1]
      end

      icon_path = Regexp.last_match(1) if line.start_with?("application: label=") && line =~ /icon='([^']*)'/
    end

    {
      package_name: package_name,
      version_name: version_name,
      app_label: app_label,
      icon_path: icon_path
    }
  end

  # (5) Extract a single file from the APK (which is a ZIP) by path
  def extract_file_from_zip(apk_path, file_path)
    Zip::File.open(apk_path) do |zip|
      entry = zip.find_entry(file_path)
      return entry.get_input_stream.read if entry
    end
    nil
  end

  # (6) Return a list of all files from the APK, for scanning patterns
  def zip_file_list(apk_path)
    files = []
    Zip::File.open(apk_path) do |zip|
      zip.each { |entry| files << entry.name }
    end
    files
  end

  # (7) Check for Flutter presence
  def flutter_detection?(file_list)
    file_list.any? do |path|
      path.match?(/libflutter\.so/) || path.match?(%r{assets/flutter_assets}i)
    end
  end

  # (8) Check for React Native presence
  def react_native_detection?(file_list)
    file_list.any? do |path|
      path.match?(/libreactnativejni\.so/) || path.match?(/index\.android\.bundle/i)
    end
  end

  # (9) Extract screen orientation from the AndroidManifest.xml
  #     using "aapt2 dump xmltree <apk> --file AndroidManifest.xml"
  #
  #     Returns a comma-separated list of orientation numeric values (e.g. "1,13")
  def extract_screen_orientations(apk_path)
    return "" unless File.exist?(apk_path)

    # Build an argument array instead of a single string
    cmd_args = [
      BrowserStack::AAPT2,
      'dump',
      'xmltree',
      apk_path,
      '--file',
      'AndroidManifest.xml'
    ]

    orientation_values = Set.new

    # nosemgrep: ruby.lang.security.dangerous-exec.dangerous-exec
    # Reason: Executing 'adb' command. Arguments passed safely using array form (*cmd_args)
    # via Open3.capture3, preventing shell injection. Input validation for Android
    # broadcast intent extras is handled separately where applicable.
    Open3.popen3(*cmd_args) do |_, stdout, stderr, _wait_thr| # nosem: ruby.lang.security.dangerous-exec.dangerous-exec
      stdout.each_line do |line|
        next unless line.include?('android:screenOrientation')

        match = line.scan(/=(\d+)/).flatten.first
        orientation_values << match if match
      end

      err_msg = stderr.read.strip
      BrowserStack.logger.error("Error executing AAPT2 xmltree: #{err_msg}") unless err_msg.empty?
    end

    orientation_values.to_a.join(',')
  end
end

# -----------------------
# Example Usage:
# require_relative 'android/helpers/app_analyzer'
# analyzer = AppAnalyzer.new("RTX73945623", "com.example.myapp")
# results = analyzer.start_analysis
# puts results.inspect
#
# Typical return value:
# {
#   bundle_id: "com.example.myapp",
#   app_name: "My App",
#   app_version: "1.2.3",
#   is_flutter: true,
#   is_react_native: false,
#   app_icon: "iVBORw0KGgoAAAANSUhEUgAA...",
#   supported_orientations: "1,13"
# }
