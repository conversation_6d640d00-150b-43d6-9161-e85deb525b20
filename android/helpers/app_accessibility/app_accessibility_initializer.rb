# frozen_string_literal: true

require_relative '../../models/android_device'
require 'android_toolkit'

require_relative 'orientation_service'
require_relative 'dom_data_service'
require_relative 'error_service'
require_relative 'message_service'
require_relative 'broadcast_service'
require_relative 'app_accessibility_flow'
require_relative '../../data_reporter'
require_relative 'eds_reporter'
require_relative 'watcher_service'
require_relative 's3_uploader'
require_relative 'notifier'

# This module extracts the initialization logic from AppAccessibility,
# reducing the main class's line count.
module AppAccessibilityInitializer
  def initialize_context(device_id, session_id, product, first_scan, automate_obj)
    @context = {
      device_id: device_id,
      session_id: session_id,
      product: product,
      first_scan: first_scan,
      automate_obj: automate_obj,
      async_mode: automate_obj['async_mode'],
      performance_stats: {}
    }

    device_obj = BrowserStack::AndroidDevice.new(
      device_id,
      self.class.name,
      BrowserStack.logger
    )
    @context[:device_obj] = device_obj

    data_reporter = DataReporter.new(
      session_id: session_id,
      device: device_id,
      product: product,
      eds_event_type: 'web_events'
    )
    @context[:data_reporter] = data_reporter

    @context[:eds_reporter]         = EDSReporter.new(data_reporter, @context)
    @context[:watcher]              = WatcherService.new(@context)
    @context[:s3_uploader]          = S3Uploader.new(@context)
    @context[:notifier]             = Notifier.new(@context, @context[:s3_uploader])
    @context[:dom_data_service]     = DomDataService.new(@context, @context[:eds_reporter])
    @context[:orientation_service]  = OrientationService.new(@context)
    @context[:error_service]        = ErrorService.new(@context, @context[:eds_reporter])
    @context[:message_service]      = MessageService.new(@context)
    @context[:broadcast_service]    = BroadcastService.new(@context, @context[:error_service])
  end
end
