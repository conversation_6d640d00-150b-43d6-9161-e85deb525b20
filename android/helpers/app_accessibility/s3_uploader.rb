# frozen_string_literal: true

require 'aws-sdk-s3'

# Handles uploading files (screenshots, icons, etc.) to S3.
class S3Uploader
  def initialize(context)
    @context = context
    init_config if @context[:async_mode]
  end

  def init_config
    app_a11y_conf = JSON.parse(File.read(AppAccessibility::APP_ACCESSIBILITY_CONF_PATH))
    @s3_access_key = app_a11y_conf["s3_access_key"]
    @s3_access_secret = app_a11y_conf["s3_access_secret"]
    @s3_bucket_name = app_a11y_conf["s3_bucket_name"]
    @s3_bucket_region = app_a11y_conf["s3_bucket_region"]
  rescue StandardError => e
    log("Exception while init_config Exception : #{e.inspect}")
  end

  # Uploads a file to S3 with public-read ACL.
  #
  # @param s3_key       [String] S3 key path
  # @param file_content [String] Raw (decoded) file content
  # @param automate_obj [Hash]   Must include region, s3_access_key, s3_secret_key, s3_bucket
  def upload_to_s3(s3_key, file_content, automate_obj)
    s3 = Aws::S3::Client.new(
      region: automate_obj['region'] || @s3_bucket_region,
      access_key_id: automate_obj['s3_access_key'] || @s3_access_key,
      secret_access_key: automate_obj['s3_secret_key'] || @s3_access_secret
    )

    s3.put_object(
      bucket: automate_obj['s3_bucket'] || @s3_bucket_name,
      key: s3_key,
      body: file_content,
      content_type: 'image/jpeg',
      acl: 'public-read'
    )
    log("File uploaded successfully with public-read access!")
  rescue Aws::S3::Errors::ServiceError => e
    log("Failed to upload file to S3: #{e.message}")
  end

  private

  # Logs a message with a standard prefix.
  #
  # @param message [String]
  def log(message)
    BrowserStack.logger.info("[S3Uploader] #{message}")
  end
end
