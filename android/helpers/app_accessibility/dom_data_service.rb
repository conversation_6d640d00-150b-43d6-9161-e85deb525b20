# frozen_string_literal: true

require 'json'

# Handles fetching + parsing DOM data from the device, as well as performance stats.
class DomDataService
  def initialize(context, eds_reporter)
    @context      = context
    @device_obj   = @context[:device_obj]
    @eds_reporter = eds_reporter
  end

  # Fetches DOM data and performance stats from the device, then parses JSON.
  #
  # @return [Hash] parsed JSON from the watcher data
  def fetch_dom_data_and_parse
    raw_data = fetch_watcher_data
    performance_data = fetch_performance_stats
    @context[:performance_stats] = JSON.parse(performance_data, max_nesting: 400)["performance_stats"]
    remove_dom_from_device
    JSON.parse(raw_data, max_nesting: 400)
  end

  # Returns true if the raw_json indicates a failure or missing DOM data.
  def dom_data_failure?(raw_json)
    raw_json.key?("success") && !raw_json["success"]
  end

  # Handles scenario where DOM data was unsuccessful or duplicated.
  #
  # @return [Hash] either a duplicate indicator if async, or the raw_json
  def handle_dom_data_failure(raw_json, start_time)
    end_time = current_time_ms
    @eds_reporter.send_data_to_eds("accessibility-info", end_time - start_time, "pass", raw_json)
    return { 'duplicate': true } if @context[:async_mode]

    raw_json
  end

  private

  # Fetches the watcher data file content.
  def fetch_watcher_data
    cmd = "run-as #{AppAccessibility::WATCHER_PACKAGE} cat \
      /data/user/0/#{AppAccessibility::WATCHER_PACKAGE}/files/appa11y_dom_tree.txt"
    @device_obj.adb.shell(cmd)
  rescue StandardError => e
    BrowserStack.logger.error("Failed to fetch watcher data: #{e.message}")
    "{}"
  end

  # Fetches additional performance stats from the device.
  def fetch_performance_stats
    cmd = "run-as #{AppAccessibility::WATCHER_PACKAGE} cat \
       /data/user/0/#{AppAccessibility::WATCHER_PACKAGE}/files/appa11y_session_hash.json"
    @device_obj.adb.shell(cmd)
  end

  # Removes the DOM file from the device to avoid stale data.
  def remove_dom_from_device
    cmd = "run-as #{AppAccessibility::WATCHER_PACKAGE} rm \
      /data/user/0/#{AppAccessibility::WATCHER_PACKAGE}/files/appa11y_dom_tree.txt"
    @device_obj.adb.shell(cmd)
  rescue StandardError => e
    BrowserStack.logger.error("Failed to remove DOM file: #{e.message}")
  end

  # Returns current time in milliseconds
  def current_time_ms
    (Time.now.to_f * 1000).to_i
  end
end
