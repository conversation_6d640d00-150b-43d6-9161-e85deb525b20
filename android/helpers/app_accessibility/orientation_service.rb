# frozen_string_literal: true

# Handles screen orientation checks on the device.
class OrientationService
  def initialize(context)
    @context = context
  end

  # Iterates through possible user_rotation settings to find supported orientations.
  # Returns a concatenated String of distinct orientations (e.g.,"0123").
  def screen_orientation_check
    return "" if @context[:async_mode]

    adb = @context[:device_obj].adb
    original_value = adb.shell("settings get system user_rotation")

    orientation_values = (0..3).map do |i|
      adb.shell("settings put system user_rotation #{i}")
      sleep(0.5)
      orientation_line = adb.shell("dumpsys display | grep 'orientation' | awk '{print $7}'").chomp
      orientation_line.split("=").last
    end.compact

    orientation_values.uniq.join('')
  rescue StandardError => e
    BrowserStack.logger.error("Failed to check screen orientation: #{e.message}")
    ""
  ensure
    adb.shell("settings put system user_rotation #{original_value}") if original_value
  end
end
