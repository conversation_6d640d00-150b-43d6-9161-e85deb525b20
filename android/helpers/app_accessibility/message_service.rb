# frozen_string_literal: true

# Builds the final message payload(s) for EDS or further processing.
class MessageService
  def initialize(context)
    @context = context
  end

  # Builds the final message hash from the raw JSON and app data.
  def build_message(raw_json, app_details, package_name, supported_orientations)
    {
      os: "android",
      sessionId: @context[:session_id],
      deviceInfo: raw_json["deviceInfo"],
      metaData: raw_json["metaData"],
      ocrData: raw_json["ocrData"],
      supported_orientations: app_details[:app_orientations],
      supported_screen_orientations: supported_orientations,
      app_name: app_details[:app_name],
      bundle_id: package_name,
      app_version: app_details[:app_version],
      is_flutter: app_details[:is_flutter],
      is_react_native: app_details[:is_react_native]
    }
  end

  # Merges additional fields (data, screenshot, app_icon) into the base message.
  def message_with_full_data(raw_json, base_msg, app_details)
    merged_msg = base_msg.dup
    merged_msg[:data]       = raw_json["data"]
    merged_msg[:screenshot] = raw_json["screenshot"]
    merged_msg[:app_icon]   = app_details[:app_icon] if @context[:first_scan]
    merged_msg
  end
end
