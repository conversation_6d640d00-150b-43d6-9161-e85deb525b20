#!/bin/bash
set -x
TODO=$1
DEVICEID=$2

WORK_DIR="/usr/local/.browserstack"
STATE_FILES_DIR="/usr/local/.browserstack/state_files"
DRIVER_ACTIONS="/usr/local/.browserstack/mobile/android/driver_actions.sh"
LIVE_SCRIPT="/usr/local/.browserstack/mobile/android/live/scripts/live_actions.sh"
CONFIG="/usr/local/.browserstack/config"
FILE_UPLOAD_DIRECTORY="/sdcard/automate_uploads/"
TEMP_STATUSBAR_FILE="/data/local/tmp/check_statusbar"
SESSION_START_DIR="$STATE_FILES_DIR/session_start"
DATA_LOCAL_TMP="/data/local/tmp"
DEVICELOGS_FILE="/var/log/browserstack/app_log_$DEVICEID.log"
DUP_DEVICELOGS_FILE="/tmp/app_log_$DEVICEID.log"
CRASHLOGS_FILE="/var/log/browserstack/app_crash_$DEVICEID.log"
PARSED_CRASHLOGS_FILE="/tmp/app_crash_$DEVICEID.log"
APP_SESSION_FILE="/tmp/app_session_$DEVICEID"
MOBILE_PACKAGE_FILE="/sdcard/app_package_name"
FLOW_TO_HAR="/usr/local/.browserstack/mobile/android/scripts/har_dump.py"
ZOMBIE="/usr/local/.browserstack/mobile/common/push_to_zombie.rb"
VIDEO_RECORDING_SCRIPT="/usr/local/.browserstack/mobile/android/helpers/android_video_recording.sh"
INTERACTION_CONFIG_FILE="/usr/local/.browserstack/mobile/android/live/interaction_server/device_config.json"
DUPLICATE_SESSION_SUMMARY_FILE="/tmp/duplicate_session_$DEVICEID"

mkdir -p /usr/local/.browserstack/automate_screenshot

source /usr/local/.browserstack/mobile/android/common.sh
source /usr/local/.browserstack/mobile/android/check_status_bar_management.sh
source /usr/local/.browserstack/mobile/android/helpers/version_comparison.sh

kill_mitm() {
  MITMPROXY_LISTEN_PORT=`cat /tmp/mitmdump_port_$DEVICEID`
  if [[ ! -z $MITMPROXY_LISTEN_PORT ]]; then
    MITM_PIDS=`ps aux | grep mitmdump | grep $MITMPROXY_LISTEN_PORT | grep -v grep | awk '{print $2}' | xargs kill -9`
  fi
}

log() {
  echo "`date -u`: "$@ >> /var/log/browserstack/selenium_$DEVICEID.log
}

start_minicap() {
  STREAMING_ARGS=$(cat $CONFIG/streaming_params_$DEVICEID)

  if [ -f "$INTERACTION_CONFIG_FILE" ] && jq -e ".deviceConfigurations[\"$device_model\"]" "$INTERACTION_CONFIG_FILE" > /dev/null; then
    stream_width=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_width" "$INTERACTION_CONFIG_FILE")
    stream_height=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_height" "$INTERACTION_CONFIG_FILE")
  else
    echo "$device_model not found in $INTERACTION_CONFIG_FILE or file absent. Falling back to STREAMING_ARGS."
    stream_width=$(echo "$STREAMING_ARGS" | grep -o 'streaming_width=[^ ]*' | cut -d '=' -f2)
    stream_height=$(echo "$STREAMING_ARGS" | grep -o 'streaming_height=[^ ]*' | cut -d '=' -f2)
  fi

  width_height="${stream_width}x${stream_height}"
  real_size=$(adb -s $DEVICEID shell 'wm size' | grep 'Physical size' | awk -F ': ' '{ print $2 }' | tr -d "\n\r")
  adb -s $DEVICEID shell "cd /data/local/tmp; CLASSPATH=/data/local/tmp/minicap-debug.apk app_process /system/bin io.devicefarmer.minicap.Main -P $real_size@$width_height/0 -t &"  >> /var/log/browserstack/screencap_$DEVICEID.log &
}

start_screencap() {
  if [[ -z $device_model ]]; then
      warn "No device_model. Trying to fetch..."
      get_device_model
  fi

  STREAMING_ARGS=$(cat $CONFIG/streaming_params_$DEVICEID)

  if [ -f "$INTERACTION_CONFIG_FILE" ] && jq -e ".deviceConfigurations[\"$device_model\"]" "$INTERACTION_CONFIG_FILE" > /dev/null; then
    stream_width=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_width" "$INTERACTION_CONFIG_FILE")
    stream_height=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_height" "$INTERACTION_CONFIG_FILE")
  else
    echo "$device_model not found in $INTERACTION_CONFIG_FILE or file absent. Falling back to STREAMING_ARGS."
    stream_width=$(echo "$STREAMING_ARGS" | grep -o 'streaming_width=[^ ]*' | cut -d '=' -f2)
    stream_height=$(echo "$STREAMING_ARGS" | grep -o 'streaming_height=[^ ]*' | cut -d '=' -f2)
  fi

  interaction_ags="$stream_width $stream_height"

  adb -s $DEVICEID shell <<__EOF &
    cd $DATA_LOCAL_TMP
    sh start_binary.sh $DATA_LOCAL_TMP/bs_screencap "$interaction_ags debug" &
    cd /
__EOF
}

set_device_orientation(){
  adb -s $DEVICEID shell am startservice --user 0 -n com.android.browserstack/.services.OrientationService --es "orientation" "$1"
}

set_timezone(){
  if device_is_a bsrun_device; then
    run_as_root "setprop persist.sys.timezone $1"
  else
    adb -s $DEVICEID shell service call alarm 3 s16 $1
  fi
}

get_device_orientation(){
  # String echoed is written to file
  get_os_version
  if vers_lt "$OS_VERSION" "13"; then
    rotation_angle=`timeout 10 adb -s $DEVICEID shell dumpsys input | grep 'SurfaceOrientation' | awk '{ print $2 }' | tr -d '[[:space:]]'`
  else
    rotation_angle=`timeout 10 adb -s $DEVICEID shell dumpsys input | grep -Eoi -m 1  'viewport internal.*orientation=[0-9]' | awk '{print $NF}' | tr -d -c 0-9 |  tr -d '[[:space:]]'`
  fi

  if [ $(( $rotation_angle % 2 )) = 0 ]
  then
    echo "portrait"
  else
    echo "landscape"
  fi
}

archive_and_truncate_appium_logs() {
  DEVICE=$1
  APPIUM_LOGS="/var/log/browserstack/appium_${DEVICEID}.log"
  APPIUM_LOGS_ARCHIVE="/var/log/browserstack/appium_${DEVICEID}.log.archive"
  cat $APPIUM_LOGS >> $APPIUM_LOGS_ARCHIVE
  truncate -s 0 $APPIUM_LOGS
}

upload_network_logs_chrome_har() {
  HAR_FILE="/tmp/har_file_${DEVICEID}_${SESSION_ID}.har"
  echo "Trying to upload network logs, present in $HAR_FILE with SESSION_ID ${SESSION_ID}, REGION $AWS_REGION, BUCKET $AWS_BUCKET, GENRE $GENRE, preferred AWS_STORAGE_CLASS $AWS_STORAGE_CLASS"

  if [ -f $HAR_FILE ]; then
    echo "Network logs found."
    s3_url="https://s3$AWS_REGION.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$SESSION_ID-har-logs.txt"
    FILE_SIZE_KB=$(expr $(ls -l $HAR_FILE  | awk '{print $5}') / 1024)
    # timeout 100 /usr/local/bin/s3curl --id=$AWS_KEY --key=$AWS_SECRET --contentType "text/json" --put=$HAR_FILE -- -H "x-amz-acl: public-read" $s3_url
    FILE_SIZE_BYTES=$(ls -l $HAR_FILE  | awk '{print $5}')
    UPDATED_NETWORKLOGS_HAR_FILE=$HAR_FILE
    ZIP_LOGS_TIME=-1
    ZIPPED_FILE_SIZE_BYTES=-1
    UPDATED_FILE_SIZE_KB=$FILE_SIZE_KB
    S3_UPLOAD_TIME=-1

    if [[ $AUT_ZIP_LOGS == "true" ]] && [ $FILE_SIZE_BYTES -gt 0 ]; then
      log_start_time
      ZIPPED_NETWORKLOGS_HAR_FILE="$HAR_FILE.gz"
      zip_response=`gzip -1 -k "$HAR_FILE"`
      exit_code=$?

      if [ $exit_code -eq 0 ] && [ -f "$ZIPPED_NETWORKLOGS_HAR_FILE" ];
      then
        UPDATED_NETWORKLOGS_HAR_FILE=$ZIPPED_NETWORKLOGS_HAR_FILE
        echo "upload_network_logs_chrome_har: Zipped Network Logs Successfully"
        ZIPPED_FILE_SIZE_BYTES=$(ls -l $UPDATED_NETWORKLOGS_HAR_FILE  | awk '{print $5}')
        UPDATED_FILE_SIZE_KB=$(expr $ZIPPED_FILE_SIZE_BYTES / 1024)
      else
        echo "Issue in zipping file"
      fi
      ZIP_LOGS_TIME=$(log_end_time)
    fi

    if [ $UPDATED_FILE_SIZE_KB -lt 128 ]; then
      AWS_STORAGE_CLASS="STANDARD"
    fi

    log_start_time
    response=`timeout 101 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "text/json" "$UPDATED_NETWORKLOGS_HAR_FILE" "public-read" "$s3_url" "$AWS_REGION" 100 "$SESSION_ID" "$GENRE" "$AWS_STORAGE_CLASS" "$AUT_ZIP_LOGS"`
    exit_code=$?
    S3_UPLOAD_TIME=$(log_end_time)
    if [ exit_code -ne 0 ]
    then
      eds_data='{"feature_usage":{"networkLogs":{"success":"false","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","exception":"'$response'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
    else
      eds_data='{"feature_usage":{"networkLogs":{"success":"true","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","upload_logs_stats":{"s3_zip_nw_logs_android_'$GENRE'":"'$AUT_ZIP_LOGS'"}}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      if [[ $AUT_ZIP_LOGS == "true" ]]; then
        eds_data='{"feature_usage":{"networkLogs":{"success":"true","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","upload_logs_stats":{"s3_zip_nw_logs_android_'$GENRE'":"'$AUT_ZIP_LOGS'","original_file_size":"'$FILE_SIZE_BYTES'","compression_time":"'$ZIP_LOGS_TIME'","compressed_log_size":"'$ZIPPED_FILE_SIZE_BYTES'","s3_upload_time":"'$S3_UPLOAD_TIME'"}}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      fi
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
    fi
    rm $HAR_FILE
    rm -f $ZIPPED_NETWORKLOGS_HAR_FILE
  else
    echo "Network logs NOT found."
  fi
}

upload_video_offset() {
  VIDEO_OFFSET_FILE="/tmp/video_offset_files/offset_file_session_${SESSION_ID}"
  echo "Trying to upload upload offset, present in $VIDEO_OFFSET_FILE"

  if [ -f $VIDEO_OFFSET_FILE ]; then
    echo "Offset file found."
    s3_url="https://s3$AWS_REGION.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$SESSION_ID-video-offset.json"
    timeout 101 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "text/json" "$VIDEO_OFFSET_FILE" "public-read" "$s3_url" "$AWS_REGION" 100 "$SESSION_ID" "$GENRE"
    rm $VIDEO_OFFSET_FILE
  else
    echo "Video Offset file NOT found."
  fi
}

function log_start_time() {
  START=""
  END=""
  START=$(date +%s%N)
}
function log_end_time() {
  END=$(date +%s%N)
  LOG_TIME=`echo "($END - $START)/1000000" | bc`
  echo $LOG_TIME
}

function install_settings_and_unlock_apks() {
  bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$APPIUM_SETTINGS_PATH "io.appium.settings" "false" "false" "false"
  bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$APPIUM_UNICODE_IME_PATH "io.appium.android.ime" "false" "false" "false"
  bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$APPIUM_UNLOCK_PATH "io.appium.unlock" "false" "false" "false"
}

function install_settings_apk() {
  bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$APPIUM_SETTINGS_PATH "io.appium.settings" "false" "false" "false"
}

function get_kind() {
  case $GENRE in
    "app_automate") KIND="app-" ;;
    *) KIND="" ;;
  esac
  echo $KIND
}

function get_eds_kind() {
  case $GENRE in
    "app_automate") EDS_KIND="app_" ;;
    *) EDS_KIND="" ;;
  esac
  echo $EDS_KIND
}

function start_video() {
  RES=$1
  SESSIONID=$2
  GENRE=$3
  KIND=$(get_kind)
  EDS_KIND=$(get_eds_kind)
  device_model=${4}
  OS_VERSION=${5}
  use_scrcpy=${6}

  echo  "Starting video recording for session: $SESSIONID , device: $DEVICEID"
  get_device_model
  get_os_version
  recording_script=`device_attribute "$device_model" "$OS_VERSION" video_recording_script`
  bash "${WORK_DIR}/${recording_script}" start_video_recording $DEVICEID $RES $SESSIONID "$device_model" $use_scrcpy

  if [ $? -ne 0 ]
  then
    #todo: push stacktrace of failure to zombie / log
    #Video Capture Command Failed
    eds_data='{"feature_usage":{"videoLogs":{"success":"false","exception":"capture_command_failure"}},"hashed_id":"'"$SESSIONID"'","timestamp":"'"$(date +%s)"'"}'
    $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
    $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""video-capture-command-failure" "timeout-error" "android" "" "$DEVICEID" "$SESSION_ID"
    echo "Video Capture Command Failed Exit Code $?"
  fi
}

case $TODO in
  start )
    DEBUG_PORT=$3
    WIDTH=$4
    ORIENTATION=$5
    APPIUM_VERSION=$6
    APPIUM_PORT=$7
    CHROME_DRIVER_PORT=$8
    ANDROID_BOOTSTRAP_PORT=$9
    TIMEZONE=${10}
    APP_TESTING=${11}
    APP_TESTING_BUNDLEID=${12}
    DEVICELOGS_ENABLED=${13}
    session_id=${14}
    CHROME_DRIVER_VERSION=${15}
    ENABLE_CONTACTS_APP_ACCESS="${16}"
    AUTOMATION_NAME=${17}
    AUTOMATION_VERSION=${18}

    log "Start request for selenium"
    touch $STATE_FILES_DIR/session_$DEVICEID
    touch_in_session $session_id
    get_os_version

    DEFAULT_APPIUM_VERSION=$(get_default_appium_version $OS_VERSION)

    wifi_check=`$ADB -s $DEVICEID shell dumpsys wifi | grep mNetworkInfo`
    log "Wifi check: $wifi_check"

    # Restart appium only after session-touch to avoid device check
    SHOULD_WAIT_FOR_APPIUM=1
    APPIUM_CREATE_TIME=$( date +%s )
    if [[ ${APPIUM_VERSION} != ${DEFAULT_APPIUM_VERSION} ]] || [[ ! -z "$CHROME_DRIVER_VERSION" ]]; then
      sudo bash /usr/local/.browserstack/mobile/android/helpers/create_appium_service.sh "$DEVICEID" "$APPIUM_PORT" "$CHROME_DRIVER_PORT" "$ANDROID_BOOTSTRAP_PORT" "$APPIUM_VERSION" "create_kill_and_wait" "$CHROME_DRIVER_VERSION" "$AUTOMATION_NAME" "$AUTOMATION_VERSION"
    else
      # We dont need to kill/restart appium as we ensure in /stop that the appium being used is default one. Create only creates the RUN_FILE if it doesn't already exist
      sudo bash /usr/local/.browserstack/mobile/android/helpers/create_appium_service.sh "$DEVICEID" "$APPIUM_PORT" "$CHROME_DRIVER_PORT" "$ANDROID_BOOTSTRAP_PORT" "$APPIUM_VERSION" "create_and_wait" "$CHROME_DRIVER_VERSION"
      touch "/tmp/avoid_appium_apk_install_$DEVICEID"
    fi
    APPIUM_CREATE_TIME=$(bc <<< "`date +%s` - $APPIUM_CREATE_TIME")

    # Ensuring appium kill(for non-default appium version) and status logs gets archived before session start
    archive_and_truncate_appium_logs $DEVICEID

    if [ "$?" = "0" ]; then
      start_logcat_capture

      SCREEN_UNLOCK_TIME=$( date +%s )
      ensure_screen_is_unlocked
      SCREEN_UNLOCK_TIME=$(bc <<< "`date +%s` - $SCREEN_UNLOCK_TIME")
      SET_ORIENTATION_TIME=$( date +%s )
      if [[ $ORIENTATION != "portrait" ]]; then
        # Set orientation "portrait" is also called in ensure_screen_is_unlocked
        set_device_orientation $ORIENTATION
      fi
      SET_ORIENTATION_TIME=$(bc <<< "`date +%s` - $SET_ORIENTATION_TIME")

      log_app_anr &

      SET_TIMEZONE_TIME=$( date +%s )
      if [[ $TIMEZONE != "" ]]; then
        set_timezone $TIMEZONE
      fi
      SET_TIMEZONE_TIME=$(bc <<< "`date +%s` - $SET_TIMEZONE_TIME")

      SCREENCAP_DIMENSTIONS=$( date +%s )
      SCREENCAP_DIMENSTIONS=$(bc <<< "`date +%s` - $SCREENCAP_DIMENSTIONS")

      adb -s $DEVICEID forward tcp:$DEBUG_PORT tcp:2020

      APPIUM_APKS_TIME=$( date +%s )

      get_appium_apks_path "$APPIUM_VERSION" "$AUTOMATION_NAME" "$AUTOMATION_VERSION"
      if [ ${APPIUM_VERSION} != ${DEFAULT_APPIUM_VERSION} ]; then
        if ( vers_lt "$APPIUM_VERSION" "1.10.1" && vers_gte "$APPIUM_VERSION" "1.6.5" ); then
          install_settings_and_unlock_apks
        elif [ "$APPIUM_VERSION" == "1.4.16" ] || [ "$APPIUM_VERSION" == "1.5.3" ]; then
          adb -s $DEVICEID uninstall io.appium.settings
          adb -s $DEVICEID uninstall io.appium.unlock
        else
          install_settings_apk
        fi

        if [ "$APP_TESTING" == "true" ] && [ vers_gte "$OS_VERSION" "4.4" ] && [[ "$AUTOMATION_NAME" != "espresso" ]]; then
          bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$UIAUTOMATOR2_APKS_PATH/$UIAUTOMATOR2_SERVER_APK "io.appium.uiautomator2.server" "false" "false" "false"
          bash $DRIVER_ACTIONS ensure_install $DEVICEID $APPIUM_ROOT/$UIAUTOMATOR2_APKS_PATH/$UIAUTOMATOR2_SERVER_TEST_APK "io.appium.uiautomator2.server.test" "false" "false" "false"
        fi

        bash $DRIVER_ACTIONS grant_permissions_and_launch_appium_settings $DEVICEID
        bash $DRIVER_ACTIONS grant_permissions $DEVICEID
      fi

      APPIUM_APKS_TIME=$(bc <<< "`date +%s` - $APPIUM_APKS_TIME")

      SCREENCAP_START_TIME=$( date +%s )
      if vers_gte "$OS_VERSION" "12"; then
        start_minicap
      else
        start_screencap
      fi
      SCREENCAP_START_TIME=$(bc <<< "`date +%s` - $SCREENCAP_START_TIME")

      PRODUCT="automate"

      APP_AUTOMATE_CODE_TIME=$( date +%s )
      if [ "$APP_TESTING" == "true" ]; then
        PRODUCT="app_automate"
        dismiss_crash_popup &
        disable_chrome_welcome_screen
        play_services $DEVICEID "enable" &

        # Allow user's app and appium apps in check_status_bar
        echo $APP_TESTING_BUNDLEID > $APP_SESSION_FILE
        echo 'io.appium.unlock' >> $APP_SESSION_FILE
        echo 'io.appium.settings' >> $APP_SESSION_FILE

        # Allow other apps in check_status_bar
        if [[ `cat /tmp/duplicate_session_$DEVICEID | jq -r ".other_app_bundle_ids"` != "null" ]]; then
          for other_app in `cat /tmp/duplicate_session_$DEVICEID | jq -r ".other_app_bundle_ids[]"`; do
            echo `echo "$other_app" | tr -d "\r\n"` >> $APP_SESSION_FILE
          done
        fi
        adb -s $DEVICEID push $APP_SESSION_FILE $MOBILE_PACKAGE_FILE

        adb -s $DEVICEID shell "mkdir -p $SESSION_DIR; touch $SESSION_DIR/bs-session-app-automate.txt"

        if [[ $DEVICELOGS_ENABLED == "true" ]]; then
          truncate -s 0 $DEVICELOGS_FILE
          adb -s $DEVICEID logcat -c
          touch $DEVICELOGS_FILE
        fi
      fi
      APP_AUTOMATE_CODE_TIME=$(bc <<< "`date +%s` - $APP_AUTOMATE_CODE_TIME")

      enable_contacts_param=""
      if [ $ENABLE_CONTACTS_APP_ACCESS == "true" ]; then
        enable_contacts_param='--enable-contacts'
      fi
      restart_check_status_bar $PRODUCT "${enable_contacts_param}"

      GMS_TIME=$( date +%s )
      log_gms_version
      GMS_TIME=$(bc <<< "`date +%s` - $GMS_TIME")

      INSTRUMENTATION_LOG_FILE=/tmp/$DEVICEID-instrumentation.log
      echo "Log was generated on:" `date` > $INSTRUMENTATION_LOG_FILE

      for time_var in APPIUM_CREATE_TIME SCREEN_UNLOCK_TIME SET_ORIENTATION_TIME SET_TIMEZONE_TIME SCREENCAP_DIMENSTIONS APPIUM_APKS_TIME SCREENCAP_START_TIME APP_AUTOMATE_CODE_TIME GMS_TIME; do
        echo $time_var ${!time_var}
        echo $time_var ${!time_var} >> $INSTRUMENTATION_LOG_FILE
      done
      exit 0
    else
      exit 1
    fi;;
  push_file )
    SOURCE_FILE=$3

    file_size=$(stat --format=%B $SOURCE_FILE)
    log "File size is $file_size bytes."
    if [ "$(($file_size/1024/1024))" -gt "25" ]
    then
      log "Allowed file size limit exceeded"
      echo "/tmp/skipped_due_to_size" > /tmp/upload_$DEVICEID
      return
    fi
    randomstring=$(cat /dev/urandom | LC_CTYPE=C tr -dc 'a-zA-Z0-9' | head -c 6)
    TARGET_FILE="$FILE_UPLOAD_DIRECTORY$randomstring/$(basename $SOURCE_FILE)"
    log "Push file $SOURCE_FILE to $TARGET_FILE"
    adb -s $DEVICEID push $SOURCE_FILE $TARGET_FILE
    echo $TARGET_FILE > /tmp/upload_$DEVICEID;;
  set_orientation )
    ORIENTATION=$3

    log "Setting the orientation to $ORIENTATION"
    set_device_orientation $ORIENTATION;;
  get_orientation )
    log "retrieving Orientation"

    get_device_orientation > /tmp/orientation_$DEVICEID;;
  custom_screenshot )
    ORIENTATION=$3
    X=$4
    Y=$5
    WIDTH=$6
    HEIGHT=$7
    log "Custom Screenshot Start"
    adb -s $DEVICEID shell screencap -p | perl -pe 's/\x0D\x0A/\x0A/g' > /tmp/screenshot_$DEVICEID.png
    if [[ $ORIENTATION == "landscape" ]];then
      mogrify -transpose -flip /tmp/screenshot_$DEVICEID.png
    fi
    if [[ $HEIGHT != "" ]];then
      mogrify -crop "$WIDTH"x"$HEIGHT"+$X+$Y /tmp/screenshot_$DEVICEID.png
    fi
    log "Custom Screenshot End";;
  active_window )
    SESSIONID=$3

    ACTIVE_WINDOW=`adb -s $DEVICEID shell dumpsys input_method | grep mServedView | tr -d '\040\011\012\015'`
    log "Got active window data $ACTIVE_WINDOW"
    BUCKET=""

    case $ACTIVE_WINDOW in
      mServedView=org.chromium.chrome.browser.widget.AlertDialogEditText* )
        BUCKET="basic-auth"
        ;;
    esac

    DATA="{\"url\":\"$BUCKET\",\"sessionid\":\"$SESSIONID\"}"
    $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "real-mobile-active-window" "$ACTIVE_WINDOW" "android" "$DATA"
    ;;
  start_video )
    RES=$3
    SESSIONID=$4
    GENRE=$5
    device_model=$6
    OS_VERSION=$7
    use_scrcpy=$8
    start_video $RES $SESSIONID "$GENRE" "$device_model" "$OS_VERSION" "$use_scrcpy" &
    ;;
  upload_devicelogs )
    start=`date +%s`
    SESSION_ID=$3
    AWS_KEY=$4
    AWS_SECRET=$5
    AWS_BUCKET=$7
    GENRE=$8
    AWS_STORAGE_CLASS=${9}
    KIND=$(get_kind)
    [[ $AWS_BUCKET = "bs-stag" || $6 = "us-east-1" ]] && AWS_REGION="" || AWS_REGION="-$6"

    FILE_SIZE_KB=$(expr $(ls -l $DUP_DEVICELOGS_FILE  | awk '{print $5}') / 1024)
    if [ $FILE_SIZE_KB -lt 128 ]; then
      AWS_STORAGE_CLASS="STANDARD"
    fi

    s3url="https://s3$AWS_REGION.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$SESSION_ID-device-logs.txt"
    response=`timeout 101 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "text/plain" "$DUP_DEVICELOGS_FILE" "public-read" "$s3url" "$AWS_REGION" 100 "$SESSION_ID" "$GENRE" "$AWS_STORAGE_CLASS"`
    exit_code=$?
    end=`date +%s`
    runtime=$((end-start))
    upload_metadata=$( jq -n \
                  --arg FILE_SIZE_KB "$FILE_SIZE_KB" \
                  --arg runtime "$runtime" \
                  --arg tl "$TARGET_LOCATION" \
                  '{FILE_SIZE_KB: $FILE_SIZE_KB, runtime: $runtime}' )
    # timeout 300 /usr/local/bin/s3curl --id=$AWS_KEY --key=$AWS_SECRET --contentType "text/plain" --put=$DUP_DEVICELOGS_FILE -- -H "x-amz-acl: public-read" $s3url
    if [ exit_code -ne 0 ]
    then
      #timeout occured
      eds_data='{"feature_usage":{"deviceLogs":{"success":"false","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","exception":"'$response'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""devicelogs-upload-timeout" "timeout-error" "android" "$upload_metadata" "$DEVICEID" "$SESSION_ID"
    else
      eds_data='{"feature_usage":{"deviceLogs":{"success":"true","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""devicelogs-upload-done" "success" "android" "$upload_metadata" "$DEVICEID" "$SESSION_ID"
    fi

    rm -rf $DUP_DEVICELOGS_FILE

    ;;
  upload_crashlogs )
    start=`date +%s`
    SESSION_ID=$3
    AWS_KEY=$4
    AWS_SECRET=$5
    AWS_BUCKET=$7
    GENRE=$8
    AWS_STORAGE_CLASS=${9}
    KIND=$(get_kind)
    [[ $AWS_BUCKET = "bs-stag" || $6 = "us-east-1" ]] && AWS_REGION="" || AWS_REGION="-$6"

    FILE_SIZE_KB=$(expr $(ls -l $PARSED_CRASHLOGS_FILE  | awk '{print $5}') / 1024)
    if [ $FILE_SIZE_KB -lt 128 ]; then
      AWS_STORAGE_CLASS="STANDARD"
    fi

    s3url="https://s3$AWS_REGION.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$SESSION_ID-crash_reports.txt"
    response=`timeout 101 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "text/plain" "$PARSED_CRASHLOGS_FILE" "public-read" "$s3url" "$AWS_REGION" 100 "$SESSION_ID" "$GENRE" "$AWS_STORAGE_CLASS"`
    exit_code=$?
    end=`date +%s`
    runtime=$((end-start))
    upload_metadata=$( jq -n \
                  --arg FILE_SIZE_KB "$FILE_SIZE_KB" \
                  --arg runtime "$runtime" \
                  --arg tl "$TARGET_LOCATION" \
                  '{FILE_SIZE_KB: $FILE_SIZE_KB, runtime: $runtime}' )
    # timeout 300 /usr/local/bin/s3curl --id=$AWS_KEY --key=$AWS_SECRET --contentType "text/plain" --put=$PARSED_CRASHLOGS_FILE -- -H "x-amz-acl: public-read" $s3url
    if [ exit_code -ne 0 ]
    then
      #timeout occured
      eds_data='{"feature_usage":{"crashLogs":{"success":"false","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","exception":"'$response'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""crashlogs-upload-timeout" "timeout-error" "android" "$upload_metadata" "$DEVICEID" "$SESSION_ID"
    else
      eds_data='{"feature_usage":{"crashLogs":{"success":"true","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""crashlogs-upload-done" "success" "android" "$upload_metadata" "$DEVICEID" "$SESSION_ID"
    fi

    rm -rf $PARSED_CRASHLOGS_FILE

    ;;
  upload_appiumlogs )
    start=`date +%s`
    SESSION_ID=$3
    AWS_KEY=$4
    AWS_SECRET=$5
    AWS_BUCKET=$7
    GENRE=$8
    AWS_STORAGE_CLASS=${9}
    AUT_ZIP_LOGS=${10}
    KIND=$(get_kind)
    [[ $AWS_BUCKET = "bs-stag" || $6 = "us-east-1" ]] && AWS_REGION="" || AWS_REGION="-$6"
    APPIUM_LOGS="/tmp/appium_processed_${SESSION_ID}.log"
    if [ ! -f $APPIUM_LOGS ]; then
      echo "Appium logs NOT found for $SESSION_ID"
      return
    fi
    FILE_SIZE_KB=$(expr $(ls -l $APPIUM_LOGS  | awk '{print $5}') / 1024)

    s3url="https://s3$AWS_REGION.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$SESSION_ID-appium-logs.txt"

    log_start_time
    FILE_SIZE_BYTES=$(ls -l $APPIUM_LOGS  | awk '{print $5}')
    UPDATED_APPIUM_LOGS=$APPIUM_LOGS
    ZIP_LOGS_TIME=-1
    ZIPPED_FILE_SIZE_BYTES=-1
    UPDATED_FILE_SIZE_KB=$FILE_SIZE_KB
    S3_UPLOAD_TIME=-1

    if [[ $AUT_ZIP_LOGS == "true" ]] && [ $FILE_SIZE_BYTES -gt 0 ]; then
      ZIPPED_APPIUM_LOGS="$APPIUM_LOGS.gz"
      zip_response=`gzip -1 -k "$APPIUM_LOGS"`
      exit_code=$?

      if [ $exit_code -eq 0 ] && [ -f "$ZIPPED_APPIUM_LOGS" ];
      then
        UPDATED_APPIUM_LOGS=$ZIPPED_APPIUM_LOGS
        echo "Zipped Appium Logs Successfully"
        ZIPPED_FILE_SIZE_BYTES=$(ls -l $UPDATED_APPIUM_LOGS  | awk '{print $5}')
        UPDATED_FILE_SIZE_KB=$(expr $ZIPPED_FILE_SIZE_BYTES / 1024)
      else
        echo "Issue in zipping file"
      fi
      ZIP_LOGS_TIME=$(log_end_time)
    fi

    if [ $UPDATED_FILE_SIZE_KB -lt 128 ]; then
      AWS_STORAGE_CLASS="STANDARD"
    fi

    log_start_time
    # timeout 300 /usr/local/bin/s3curl --id=$AWS_KEY --key=$AWS_SECRET --contentType "text/plain" --put=$APPIUM_LOGS -- -H "x-amz-acl: public-read" $s3url
    response=`timeout 301 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "text/plain" "$UPDATED_APPIUM_LOGS" "public-read" "$s3url" "$AWS_REGION" 300 "$SESSION_ID" "$GENRE" "$AWS_STORAGE_CLASS" "$AUT_ZIP_LOGS"`
    exit_code=$?
    S3_UPLOAD_TIME=$(log_end_time)
    end=`date +%s`
    runtime=$((end-start))
    upload_metadata=$( jq -n \
                  --arg FILE_SIZE_KB "$UPDATED_FILE_SIZE_KB" \
                  --arg runtime "$runtime" \
                  --arg tl "$TARGET_LOCATION" \
                  '{FILE_SIZE_KB: $FILE_SIZE_KB, runtime: $runtime}' )
    if [ exit_code -ne 0 ]
    then
      eds_data='{"feature_usage":{"appiumLogs":{"success":"false","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","exception":"'$response'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""appiumlogs-upload-timeout" "timeout-error" "android" "$upload_metadata" "$DEVICEID" "$SESSION_ID"
    else
      eds_data='{"feature_usage":{"appiumLogs":{"success":"true","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","upload_logs_stats":{"s3_zip_appium_logs_android_'$GENRE'":"'$AUT_ZIP_LOGS'"}}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      if [[ $AUT_ZIP_LOGS == "true" ]]; then
        eds_data='{"feature_usage":{"appiumLogs":{"success":"true","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","upload_logs_stats":{"s3_zip_appium_logs_android_'$GENRE'":"'$AUT_ZIP_LOGS'","original_file_size":"'$FILE_SIZE_BYTES'","compression_time":"'$ZIP_LOGS_TIME'","compressed_log_size":"'$ZIPPED_FILE_SIZE_BYTES'","s3_upload_time":"'$S3_UPLOAD_TIME'"}}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      fi
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""appiumlogs-upload-done" "success" "android" "$upload_metadata" "$DEVICEID" "$SESSION_ID"
    fi

    rm -rf $APPIUM_LOGS $ZIPPED_APPIUM_LOGS
    ;;
  upload_playwrightlogs )
    start=`date +%s`
    SESSION_ID=$3
    AWS_KEY=$4
    AWS_SECRET=$5
    AWS_BUCKET=$7
    GENRE=$8
    AWS_STORAGE_CLASS=${9}
    KIND=$(get_kind)
    [[ $AWS_BUCKET = "bs-stag" || $6 = "us-east-1" ]] && AWS_REGION="" || AWS_REGION="-$6"

    PLAYWRIGHT_LOGS="/tmp/playwright_server_${SESSION_ID}.log"
    if [ ! -f $PLAYWRIGHT_LOGS ]; then
      echo "PLAYWRIGHT logs NOT found for $SESSION_ID"
      return
    fi
    FILE_SIZE_KB=$(expr $(ls -l $PLAYWRIGHT_LOGS  | awk '{print $5}') / 1024)
    if [ $FILE_SIZE_KB -lt 128 ]; then
      AWS_STORAGE_CLASS="STANDARD"
    fi

    s3url="https://s3$AWS_REGION.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$SESSION_ID-playwright-logs.txt"
    # timeout 300 /usr/local/bin/s3curl --id=$AWS_KEY --key=$AWS_SECRET --contentType "text/plain" --put=$PLAYWRIGHT_LOGS -- -H "x-amz-acl: public-read" $s3url
    response=`timeout 301 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "text/plain" "$PLAYWRIGHT_LOGS" "public-read" "$s3url" "$AWS_REGION" 300 "$SESSION_ID" "$GENRE" "$AWS_STORAGE_CLASS"`
    exit_code=$?
    end=`date +%s`
    runtime=$((end-start))
    upload_metadata=$( jq -n \
                  --arg FILE_SIZE_KB "$FILE_SIZE_KB" \
                  --arg runtime "$runtime" \
                  --arg tl "$TARGET_LOCATION" \
                  '{FILE_SIZE_KB: $FILE_SIZE_KB, runtime: $runtime}' )
    if [ exit_code -ne 0 ]
    then
      eds_data='{"feature_usage":{"playwrightLogs":{"success":"false","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","exception":"'$response'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""playwrightlogs-upload-timeout" "timeout-error" "android" "$upload_metadata" "$DEVICEID" "$SESSION_ID"
    else
      eds_data='{"feature_usage":{"playwrightLogs":{"success":"true","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""playwrightlogs-upload-done" "success" "android" "$upload_metadata" "$DEVICEID" "$SESSION_ID"
    fi

    rm -rf $PLAYWRIGHT_LOGS
    ;;
  upload_networklogs )
    start=`date +%s`
    SESSION_ID=$3
    AWS_KEY=$4
    AWS_SECRET=$5
    AWS_BUCKET=$7
    GENRE=$8
    CAPTURE_CONTENT_FLAG=$9
    AWS_STORAGE_CLASS=${10}
    AUT_ZIP_LOGS=${11}
    KIND=$(get_kind)
    EDS_KIND=$(get_eds_kind)
    [[ $AWS_BUCKET = "bs-stag" || $6 = "us-east-1" ]] && AWS_REGION="" || AWS_REGION="-$6"

    NETWORKLOGS_FILE="/tmp/mitm_flow_file_$DEVICEID.txt"
    NETWORKLOGS_HAR_FILE="/tmp/mitm_har_file_$DEVICEID.txt"

    # pushing mitm flow file size to zombie
    FILE_SIZE_KB=$(expr $(ls -l $NETWORKLOGS_FILE  | awk '{print $5}') / 1024)
    $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""mitm-flow-file-size-check" "" "android" "$FILE_SIZE_KB" "$DEVICEID" "$SESSION_ID"

    if [ "$FILE_SIZE_KB" -gt 512000 ]; then
        CAPTURE_CONTENT_FLAG=false
    fi
    # convert flow to har
    PYTHONDONTWRITEBYTECODE=1 timeout 600 $MITMDUMP -n -r $NETWORKLOGS_FILE -s $FLOW_TO_HAR --set hardump=$NETWORKLOGS_HAR_FILE --set captureContent=$CAPTURE_CONTENT_FLAG 2>&1 >> /var/log/browserstack/mitm_$DEVICEID.log

    exit_code=$?
    if [ $exit_code -ne 0 ]
    then
      GENERIC_MSG="timeout"
      if [ $exit_code -ne 124 ]
      then
        GENERIC_MSG="error"
        PARSE_ERROR_URL=$(tail -2 /var/log/browserstack/mitm_$DEVICEID.log)
        echo "$SESSIONID: Got parse error for the URL: $PARSE_ERROR_URL"
      fi
      eds_data='{"feature_usage":{"networkLogs":{"success":"false","storage_class":"'$AWS_STORAGE_CLASS'","exception":"parse_'"$GENERIC_MSG"'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""networklogs-parse-""$GENERIC_MSG" "$GENERIC_MSG" "android" "$FILE_SIZE_KB" "$DEVICEID" "$SESSION_ID"
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
    else
      FILE_SIZE_KB=$(expr $(ls -l $NETWORKLOGS_HAR_FILE  | awk '{print $5}') / 1024)

      # Temporary change to instrument a customer issue (AA-3852). should be removed ultimately.
      CHUNK_BODY_INCOMPLETE_COUNT=$(grep -c "Failed parsing or buffering the chunk-encoded client body" "$NETWORKLOGS_HAR_FILE")
      if [ $CHUNK_BODY_INCOMPLETE_COUNT -gt 0 ]
      then
        $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""network-logs-chunk-incomplete" "privoxy-request-body-parse-error" "$device_model" "$CHUNK_BODY_INCOMPLETE_COUNT" "$DEVICEID" "$SESSION_ID"
      fi

      FILE_SIZE_BYTES=$(ls -l $NETWORKLOGS_HAR_FILE  | awk '{print $5}')
      UPDATED_NETWORKLOGS_HAR_FILE=$NETWORKLOGS_HAR_FILE
      ZIP_LOGS_TIME=-1
      ZIPPED_FILE_SIZE_BYTES=-1
      UPDATED_FILE_SIZE_KB=$FILE_SIZE_KB
      S3_UPLOAD_TIME=-1

      if [[ $AUT_ZIP_LOGS == "true" ]] && [ $FILE_SIZE_BYTES -gt 0 ]; then
        log_start_time
        ZIPPED_NETWORKLOGS_HAR_FILE="$NETWORKLOGS_HAR_FILE.gz"
        zip_response=`gzip -1 -k "$NETWORKLOGS_HAR_FILE"`
        exit_code=$?

        if [ $exit_code -eq 0 ] && [ -f "$ZIPPED_NETWORKLOGS_HAR_FILE" ];
        then
          UPDATED_NETWORKLOGS_HAR_FILE=$ZIPPED_NETWORKLOGS_HAR_FILE
          echo "upload_networklogs: Zipped Network Logs Successfully"
          ZIPPED_FILE_SIZE_BYTES=$(ls -l $UPDATED_NETWORKLOGS_HAR_FILE  | awk '{print $5}')
          UPDATED_FILE_SIZE_KB=$(expr $ZIPPED_FILE_SIZE_BYTES / 1024)
        else
          echo "Issue in zipping file"
        fi
        ZIP_LOGS_TIME=$(log_end_time)
      fi

      if [ $UPDATED_FILE_SIZE_KB -lt 128 ]; then
        AWS_STORAGE_CLASS="STANDARD"
      fi

      log_start_time
      s3url="https://s3$AWS_REGION.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$SESSION_ID-har-logs.txt"
      response=`timeout 301 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "text/plain" "$UPDATED_NETWORKLOGS_HAR_FILE" "public-read" "$s3url" "$AWS_REGION" 300 "$SESSION_ID" "$GENRE" "$AWS_STORAGE_CLASS" "$AUT_ZIP_LOGS"`
      exit_code=$?
      # timeout 300 /usr/local/bin/s3curl --id=$AWS_KEY --key=$AWS_SECRET --contentType "text/plain" --put=$NETWORKLOGS_HAR_FILE -- -H "x-amz-acl: public-read" $s3url
      S3_UPLOAD_TIME=$(log_end_time)

      end=`date +%s`
      runtime=$((end-start))
      upload_metadata=$( jq -n \
                  --arg FILE_SIZE_KB "$UPDATED_FILE_SIZE_KB" \
                  --arg runtime "$runtime" \
                  --arg tl "$TARGET_LOCATION" \
                  '{FILE_SIZE_KB: $FILE_SIZE_KB, runtime: $runtime}' )

      if [ exit_code -ne 0 ]
      then
        #timeout occured
        eds_data='{"feature_usage":{"networkLogs":{"success":"false","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","exception":"'$response'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
        $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
        $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""networklogs-upload-timeout" "timeout-error" "$device_model" "$upload_metadata" "$DEVICEID" "$SESSION_ID"
      else
        eds_data='{"feature_usage":{"networkLogs":{"success":"true","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","upload_logs_stats":{"s3_zip_nw_logs_android_'$GENRE'":"'$AUT_ZIP_LOGS'"}}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
        if [[ $AUT_ZIP_LOGS == "true" ]]; then
          eds_data='{"feature_usage":{"networkLogs":{"success":"true","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","upload_logs_stats":{"s3_zip_nw_logs_android_'$GENRE'":"'$AUT_ZIP_LOGS'","original_file_size":"'$FILE_SIZE_BYTES'","compression_time":"'$ZIP_LOGS_TIME'","compressed_log_size":"'$ZIPPED_FILE_SIZE_BYTES'","s3_upload_time":"'$S3_UPLOAD_TIME'"}}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
        fi
        $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
        $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_zombie.rb "android" "$KIND""networklogs-upload-done" "success" "$device_model" "$upload_metadata" "$DEVICEID" "$SESSION_ID"
        push_to_influxdb_v1 "networklogs-upload-done" "cleanup" "upload_network_logs" "$DEVICEID" "false"
      fi
    fi

    rm -rf $NETWORKLOGS_FILE $NETWORKLOGS_HAR_FILE $ZIPPED_NETWORKLOGS_HAR_FILE
    ;;
  upload_network_logs_har )
    SESSION_ID=$3
    AWS_KEY=$4
    AWS_SECRET=$5
    AWS_BUCKET=$7
    GENRE=$8
    AWS_STORAGE_CLASS=${10}
    AUT_ZIP_LOGS=${11}

    [[ $AWS_BUCKET = "bs-stag" || $6 = "us-east-1" ]] && AWS_REGION="" || AWS_REGION="-$6"

    upload_network_logs_chrome_har
     ;;
  stop_mitm )
    kill_mitm

    NETWORKLOGS_FILE="/var/log/browserstack/mitm_flow_file_$DEVICEID.txt"
    MITMFLOW_FILE="/tmp/mitm_flow_file_$DEVICEID.txt"

    if [[ -f $NETWORKLOGS_FILE ]]; then
      cp $NETWORKLOGS_FILE $MITMFLOW_FILE
      truncate -s 0 $NETWORKLOGS_FILE
    fi
    ;;

  upload_google_login_screenshot )
    echo "$SESSIONID: Uploading screenshot for $DEVICEID "
    SESSION_ID=$3
    AWS_KEY=$4
    AWS_SECRET=$5
    AWS_BUCKET=$7
    GENRE=$9
    [[ $AWS_BUCKET = "bs-stag" || $6 = "us-east-1" ]] && AWS_REGION="" || AWS_REGION="-$6"

    GOOGLE_LOGIN_DIR="/usr/local/.browserstack/google_login_screenshot_$DEVICEID"
    DESTINATION_SCREENSHOT_FILE="$GOOGLE_LOGIN_DIR/google_login_status_$SESSION_ID.png"
    rm -rf $GOOGLE_LOGIN_DIR; mkdir $GOOGLE_LOGIN_DIR
    adb -s $DEVICEID pull /sdcard/login_status.png $DESTINATION_SCREENSHOT_FILE
    sudo chown -R $USER:$USER $GOOGLE_LOGIN_DIR
    if [[ -f $DESTINATION_SCREENSHOT_FILE ]]; then
      echo "Uploading google_login_status png to S3 "
      s3url="https://s3$AWS_REGION.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$SESSION_ID.png"
      timeout 31 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "image/png" "$DESTINATION_SCREENSHOT_FILE" "public-read" "$s3url" "" 30
      if [ $? -ne 0 ]
      then
        echo "Google login screenshot upload failed for $SESSION_ID on device $DEVICEID"
      else
        echo "Google login screenshot upload done for $SESSION_ID on device $DEVICEID"
      fi
    fi
  ;;
  stop_video )
    echo "$SESSION_ID: Stopping video recording for $DEVICEID "
    log_start_time
    SESSION_ID=$3
    AWS_KEY=$4
    AWS_SECRET=$5
    AWS_BUCKET=$7
    GENRE=$9
    STOP_REQ_TIMESTAMP=${10}
    device_model=${11}
    OS_VERSION=${12}
    use_scrcpy_for_video_recording=${13}
    AWS_STORAGE_CLASS=${14}
    USE_RTC_APP=`jq -r '.use_rtc_app' $DUPLICATE_SESSION_SUMMARY_FILE`
    KIND=$(get_kind)
    EDS_KIND=$(get_eds_kind)
    [[ $AWS_BUCKET = "bs-stag" || $6 = "us-east-1" ]] && AWS_REGION="" || AWS_REGION="-$6"
    if [[ $device_model == 'Pixel 5' ]]; then
      use_scrcpy_for_video_recording="true"
    fi

    VIDEO_FILE=$8
    if [ -f "/usr/local/.browserstack/files_to_be_processed/files_to_upload/async_$SESSION_ID" ]; then
      VIDEO_DIR="/usr/local/.browserstack/video_$DEVICEID/$SESSION_ID"
      if [[ $use_scrcpy_for_video_recording == "true" ]]; then
        sudo rm -rf $VIDEO_DIR ; mkdir -p $VIDEO_DIR ; sudo chown -R $USER:$USER $VIDEO_DIR
        mv /usr/local/.browserstack/video_$DEVICEID/screenrecord $VIDEO_DIR
      fi
    else
      VIDEO_DIR="/usr/local/.browserstack/video_$DEVICEID"
    fi
    VIDEO_PROCESS_COUNT_FILE="$VIDEO_DIR/video_process_count_$DEVICEID"
    DEST_VIDEO_FILE="$VIDEO_DIR/video_$DEVICEID.mp4"
    PRE_TIME=$(log_end_time)
    echo "$SESSION_ID: PRE_TIME: $PRE_TIME ms"
    VIDEO_RECORDING_DIRECTORY=""
    recording_type=""
    # RTC app based recording > scrcpy > adb screenrecord
    if [ "$USE_RTC_APP" = "v2" ]; then
      recording_type="MediaProjection"
    elif [[ $use_scrcpy_for_video_recording == "true" ]]; then
      recording_type="scrcpy"
    else
      recording_type="adb_screenrecord"
    fi

    if [ "$USE_RTC_APP" = "v2" ]; then
      # Pull video from device
      log_start_time
      sudo rm -rf $VIDEO_DIR ; mkdir -p $VIDEO_DIR ; sudo chown -R $USER:$USER $VIDEO_DIR
      adb -s $DEVICEID pull /sdcard/Movies $VIDEO_DIR
      adb -s $DEVICEID shell rm -rf /sdcard/Movies
      sudo chown -R $USER:$USER $VIDEO_DIR/Movies
      PULL_TIME=$(log_end_time)
      TOTAL_VIDEO_FILES=$(ls $VIDEO_DIR/Movies/$SESSION_ID*.mp4 | wc -l | tr -d '[:space:]')
      VIDEO_RECORDING_DIRECTORY="Movies"
      STOP_VIDEO_RECORDING_TIME=""
    else
      VIDEO_RECORDING_DIRECTORY="screenrecord"
      log_start_time
      recording_script=`device_attribute "$device_model" "$OS_VERSION" video_recording_script`
      bash "${WORK_DIR}/${recording_script}" stop_video_recording $DEVICEID "" $SESSION_ID "$device_model" $use_scrcpy_for_video_recording
      echo "$SESSION_ID: ADB screenrecord was killed, proceeding to pull video files"
      STOP_VIDEO_RECORDING_TIME=$(log_end_time)
      echo "$SESSION_ID: STOP_VIDEO_RECORDING_TIME: $STOP_VIDEO_RECORDING_TIME ms"

      log_start_time
      # Increase sleep to avoid Video corrupt errors since video needs some time to finish post-processing after stop
      sleep 3
      if [[ $device_model == 'Pixel 4' ]] || [[ $device_model == 'Pixel 5' ]]; then
        sleep 5
      fi

      if [[ $use_scrcpy_for_video_recording != "true" ]]; then
        log_start_time
        upload_video_offset
        VIDEO_OFFSET_UPLOAD_TIME=$(log_end_time)
        log_start_time
        echo "$SESSION_ID: VIDEO_OFFSET_UPLOAD_TIME: $VIDEO_OFFSET_UPLOAD_TIME ms"
        sudo rm -rf $VIDEO_DIR ; mkdir -p $VIDEO_DIR ; sudo chown -R $USER:$USER $VIDEO_DIR
        adb -s $DEVICEID pull /sdcard/screenrecord $VIDEO_DIR
        adb -s $DEVICEID shell rm -rf /sdcard/screenrecord
      fi
      adb -s $DEVICEID pull /sdcard/screenrecord_count $VIDEO_DIR
      adb -s $DEVICEID shell rm -rf /sdcard/screenrecord_count

      sudo chown -R $USER:$USER $VIDEO_DIR/screenrecord
      sudo chown -R $USER:$USER $VIDEO_DIR/screenrecord_count
      PULL_TIME=$(log_end_time)
      TOTAL_VIDEO_FILES=$(ls $VIDEO_DIR/screenrecord/video_*.mp4 | wc -l | tr -d '[:space:]')
      VIDEO_PROCESS_COUNT=$(cat $VIDEO_DIR/screenrecord_count)
      # There can be scenarios where TOTAL_VIDEO_FILES is lesser than VIDEO_PROCESS_COUNT (video recording issues). In those scenarios, we do not stitch the last [VIDEO_PROCESS_COUNT - TOTAL_VIDEO_FILES] files. Currently these scenarios are found for videos recorded via scrcpy.
      # NOTE : This is applicable only for devices where video files are stitched using ffmpeg and not mp4Box
      if [[ $use_scrcpy_for_video_recording == "true" ]]; then
        TOTAL_VIDEO_FILES=$VIDEO_PROCESS_COUNT
      fi
    fi

    if [[ ! -d "$VIDEO_DIR/$VIDEO_RECORDING_DIRECTORY" ]]; then
      # If video dir not present
      eds_data='{"feature_usage":{"videoLogs":{"success":"false","exception":"screenrecord-dir-missing","recording_method":"'"$recording_type"'","stop_recording_time":"'"$STOP_VIDEO_RECORDING_TIME"'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
      $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
      $BUNDLE exec $ZOMBIE "android" "$KIND""${VIDEO_RECORDING_DIRECTORY}-dir-missing" "${VIDEO_RECORDING_DIRECTORY}-dir-missing" "$device_model" "No $VIDEO_RECORDING_DIRECTORY directory" "$DEVICEID" "$SESSION_ID" "" "" "" "" "" "" "$OS_VERSION"

    elif [ -f "/usr/local/.browserstack/files_to_be_processed/files_to_upload/async_$SESSION_ID" ]; then
      # If async flow enabled
      echo "$SESSION_ID: All video Files Pulled & deleted in $PULL_TIME ms, will join them in ASYNC"
      log_start_time
      LAST_VIDEO_FILE=$((TOTAL_VIDEO_FILES-1))
      INFO_OUTPUT=$( MP4Box -info "$VIDEO_DIR/screenrecord/video_$LAST_VIDEO_FILE.mp4" 2>&1)
      echo "$SESSION_ID: LAST VIDEO INFO OUTPUT $INFO_OUTPUT"
      if [[ $INFO_OUTPUT == *"Invalid IsoMedia File"* ]]; then
        # See MOB-8574
        touch $NEEDS_REBOOT_FILE
        echo "$SESSION_ID: Touched $NEEDS_REBOOT_FILE file"
        $BUNDLE exec $ZOMBIE "android" "$KIND""video-invalid-media-file-device-rebooted" "invalid-ios-media-device-rebooted" "$device_model" "$VIDEO_DATA" "$DEVICEID" "$SESSION_ID" "" "" "" "" "" "" "$OS_VERSION"
      fi
      mkdir -p /usr/local/.browserstack/files_to_be_processed/files_to_upload/VIDEO
      JSON_STRING=$( jq -n --arg session_id "$SESSION_ID" --arg device_identifier "$DEVICEID" --arg os_version "$OS_VERSION" --arg device_model "$device_model" --arg video_file "$VIDEO_FILE" --arg genre "$GENRE" --arg ffprobe "$FFPROBE" --arg ffmpeg "$FFMPEG" --arg aws_bucket "$AWS_BUCKET" --arg aws_secret "$AWS_SECRET" --arg aws_key "$AWS_KEY" --arg aws_region "$AWS_REGION" --arg kind "$KIND" --arg eds_kind "$EDS_KIND" --arg stop_req_timestamp "$STOP_REQ_TIMESTAMP" '{upload_type: "video-file", eds_kind: $eds_kind, session_id: $session_id, device_identifier: $device_identifier, os_version: $os_version, device_model: $device_model, video_file: $video_file, aws_bucket: $aws_bucket, aws_secret: $aws_secret, aws_key: $aws_key, aws_region: $aws_region, kind: $kind, genre: $genre, ffprobe: $ffprobe, ffmpeg: $ffmpeg, stop_req_timestamp :$stop_req_timestamp}' )
      echo "$JSON_STRING" > /usr/local/.browserstack/files_to_be_processed/files_to_upload/VIDEO/video_file_${SESSION_ID}.json
      rm -rf /usr/local/.browserstack/files_to_be_processed/files_to_upload/async_$SESSION_ID
      JSON_WRITE_TIME=$(log_end_time)
      echo "$SESSION_ID: JSON_WRITE_TIME: $JSON_WRITE_TIME ms"

    else
      # Video dir present, analyse video files and attempt stitch
      if [ "$USE_RTC_APP" = "v2" ]; then
        mv $VIDEO_DIR/Movies/$SESSION_ID*.mp4 $DEST_VIDEO_FILE
        OUTPUT=""
        INFO_OUTPUT=""
      else
        echo "$SESSION_ID: All Files Pulled in $PULL_TIME ms, proceeding to join them, TOTAL_VIDEO_FILES : $TOTAL_VIDEO_FILES VIDEO_PROCESS_COUNT : $VIDEO_PROCESS_COUNT"
        FILE_PARAMS=$(for (( i=0; i<TOTAL_VIDEO_FILES; i++)); do echo $VIDEO_DIR/screenrecord/video_$i.mp4; done | xargs echo | sed -e 's/ / \-cat /g')
        log_start_time
        CORRUPT_VIDEO_FILE=""
        for (( i=0; i<TOTAL_VIDEO_FILES; i++)); do
          INFO_OUTPUT=$( MP4Box -info "$VIDEO_DIR/screenrecord/video_$i.mp4" 2>&1)
          echo "$SESSION_ID: VIDEO INFO OUTPUT $INFO_OUTPUT"
          if [[ $INFO_OUTPUT == *"Invalid IsoMedia File"* && $CORRUPT_VIDEO_FILE == "" ]]; then
            CORRUPT_VIDEO_FILE=$i
          fi
        done

        empty_video_files=0
        video_part_duration_sum=0
        video_durations=""
        video_framerate=""
        for (( i=0; i<VIDEO_PROCESS_COUNT; i++)); do
          VIDEOPART_FILE="$VIDEO_DIR/screenrecord/video_$i.mp4"
          VIDEOPART_DURATION_SEC=$($FFPROBE -i $VIDEOPART_FILE -v quiet -show_entries format=duration -hide_banner -of default=noprint_wrappers=1:nokey=1 || echo 0)
          TOTAL_FRAMES=$($FFPROBE -v error -select_streams v:0 -count_frames -show_entries stream=nb_read_frames -print_format csv="p=0" $VIDEOPART_FILE || echo 0)
          VIDEO_FPS=0
          if (( $(bc <<< "$VIDEOPART_DURATION_SEC != 0") )); then
            VIDEO_FPS=$(echo "scale=2; $TOTAL_FRAMES / $VIDEOPART_DURATION_SEC" | bc)
          fi
          echo "$SESSION_ID: Video duration of video_$i.mp4 : $VIDEOPART_DURATION_SEC seconds, frame rate of video_$i.mp4 : $VIDEO_FPS"
          video_durations+="video_$i-$VIDEOPART_DURATION_SEC, "
          video_framerate+="video_$i-$VIDEO_FPS, "
          video_part_duration_sum=$(echo $VIDEOPART_DURATION_SEC $video_part_duration_sum | awk '{printf "%.2f\n",$1+$2}')
          if [[ $(echo "if (${VIDEOPART_DURATION_SEC} > 0.0) 1 else 0" | bc) -eq 0 ]]; then
            empty_video_files=$(echo $empty_video_files 1 | awk '{printf "%.2f\n",$1+$2}')
          fi
        done

        echo "$SESSION_ID: video_durations : $video_durations, video_fps : $video_framerate, video_part_duration_sum : $video_part_duration_sum, empty_video_files: $empty_video_files"

        # If device is a Samsung Tab S7, Samsung Galaxy S21 we will stitch video files with ffmpeg, to avoid issues with different AVC basline profiles
        # Adding S21 Ultra and OnePlus 7T as per MOB-8444
        # Adding S21 plus as per AA_5835
        # Adding S22 and S22 plus as per MOB-10623
        # Adding S22 Ultra and Tab S8 as per MOBPE-539
        if device_is_a uses_ffmpeg_for_video_stitch; then
          STITCH_CMD='ffmpeg'
          corrupted_files_stitching_skipped=""
          num_corrupted_files=0
          for (( i=0; i<TOTAL_VIDEO_FILES; i++)); do
            file=$VIDEO_DIR/screenrecord/video_$i.mp4

            if $FFPROBE -loglevel warning $file; then
              echo "file '$file'" >> /tmp/video_concat_file_$DEVICEID
              echo "$file is not corrupted"
            else
              corrupted_files_stitching_skipped+="video_$i.mp4,"
              num_corrupted_files=$((num_corrupted_files+1))
              echo "$file is corrupted, will skip it while stitching"
            fi
          done
          if [ $num_corrupted_files -ne 0 ]
          then
            $BUNDLE exec $ZOMBIE "android" "$KIND""video-files-skipped-during-stitch" "skipped-video-files" "$device_model" "$num_corrupted_files-$corrupted_files_stitching_skipped" "$DEVICEID" "$SESSION_ID" "" "" "" "" "" "" "$OS_VERSION"
            echo "Skipped video files during ffmpeg stitch for $SESSION_ID on device $DEVICEID: $corrupted_files_stitching_skipped"
          else
            echo "No video files skipped during ffmpeg stitch for $SESSION_ID on device $DEVICEID"
          fi
          OUTPUT=$(${FFMPEG} -f concat -safe 0 -i /tmp/video_concat_file_$DEVICEID -c copy $DEST_VIDEO_FILE 2>&1)
          rm /tmp/video_concat_file_$DEVICEID
        else
          STITCH_CMD='MP4Box'
          OUTPUT=$(MP4Box -noprog -add $FILE_PARAMS $DEST_VIDEO_FILE 2>&1)
        fi
        STITCH_TIME=$(log_end_time)
        echo "$SESSION_ID: $STITCH_CMD OUTPUT $OUTPUT"

        SCREENRECORD_FILES=`ls -lrth $VIDEO_DIR/screenrecord/ | awk '{print $9 "-" $5","}'`

        rm -rf $VIDEO_DIR/screenrecord
        rm -rf $VIDEO_DIR/screenrecord_count
      fi

      FILE_SIZE_KB=$(expr $(ls -l $DEST_VIDEO_FILE  | awk '{print $5}') / 1024)
      # Converting time to ms which will be pushed to BQ.
      VIDEO_DURATION_SEC=$($FFPROBE -i $DEST_VIDEO_FILE -v quiet -show_entries format=duration -hide_banner -of default=noprint_wrappers=1:nokey=1)
      VIDEO_DURATION=$(echo $VIDEO_DURATION_SEC 1000 | awk '{printf "%.2f\n",$1*$2}')
      DEST_VIDEO_TOTAL_FRAMES=$($FFPROBE -v error -select_streams v:0 -count_frames -show_entries stream=nb_read_frames -print_format csv="p=0" $DEST_VIDEO_FILE || echo 0)
      DEST_VIDEO_BITRATE=$($FFPROBE -v error -select_streams v:0 -show_entries stream=bit_rate -of default=noprint_wrappers=1:nokey=1 $DEST_VIDEO_FILE || echo 0)
      DEST_VIDEO_FPS=0
      if (( $(bc <<< "$VIDEO_DURATION_SEC != 0") )); then
        DEST_VIDEO_FPS=$(echo "scale=2; $DEST_VIDEO_TOTAL_FRAMES / $VIDEO_DURATION_SEC" | bc)
      fi
      VIDEO_DURATION_DATA="{\"Deviceid\":\"$DEVICEID\",\"SessionId\":\"$SESSION_ID\",\"FileSize\":\"$FILE_SIZE_KB\",\"EmptyVideoFiles\":\"$empty_video_files\",\"StitchedVideoDuration\":\"$VIDEO_DURATION_SEC\",\"StitchedVideoBitrate\":\"$DEST_VIDEO_BITRATE\",\"StitchedVideoFrameRate\":\"$DEST_VIDEO_FPS\",\"SumVideoPartDurations\":\"$video_part_duration_sum\",\"VideoPartDurations\":\"$video_durations\",\"VideoPartFrameRate\":\"$video_framerate\",\"RecordingMethod\":\"$recording_type\",\"StitchingMethod\":\"$STITCH_CMD\",\"PullTime\":\"$PULL_TIME\"}"

      $BUNDLE exec $ZOMBIE "android" "$KIND""video-rec-data" "video-durations-and-size" "$device_model" "$VIDEO_DURATION_DATA" "$DEVICEID" "$SESSION_ID" "" "" "" "" "" "" "$OS_VERSION"

      if [[ $OUTPUT == *"Incomplete box mdat"* ]] || [ $TOTAL_VIDEO_FILES == 0 ]; then
        echo "$SESSION_ID: File is corrupted, pushing details to Zombie"
        eds_data='{"feature_usage":{"videoLogs":{"success":"false","storage_class":"'$AWS_STORAGE_CLASS'","recording_method":"'"$recording_type"'","exception":"merge_command_failure","duration":"'"$VIDEO_DURATION"'","stop_recording_time":"'"$STOP_VIDEO_RECORDING_TIME"'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
        $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
        VIDEO_DATA="{\"Deviceid\":\"$DEVICEID\",\"SessionId\":\"$SESSION_ID\",\"StitchOutput\":\"$OUTPUT\",\"UnStichedRecordingFiles\":\"$SCREENRECORD_FILES\"}"
        $BUNDLE exec $ZOMBIE "" "$KIND""video-rec-corrupted" "$CORRUPT_VIDEO_FILE" "" "$VIDEO_DATA" "$DEVICEID" "$SESSION_ID"
      elif [[ $INFO_OUTPUT == *"Invalid IsoMedia File"* ]]; then
        # See MOB-8574
        touch $NEEDS_REBOOT_FILE
        eds_data='{"feature_usage":{"videoLogs":{"success":"false","storage_class":"'$AWS_STORAGE_CLASS'","recording_method":"'"$recording_type"'","exception":"invalid_iso_file","duration":"'"$VIDEO_DURATION"'","stop_recording_time":"'"$STOP_VIDEO_RECORDING_TIME"'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
        $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
        $BUNDLE exec $ZOMBIE "android" "$KIND""video-invalid-media-file" "invalid-ios-media-file" "$device_model" "$VIDEO_DATA" "$DEVICEID" "$SESSION_ID" "" "" "" "" "" "" "$OS_VERSION"
      elif [[ -f $DEST_VIDEO_FILE ]]; then
        # Checking for concatenation failures
        # Example: Cannot concatenate files: Different AVC Level Indication between source (32) and destination (40)
        # In case of this error, we generate an incomplete video file, continuing with upload
        video_concat_success="true"
        if [[ $OUTPUT == *"Cannot concatenate files"* ]]; then
          echo "$SESSION_ID: Cannot concatenate files, pushing details to Zombie"
          VIDEO_DATA="{\"Deviceid\":\"$DEVICEID\",\"SessionId\":\"$SESSION_ID\",\"StitchOutput\":\"$OUTPUT\"}"
          $BUNDLE exec $ZOMBIE "android" "$KIND""video-rec-cannot-concatenate" "video-concatenation-failed" "$device_model" "$VIDEO_DATA" "$DEVICEID" "$SESSION_ID" "" "" "" "" "" "" "$OS_VERSION"
          video_concat_success="false"
        fi
        $QTFASTSTART $DEST_VIDEO_FILE
        s3url="https://s3$AWS_REGION.amazonaws.com/$AWS_BUCKET/$SESSION_ID/$VIDEO_FILE.mp4"
        log_start_time
        if [ $FILE_SIZE_KB -lt 128 ]; then
          AWS_STORAGE_CLASS="STANDARD"
        fi
        response=`timeout 301 ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$AWS_KEY" "$AWS_SECRET" "video/mp4" "$DEST_VIDEO_FILE" "public-read" "$s3url" "$AWS_REGION" 300 "$SESSION_ID" "$GENRE" "$AWS_STORAGE_CLASS"`
        # response=`timeout 300 /usr/local/bin/s3curl --id=$AWS_KEY --key=$AWS_SECRET --contentType "video/mp4" --put=$DEST_VIDEO_FILE -- -H "x-amz-acl: public-read" $s3url`
        exit_code=$?
        video_uploaded_timestamp=`date +%s`
        video_upload_delay=$(($video_uploaded_timestamp-$STOP_REQ_TIMESTAMP))
        check_valid_key=`echo $response | grep "InvalidAccess" | wc -l`
        VIDEO_UPLOAD_END_TIME=$(log_end_time)
        if [ exit_code -ne 0 ] || [ "$check_valid_key" -ne "0" ]
        then
          #timeout occured
          eds_data='{"feature_usage":{"videoLogs":{"success":"false","storage_class":"'$AWS_STORAGE_CLASS'","recording_method":"'"$recording_type"'","exception":"upload_failure","duration":"'"$VIDEO_DURATION"'","stop_recording_time":"'"$STOP_VIDEO_RECORDING_TIME"'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
          $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
          $BUNDLE exec $ZOMBIE "android" "$KIND""video-rec-upload-timeout" "timeout-error" "$device_model" "$FILE_SIZE_KB" "$DEVICEID" "$SESSION_ID" "" "" "" "" "" "" "$OS_VERSION"
        else
          if [[ $video_concat_success == "true" ]]; then
            eds_data='{"feature_usage":{"videoLogs":{"success":"true","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","recording_method":"'"$recording_type"'","duration":"'"$VIDEO_DURATION"'","upload_delay":"'"$video_upload_delay"'","stop_recording_time":"'"$STOP_VIDEO_RECORDING_TIME"'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
          else
            eds_data='{"feature_usage":{"videoLogs":{"success":"false","size":"'$FILE_SIZE_KB'","storage_class":"'$AWS_STORAGE_CLASS'","recording_method":"'"$recording_type"'","duration":"'"$VIDEO_DURATION"'","upload_delay":"'"$video_upload_delay"'","exception":"concat_error","stop_recording_time":"'"$STOP_VIDEO_RECORDING_TIME"'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
          fi
          $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
          $BUNDLE exec $ZOMBIE "android" "$KIND""video-rec-upload-done" "success" "$device_model" "$FILE_SIZE_KB" "$DEVICEID" "$SESSION_ID" "" "" "" "" "" "" "$OS_VERSION"
          $BUNDLE exec $ZOMBIE "android" "$KIND""video-rec-upload-time" "success" "$device_model" "$VIDEO_UPLOAD_END_TIME" "$DEVICEID" "$SESSION_ID" "" "" "" "" "" "" "$OS_VERSION"
          push_to_influxdb_v1 "video-rec-upload-done" "cleanup" "session_video_upload" "$DEVICEID" "false"
        fi
      else
        eds_data='{"feature_usage":{"videoLogs":{"success":"false","exception":"unknown","storage_class":"'$AWS_STORAGE_CLASS'","recording_method":"'"$recording_type"'","stop_recording_time":"'"$STOP_VIDEO_RECORDING_TIME"'"}},"hashed_id":"'"$SESSION_ID"'","timestamp":"'"$(date +%s)"'"}'
        $BUNDLE exec /usr/local/.browserstack/mobile/common/push_to_eds.rb "$EDS_KIND""automate_test_sessions" $eds_data
        VIDEO_DATA="{\"Deviceid\":\"$DEVICEID\",\"SessionId\":\"$SESSION_ID\",\"StitchOutput\":\"$OUTPUT\",\"UnStichedRecordingFiles\":\"$SCREENRECORD_FILES\"}"
        $BUNDLE exec $ZOMBIE "android" "$KIND""video-unknown-failure" "unknown" "$device_model" "$VIDEO_DATA" "$DEVICEID" "$SESSION_ID" "$CORRUPT_VIDEO_FILE" "" "" "" "" "" "$OS_VERSION"
      fi
      echo "$DEVICEID :: $SESSION_ID file size=$FILE_SIZE_KB, stitch time=$STITCH_TIME, pull time=$PULL_TIME and uploaded in $VIDEO_UPLOAD_END_TIME"
    fi
    ;;
esac
