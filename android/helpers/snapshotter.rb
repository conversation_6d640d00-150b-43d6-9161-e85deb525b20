# Contains method for getting screenshots for the particular device

class Snapshotter
  TOTAL_ALLOWED_SNAPSHOT_PROCESSES = 10

  def self.valid_request?(params)
    params["device"] && params["file"] && params["bucket"] && params["folder"]
  end

  def self.valid_session?(session_info_file, request_session_id)
    session_info_file ? File.read(session_info_file).include?(request_session_id) : true
  end

  def self.can_take_screenshots?(device, script, logger = nil)
    # Ignoring commands that include `sh -c` since these commands will further spawn a subshell to execute the
    # ruby command in, and then wait on its output. This would cause for us to count one process twice
    num_processing = `ps aux | grep #{script} | grep #{device} | grep -v grep | grep -v "sh -c" | wc -l`.strip.to_i
    logger.info "hub_snapshot backlogs: #{num_processing}"
    num_processing <= TOTAL_ALLOWED_SNAPSHOT_PROCESSES
  end

  def self.take_screenshot(cmd)
    spawn_with_reaper(cmd)
  end
end
