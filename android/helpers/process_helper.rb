require_relative '../../common/push_to_zombie'
require 'browserstack_logger'
require 'android_toolkit'

class ProcessHelper
  # This is a class that will handle anything related to tracking processes which ran on the device during a session.

  def initialize(device, session_id = nil)
    @device = device
    @session_id = session_id

    @adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
  end

  def log_port_scanning_detection
    BrowserStack.logger.info("Running log_port_scanning_detection")
    device_port_scanning_detected_file = "/data/local/tmp/port_scanning_detected"
    begin
      data = @adb.cat(device_port_scanning_detected_file)
      BrowserStack.logger.info("Sending process data to zombie: #{data}")
      zombie_push('android', "port_scanning_detected", '', '', data, @device, @session_id)
      @adb.shell("rm -f #{device_port_scanning_detected_file}")
    rescue AndroidToolkit::ADB::FileNotFoundError, AndroidToolkit::ADB::ExecutionError => e
      BrowserStack.logger.info("Did not send process data, reason: #{e.message}")
      false
    end
  end

  def self.run_from_bash
    device_id = ARGV[0].to_s.strip
    session_id = ARGV[1].to_s.strip
    method_to_call = ARGV[2].to_s.strip
    stats_helper = ProcessHelper.new(device_id, session_id)
    stats_helper.send(method_to_call)
  rescue StandardError => e
    ExitFile.write(e.message)
    raise e
  end
end

ProcessHelper.run_from_bash if $PROGRAM_NAME == __FILE__
