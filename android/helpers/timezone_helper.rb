require_relative '../../common/push_to_zombie'
require_relative '../../common/helpers'
require_relative '../models/android_device'
require_relative '../lib/custom_exceptions'
require 'android_toolkit'
require 'browserstack_logger'

class TimezoneHelper

  def initialize(device, logger, product: nil, session_id: nil)
    @logger = logger
    @device_obj = BrowserStack::AndroidDevice.new(device, "TimezoneHelper", @logger)
    @device = device
    @session = session_id
    @product = product
    @adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    @cleanup = false
    @eds = EDS.new({}, @logger)
    @min_os_version_timezone = 9
    @eds_event_type = "web_events"
    @eds_event_name = "UpdateTimezoneAndroid"
    @zombie_kind_success = "timezone-android-success"
    @zombie_kind_failure = "timezone-android-failure"
    AndroidToolkit::Log.logger = @logger
    @log_message_prefix = "[ANDROID_TIMEZONE]"
  end

  def device_time_zone
    result_timezone = @adb.getprop("persist.sys.timezone")
    log :info, "Getting timezone: #{result_timezone}"
    result_timezone
  end

  def change_time_zone(timezone = "GMT", cleanup: false)
    if @device_obj.os_version.to_i < @min_os_version_timezone
      log :info, "Timezone through adb not supported on device."
      return false
    end
    @cleanup = cleanup
    log :info, "Changing timezone of device #{@device} to #{timezone}"
    begin
      @adb.shell("service call alarm 3 s16 #{timezone}")

      if device_time_zone != timezone
        error_msg = "Device timezone: #{device_time_zone} is not equal to set timezone: #{timezone}"
        log :error, error_msg

        log_data_to_bq(timezone, false, error_msg)
        return false
      end

      log_data_to_bq(timezone, true)
    rescue StandardError => e
      log :error, "Could not change timezone for device"
      log :error, e.message
      log_data_to_bq(timezone, false, error_msg: e.message)

      return false
    end

    true
  end

  def prep_and_send_data_to_zombie(kind, error, data)
    zombie_push("android", kind, error, "", data, @device, @session, '')
  end

  def log_data_to_bq(timezone, success, error_msg: '')
    zombie_kind = success ? @zombie_kind_success : @zombie_kind_failure
    params = {
      event_name: @eds_event_name,
      product: @product,
      os: "Android",
      team: "device_features",
      event_json: {
        session_id: @session,
        success: success,
        timezone: timezone
      }
    }
    @eds.push_logs(@eds_event_type, params) unless @cleanup
    prep_and_send_data_to_zombie(zombie_kind, error_msg, { timezone: timezone })
  end

  def log(level, msg)
    msg = "#{@log_message_prefix} #{msg}"
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, msg)
    else
      @logger.send(level.to_sym, msg, @logger_params)
    end
  end
end
