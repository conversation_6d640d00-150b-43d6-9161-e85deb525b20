require_relative '../constants'
require "#{BrowserStack::BS_DIR}/mobile/android/lib/custom_exceptions.rb"
require "#{BrowserStack::BS_DIR}/mobile/common/push_to_zombie"
require 'json'
require 'fileutils'
require 'browserstack_logger'
require 'android_toolkit'
require 'time'
require_relative "./run_as_root_helper"
require_relative "./settings_helper"

class DateTimeHelper

  def initialize(device, session_id, logger, product, send_to_bq)
    @device = device
    @session_id = session_id
    @product = product
    @adb = AndroidToolkit::ADB.new(udid: @device)
    @logger_params = {
      device: @device,
      session_id: @session_id,
      subcomponent: BrowserStack::UPDATE_ANDROID_SETTING_SET_DATE_TIME
    }
    @send_to_bq = send_to_bq
    @logger = logger
    @root_helper = BrowserStack::RunAsRootHelper.new({ device_id: @device, logger: @logger } , @logger_params)
    @device_obj = BrowserStack::AndroidDevice.new(@device, "DateTimeHelper", @logger)
    @os_version = @adb.os_version
    @root_command = RootCommand.new(@device_obj, @logger, @logger_params)
  end

  def log(level, message)
    BrowserStack.logger.send(level.to_sym, message, @logger_params)
  end

  def convert_date_time_to_millis(date, time)
    final_date_time = nil
    final_date_time_millis = nil
    device_date_time = @adb.shell("date +%F::%R::%z").split("::")

    write_state_file({ date_time: device_date_time })

    if date.nil? && !time.nil?
      str = "#{device_date_time[0]} #{time} #{device_date_time[2]}"
      final_date_time = Time.strptime(str, "%Y-%m-%d %H:%M %z")
    elsif !date.nil? && time.nil?
      str = "#{date} #{device_date_time[1]} #{device_date_time[2]}"
      final_date_time = Time.strptime(str, "%b %d %Y %H:%M %z")
    else
      str = "#{date} #{time} #{device_date_time[2]}"
      final_date_time = Time.strptime(str, "%b %d %Y %H:%M %z")
    end
    final_date_time_millis = final_date_time.strftime('%s%L')
    puts "test case maa #{final_date_time} , #{final_date_time_millis}"
    [final_date_time, final_date_time_millis]
  end

  def check_device_date_compare(comparable_date)
    # Compare device and comparable_date to check if device time is set in the future/past
    device_datetime_raw = @adb.shell("date").strip
    # To avoid false positives as we sometimes get empty output
    return false if device_datetime_raw.empty?

    device_datetime = Time.parse(device_datetime_raw)

    # Find the difference in days between device and host times
    hours_difference_in_time = ((device_datetime - comparable_date).abs / 60 / 60).to_i

    log :info, "Device time and host time difference #{@device}: #{hours_difference_in_time}"

    hours_difference_in_time > 15
  end

  def cleanup
    return false unless set_date_time_session_running?

    log(:info, "Started cleaning up device: #{@device}")
    start_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)
    begin
      host_datetime = Time.parse(`date`.strip)
      host_datetime_millis = host_datetime.strftime('%s%L')
      change_date_time_millis(host_datetime_millis)
      if check_device_date_compare(host_datetime)
        raise UpdateAndroidSettingsCustomException,
              :set_time_date_comparison_check_failed
      end

      time_taken = Process.clock_gettime(Process::CLOCK_MONOTONIC) - start_time
      remove_state_file
      send_data_to_bq('set_date_time_cleanup_android', true, time_taken) if @send_to_bq

    rescue StandardError => e
      log(:error, "Failed cleaning up device: #{@device} #{e.message} #{e.backtrace.join("\n")}")
      time_taken = Process.clock_gettime(Process::CLOCK_MONOTONIC) - start_time
      send_data_to_bq('set_date_time_cleanup_android', false, time_taken, e.message.to_s) if @send_to_bq
    end
  end

  def change_date_time(date, time)
    log(:info, "change date and time started with Date: #{date} with time: #{time}")
    start_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)
    begin
      raise UpdateAndroidSettingsCustomException, :set_time_validation_error if date.nil? && time.nil?

      final_date_time, final_date_time_millis = convert_date_time_to_millis(date, time)
      log(:info, "converted the date time to millis and proper date time format #{final_date_time} millis:#{
        final_date_time_millis}")

      raise UpdateAndroidSettingsCustomException, :set_time_validation_error if final_date_time_millis.nil?

      change_date_time_millis(final_date_time_millis)
      if check_device_date_compare(final_date_time)
        raise UpdateAndroidSettingsCustomException,
              :set_time_date_comparison_check_failed
      end

      log :info, "change date and time completed with Date time: #{final_date_time}"
      time_taken = Process.clock_gettime(Process::CLOCK_MONOTONIC) - start_time
      send_data_to_bq('set_date_time_android', true, time_taken)
      true
    rescue StandardError => e
      log(:error, "Failed to set date and time with error message :#{
        e.message} #{e.backtrace.join("\n")}")
      time_taken = Process.clock_gettime(Process::CLOCK_MONOTONIC) - start_time
      send_data_to_bq('set_date_time_android', false, time_taken)

      raise e
    end
  end

  def change_time_format(time_format)
    BrowserStack.logger.info "Started setting time format to: #{time_format}"

    raise SettingsException.new("Invalid params", 400) unless ["true", "false"].include?(time_format.to_s)

    setting = "set_time_format"
    value = time_format.to_s == "true" ? "12" : "24"

    settings_helper = BrowserStack::SettingsHelper.new(@device, BrowserStack.logger, @product, @session_id)
    settings_helper.apply_setting(setting, value)

    BrowserStack.logger.info "Successfully set time format to: #{time_format}"
  rescue StandardError => e
    log(:error, "Failed to set time format with error message :#{
        e.message} #{e.backtrace.join("\n")}")
    raise e
  end

  def change_date_time_millis(final_date_time_millis)
    if Gem::Version.new(@os_version) >= Gem::Version.new(9)
      date_change_cmd = "service call alarm 2 i64 #{final_date_time_millis}"
      @adb.shell(date_change_cmd)
      true
    elsif @root_helper.run_as_root_working?
      @root_command.run("service call alarm 2 i64 #{final_date_time_millis}")
      true
    else
      raise UpdateAndroidSettingsCustomException, :set_time_root_failed

    end
  end

  def set_date_time_session_running?
    File.exist?("#{BrowserStack::STATE_FILES_DIR}/#{BrowserStack::ANDROID_SETTINGS_SET_DATE_TIME}_#{@device}.json")
  end

  def remove_state_file
    FileUtils.rm_rf("#{BrowserStack::STATE_FILES_DIR}/#{BrowserStack::ANDROID_SETTINGS_SET_DATE_TIME}_#{@device}.json")
    true
  end

  def write_state_file(data)
    File.write("#{BrowserStack::STATE_FILES_DIR}/#{BrowserStack::ANDROID_SETTINGS_SET_DATE_TIME}_#{@device}.json",
               data.to_json)
    true
  end

  def send_data_to_bq(action, success, time_taken, error_message = nil)
    eds = EDS.new({}, BrowserStack.logger)
    event = action.to_s
    zombie_push('android', "#{event}-fail", error_message, nil, nil, @device, @session_id) unless success

    data = {
      event_name: event,
      product: @product,
      os: 'Android',
      team: 'device_features',
      event_json: {
        session_id: @session_id,
        device: @device,
        success: success,
        time_taken: time_taken,
        message: error_message
      }
    }
    eds.push_logs("web_events", data).join

    true
  rescue StandardError => e
    log(:error,
        "Failed to send data to bq, for device: #{@device}, session: #{@session_id}, product: #{
          @product}, action: #{action}, success: #{success}, time_taken: #{
          time_taken}, error_message: #{error_message}")

    false
  end

  if $PROGRAM_NAME == __FILE__
    command = ARGV[0]
    device_id = ARGV[1].to_s.strip
    session_id = ARGV[2].to_s.strip
    product = ARGV[3].to_s.strip
    date_time_helper = DateTimeHelper.new(device_id, session_id, BrowserStack.logger, product, true)
    case command
    when "change_date_time"
      date = ARGV[4].to_s.strip
      time = ARGV[5].to_s.strip
      date_time_helper.change_date_time(date, time)
    when "cleanup"
      date_time_helper.cleanup
    else
      log_info("Invalid params")
    end
  end
end
