require "#{<PERSON><PERSON><PERSON><PERSON>_COMMON_HOME}/mobile_session_info/lib/mobile_session_info"
require "#{MOBILE_COMMON_HOME}/mobile_session_info/lib/whitelisted_keys"

class WriteSessionInfo
  def initialize(logger, device, params, session_subtype=nil)
    @logger          = logger
    @device          = device
    @params          = params
    @session_subtype = session_subtype
  end

  def save

    @logger.info "Writing session info for #{@device}"
    MobileSessionInfo.new(@device, @params, @session_subtype).save
  rescue StandardError => e
    @logger.error "Exception writing MobileSessionInfo #{@device}: #{e.message} \n#{e.backtrace.join("\n")}"

  end

  def update

    @logger.info "Updating session info keys #{keys} for #{@device}"
    MobileSessionInfo.new(@device, @params, @session_subtype).update
  rescue StandardError => e
    @logger.error "Exception updating MobileSessionInfo #{@device}: #{e.message} \n#{e.backtrace.join("\n")}"

  end
end
