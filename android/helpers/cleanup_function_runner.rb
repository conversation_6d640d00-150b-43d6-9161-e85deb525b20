require_relative '../constants'
require_relative '../models/android_device'
require_relative '../lib/database'
require 'json'

class CleanupFunctionRunner

  def initialize(device, logger)
    @logger = logger
    @device_obj = BrowserStack::AndroidDevice.new(device, "CleanupFunctionRunner", @logger)
    @device = device
    @cleanup_rules ||= BrowserStack::CleanupRules.cleanup_rules
    AndroidToolkit::Log.logger = @logger
  end

  def csv_to_array(value)
    return [] unless value.is_a?(String) && !value.strip.empty?

    value.split(',').map(&:strip).reject(&:empty?)
  end

  def should_run?(function_name)
    device_model = @device_obj.model
    device_manufacturer = @device_obj.manufacturer
    device_version = @device_obj.os_version.to_f

    rules = @cleanup_rules.where(cleanup_step: function_name).all
    @logger.info("Matched rules for #{function_name}: #{rules.inspect}")

    return false if rules.empty?

    rules.each do |rule|
      rule = rule.transform_keys(&:to_sym) if rule.is_a?(Hash)

      include_models = csv_to_array(rule[:include_models])
      exclude_models = csv_to_array(rule[:exclude_models])
      include_manufacturers = csv_to_array(rule[:include_manufacturers])
      exclude_manufacturers = csv_to_array(rule[:exclude_manufacturers])
      version_gt = rule[:version_gt]&.to_f
      version_lt = rule[:version_lt]&.to_f

      next if include_models.any? && !include_models.include?(device_model)
      next if exclude_models.include?(device_model)
      next if include_manufacturers.any? && !include_manufacturers.include?(device_manufacturer)
      next if exclude_manufacturers.include?(device_manufacturer)
      next if version_gt && device_version <= version_gt
      next if version_lt && device_version >= version_lt

      return true
    end

    false
  end

end
