require_relative '../models/android_device'
require_relative '../lib/root_command'
require_relative '../constants'
require_relative '../../common/push_to_zombie'

require 'android_toolkit'
require 'logger'

class RepluggingScript
  REPLUGGING_SCRIPT_FILE_PATH_ROOTED = File.expand_path('../live/scripts/check_adb_connection_rooted.sh', __dir__)
  BUSYBOX = "/data/local/tmp/busybox".freeze

  attr_reader :device, :adb, :device_obj, :root_command

  def initialize(device, logger)
    @device = device
    @logger = logger

    AndroidToolkit::Log.logger = @logger
    @adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    @device_obj = BrowserStack::AndroidDevice.new(@device, "RepluggingScript", @logger)
    @root_command = RootCommand.new(@device_obj, @logger, @logger_params)
  end

  def start
    return if !['M2003J15SC', 'Redmi Note 8', 'Redmi Note 7 Pro', 'Redmi Note 9 Pro Max',
                'Redmi Note 5 Pro'].include?(@device_obj.model) && @device_obj.supports_device_owner?

    push

    if device_obj.os_version.to_i >= 11 || !device_obj.rooted?
      load_with_adb_user
    else
      load_with_root_user
    end
  rescue StandardError => e
    zombie_push('android', 'replugging-script-load-failed', '', '', e.message, device)
    raise e
  end

  def self.run_from_bash
    device_id = ARGV[0].to_s.strip
    command = ARGV[1].to_s.strip.downcase.to_sym

    logger = Logger.new($stdout)

    helper = RepluggingScript.new(device_id, logger)
    helper.send(command)
  rescue StandardError => e
    ExitFile.write(e.message)
    raise e
  end

  private

  def push
    system("chmod a+x #{REPLUGGING_SCRIPT_FILE_PATH_ROOTED}")
    adb.push(REPLUGGING_SCRIPT_FILE_PATH_ROOTED, BrowserStack::DATA_LOCAL_TMP)
  end

  def load_with_adb_user
    adb.shell("nohup #{start_cmd}")
  end

  def load_with_root_user
    root_command.run(start_cmd)
  end

  def start_cmd
    "#{BrowserStack::DATA_LOCAL_TMP}/check_adb_connection_rooted.sh #{device_obj.recovery_device?} &"
  end

  def log(level, msg)
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, msg)
    else
      @logger.send(level.to_sym, msg, @logger_params)
    end
  end
end

RepluggingScript.run_from_bash if $PROGRAM_NAME == __FILE__
