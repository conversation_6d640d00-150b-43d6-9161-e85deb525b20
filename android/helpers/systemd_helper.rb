require_relative '../constants'
require_relative '../lib/os_utils'
require_relative '../lib/utils/erb_binding'

module SystemDHelper
  include <PERSON><PERSON><PERSON><PERSON>tack

  def systemd_start_service(process_name, device: "", return_cmd: false)
    command = "sudo systemctl start #{systemd_service_name(process_name, device: device)}"
    return command if return_cmd # Only returns the command

    OSUtils.execute(command)
  end

  def systemd_restart_service(process_name, device: "", return_cmd: false)
    command = "sudo systemctl restart #{systemd_service_name(process_name, device: device)}"
    return command if return_cmd # Only returns the command

    OSUtils.execute(command)
  end

  def systemd_stop_service(process_name, device: "")
    OSUtils.execute("sudo systemctl stop #{systemd_service_name(process_name, device: device)}")
  end

  def systemd_daemon_reload
    OSUtils.execute("sudo systemctl daemon-reload")
  end

  def systemd_create_service(process_name, template_file, device: "", binding_data: {})
    service = systemd_service_name(process_name, device: device)
    service_file_path = systemd_service_file_full_path(service)
    old_content = File.exist?(service_file_path) ? File.read(service_file_path) : ""
    new_service_file_content = ERB.new(File.read(template_file)).result(ErbBinding.new(binding_data).send(:get_binding))
    return false if new_service_file_content == old_content

    File.open("/tmp/#{service}", "w") { |f| f.write(new_service_file_content) }
    move_service_to_systemd_directory(process_name, device: device)
    systemd_daemon_reload
    true
  end

  def systemd_service_name(process_name, device: "")
    device == "" ? "#{process_name}.service" : "#{process_name}_#{device}.service"
  end

  def move_service_to_systemd_directory(process_name, device: "", old_dir: "/tmp")
    service = systemd_service_name(process_name, device: device)
    OSUtils.execute("sudo mv #{old_dir}/#{service} #{systemd_service_file_full_path(service)}")
  end

  def systemd_check_if_service_in_list(process_name, device: "")
    service = systemd_service_name(process_name, device: device)
    File.exist?(File.join(SYSTEMD_SERVICES_PATH, service))
  end

  def systemd_list_all_services(process_name: "")
    OSUtils.execute("systemctl list-unit-files --type=service | grep #{process_name} | awk '{print $1}'").split("\n")
  end

  def systemd_remove_service(service)
    OSUtils.execute("sudo rm #{systemd_service_file_full_path(service)}")
  end

  def systemd_service_file_full_path(service)
    File.join(SYSTEMD_SERVICES_PATH, service)
  end
end
