require 'fileutils'
require_relative '../constants'

class StateFileHelper
  def initialize(file_name, directory = BrowserStack::STATE_FILES_DIR)
    @path = "#{directory}/#{file_name}"
  end

  def exist?
    # checks if state file exists
    File.exist? @path
  end

  def touch
    # creates statefile
    FileUtils.touch @path
  end

  def remove
    # removes statefile
    FileUtils.rm_f @path
  end

  def reset
    # Deletes and recreates the file
    remove
    touch
  end

  def write(content)
    File.write(@path, content)
  end

  def read
    return nil unless exist?

    File.read(@path).strip
  end

  def file_age_in_seconds
    unless exist?
      touch
      # to make all the older_than methods return true, we return infinity
      return Float::INFINITY
    end
    (Time.now - File.stat(@path).mtime).to_i
  end

  def older_than_seconds?(seconds)
    file_age_in_seconds > seconds
  end

  def older_than_minutes?(minutes)
    older_than_seconds?(minutes * 60)
  end

  def older_than_hours?(hours)
    older_than_minutes?(hours * 60)
  end

  def older_than_days?(days)
    older_than_hours?(days * 24)
  end
end
