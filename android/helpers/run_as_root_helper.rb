#!/usr/bin/env ruby

require 'android_toolkit'
require 'browserstack_logger'

require_relative '../exit_file'
require_relative '../constants'
require_relative '../../common/push_to_zombie'
require_relative '../models/android_device'
require_relative '../lib/root_command'

module BrowserStack
  class RunAsRootHelper
    def self.run_from_bash
      method = ARGV[0].to_s.strip.downcase.to_sym
      device_id = ARGV[1].to_s.strip

      logger_params = {}
      logger_params[:device] = device_id
      logger_params[:component] = 'RunAsRootHelper_bash'

      logger = BrowserStack.init_logger("#{BrowserStack::LOGGING_DIR}/cleanup_#{device_id}.log", logger_params)

      run_as_root_helper = RunAsRootHelper.new(device_id: device_id, logger: logger)
      run_as_root_helper.send(method)
    rescue NoMethodError => e
      ExitFile.write(e.message)
      raise e
    end

    def initialize(params = {}, logger_params = {})
      @device_id = params[:device_id]
      @logger_params = logger_params
      @logger = params[:logger]
      @device_obj = BrowserStack::AndroidDevice.new(@device_id, "RunAsRootHelper", @logger)
      @root_command = RootCommand.new(@device_obj, @logger, @logger_params)
    end

    def ensure_run_as_root_cleanup(attempts = 0)
      return true if run_as_root_working?

      #fail cleanup if reboot has already been attempted and command is still failing
      if attempts != 0
        log :error, "Unable to execute run_as_root command after reboot"
        raise "Executing run_as_root failed"
      end
      log :error, "Rebooting to attempt fix of run as root"
      @device_obj.reboot_and_wait
      ensure_run_as_root_cleanup(1)
    end

    def run_as_root_working?
      log :info, 'Running date command as root to ensure run_as_root working'
      begin
        @root_command.run("date", 0)
      rescue StandardError => e
        log :error, "Unable to execute run_as_root command: #{e.message}"
        return false
      end
      log :info, 'run_as_root working'
      true
    end

    def log(level, msg)
      if @logger.instance_of?(Logger)
        @logger.send(level.to_sym, msg)
      else
        @logger.send(level.to_sym, msg, @logger_params)
      end
    end
  end
end

BrowserStack::RunAsRootHelper.run_from_bash if $PROGRAM_NAME == __FILE__
