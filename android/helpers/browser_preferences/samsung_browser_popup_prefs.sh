#!/bin/bash

# This file should be pushed to the device, run as root, and removed
# Prevents "An update is available, do you want to update" popups on Samsung Browser

DLT=/data/local/tmp
SBROWSER_PACKAGE=com.sec.android.app.sbrowser
SBROWSER_DIR=/data/data/$SBROWSER_PACKAGE
SHARED_PREFS=$SBROWSER_DIR/shared_prefs
SOURCE=$DLT/update_popup_preferences.xml
DEST=$SHARED_PREFS/update_popup_preferences.xml

echo "Running as:"
whoami

am force-stop $SBROWSER_PACKAGE

echo "\nSBrowser dir before"
ls -lah $SBROWSER_DIR

echo "\nCopying pref file"
mkdir -p $SHARED_PREFS && cp $SOURCE $DEST

echo "\nGiving r-w-x permissions to pref folder"
chmod 771 $SHARED_PREFS

echo "\nGiving r-w permissions to pref file"
chmod 666 $DEST

echo "\nFinding owner of sbrowser files"
owner=`ls -lah $SBROWSER_DIR | grep -Ev "system|root|grep" | awk '{print $3}' | uniq`
echo "\nOwner is $owner"

echo "\nPref File before"
ls -lah $DEST

echo "\nChanging owner and group of pref folder"
chown $owner $SHARED_PREFS
chgrp $owner $SHARED_PREFS

echo "\nChanging owner and group of pref file"
chown $owner $DEST
chgrp $owner $DEST

echo "\nPref File after"
ls -lah $DEST

echo "\nPref file contents:"
cat $DEST

am force-stop $SBROWSER_PACKAGE
