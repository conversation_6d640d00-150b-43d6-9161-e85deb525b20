require "android_toolkit"
require "browserstack_logger"

require_relative "../constants"
require_relative "../../common/push_to_zombie"
require_relative "../lib/os_utils"
require_relative "./utils"
require_relative "./popup_helper"
require_relative '../models/android_device'

# This class will handle anything related to video recording using MediaProjection APIs
class RtcHelper

  def self.run_from_bash
    raise StandardError, "Not enough arguments" if ARGV.size < 3

    function_to_call = ARGV[0].to_s.strip
    device_id = ARGV[1].to_s.strip
    session_id = ARGV[2].to_s.strip
    args = ARGV[3..]

    rtc_helper = RtcHelper.new(device_id, session_id)
    if function_to_call == "enable_popup_permission"
      rtc_helper.send(function_to_call)
    else
      rtc_helper.send(function_to_call, *args)
    end
  rescue StandardError => e
    BrowserStack.logger.error "Error in RtcHelper.run_from_bash #{e.message} #{e.backtrace}"
    zombie_push("android", "video-recording-start-failed", e.message, "", "", device_id,
                session_id)
  end

  def initialize(device, session_id)
    raise "Device cannot be empty" if device.nil? || device == ""
    raise "Session id cannot be empty" if session_id.nil? || session_id == ""

    @device = device
    @session_id = session_id
    @duplicate_session_file = "/tmp/duplicate_session_#{device}"
    data = self.class.session_details(@duplicate_session_file)
    @test_framework = data["browserstack_framework"] == 'maestro' ? "maestro" : "espresso"
    @summary_file = "/tmp/#{@test_framework}_summary_#{device}"
    @logger = Logger.new($stdout)
    @device_obj = BrowserStack::AndroidDevice.new(device, self.class.to_s, @logger, {})
  end

  def self.session_details(summary_file)

    JSON.parse(File.read(summary_file))
  rescue StandardError
    {}

  end

  def start_video_recording(*args)
    os_version = args[0].to_s.strip
    framework = args[1].to_s.strip
    params = { 'automate_session_id' => @session_id, 'framework' => framework }

    start_rtc2_app
    popup_handler_start_time = Time.now.to_i
    unless handle_automate_media_projection_popup(@device, params, os_version)
      session_info_duplicate = self.class.session_details(@duplicate_session_file)
      session_info_duplicate[:media_projection_fallback_required] = true
      session_info_duplicate[:use_rtc_app] = ''
      File.open(@duplicate_session_file, 'w') { |f| f.write(session_info_duplicate.to_json) }

      session_info = self.class.session_details(@summary_file)
      session_info[:media_projection_fallback_required] = true
      session_info[:use_rtc_app] = ''
      File.open(@summary_file, 'w') { |f| f.write(session_info.to_json) }
    end
    popup_handler_stop_time = Time.now.to_i
    zombie_push("android", "media-projection-popup-handling-time", "", "",
                (popup_handler_stop_time - popup_handler_start_time), @device, @session_id)
  end

  def set_locale
    session_details_json = self.class.session_details(@duplicate_session_file)
    language = session_details_json["language"]
    locale = session_details_json["localization"]

    language_or_locale = language || locale
    if language_or_locale
      BrowserStack.logger.info "Setting device language to #{language_or_locale}"
      success = set_device_locale(@device, language_or_locale, BrowserStack.logger, { async: false })
      if language
        send_eds_event("feature_usage", "language",
                       success)
      else
        send_eds_event("feature_usage" , "locale", success)
      end
      # We are putting sleep here to let USB Debugging connected notification dismiss by itself
      # More details on https://browserstack.atlassian.net/browse/AASI-3144?focusedCommentId=714714
      if session_details_json["singleRunnerInvocation"].to_s == "true"
        sleep 6
      else
        sleep 3
      end
    end
  end

  def stop_video_recording
    cmd = "adb -s #{@device} shell am startservice --user 0 -n fr.pchab.AndroidRTC2/.RTCService "\
    "--es 'action' 'stop-video-recording'"
    output, status = OSUtils.execute(cmd, true)
    BrowserStack.logger.info("Stopping rtc2 app for video recording: output #{output}")
    return if self.class.session_details(@duplicate_session_file)['media_projection_fallback_required'].to_s == 'true'

    video_stop_time = Time.now.to_i
    video_start_time = fetch_video_start_time
    BrowserStack.logger.info("Pushing expected video duration to zombie:
     Video start time: #{video_start_time} Video stop time: #{video_stop_time}")
    video_duration_data = { "ExpectedTotalDuration": (video_stop_time - video_start_time) }
    zombie_push("android", "app-video-rec-data-totality", "", "", video_duration_data, @device,
                @session_id)
  end

  def self.save_video_start_time(popup_handler_time, device)
    duplicate_session_file = "/tmp/duplicate_session_#{device}"
    session_info = session_details(duplicate_session_file)
    session_info[:video_start_time] = popup_handler_time
    # Write the updated content back to the file
    File.open(duplicate_session_file, 'w') { |f| f.write(session_info.to_json) }
  end

  def fetch_video_start_time
    session_info = self.class.session_details(@duplicate_session_file)
    session_info["video_start_time"].to_i
  end

  def start_rtc2_app
    press_home_screen_button(@device) unless home_screen_focused?(@device)

    cmd = "adb -s #{@device} shell am startservice --user 0 -n fr.pchab.AndroidRTC2/.RTCService "\
    "--es action 'start-video-recording' --ei width 800 --ei height 800 --es genre 'app_automate-#{@test_framework}' "\
    "--es sessionId '#{@session_id}' --es enableRotationUsingAppOrientation 'true' -ei fps 30"
    output, status = OSUtils.execute(cmd, true)
    BrowserStack.logger.info("Starting rtc2 app for video recording: output: #{output}")
  end

  def self.look_for_video_start_time(media_projection_permission_popup_cmd, device)
    Thread.new do
      sleep 0.5 while system media_projection_permission_popup_cmd

      BrowserStack.logger.info "Media Projection popup handled, noting video start time"
      # From the POC we know that video recording starts as soon as pop up Start Now is clicked.
      save_video_start_time(Time.now.to_i, device)
    end

  end

  def enable_popup_permission
    return false unless @device_obj.model == "BON-AL00"

    base_command = "adb -s #{@device} shell "
    # enable Drop zone
    cmd = "#{base_command}appops set #{BrowserStack::RTCAPP_2_PACKAGE_NAME} SYSTEM_ALERT_WINDOW allow"
    output, status = OSUtils.execute(cmd, true)

    # open settings
    settings = "android.settings.APPLICATION_DETAILS_SETTINGS"
    cmd = "#{base_command}am start -a #{settings} -d package:#{BrowserStack::RTCAPP_2_PACKAGE_NAME}"
    output, status = OSUtils.execute(cmd, true)
    sleep 2

    # open more permissions
    popup_handler = BrowserStack::PopupHelper.new(
      device_id: @device,
      os_version: Gem::Version.new(@device_obj.os_version),
      session_id: nil
    )
    popup_handler.tap_element_via_adb("text", "More permissions")
    sleep 1

    # check & tap if permission denied
    if popup_handler.tap_element_via_adb("content-desc", "Background pop-ups permission toggle, permission denied")
      BrowserStack.logger.info "Background pop-ups permission granted"
    else
      BrowserStack.logger.info "Background pop-ups permission already granted, no need to give again"
    end
    # navigate to home screen
    cmd = "#{base_command}input keyevent 3"
    OSUtils.execute(cmd, true)

    true
  rescue StandardError => e
    BrowserStack.logger.error "Failed to enable rtc pop-ups permission #{e}"
    raise e
  end

  def eds
    @eds ||= EDS.new({}, BrowserStack.logger)
  end

  def send_eds_event(column, feature, success, error="")
    eds_data = {
      "#{column}": {
        "#{feature}": {
          "success": success,
          "exception": error
        }
      },
      "hashed_id": @session_id,
      "timestamp": Time.now.to_i.to_s
    }
    eds.push_logs(EdsConstants::APP_AUTOMATE_TEST_SESSIONS, eds_data)
  end

end

RtcHelper.run_from_bash if $PROGRAM_NAME == __FILE__
