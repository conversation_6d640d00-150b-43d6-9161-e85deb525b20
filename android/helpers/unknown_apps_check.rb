#!/usr/bin/env ruby

require_relative '../constants'
require_relative '../lib/database'
require_relative '../../common/push_to_zombie'
require_relative 'apk_installer'
require 'android_toolkit'

require 'browserstack_logger'

# This class checks for any unknown apps installed on a device
class UnknownAppsChecker
  NUM_UNKNOWN_APPS_UNINSTALL_LIMIT = 50

  attr_accessor :unknown_apps, :known_apps

  def initialize(device, model, logger, logger_params = {})
    @device = device
    @model = model
    @model_specific_whitelist = {
      "moto g(9) play" => ["com.amazon.appmanager"],                #Motorola Moto G9 Play
      "moto g(7) play" => ["com.amazon.mShop.android.shopping",     #Motorola Moto G7 Play
                           "com.amazon.appmanager",
                           "com.LogiaGroup.LogiaDeck"],
      "SM-G920F" => ["b2b.hrs.android.lib"],                        #Samsung Galaxy S6
      "Pixel 5" => ["com.google.android.apps.security.securityhub"],  #Google Pixel 5
      "Redmi Note 9 Pro Max" => ["com.facemoji.lite.xiaomi"],       #Xiaomi Redmi Note 9 Pro Max
      "M2101K7AI" => ["com.facemoji.lite.xiaomi"]               #Xiaomi Redmi Note 10
    }
    @logger = logger
    @logger_params = logger_params
    @device_obj = BrowserStack::AndroidDevice.new(device, self.class.to_s, logger, logger_params)
  end

  def check
    if !@device_obj.nil? && @device_obj.dedicated_cleanup?
      log :info, "Skipping unknown apps uninstall on device #{@device}"
      return
    end
    if unknown_apps_installed?
      log :info, "Found unknown apps on device: #{all_unknown_apps}"
      zombie_push('android', 'unknown-apps-found', '', '', all_unknown_apps.to_s, @device)
      handle_unknown_apps
      return
    end

    log :info, "No unknown apps found on device #{@device}"
  end

  def handle_unknown_apps
    num_unknown_apps = all_unknown_apps.size

    if num_unknown_apps > NUM_UNKNOWN_APPS_UNINSTALL_LIMIT
      # Sending alert when multiple (>50) unknown apps are found,
      # could be an issue with detection so don't uninstall them all
      log :info, "Found #{num_unknown_apps} unknown apps, sending alert"
      # This kind will trigger a pushover alert
      zombie_push('android', 'multiple-unknown-apps-found', '', '', all_unknown_apps.to_s, @device)
    else
      # Try to uninstall unknown apps
      log :info, "Found #{num_unknown_apps} unknown app(s), attempting to uninstall"
      uninstall_apps(@unknown_apps)

      # Check if any unknown apps are still installed
      uninstall_response = unknown_apps_installed?(force_refresh = true) ? 'failed' : 'success'
      zombie_push('android', "uninstall-unknown-apps-#{uninstall_response}", '', '', all_unknown_apps.to_s, @device)

      # Exit with failure if unknown apps are still installed
      raise StandardError, "Could not uninstall #{all_unknown_apps}" if uninstall_response == 'failed'
    end
  end

  # Returns true if device contains any app not listed in DB
  def unknown_apps_installed?(force_refresh = false) # rubocop:todo Style/OptionalBooleanParameter
    !all_unknown_apps(force_refresh).empty?
  end

  # Return array of all apps installed on device
  def all_device_apps
    adb.list_packages
  end

  # Return array of known apps from DB
  def all_known_apps
    @known_apps ||= BrowserStack::AppsDatabase.all_apps
    @known_apps
  end

  def remove_google_apps(device_apps)
    return unless @device_obj.exclude_and_uninstall_google_apps

    google_apps = [
      BrowserStack::TRICHROME_LIBRARY_PACKAGE_NAME,
      BrowserStack::PLAY_SERVICES_PACKAGE_NAME,
      BrowserStack::PLAY_STORE_PACKAGE_NAME,
      BrowserStack::WEBVIEW_PACKAGE_NAME,
      BrowserStack::PDFVIEWER_APP_PACKAGE_NAME,
      BrowserStack::GOOGLEMAPS_PACKAGE_NAME,
      BrowserStack::GOOGLE_PHOTOS_PACKAGE,
      BrowserStack::BROWSER_PACKAGE_MAP['chrome']
    ]

    google_apps.each do |app|
      @unknown_apps.append(app) if device_apps.include?(app)
    end
  end

  # Return array of unknown apps on device
  def all_unknown_apps(force_refresh = false) # rubocop:todo Style/OptionalBooleanParameter
    # Memoize unknown_apps unless force_refresh is true
    return @unknown_apps if !@unknown_apps.nil? && !force_refresh

    # Check difference between all installed apps and all known apps in DB
    @unknown_apps = (all_device_apps - all_known_apps)

    #Check model specific whitelist
    if @model_specific_whitelist.key?(@model)
      apps_to_ignore = @model_specific_whitelist[@model]
      @unknown_apps -= apps_to_ignore
      log :info, "Apps: #{apps_to_ignore} are whitelisted for device model #{@model}, ignoring"
    end
    remove_google_apps(all_device_apps)
    log :info, "List of unknown apps found: #{@unknown_apps}"
    @unknown_apps
  end

  def uninstall_apps(apps)
    apps.each do |app|
      adb_response = adb.uninstall(app, verify_uninstall: true)
      log :info, "Uninstall app #{app} response: #{adb_response}"
    rescue AndroidToolkit::ADB::ADBError => e
      log :warn, "Couldn't uninstall #{app} due to error #{e.message}, trying to pm disable-user"
      begin
        adb.shell("pm disable-user #{app}")
        adb_response = adb.shell("pm uninstall --user 0 #{app}")
        log :info, "Uninstall app #{app} response: #{adb_response}"
      rescue StandardError => e
        log :warn, "Couldn't disable/uninstall #{app} with pm due to error #{e.message}"
      end
    end
  end

  def adb
    @adb ||= AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
  end

  def log(level, msg)
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, msg)
    else
      @logger.send(level.to_sym, msg, @logger_params)
    end
  end
end

if $PROGRAM_NAME == __FILE__
  device_id = ARGV[0]
  model = ARGV[1]
  raise 'No device id given' if device_id.nil?

  logger_params = {}
  logger_params[:device] = device_id
  logger_params[:component] = 'UnknownAppsChecker_bash'

  logger = BrowserStack.init_logger("#{BrowserStack::LOGGING_DIR}/cleanup_#{device_id}.log", logger_params)
  app_checker = UnknownAppsChecker.new(device_id, model, logger)
  app_checker.check
end
