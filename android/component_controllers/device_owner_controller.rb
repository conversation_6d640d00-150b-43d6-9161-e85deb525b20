# frozen_string_literal: true

require 'logger'
require 'android_toolkit'
require_relative '../../common/push_to_zombie'
require_relative '../lib/kv_store'

# Documentation:
# https://browserstack.atlassian.net/wiki/spaces/ENG/pages/3389162502/BrowserStackDeviceOwner
class DeviceOwnerController

  ADB_TOGGLE_TRACKER = '/sdcard/Android/data/com.browserstack.deviceowner/files/adb_toggle.txt'
  LOG_PUSHED_TRACKING_KEY = 'LOG_PUSHED_TRACKING_KEY'
  LOG_PUSH_THRESHOLD = 60 * 60

  def initialize(device_id, logger, logger_params = {})
    @logger = logger
    @logger_params = logger_params
    @device = BrowserStack::AndroidDevice.new(device_id, self.class.to_s, logger, logger_params)
  end

  def adb
    @adb ||= AndroidToolkit::ADB.new(udid: @device.id, path: BrowserStack::ADB)
  end

  def cm
    @cm ||= BrowserStack::KVStore.new
  end

  def poller_running?

    adb.shell('dumpsys activity services "com.browserstack.deviceowner/.PollingService" | grep "app="')
       .split('=')
       .pop != 'null'
  rescue AndroidToolkit::ADB::ExecutionError => e
    log(:error, "Failed to check status of poller service probably not running #{e.message}")
    false

  end

  def start_poller(host_ip)
    adb.shell("am startservice --es host_ip #{host_ip}" \
                " --es device_id #{@device.id}" \
                " --es reboot_flow_enabled " \
                "'#{@device.supports_device_owner?}' " \
                "com.browserstack.deviceowner/.PollingService")
  end

  def stop_poller
    adb.shell("am stopservice --user 0 -n com.browserstack.deviceowner/.PollingService")
  rescue AndroidToolkit::ADB::ADBError
    BrowserStack.logger.info("stop_poller failed - might be handled already")
  end

  def ensure_poller_is_running(host_ip)
    if poller_running?
      log(:info, "DO poller is running")
      return
    end

    log(:info, "Poller not running, starting")
    start_poller(host_ip)
  end

  def device_key
    "#{LOG_PUSHED_TRACKING_KEY}_#{@device.id}"
  end

  def push_off_adb_do_logs
    val = cm.get(device_key)

    if (!val.nil? && cm.time_since_last_update(device_key) < LOG_PUSH_THRESHOLD) || adb.ls(ADB_TOGGLE_TRACKER).empty?
      return
    end

    output = adb.shell("cat #{ADB_TOGGLE_TRACKER}")
    log(:info, "Pushing zombie log with toggle value #{output}")
    zombie_key_value(
      platform: 'android',
      kind: "do_off_adb_recovery",
      data: output,
      device: @device.id.to_s,
      os_version: @device.os_version.to_s
    )
    adb.shell("rm -rf #{ADB_TOGGLE_TRACKER}")

    cm.touch device_key

  rescue StandardError => e
    log(:error, "Failed to push off_adb_recovery logs to zombie #{e.message}")
  end

  def log(level, msg)
    formatted_msg = "#{self.class} #{@device.id} #{msg}"
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, formatted_msg)
    else
      @logger.send(level.to_sym, msg, @logger_params.merge(component: self.class.to_s))
    end
  end
end
