require_relative './espresso_test_logs_parser'
require 'browserstack_logger'

class TestLogsParser
  def initialize(framework, log_split_manager)
    @tests = {}
    @log_split_manager = log_split_manager
    BrowserStack.logger.info("[TestLogsParser] Logger initialized with framework #{framework}")
    @parser = case framework
              when FRAMEWORKS[:ESPRESSO] then EspressoTestLogsParser.new(@tests)
              else raise "Unsupported framework: #{framework}"
              end
  end

  def parse(file_path)

    File.open(file_path, 'r') do |file|
      first_log_time = nil
      end_of_file = file.size
      last_log_time = nil

      while (line = file.gets)
        byte_position = file.pos - line.bytesize # Start of the current line
        next_position = file.pos # End of the current line
        parsed_details = @parser.parse_line(line, byte_position, next_position)
        if parsed_details
          first_log_time ||= Time.parse(parsed_details[:timestamp])
          last_log_time = Time.parse(parsed_details[:timestamp])
        end
      end

      BrowserStack.logger.info "found tests #{@tests}"

      # Handle missing start and end times
      fix_missing_times(first_log_time, end_of_file, last_log_time)
      update_first_and_last_tests(end_of_file)
    end
    @tests
  end

  def update_first_and_last_tests(end_of_file_pos)
    first_test = @tests.values.first
    last_test = @tests.values.last

    first_test.log_start_pos = 0 if first_test&.device_log_boundary_valid?

    last_test.log_end_pos = end_of_file_pos if last_test&.device_log_boundary_valid?
  end

  def fix_missing_times(first_log_time, end_of_file_pos, last_log_time)
    sorted_tests = @tests.values

    BrowserStack.logger.info "found sorted tests #{sorted_tests}"

    sorted_tests.each_with_index do |test, i|
      if test.start_time.nil?
        if i == 0
          # First test case - set to first log position
          test.start_time = first_log_time
          test.log_start_pos = 0
          test.update_duration
        else
          previous_test = sorted_tests[i - 1]
          if previous_test.end_time
            # If previous test exists and is complete → use its end position + 1
            test.start_time = previous_test.end_time
            test.log_start_pos = previous_test.log_end_pos ? previous_test.log_end_pos + 1 : nil
            test.update_duration
          else
            # If previous test is incomplete or missing → set both to nil
            test.log_start_pos = nil # TODO: Instrument failure
            @log_split_manager.update_log_split_failures_count(LOG_SPLIT_ERRORS[:device_log_split_failure])
            @log_split_manager.add_log_split_error(test.test_id, "invalid_device_log_boundary")
          end
        end
      end

      if test.end_time.nil?
        if i < sorted_tests.size - 1
          next_test = sorted_tests[i + 1]
          if next_test.start_time
            test.end_time = next_test.start_time
            test.log_end_pos = next_test.log_start_pos ? next_test.log_start_pos - 1 : nil
            test.update_duration
          end
        else
          # Last test case → set end time to start time but leave log_end_pos as nil if unknown
          test.end_time = last_log_time
          test.log_end_pos = end_of_file_pos || nil
          test.update_duration
        end
      end
    end
  end
end
