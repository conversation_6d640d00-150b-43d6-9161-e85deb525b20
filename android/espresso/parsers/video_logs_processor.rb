require_relative './log_process'

class VideoLogsProcessor < LogProcessor
  include LoggerModule

  def should_process?
    log("should_process?: #{@log_split_manager.log_split_enabled} #{@log_split_manager.video_enabled}")
    return false unless @log_split_manager.log_split_enabled
    return false unless @log_split_manager.video_enabled

    true
  end

  def process_log
    log("process_log: Video processing started")
    video_start_time = @log_split_manager.time_components[:video][:start_time]&.to_i
    video_stop_time = @log_split_manager.time_components[:video][:stop_time]&.to_i
    log("Got video times: #{video_start_time} #{video_stop_time}")

    @log_split_manager.tests.each_value do |test|
      test.calculate_video_offsets(video_start_time)

      next if test.video_boundary_valid?

      log("process_log: Video boundary invalid for test: #{test}")
      @log_split_manager.update_log_split_failures_count(LOG_SPLIT_ERRORS[:video_log_split_failure])
      @log_split_manager.add_log_split_error(test.test_id, "invalid_video_log_boundary")
    end

    update_first_and_last_boundaries(video_start_time, video_stop_time)
  end

  def update_first_and_last_boundaries(video_start_time, video_stop_time)
    first_test = @log_split_manager.tests.values.first
    last_test = @log_split_manager.tests.values.last

    first_test.video_start_offset = 0 if first_test&.video_boundary_valid?

    last_test.video_end_offset = video_stop_time - video_start_time if last_test&.video_boundary_valid?
  end

  def backup_and_upload_log
    # Not need of upload for video logs
    nil
  end

end