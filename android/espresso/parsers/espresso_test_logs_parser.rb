require 'browserstack_logger'
require_relative './base_test_logs_parser'
require_relative '../models/test'

class EspressoTestLogsParser < BaseTestLogsParser
  def initialize(tests)
    super()
    @tests = tests
    @previous_timestamp = nil
  end

  def parse_line(line, position, next_position)
    timestamp, log_level, message = extract_log_details(line)

    return unless timestamp && log_level && message

    case message
    when /started: (.+)\((.+)\)/
      test_name = Regexp.last_match(1)
      test_class = Regexp.last_match(2)
      key = "#{test_class}.#{test_name}"
      @tests[key] = Test.new(name: test_name, klass: test_class)
      @tests[key].start(timestamp, test_class, position)
    when /finished: (.+)\((.+)\)/
      test_name = Regexp.last_match(1)
      test_class = Regexp.last_match(2)
      key = "#{test_class}.#{test_name}"
      test = (@tests[key] ||= Test.new(name: test_name, klass: test_class))
      test.pass(timestamp, next_position)
    when /failed: (.+)\((.+)\)/
      test_name = Regexp.last_match(1)
      test_class = Regexp.last_match(2)
      key = "#{test_class}.#{test_name}"
      test = (@tests[key] ||= Test.new(name: test_name, klass: test_class))
      test.fail(timestamp, next_position)
    when /ignored: (.+)\((.+)\)/
      test_name = Regexp.last_match(1)
      test_class = Regexp.last_match(2)
      key = "#{test_class}.#{test_name}"
      @tests[key] = Test.new(name: test_name, klass: test_class)
      @tests[key].skip(timestamp, next_position)
    when /assumption failed: (.+)\((.+)\)/
      test_name = Regexp.last_match(1)
      test_class = Regexp.last_match(2)
      key = "#{test_class}.#{test_name}"
      test = (@tests[key] ||= Test.new(name: test_name, klass: test_class))
      test.assumption_failed(timestamp, next_position)
    when /TestTimedOutException/
      last_test = @tests.values.last
      last_test&.timeout(timestamp, next_position)
    end

    {
      timestamp: timestamp,
      log_level: log_level,
      message: message
    }
  end

  # Extracts log details from a given log line.
  #
  # @param line [String] the log line to be parsed.
  # @return [Array] an array containing the timestamp, log level, and message extracted from the log line.
  #   Returns [nil, nil, nil] if the line does not match the expected format.
  #
  # @example
  #   line = "2025-03-26 19:20:42.130 +0000 I/TestRunner(21239): started: \
  #           The counter can be incremented 1(Sample feature for Cucumber with Espresso)"
  #   extract_log_details(line)
  #   # => ["2025-03-26 19:20:42.130 +0000", "I", "started: The counter can be incremented 1\
  #         (Sample feature for Cucumber with Espresso)"]
  #
  # @example
  #   line = "2025-03-26 19:20:45.574 +0000 I/TestRunner(21239): finished: The counter can be incremented 1\
  #   (Sample feature for Cucumber with Espresso)"
  #   extract_log_details(line)
  #   # => ["2025-03-26 19:20:45.574 +0000", "I", "finished: The counter can be incremented 1\
  #     (Sample feature for Cucumber with Espresso)"]
  def extract_log_details(line)
    match = line.match(
      %r{(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3} [+-]\d{4}) ([IE])/TestRunner\s*\(\s*(\d+)\s*\): (.+)}
    )
    return [nil, nil, nil] unless match

    [match[1], match[2], match[4]]

  end
end
