require 'fileutils'
require 'json'

require_relative './log_process'
require_relative '../../../android/proxies/mitmproxy/mitm_flow_convertor'
require_relative '../helpers/errors'

class NetworkLogsProcessor < LogProcessor

  def initialize(log_split_manager)
    super(log_split_manager, network_log_file(log_split_manager.device))
    @harlogs = "#{@log_split_manager.session_id}-har-logs.txt"
    @harlogs_filepath = File.join(BrowserStack::LOGGING_DIR, @harlogs)
    @fallback_happened = false
    @output_file = "/var/log/browserstack/mitm_#{log_split_manager.device}.log"
    @capture_content_enabled = is_true?(log_split_manager.summary_data["networkLogsCaptureContent"])
    @har_dump_log_split_script = File.join(BrowserStack::BS_DIR, "mobile/android/scripts/har_dump/split_flow_to_har.py")
    @har_dump_script = File.join(BrowserStack::BS_DIR, "mobile/android/scripts/har_dump.py")
    @tmp_test_details_file = File.join(BrowserStack::TMP_DIR, "test_details_#{log_split_manager.session_id}.json")
  end

  def network_log_file(device)
    "/tmp/mitm_flow_file_#{device}.txt"
  end

  def script_args(fallback: false)
    args = {
      captureContent: @capture_content_enabled
    }
    args[:testDetails] = test_details_file unless fallback

    args
  end

  def test_details_file
    log("Creating temporary test_details file at: #{@tmp_test_details_file}")
    write_to_file(@tmp_test_details_file, tests_json)
    @tmp_test_details_file
  end

  def tests_json
    tests_hash = {}
    @log_split_manager.tests.each do |key, test|
      tests_hash[key] = {
        "name" => key,
        "start_time" => test.start_time ? test.start_time.strftime("%Y-%m-%d %H:%M:%S %z") : nil,
        "network_log_boundary" => {
          "start" => nil,
          "end" => nil
        }
      }
    end
    log("test json file content: #{tests_hash}")
    tests_hash
  end

  def update_boundaries
    log("Updating network log boundaries")
    updated_test_details = JSON.parse(File.read(@tmp_test_details_file))
    log("Updated test details: #{updated_test_details}")

    invalid_test_found = false
    valid_tests = 0
    updated_test_details.each do |name, test|
      @log_split_manager.tests[name].update_network_log_boundary(test["network_log_boundary"]["start"],
                                                                 test["network_log_boundary"]["end"])
      if @log_split_manager.tests[name].network_log_boundary_valid?
        valid_tests += 1
        next
      end

      invalid_test_found = true
      @log_split_manager.update_log_split_failures_count(LOG_SPLIT_ERRORS[:network_log_split_failure])
      @log_split_manager.add_log_split_error(test.test_id, "invalid_network_log_boundary")
    end

    if invalid_test_found || valid_tests < @log_split_manager.tests.keys.count
      raise InvalidLogBoundaryError, "Invalid network log boundary found"
    end
  end

  def convert_flow_to_har(fallback: false)
    script = fallback ? @har_dump_script : @har_dump_log_split_script

    flow_to_har = FlowToHar.new(
      flow_file: @log_file,
      output_file: @output_file,
      har_dump_script: script,
      hardump_file_path: @harlogs_filepath,
      env_vars: {
        PYTHONDONTWRITEBYTECODE: 1
      },
      set_values: script_args(fallback: fallback)
    )

    flow_to_har.convert
  end

  def process_log
    @file_to_upload = {
      file_path: @harlogs_filepath,
      s3_path: @harlogs,
      tag: "#{@log_split_manager.framework}_networklogs"
    }

    if File.zero?(@log_file)
      fallback_network_logs("DumpFile is empty")
      return
    elsif tests_have_invalid_start_time?
      fallback_network_logs("No Tests contain start_time")
      return
    end

    begin
      log("Converting flow to har file")
      convert_flow_to_har(fallback: false)
      update_boundaries
      log("successfully converted flow to har file")
    rescue InvalidLogBoundaryError => e
      log("Processing network logs with logs split failed with InvalidLogBoundaryError - #{e.message} #{e.backtrace}" )
      fallback_network_logs("Invalid log boundary found")
    rescue StandardError => e
      log("Processing network logs with logs split failed with error - #{e.message} #{e.backtrace}" )
      fallback_network_logs("Error: #{e.message[0, 100]}")
    end
  end

  def fallback_network_logs(error_reason)
    return if @fallback_happened

    @fallback_happened = true
    log("Fallback to consolidated network logs because of #{error_reason}")
    push_to_zombie("network-log-split-fallback", error_reason, {})
    @log_split_manager.update_log_split_failures_count(LOG_SPLIT_ERRORS[:network_log_split_fallback])

    # Truncates any partial har file generation
    File.truncate(@harlogs_filepath, 0) if File.exist?(@harlogs_filepath)
    convert_flow_to_har(fallback: true)
    update_consolidated_bounds
  rescue FlowToHarConversionError => e
    @log_split_manager.log("FlowToHarConversionError exception in network logs fallback:  #{e.message}, #{e.backtrace}")
    handle_parse_error(e.status.exitstatus, @output_file, @log_file)
    @file_to_upload[:tag] = "#{@log_split_manager.framework}_corruptNetworkLogs"
  rescue StandardError => e
    @log_split_manager.log("StandardError exception in network logs fallback: #{e.message}, #{e.backtrace}")
    @file_to_upload[:tag] = "#{@log_split_manager.framework}_corruptNetworkLogs"
    push_to_zombie("network-log-split-fallback-error", e.message[0, 100], {})
  end

  def update_consolidated_bounds
    log("Updating network logs boundaries for every test with nil values")

    @log_split_manager.tests.each_value do |test|
      test.update_network_log_boundary(start: nil, end_range: nil)
    end
  end

  def handle_parse_error(exit_code, output_file, flow_file)
    generic_msg = exit_code == 124 ? 'timeout' : 'error'
    parse_error_url = ''

    if generic_msg == 'error'
      parse_error_url = `tail -2 #{output_file}`.strip
      log("Got parse error for the URL: #{parse_error_url}", :info,
          session_id: @log_split_manager.session_id)
    end

    exact_file_size = begin
      File.size(flow_file)
    rescue StandardError
      0
    end
    push_to_zombie(
      "app-networklogs-parse-#{generic_msg}",
      "#{generic_msg}: #{parse_error_url}",
      exact_file_size
    )
  end

  def validate_log_file
    return if @log_file.empty?

    log("log file exists - #{File.exist?(@log_file)} - #{@log_file}")
    raise FileNotFound, "Log file: #{@log_file} not found" unless File.exist?(@log_file)
  end

  def should_process?
    return false unless @log_split_manager.log_split_enabled
    return false unless @log_split_manager.network_enabled

    true
  end

  def tests_have_invalid_start_time?
    @log_split_manager.tests.values.all? { |test| test.start_time.nil? }
  end
end
