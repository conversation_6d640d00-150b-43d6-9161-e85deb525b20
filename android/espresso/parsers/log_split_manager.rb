require 'json'
require 'fileutils'
require 'date'

require_relative '../helpers/errors'
require_relative './test_logs_parser'
require_relative './network_logs_processor'
require_relative './video_logs_processor'
require_relative './cucumber_tests_parser'

class LogSplitManager
  include LoggerModule
  log_instance_variables(enabled: true, variables: [:session_id])

  attr_accessor :test_details, :split_time, :final_test_status, :log_split_errors, :log_split_failures,
                :test_details_valid, :mitmproxy_instance
  attr_reader :total_session_time, :instrumentation_file, :device, :session_id, :time_components, :retry_config,
              :base_s3_url, :build_id, :logs_stability_file, :total_tests_or_sessions, :framework, :callback_file,
              :tests, :summary_data_v2, :log_split_enabled, :summary_data, :network_enabled, :video_enabled,
              :cucumber_test_suite, :callback_data

  def initialize(device, session_id, framework, conf = {})
    @device = device
    @session_id = session_id
    @framework = framework
    @base_s3_url = conf[:base_s3_url]
    @build_id = conf[:build_id]
    @video_enabled = conf[:video_enabled]
    @network_enabled = conf[:network_enabled]
    @device_enabled = conf[:device_enabled]
    @time_components = conf[:time_components]
    @callback_file = conf[:callback_file]
    @callback_data = conf[:callback_data]
    @logs_stability_file = conf[:logs_stability_file]
    @total_tests_or_sessions = conf[:total_tests_or_session]
    @log_split_failures = {}
    @log_split_errors = {}
    @summary_data_v2 = conf[:summary_data_v2]
    @summary_data = conf[:summary_data]
    @log_split_enabled = is_true?(@summary_data["splitLogs"])
    @cucumber_test_suite = @summary_data['test_suite_details']['is_cucumber_test_suite']
    @tests = {}
  end

  def process_logs
    log("Started processing logs #{@video_enabled} #{@network_enabled} #{@device_enabled}")
    process_device_logs # Always parse for timestamps of tests in other logs
    process_video_logs if @video_enabled

    @tests = CucumberTestsParser.new(self).merged_tests if @cucumber_test_suite

    process_network_logs if @network_enabled
    instrument_log_processing
    log("Finished processing logs")
  rescue StandardError => e
    raise e
  end

  def instrument_log_processing
    if @device_enabled
      send_log_split_feature_usage_data(
        @log_split_failures[LOG_SPLIT_ERRORS[:device_log_split_failure]],
        LOG_SPLIT_FEATURE_USAGE[:deviceLogSplitting],
        success: @log_split_failures[LOG_SPLIT_ERRORS[:device_log_split_error]].nil?
      )
    end

    if @video_enabled
      send_log_split_feature_usage_data(
        @log_split_failures[LOG_SPLIT_ERRORS[:video_log_split_failure]],
        LOG_SPLIT_FEATURE_USAGE[:videoLogSplitting],
        success: @log_split_failures[LOG_SPLIT_ERRORS[:video_log_split_error]].nil?
      )
    end

    if @network_enabled
      send_log_split_feature_usage_data(
        @log_split_failures[LOG_SPLIT_ERRORS[:network_log_split_failure]],
        LOG_SPLIT_FEATURE_USAGE[:networkLogSplitting],
        success: @log_split_failures[LOG_SPLIT_ERRORS[:network_log_split_error]].nil?
      )

      send_log_split_feature_usage_data(
        @log_split_failures[LOG_SPLIT_ERRORS[:network_log_split_fallback]],
        LOG_SPLIT_ERRORS[:network_log_split_fallback],
        success: @log_split_failures[LOG_SPLIT_ERRORS[:network_log_split_fallback]].nil?
      )
    end

    errors = get_log_splitting_error_reasons(@log_split_failures)
    add_error_reason(errors) if errors && !errors.empty?
  end

  def send_log_split_feature_usage_data(failures, feature, success: nil)
    success = failures.nil? if success.nil? # TODO: Send instrumentation
    eds = EDS.new({}, BrowserStack.logger)
    feature_usage = {
      feature => {
        "success": success.to_s
      }
    }
    feature_usage[feature]['exception'] = failures.to_s[0, 100] if success.to_s.downcase != "true"

    eds_data = {
      "feature_usage": feature_usage,
      "hashed_id": @session_id.to_s,
      "timestamp": Time.now.to_i.to_s
    }
    log("Sending feature usage data to eds: #{eds_data}")
    eds.push_logs(EdsConstants::APP_AUTOMATE_TEST_SESSIONS, eds_data).join
  end

  def add_error_reason(error_reason)
    log("Adding error reasons: #{error_reason}")
    @callback_data["error_reason"] = [] unless @callback_data["error_reason"].is_a?(Array)
    @callback_data["error_reason"] += error_reason
  end

  def get_log_splitting_error_reasons(log_split_failures)
    return [] if log_split_failures.empty?

    log_split_failure_reasons = []
    if log_split_failures[LOG_SPLIT_ERRORS[:device_log_split_failure]]
      log_split_failure_reasons.push("device-partial-split-failure")
    end
    if log_split_failures[LOG_SPLIT_ERRORS[:video_log_split_failure]]
      log_split_failure_reasons.push("video-partial-split-failure")
    end

    # Log splitting doesn't stops if device/video log splitting throws an error.
    # Hence error thrown by these log processor won't be handled by #handle_log_processing_error
    # We want to track these errors as error_reasons too.
    if log_split_failures[LOG_SPLIT_ERRORS[:device_log_split_error]]
      log_split_failure_reasons.push("device-split-failure")
    end
    if log_split_failures[LOG_SPLIT_ERRORS[:video_log_split_error]]
      log_split_failure_reasons.push("video-split-failure")
    end
    if log_split_failures[LOG_SPLIT_ERRORS[:network_log_split_error]]
      log_split_failure_reasons.push("network-split-failure")
    end
    if log_split_failures[LOG_SPLIT_ERRORS[:network_log_split_fallback]]
      log_split_failure_reasons.push("network-split-fallback")
    end

    log_split_failure_reasons
  end

  def process_device_logs

    test_logs_parser = TestLogsParser.new(@framework, self)
    @tests = test_logs_parser.parse(device_log_file)
  rescue StandardError => e
    log("Exception in device logs parsing #{e.message} #{e.backtrace} ")
    push_to_zombie("device-log-split-error", "Error: #{e.message[0, 100]}", {})
    update_log_split_failures_count(LOG_SPLIT_ERRORS[:device_log_split_error])

  end

  def process_video_logs

    log("started processing video logs")
    video_logs_processor = VideoLogsProcessor.new(self)
    video_logs_processor.process
    log("stopped processing video logs")
  rescue StandardError => e
    log("Exception in video logs processing #{e.message} #{e.backtrace} ")
    push_to_zombie("video-log-split-error", "Error: #{e.message[0, 100]}", {})
    update_log_split_failures_count(LOG_SPLIT_ERRORS[:video_log_split_error])

  end

  def process_network_logs

    log("started processing network logs")
    NetworkLogsProcessor.new(self).process
    log("stopped processing network logs")
  rescue StandardError => e
    log("Exception in processing network logs: #{e.message} #{e.backtrace}")
    push_to_zombie("network-log-split-error", "Error: #{e.message[0, 100]}", {})
    update_log_split_failures_count(LOG_SPLIT_ERRORS[:video_log_split_error])

  end

  def add_log_split_error(test, error)
    if @log_split_errors.key?(error)
      @log_split_errors[error] << test
    else
      @log_split_errors[error] = [test]
    end
  end

  def update_log_split_failures_count(failure_type, test_property = nil)
    log("Updating log split failure count for #{failure_type} #{test_property}")
    if test_property
      if @log_split_failures[failure_type]
        existing_value = @log_split_failures[failure_type][test_property]
        @log_split_failures[failure_type][test_property] = existing_value.is_a?(Integer) ? existing_value + 1 : 1
      else
        @log_split_failures[failure_type] = {}
        @log_split_failures[failure_type][test_property] = 1
      end
    else
      existing_value = @log_split_failures[failure_type]
      @log_split_failures[failure_type] = existing_value.is_a?(Integer) ? existing_value + 1 : 1
    end
  end

  def push_to_zombie(kind, error, data)
    data[:framework] = @framework if data.is_a?(Hash)

    zombie_push(
      ANDROID,
      kind,
      error,
      ANDROID,
      data,
      @device,
      @session_id
    )
  end

  def test_key(klass, name)
    "#{klass}.#{name}"
  end

  private

  def device_log_file
    File.join(LOGGING_DIR, "app_log_#{@device}.log")
  end
end

