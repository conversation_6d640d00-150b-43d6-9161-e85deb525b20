require 'json'
require 'time'
require_relative '../../lib/logging/logger'

class CucumberTestsParser
  include LoggerModule

  def initialize(log_split_manager)
    @log_split_manager = log_split_manager
  end

  def merged_tests
    log("get_tests: changing normal tests to cucumber tests #{@log_split_manager.tests}")

    merged_tests = {}

    @log_split_manager.tests.each_value do |test|
      base_name = test.name.gsub(/\s\d+$/, '') # Remove trailing number
      key = "#{test.klass}.#{base_name}" # Unique key with klass

      if merged_tests.key?(key)
        merged_tests[key].merge(test)
      elsif test.name != base_name
        merged_tests[key] = test
      else
        merged_tests[key] = Test.copy(test)
        test.name = base_name
      end
    end

    log("get_tests: merged tests: #{merged_tests}")
    merged_tests
  end
end
