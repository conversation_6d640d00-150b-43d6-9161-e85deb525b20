require 'English'
require 'time'
require 'json'

require_relative '../../lib/logging/logger'
require_relative '../helpers/errors'
require_relative '../helpers/frameworks_s3_helper'
require_relative '../../../android/scripts/upload_to_s3'
require_relative '../../../android/scripts/logs_stability_tracker'

class LogProcessor
  include LoggerModule
  def initialize(log_split_manager, log_file = "")
    @log_split_manager = log_split_manager
    @log_file = log_file
    @output_log_file = nil
    @tag_for_upload = nil
    @file_to_upload = {
      file_path: @log_file,
      tag: @tag_for_upload,
      s3_path: nil
    }
  end

  def process
    log("processing log file #{@log_file}")
    return unless should_process?

    log("should process success")
    validate_log_file
    log("validated_log_file")
    process_log
    log("processed_log_file")
    validate_test_details
    post_validation_steps
    backup_and_upload_log
  end

  def should_process?
    # Child class should implement this
    raise NotImplementedError
  end

  def process_log
    # Child class should implement this
  end

  def validate_test_details
    # Child class should implement this
  end

  def post_validation_steps
    # Child class should implement this
  end

  def validate_log_file
    return if @log_file.empty?

    raise FileNotFound, "Log file: #{@log_file} not found" unless File.exist?(@log_file)
    raise FileEmpty, "Log file: #{@log_file} is empty" if File.zero?(@log_file)
  end

  def s3_url_for_upload
    "#{@log_split_manager.base_s3_url}/#{@log_split_manager.session_id}/#{@file_to_upload[:s3_path]}"
  end

  def backup_and_upload_log(tag: nil)
    unless @log_split_manager.log_split_enabled
      log("Log split is not enabled, not uploading")
      return
    end

    s3_url = s3_url_for_upload
    filepath = @file_to_upload[:file_path]
    tag ||= @file_to_upload[:tag]

    ext = File.extname(s3_url).delete_prefix('.')
    timestamp = Time.now.to_i * 1_000_000_000 + Time.now.nsec
    backup_file = "/tmp/#{@log_split_manager.framework}_logs_#{timestamp}_#{tag}.#{ext}"

    log("Backup and uploading file - #{filepath} with tag: #{tag} to s3_url #{s3_url}")

    unless File.exist?(filepath)
      log("file not existing - returning")

      push_to_zombie("#{tag}-upload-file-missing", "file-not-exists-error", "")
      return
    end

    safe_cp_status = safe_cp(filepath, backup_file)

    if safe_cp_status != 0
      log("Safe copying failed - #{filepath} with tag: #{tag} to s3_url #{s3_url}")
      @log_split_manager.add_error_reason(["machine-storage-issue"])
    end

    upload_file_to_s3(backup_file, tag)
  end

  def upload_file_to_s3(file_path, tag, retry_count: nil)
    log("Uploading file to S3 - #{file_path} with tag: #{tag} retry_count: #{retry_count}")
    s3_url = s3_url_for_upload
    device_id = @log_split_manager.device
    build_id = @log_split_manager.build_id
    logs_stability_file = @log_split_manager.logs_stability_file
    total_tests_or_session = @log_split_manager.total_tests_or_sessions
    retry_count ||= 1

    s3_params = FrameworksS3Helper.get_s3_params(device_id, build_id)
    ext = File.extname(s3_url).delete_prefix('.')

    if File.exist?(file_path)
      exact_file_size = File.size(file_path)
      file_size_kb = exact_file_size / 1024
      s3_storage_class = file_size_kb < 128 ? 'STANDARD' : s3_params[:storage_class]

      content_type = get_content_type_from_ext(ext)

      status, error = UploadToS3.upload_file_to_s3(
        s3_params[:key], s3_params[:secret], content_type, file_path,
        'public-read', s3_url, nil, 600, s3_storage_class
      )

      log("upload_file_s3: for #{file_path} status: #{status} error: #{error}")

      if !status
        if ext == 'txt' && retry_count < MAX_UPLOAD_RETRY_COUNT
          retry_count += 1
          log "Retrying upload... Attempt #{retry_count}"
          upload_file_to_s3(file_path, tag, retry_count: retry_count)
          return
        else
          log("upload_file_s3: upload timeout")
          push_to_zombie( "app-#{tag}-upload-timeout",  "timeout-error: #{error}", exact_file_size)
          status = "failed"
        end
      else
        log("upload_file_s3: upload done")
        push_to_zombie( "app-#{tag}-upload-done", "success", exact_file_size)
        status = "success"
      end

      push_to_zombie("app-#{tag}-upload-retry", "timeout-retry-#{retry_count}", exact_file_size) if retry_count > 1

      # Upload Stability Tracker for dry_run (skip for parselogs)
      unless tag.include?("parselogs")
        BrowserStack::LogsStabilityTracker.new.update_logs_stability_metrics(
          device_id, logs_stability_file, tag, exact_file_size, total_tests_or_session, 1, s3_url, status
        )
      end

      File.delete(file_path) if File.exist?(file_path)
    else
      log("upload_file_s3: file not found: #{file_path}")
      # Track log upload status as skipped if file doesn't exist
      unless tag.include?("parselogs")
        BrowserStack::LogsStabilityTracker.new.update_logs_stability_metrics(
          device_id, logs_stability_file, tag, 0,
          total_tests_or_session, 1, s3_url, 'failed'
        )
      end
    end
  end

  def push_to_zombie(kind, error, data)
    data[:framework] = @framework if data.is_a?(Hash)
    zombie_push(
      ANDROID,
      kind,
      error,
      ANDROID,
      data,
      @log_split_manager.device,
      @log_split_manager.session_id
    )
  end

    # Drop millisecond part of time object if it is present and return a datetime object of format -> YY:MM:DD HH:MM:SS
  def sanitized_time(time)
    DateTime.parse(DateTime.parse(time.to_s).to_time.to_s).to_time
  end

  def instrument_copy_error(source, output, status)
    cp_issue_regex = /No space left on device/

    if output =~ cp_issue_regex
      data = { status: status, source: source }.to_json
      log "#{@log_split_manager.session_id} Detected espresso system copy error : #{data}"

      push_to_zombie("copy-error", output.to_s, data)
      return 1
    end

    0
  end

  def safe_cp(source, destination)
    cp_output = `cp #{source} #{destination} 2>&1`
    status = $CHILD_STATUS.exitstatus

    instrument_copy_error("log_processor", cp_output, status)

  end

end
