#!/bin/bash
set -x
session_id="$1"
screenshots_enabled="$2"
screenshots_folder="$3"
base_s3url="$4"
aws_key="$5"
aws_secret="$6"
BUNDLE="/home/<USER>/bin/bundle"
ZOMBIE_SCRIPT="/usr/local/.browserstack/mobile/common/push_to_zombie.rb"
UPLOADS_COUNTER=/tmp/espresso_upload_counts_${session_id}.log

init_uploads_counter() {
  echo '{"total":0,"active":0,"failed":0,"success":0}' > $UPLOADS_COUNTER
}

update_upload_counts() {
  local updater=""

  for update in "$@"
  do
    [ ! -z $updater ] && updater+="|"
    updater+=$update
  done

  local tmp_file=${UPLOADS_COUNTER}.${RANDOM}.tmp
  flock $UPLOADS_COUNTER --command "jq '"$updater"' $UPLOADS_COUNTER > $tmp_file && mv $tmp_file $UPLOADS_COUNTER"
}

upload_to_s3() {
  local file="$1"
  local retry_count=${2:-1}
  local exact_file_size=$(ls -l $file  | awk '{print $5}')
  local max_upload_retry_count=2

  screenshots_s3_url="$base_s3url/$session_id/screenshots/$(basename "$file")"
  timeout 12 $BUNDLE exec ruby "/usr/local/.browserstack/mobile/android/scripts/upload_to_s3.rb" "$aws_key" "$aws_secret" "image/jpeg" "$file" "public-read" "$screenshots_s3_url" "" 10
  return_value=$?
  if [[ "$return_value" -ne 0 ]]; then
    if [[ $retry_count -le $max_upload_retry_count ]]
    then
      retry_count=$((retry_count+1))
      upload_to_s3 $file $retry_count
      return 0
    else
      echo "$session_id upload screenshots failed with exit code - $return_value"
      upload_data="{\"return_value\": $return_value, \"file_size_bytes\": $exact_file_size, \"retry_count\": $retry_count}"
      $BUNDLE exec ruby $ZOMBIE_SCRIPT "android" "espresso-screenshot-upload-failed" "screenshot upload failed" "" "$upload_data" "" "$session_id"

      update_upload_counts '.active-=1' '.failed+=1'
      return 0
    fi
  else
    update_upload_counts '.active-=1' '.success+=1'
  fi

  if [[ $retry_count -gt 1 ]]
  then
    $BUNDLE exec ruby $ZOMBIE_SCRIPT "android" "espresso-screenshot-upload-retry" "timeout-retry-$retry_count" "" "$exact_file_size" "" "$session_id"
  fi
}

if [[ "$screenshots_enabled" == "true" ]]
then
  init_uploads_counter
  screenshots_upload_start_time=$(date +%s%N)

  for file in $(find "$screenshots_folder/app_spoon-screenshots" -type f)
  do
    upload_to_s3 "$file" &
    update_upload_counts '.active+=1' '.total+=1'

    active_uploads=$(jq '.active' $UPLOADS_COUNTER)
    sleep $(bc <<< "0.01 * $active_uploads * $active_uploads")
  done

  for file in $(find "$screenshots_folder/screenshots" -type f)
  do
    upload_to_s3 "$file" &
    update_upload_counts '.active+=1' '.total+=1'

    # quadratic backoff before next screenshot upload
    # this ensures that there is a cap on max parallel upload processes depending on upload speed
    #
    # high CPU load or slow network => slower uploads => more active upload processes => quadratically more sleep before creating next process
    active_uploads=$(jq '.active' $UPLOADS_COUNTER)
    sleep $(bc <<< "0.01 * $active_uploads * $active_uploads")
  done

  wait
  screenshots_upload_end_time=$(date +%s%N)
  screenshots_upload_total_time=`echo "($screenshots_upload_end_time - $screenshots_upload_start_time)/1000000" | bc`
  echo "$session_id Screenshots upload time: $screenshots_upload_total_time ms"

  # upload time in seconds
  total_upload_time=$(bc <<< "scale=3; $screenshots_upload_total_time/1000")

  upload_summary=$(jq -c '.' $UPLOADS_COUNTER)
  $BUNDLE exec ruby $ZOMBIE_SCRIPT "android" "espresso-screenshot-upload-summary" "$total_upload_time" "" "$upload_summary" "" "$session_id"

  rm $UPLOADS_COUNTER
else
  echo "$session_id Screenshots not enabled therefore not uploading"
fi
