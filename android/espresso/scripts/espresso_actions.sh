#!/bin/bash
set -x
TODO=$1
DEVICEID=$2
BUILD_ID=$3
SESSION_ID=$4

BS_DIR="/usr/local/.browserstack"
source $BS_DIR/mobile/android/common.sh

SELENIUM_SCRIPT="/usr/local/.browserstack/mobile/android/helpers/selenium_helper.sh"
CHECK_STATUSBAR_PHONE="/data/local/tmp/check_status_bar.sh"
DEVICELOGS_FILE="/var/log/browserstack/app_log_$DEVICEID.log"
NETWORKLOGS_FILE="/var/log/browserstack/mitm_flow_file_$DEVICEID.txt"
TMP_NETWORKLOGS_FILE="/tmp/mitm_flow_file_$DEVICEID.txt"
MITM_CMD_FILE="/tmp/run_mitm_$DEVICEID.sh"
ESPRESSO_HELPER_RUNNER="/usr/local/.browserstack/mobile/android/espresso/scripts/espresso_helper_runner.rb"
ESPRESSO_MONITOR_SCRIPT="/usr/local/.browserstack/mobile/android/espresso/scripts/espresso_monitor.rb"
INSTRUMENTATION_RUNNER_SCRIPT="/usr/local/.browserstack/mobile/android/espresso/scripts/instrumentation_runner.rb"
RTC_HELPER="/usr/local/.browserstack/mobile/android/helpers/rtc_helper.rb"
ESPRESSO_SCREENSHOT_HELPER="/usr/local/.browserstack/mobile/android/espresso/helpers/screenshot_helper.rb"
ASSETS_HELPER_SCRIPT="/usr/local/.browserstack/mobile/android/espresso/helpers/assets_helper.rb"
INSTRUMENTATION_LOGS_FILE="/tmp/espresso_instrumentation_$DEVICEID"
LOGS_STABILITY_FILE="/tmp/espresso_logs_stability_$SESSION_ID"
PUSHER_MSG_FILE="/tmp/espresso_pusher_message_${DEVICEID}"
TESTSUITE_FILE="/tmp/espresso_testsuite_$DEVICEID"
SESSION_TIMEOUT_FILE="/tmp/espresso_timeout_${DEVICEID}_${SESSION_ID}"
BRT_RESTART_ON_OFF_ADB_FILE="/tmp/brt_restart_on_off_adb_${DEVICEID}_${SESSION_ID}"
SESSION_SUMMARY_FILE="/tmp/espresso_summary_$DEVICEID"
DUPLICATE_SESSION_SUMMARY_FILE="/tmp/duplicate_session_$DEVICEID"
STABILITY_TRACKER_SCRIPT="/usr/local/.browserstack/mobile/android/scripts/logs_stability_tracker.rb"
MOBILE_PACKAGE_FILE="/sdcard/app_package_name"
CALLBACK_FILE="/tmp/espresso_callback_$DEVICEID"
COMMON="/usr/local/.browserstack/mobile/common"
FLOW_TO_HAR="/usr/local/.browserstack/mobile/android/scripts/har_dump.py"
SCREENSHOTS_FOLDER="/tmp/$DEVICEID/spoon-screenshots"
SCREENSHOTS_UPLOAD_SCRIPT="/usr/local/.browserstack/mobile/android/espresso/scripts/upload_test_screenshots.sh"
STATE_FILES_DIR="/usr/local/.browserstack/state_files"
SESSION_FILE="$STATE_FILES_DIR/session_$DEVICEID"
APP_SESSION_FILE="/tmp/app_session_$DEVICEID"
DEVICE_COVERAGE_FILE_FOLDER="/sdcard/Download/reports"
MERGE_COVERAGE_JAR="/usr/local/.browserstack/webrtc/jacococli.jar"
COVERAGE_FILE_FOLDER="/tmp/$DEVICEID/coverage"
COVERAGE_FILE_NAME="coverage.ec"
MAX_TEST_RETRY_COUNT=3
MAX_UPLOAD_RETRY_COUNT=3
VIDEO_RECORDING_SCRIPT="/usr/local/.browserstack/mobile/android/helpers/android_video_recording.sh"
VIDEO_OFFSET_FILE="/tmp/video_offset_files/offset_file_session_${SESSION_ID}"
BROWSERSTACK_WATCHER_HELPER="/usr/local/.browserstack/mobile/android/lib/browserstack_watcher_helper.rb"
DEVICE_ORIENTATION="portrait"
APP_PERCY_HELPER="/usr/local/.browserstack/mobile/android/espresso/scripts/app_percy_runner.rb"
BSTACK_REVERSE_TETHER_CONTROLLER="$BS_DIR/mobile/android/lib/bstack_reverse_tether_controller.rb"
TEST_OBSERVABILITY_RUNNER="/usr/local/.browserstack/mobile/android/espresso/scripts/test_observability_runner.rb"
CURL_CA_BUNDLE="/etc/ssl/certs/ca-bundle.crt"
MAESTRO_RUNNER="/usr/local/.browserstack/mobile/android/maestro/scripts/maestro_runner.rb"
MAESTRO_HELPER_RUNNER="/usr/local/.browserstack/mobile/android/maestro/helpers/maestro_helper_runner.rb"


# This metadata file will be updated with each consecutive test execution.
# The final JUnit report will be rendered at the end (once all the tests are executed)
# The purpose of this file is to persist the data across test executions (in order to generate the final
# cumulative report)
MERGED_JUNIT_REPORT_METADATA_FILE="/tmp/temp_espresso_junit_report_$SESSION_ID.metadata"

DEVICE_SCREENSHOTS_FOLDER=("/storage/emulated/0/app_spoon-screenshots" "/storage/emulated/0/Download/screenshots")

# Path to the final junit xml report, which will be generated once all the tests
# have been executed
JUNIT_XML_REPORT_FILE="/tmp/espresso_junit_report_$SESSION_ID.xml"

source /usr/local/.browserstack/mobile/android/live_common.sh

log() {
  echo "$SESSION_ID: `date -u`: "$@ >> /var/log/browserstack/selenium_$DEVICEID.log
}

set_device_orientation(){
  safe_adb "" -s $DEVICEID shell am startservice --user 0 -n com.android.browserstack/.services.OrientationService --es "orientation" "$1"
}

set_timezone(){
  if device_is_a bsrun_device; then
    run_as_root "setprop persist.sys.timezone $1"
  else
    echo "Cannot set_timezone on non-rooted device. Ignoring."
  fi
}

function log_start_time() {
  START=""
  END=""
  START=$(date +%s%N)
}

function log_end_time() {
  END=$(date +%s%N)
  LOG_TIME=`echo "($END - $START)/1000000" | bc`
  echo $LOG_TIME
}

function get_test_expire_time() {
  current_time=$(date +%s)
  timeout_time="$(( $TOTAL_SESSION_TIME - ( $current_time - $SESSION_START_TIME ) ))"
  if [ "$timeout_time" -le 0 ]
  then
    touch $SESSION_TIMEOUT_FILE
  fi
  echo $timeout_time
}

function notify_pusher() {
  session_params=$(cat $SESSION_FILE)
  pusher_params=$(echo $session_params | jq -r '. | "type=app_frameworks&channel=" + .pusher_channel + "&token=" + .pusher_auth ' | tr -d "\r\n")
  pusher_params="${pusher_params}&event=$1&message=$2"
  pusher_url=$(echo "$session_params" | jq -r ".pusher_url" | tr -d "\r\n")
  curl --cacert $CURL_CA_BUNDLE -d "$pusher_params" "${pusher_url}/sendMessage"
}

function add_apps_for_cleanup() {
  # Put app package details for cleanup
  play_services $DEVICEID "enable" &
  echo $APP_BUNDLEID > $APP_SESSION_FILE
  echo ${TEST_BUNDLEID} >> $APP_SESSION_FILE

  if [[ `cat $SESSION_FILE | jq -r ".other_app_bundle_ids"` != "null" ]]; then
    for other_app in `cat $SESSION_FILE | jq -r ".other_app_bundle_ids[]"`; do
      echo `echo "$other_app" | tr -d "\r\n"` >> $APP_SESSION_FILE
    done
  fi

  safe_adb "" -s $DEVICEID push $APP_SESSION_FILE "$MOBILE_PACKAGE_FILE"
  restart_check_status_bar app_automate "" &
}

function upload_file_to_s3() {
  S3URL=$1
  FILEPATH=$2
  TAG=$3
  s3_storage_class=$DEVICELOG_AWS_STORAGE_CLASS
  local retry_count=${4:-1}
  EXT=${S3URL##*.}
  if [ -f $FILEPATH ]; then
    exact_file_size=$(ls -l $FILEPATH  | awk '{print $5}')
    FILE_SIZE_KB=$(expr $exact_file_size / 1024)
    if [ $FILE_SIZE_KB -lt 128 ]; then
      s3_storage_class="STANDARD"
    fi

    if [[ $EXT == "json" ]]
    then
      # timeout 10 /usr/local/bin/s3curl --id=$DEVICELOG_AWS_KEY --key=$DEVICELOG_AWS_SECRET --contentType "text/json" --put=$FILEPATH -- $S3URL
      timeout 300 $BUNDLE exec ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$DEVICELOG_AWS_KEY" "$DEVICELOG_AWS_SECRET" "text/json" "$FILEPATH" "public-read" "$S3URL" "" 10 "" "" "$s3_storage_class"
    elif [[ $EXT == "ec" ]]
    then
      timeout 300 $BUNDLE exec ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$DEVICELOG_AWS_KEY" "$DEVICELOG_AWS_SECRET" "application/octet-stream" "$FILEPATH" "public-read" "$S3URL" "" 10 "" "" "$s3_storage_class"
      # timeout 10 /usr/local/bin/s3curl --id=$DEVICELOG_AWS_KEY --key=$DEVICELOG_AWS_SECRET --contentType "application/octet-stream" --put=$FILEPATH -- $S3URL
    else
      timeout 300 $BUNDLE exec ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$DEVICELOG_AWS_KEY" "$DEVICELOG_AWS_SECRET" "text/plain" "$FILEPATH" "public-read" "$S3URL" "" 20 "$SESSION_ID" "" "$s3_storage_class"
      # timeout 10 /usr/local/bin/s3curl --id=$DEVICELOG_AWS_KEY --key=$DEVICELOG_AWS_SECRET --contentType "text/plain" --put=$FILEPATH -- $S3URL
    fi

    if [ $? -ne 0 ]
    then
      #timeout occured
      if [[ $EXT == "txt" ]] && [[ $retry_count -lt $MAX_UPLOAD_RETRY_COUNT ]]; then
        retry_count=$((retry_count+1))
        upload_file_to_s3 $S3URL $FILEPATH $TAG $retry_count
        return 0
      else
        $BUNDLE exec $COMMON/push_to_zombie.rb "android" "app-$TAG-upload-timeout" "timeout-error" "android" "$exact_file_size" "$DEVICEID" "$SESSION_ID"
      fi

      status="failed"
    else
      $BUNDLE exec $COMMON/push_to_zombie.rb "android" "app-$TAG-upload-done" "success" "android" "$exact_file_size" "$DEVICEID" "$SESSION_ID"
      status="success"
    fi

    if [[ $retry_count -gt 1 ]]; then
      $BUNDLE exec $COMMON/push_to_zombie.rb "android" "app-$TAG-upload-retry" "timeout-retry-$retry_count" "android" "$exact_file_size" "$DEVICEID" "$SESSION_ID"
    fi

    # Upload Stability Tracker for dry_run
    if ! [[ $TAG == *"parselogs"* ]]; then
      $BUNDLE exec ruby $STABILITY_TRACKER_SCRIPT $DEVICEID $LOGS_STABILITY_FILE $TAG $exact_file_size $total_tests_or_session 1 $S3URL $status
    fi

    rm -rf $FILEPATH
  else
    # When log file is not present, track log upload status in log stability tracker as skipped status
    if ! [[ $TAG == *"parselogs"* ]]; then
      $BUNDLE exec ruby $STABILITY_TRACKER_SCRIPT $DEVICEID $LOGS_STABILITY_FILE $TAG 0 $total_tests_or_session 1 $S3URL "failed"
    fi
  fi
}

# Log stability tracker pushes the metrics only when total_tests matches total uploads.
# Leaky tests for which upload_test_logs never executes donot contributes to total uploads, leading to a mismatch in numbers.
function update_logs_stability_metrics() {
  total_tests_ran=$1
  test_framework=$2
  pending_tests="$(( $total_tests_or_session - $total_tests_ran ))"
  if (( $pending_tests > 0 )); then
    $BUNDLE exec ruby $STABILITY_TRACKER_SCRIPT $DEVICEID $LOGS_STABILITY_FILE "${test_framework}_instrulogs" 1 $total_tests_or_session $pending_tests "s3url/$SESSION_ID" "success"

    # Don't consider device logs and network logs under uploaded key(success uploads) for skipped tests.
    # Skipped tests are not considered in health report calculation.
    # Marking uploads as failed helps to push stability metrics.
    if [[ $DEVICELOGS_ENABLED == "true" ]]; then
      $BUNDLE exec ruby $STABILITY_TRACKER_SCRIPT $DEVICEID $LOGS_STABILITY_FILE "${test_framework}_devicelogs" 0 $total_tests_or_session $pending_tests "s3url/$SESSION_ID" "skipped"
    fi
    if [[ $NETWORKLOGS_ENABLED == "true" ]]; then
      $BUNDLE exec ruby $STABILITY_TRACKER_SCRIPT $DEVICEID $LOGS_STABILITY_FILE "${test_framework}_networklogs" 0 $total_tests_or_session $pending_tests "s3url/$SESSION_ID" "skipped"
    fi
  fi
}

function stop_video_recording() {
  if [[ $VIDEO_ENABLED == "true" ]]; then
    # If fallback is enabled for video, then also we want to execute stop_video_recording for RTC2 app to ensure any running process, if started during start video recording, closes before we stop the session.
    if [ "$use_rtc_app" = "v2" ]; then
      $BUNDLE exec ruby $RTC_HELPER stop_video_recording $DEVICEID $SESSION_ID
    else
      bash $VIDEO_RECORDING_SCRIPT stop_video_recording $DEVICEID "" $SESSION_ID "$device_model" $use_scrcpy_for_video_recording
      echo "$SESSION_ID: ADB screenrecord was killed, proceeding to upload video offset"
    fi

    if [[ $media_projection_fallback_required == "true" ]]; then
      bash $VIDEO_RECORDING_SCRIPT stop_video_recording $DEVICEID "" $SESSION_ID "$device_model" $use_scrcpy_for_video_recording
      echo "$SESSION_ID: ADB screenrecord was killed, proceeding to upload video offset"
    fi
  fi
}

function fetch_coverage() {
  # session_id in case of singleRunnerInvocation else test_id
  test_or_session_id=$1
  test_framework=$2

  if [[ $COVERAGE_ENABLED == "true" ]]; then
    coverage_pull_start_time=$(date +%s%N)
    return_value=$(safe_adb 10 -s $DEVICEID pull "$DEVICE_COVERAGE_FILE_FOLDER/$test_or_session_id-$COVERAGE_FILE_NAME" "$COVERAGE_FILE_FOLDER/$test_or_session_id-$COVERAGE_FILE_NAME")
    if [[ "$return_value" -ne 0 ]]; then
      echo "$DEVICEID $SESSION_ID adb command to pull coverage file failed with exit code - $return_value"
      $BUNDLE exec $COMMON/push_to_zombie.rb "android" "${test_framework}-coverage-pull-failed" "" "$device_model" "$return_value" "$DEVICEID" "$test_or_session_id"
    fi
    coverage_pull_end_time=$(date +%s%N)
    coverage_pull_total_time=`echo "($coverage_pull_end_time - $coverage_pull_start_time)/1000000" | bc`
    echo "Coverage fetch time: $coverage_pull_total_time ms"
  fi
}

function flush_logcat_buffer() {
  safe_adb "" -s $DEVICEID logcat -c
}

function run_testsuite_single_invocation() {
  device_name=$1
  device_version=$2
  test_framework=$5
  use_rtc_app=$6
  logs_s3url="https://$RAILS_APIHOST/app-automate/$test_framework/builds/$BUILD_ID/sessions/tests/"
  video_url=""
  use_scrcpy_for_video_recording=$4

  # Start Video Recording
  if [[ $VIDEO_ENABLED == "true" ]]; then
    if [ "$use_rtc_app" = "v2" ]; then
      $BUNDLE exec ruby $RTC_HELPER start_video_recording $DEVICEID $SESSION_ID $device_version $test_framework
      session_video_start_time=`jq -r '.video_start_time' $DUPLICATE_SESSION_SUMMARY_FILE`
    else
      bash $SELENIUM_SCRIPT start_video $DEVICEID $RES $SESSION_ID "" "" "" $use_scrcpy_for_video_recording
      session_video_start_time=`date +%s`
    fi

    media_projection_fallback_required=`jq -r '.media_projection_fallback_required' $DUPLICATE_SESSION_SUMMARY_FILE`
    if [[ $media_projection_fallback_required == "true" ]]; then
      bash $SELENIUM_SCRIPT start_video $DEVICEID $RES $SESSION_ID "" "" "" $use_scrcpy_for_video_recording
      session_video_start_time=`date +%s`
    fi
    video_url=$3
  else
    # This is needed even when video is not enabled because we are writing session_video_start_time into
    # callback_file below and it will corrupt the callback file if we don't do this.
    session_video_start_time=`date +%s`
  fi
  callback_data=`jq -r '.' $CALLBACK_FILE`
  jq ". + { "video_start_time": $session_video_start_time }" <<< "$callback_data" > $CALLBACK_FILE

  if [ "$use_rtc_app" = "v2" ]; then
    $BUNDLE exec ruby $RTC_HELPER set_locale $DEVICEID $SESSION_ID
  fi
  # This determines how many logs uploads take place for a particular session
  total_tests_or_session=1

  # Start Device Logs
  if [[ $DEVICELOGS_ENABLED == "true" ]]; then
    flush_logcat_buffer
    echo "Flushed $DEVICEID logcat before for session $SESSION_ID"

    truncate -s 0 $DEVICELOGS_FILE
    start_device_logger &
  fi

  $BUNDLE exec ruby $INSTRUMENTATION_RUNNER_SCRIPT "invoke_test_sri" "$DEVICEID" "$device_model" "$DEVICE_NAME" "$DEVICE_VERSION" "$test_framework" "$INSTRUMENTATION_NAME" $TOTAL_SESSION_TIME "$USE_ORCHESTRATOR" "$CLEAR_PACKAGE_DATA"

  session_video_stop_time=`date +%s`
  upload_test_logs $SESSION_ID "" $test_framework
  fetch_coverage $SESSION_ID $test_framework &
  $BUNDLE exec ruby "$ESPRESSO_SCREENSHOT_HELPER" fetch_screenshots $DEVICEID "$SINGLE_RUNNER_INVOCATION_ENABLED" "$SCREENSHOTS_ENABLED" "$APP_BUNDLEID" ""
  bash "$SCREENSHOTS_UPLOAD_SCRIPT" "$SESSION_ID" "$SCREENSHOTS_ENABLED" "$SCREENSHOTS_FOLDER" "$BASE_S3URL" "$DEVICELOG_AWS_KEY" "$DEVICELOG_AWS_SECRET" &
  screenshots_upload_pid="$!"

  if [[ $IS_CUCUMBER_TEST_SUITE == "true" || $IS_SESSION_DATA_ENABLED == "true" ]]; then
    $BUNDLE exec ruby $ASSETS_HELPER_SCRIPT "fetch_assets" $DEVICEID $SESSION_ID "$IS_CUCUMBER_TEST_SUITE" "$IS_SESSION_DATA_ENABLED" "$SESSION_ASSET_MAX_SIZE_LIMIT" "$SESSION_ASSET_FILE_COUNT_LIMIT"
  fi

  local video_end_tag=$((session_video_stop_time-session_video_start_time))
  $BUNDLE exec ruby "$ESPRESSO_HELPER_RUNNER" session_completed_single_invocation "$INSTRUMENTATION_LOGS_FILE" "$SESSION_SUMMARY_FILE" "$video_end_tag" "$CALLBACK_FILE" "$TESTSUITE_FILE" "$BASE_S3URL" "$logs_s3url" "$video_url" "$DEVICEID" "$MERGED_JUNIT_REPORT_METADATA_FILE" "$JUNIT_XML_REPORT_FILE" "$device_name" "$device_version" "$device_model" "$TEST_FRAMEWORK" "$session_video_start_time" "$session_video_stop_time" "$SESSION_ID" "$LOGS_STABILITY_FILE"
  if [[ $video_end_tag -ge $MAX_VIDEO_TAG_VALUE ]]; then
    $BUNDLE exec $COMMON/push_to_zombie.rb "android" "video-tag-discrepancy" "Invalid video tag" "" "" "$DEVICEID" "$SESSION_ID"
  fi
  parse_device_logs "$SESSION_ID"
  echo "$SESSION_ID: $test_framework device logs parsed"
  upload_junit_report
  wait "$screenshots_upload_pid"
}

function run_espresso_testsuite() {
  res=$1
  test_bundleid=$2
  test_framework=$7
  use_rtc_app=$8
  logs_s3url="https://$RAILS_APIHOST/app-automate/${test_framework}/builds/$BUILD_ID/sessions/tests/"
  device_name=$4
  device_version=$5
  use_scrcpy_for_video_recording=$6
  test_suite_download_path=$9
  # Start Video Recording
  if [[ $VIDEO_ENABLED == "true" ]]; then
    if [ "$use_rtc_app" = "v2" ]; then
      $BUNDLE exec ruby $RTC_HELPER start_video_recording $DEVICEID $SESSION_ID $device_version $test_framework
      session_video_start_time=`jq -r '.video_start_time' $DUPLICATE_SESSION_SUMMARY_FILE`
    else
      bash $SELENIUM_SCRIPT start_video $DEVICEID $res $SESSION_ID "" "" "" $use_scrcpy_for_video_recording
      session_video_start_time=`date +%s`
    fi

    media_projection_fallback_required=`jq -r '.media_projection_fallback_required' $DUPLICATE_SESSION_SUMMARY_FILE`
    if [[ $media_projection_fallback_required == "true" ]]; then
      bash $SELENIUM_SCRIPT start_video $DEVICEID $RES $SESSION_ID "" "" "" $use_scrcpy_for_video_recording
      session_video_start_time=`date +%s`
    fi
  else
    # This is needed even when video is not enabled because we are writing session_video_start_time into
    # callback_file below and it will corrupt the callback file if we don't do this.
    session_video_start_time=`date +%s`
  fi
  build_start_time=`date +%s`
  callback_data=`jq -r '.' $CALLBACK_FILE`
  jq ". + { "video_start_time": $session_video_start_time }" <<< "$callback_data" > $CALLBACK_FILE

  if [ "$use_rtc_app" = "v2" ]; then
    $BUNDLE exec ruby $RTC_HELPER set_locale $DEVICEID $SESSION_ID
  fi

  total_tests_or_session=$(cat $TESTSUITE_FILE | wc -l)
  total_tests_so_far=0

  # Start Device Logs
  if [[ $DEVICELOGS_ENABLED == "true" ]]; then
    flush_logcat_buffer
    echo "Flushed $DEVICEID logcat before for session $SESSION_ID"

    truncate -s 0 $DEVICELOGS_FILE
    start_device_logger &
  fi

  function clear_app_data() {
    retry_attempt=$1
    unique_test_id=$2

    if [[ $CLEAR_PACKAGE_DATA != "true" ]]; then
      return 0
    else
      return_value=$(safe_adb 5 -s $DEVICEID shell pm clear $APP_BUNDLEID)

      if [[ $return_value -ne 0 ]]; then
        echo "$DEVICEID $unique_test_id ADB command to clear $APP_BUNDLEID data failed"
        $BUNDLE exec $COMMON/push_to_zombie.rb "android" "adb-command-clearpackagedata-failed" "ADB Command for App data cleanup failure" "$device_model" "retry:$retry_attempt" "$DEVICEID" "$unique_test_id"
      fi

      if [[ $return_value -ne 0 ]] && [[ $retry_attempt -lt $MAX_TEST_RETRY_COUNT ]]; then
        echo "App data cleanup failed, retrying."
        retry_attempt=$((retry_attempt + 1))
        clear_app_data $retry_attempt "$unique_test_id"
      fi
    fi
  }

  # Checks if previous session timeout file exists due to test execution / stop process crash
  espresso_session_timeout_files=($(find /tmp -type f -name "*espresso_timeout_$DEVICEID*"))
  espresso_session_timeout_files_count=${#espresso_session_timeout_files[@]}
  if [[ $espresso_session_timeout_files_count -ge 1 ]]; then
    echo "$DEVICEID $SESSION_ID contains $espresso_session_timeout_files_count previous session timeout files. $espresso_session_timeout_files"
    data="{\"session_timeout_file_count\": $espresso_session_timeout_files_count, \"espresso_session_timeout_files\": $espresso_session_timeout_files}"
    $BUNDLE exec $COMMON/push_to_zombie.rb "android" "espresso-previous-timeout-exists" "" "" "$data" "$DEVICEID" "$SESSION_ID"
  fi

  maestro_session_timeout_files=($(find /tmp -type f -name "*maestro_timeout_$DEVICEID*"))
  maestro_session_timeout_files_count=${#maestro_session_timeout_files[@]}
  if [[ $maestro_session_timeout_files_count -ge 1 ]]; then
    echo "$DEVICEID $SESSION_ID contains $maestro_session_timeout_files_count previous session timeout files. $maestro_session_timeout_files"
    data="{\"session_timeout_file_count\": $maestro_session_timeout_files_count, \"maestro_session_timeout_files\": $maestro_session_timeout_files}"
    $BUNDLE exec $COMMON/push_to_zombie.rb "android" "maestro-previous-timeout-exists" "" "" "$data" "$DEVICEID" "$SESSION_ID"
  fi

  video_tags_corrupted="false"

  # Run every Espresso Test
  while read -u3 testname; do
    expire_time=$(get_test_expire_time)
    if [ ! -f $SESSION_TIMEOUT_FILE ]; then
      echo "$SESSION_ID: $test_framework Test - $testname STARTED"
      # Save Video TimeStamp
      video_start_time=`date +%s`
     if [[ $test_framework == "maestro" ]]; then
        $BUNDLE exec ruby $MAESTRO_HELPER_RUNNER update_test_to_running "" $SESSION_SUMMARY_FILE "$testname" $SESSION_ID
        $BUNDLE exec ruby $MAESTRO_HELPER_RUNNER get_test_message "" $SESSION_SUMMARY_FILE $PUSHER_MSG_FILE "$testname" $SESSION_ID
      else
        $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER update_test_to_running "" $SESSION_SUMMARY_FILE "$testname"
        $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER get_test_message "" $SESSION_SUMMARY_FILE $PUSHER_MSG_FILE "$testname"
      fi
      pusher_message=$(cat $PUSHER_MSG_FILE)
      notify_pusher "update_build" "$pusher_message"
      echo "$SESSION_ID: $test_framework Test - $testname Pusher notified"

      if [[ $test_framework == "maestro" ]]; then
        test_id=`echo -n "$testname" | sha256sum | awk '{print substr ($0, 0, 8)}'`
      else
        test_id=`echo -n "$testname" | md5sum | awk '{print substr ($0, 0, 8)}'`
      fi
      unique_test_id="$SESSION_ID$test_id"

      # This ensures that the app's state is completely cleared between tests if clearPackageData true.
      clear_app_data 1 $unique_test_id

      # restart brt when device went off adb in prev test
      if [ -f $BRT_RESTART_ON_OFF_ADB_FILE ]; then
        run_ruby_code $BSTACK_REVERSE_TETHER_CONTROLLER "$DEVICEID" "restart"
        rm $BRT_RESTART_ON_OFF_ADB_FILE
        $BUNDLE exec $COMMON/push_to_zombie.rb "android" "off_adb_brt_restart" "" "android" "" "$DEVICEID" "$SESSION_ID"
      fi

      # This is done to ensure we don't see previous test ending video for media projection
      if [ "$use_rtc_app" = "v2" ]; then
        video_start_time=`date +%s`
      fi

     if [[ $test_framework == "maestro" ]]; then
        $BUNDLE exec ruby $MAESTRO_RUNNER "invoke_test" "$DEVICEID" "$device_model" "$DEVICE_NAME" "$DEVICE_VERSION" "$test_framework" "$test_suite_download_path" "$INSTRUMENTATION_LOGS_FILE" "$SESSION_SUMMARY_FILE" "$CALLBACK_FILE" "$testname" "$unique_test_id" $expire_time 1 $MAX_TEST_RETRY_COUNT
      else
        $BUNDLE exec ruby $INSTRUMENTATION_RUNNER_SCRIPT "invoke_test" "$DEVICEID" "$device_model" "$DEVICE_NAME" "$DEVICE_VERSION" "$test_framework" "$testname" "$test_bundleid" "$unique_test_id" $expire_time 1 $MAX_TEST_RETRY_COUNT "$DEVICE_ORIENTATION"
      fi


      echo "$SESSION_ID: $test_framework Test - $testname COMPLETED"
      # Save Video TimeStamp
      video_stop_time=`date +%s`
      fetch_coverage $unique_test_id $test_framework
     if [[ $test_framework == "maestro" ]]; then
        $BUNDLE exec ruby $MAESTRO_RUNNER "fetch_and_push_screenshots" "$DEVICEID" "$device_model" "$DEVICE_NAME" "$DEVICE_VERSION" "$test_framework" "$test_suite_download_path" "$INSTRUMENTATION_LOGS_FILE" "$SESSION_SUMMARY_FILE" "$CALLBACK_FILE" "$BASE_S3URL" "$DEVICELOG_AWS_KEY" "$DEVICELOG_AWS_SECRET" $unique_test_id
      else
        run_ruby_code "$ESPRESSO_SCREENSHOT_HELPER" fetch_screenshots $DEVICEID "$SINGLE_RUNNER_INVOCATION_ENABLED" "$SCREENSHOTS_ENABLED" "$APP_BUNDLEID" "$testname"
      fi

      screenshots_url_string=$exit_msg
      video_url=""

      if [[ $VIDEO_ENABLED == "true" ]]; then
        local video_start_tag=$((video_start_time-session_video_start_time))
        local video_end_tag=$((video_stop_time-session_video_start_time))
        video_tag=$video_start_tag,$video_end_tag
        video_url=$3\#t=$video_tag

        if [[ $video_start_tag -ge $MAX_VIDEO_TAG_VALUE || $video_end_tag -ge $MAX_VIDEO_TAG_VALUE ]]; then
          video_tags_corrupted="true"
        fi
      fi

     if [[ $test_framework == "maestro" ]]; then
        update_test_details "$testname" "$logs_s3url" $video_start_time "$video_url" "$SESSION_ID" "$unique_test_id" "$test_framework" "$BASE_S3URL"
      else
        update_test_details "$testname" $logs_s3url $video_start_time "$video_url" "$BASE_S3URL" "$screenshots_url_string" "$test_framework"
      fi

      echo "$SESSION_ID: $test_framework Test - $testname DETAILS UPDATED"

      parse_device_logs $unique_test_id
      echo "$SESSION_ID: $test_framework Test - $testname device logs parsed"

      upload_test_logs $unique_test_id "$testname" $test_framework
      echo "$SESSION_ID: $test_framework Test - $testname Logs Uploaded"


     if [[ $test_framework == "maestro" ]]; then
        $BUNDLE exec ruby $MAESTRO_HELPER_RUNNER "push_maestro_logs_to_s3" $INSTRUMENTATION_LOGS_FILE $SESSION_SUMMARY_FILE "$BASE_S3URL" $SESSION_ID $unique_test_id "$DEVICELOG_AWS_KEY" "$DEVICELOG_AWS_SECRET"
        echo "$SESSION_ID: $test_framework Test - $testname Maestro Logs Uploaded"
        $BUNDLE exec ruby $MAESTRO_HELPER_RUNNER get_test_message "" $SESSION_SUMMARY_FILE $PUSHER_MSG_FILE "$testname" $SESSION_ID
      else
        $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER get_test_message "" $SESSION_SUMMARY_FILE $PUSHER_MSG_FILE "$testname"
      fi

      pusher_message=$(cat $PUSHER_MSG_FILE)
      notify_pusher "update_build" "$pusher_message"
      echo "$SESSION_ID: $test_framework Test - $testname Pusher notified"

      total_tests_so_far=$((total_tests_so_far+1))

      # Empty all Logs
      reset_espresso_logs
      echo "$SESSION_ID: $test_framework Test - $testname Logs Cleaned"
    else
     if [[ $test_framework == "maestro" ]]; then
        $BUNDLE exec ruby $MAESTRO_HELPER_RUNNER timeout_session "" $SESSION_SUMMARY_FILE $TESTSUITE_FILE
      else
        $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER timeout_session "" $SESSION_SUMMARY_FILE $TESTSUITE_FILE $MERGED_JUNIT_REPORT_METADATA_FILE
      fi
      break
    fi
  done 3<"$TESTSUITE_FILE" # stream the file contents to fd-3, as stdin is used inside the loop

  # Handle tests that are still in running or queued state
  test_status=$(cat "${SESSION_SUMMARY_FILE}_v2" | jq ".test_summary" | tr -d '\n\r\t ')
  queued_count=$(echo $test_status | jq ".queued" | tr -d '\n\r\t ')
  running_count=$(echo $test_status | jq ".running" | tr -d '\n\r\t ')

  if [[ "$running_count" > 0 || "$queued_count" > 0 ]]; then
    # using the same for maestro as well
    $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER handle_dangling_tests "" $SESSION_SUMMARY_FILE
  fi

  if [[ $APP_PERCY_ENABLED == "true" ]]; then
    PERCY_LOGS_FILE="/var/log/browserstack/percy_cli.${SESSION_ID}_4${device_port}.log"
    backup_and_upload_logs "$BASE_S3URL/$SESSION_ID/$SESSION_ID-percy-logs.txt" $PERCY_LOGS_FILE "${test_framework}_percylogs"
  fi

  # Update session details, generate xml report
  build_stop_time=`date +%s`
  if [[ $video_tags_corrupted == "true" ]]; then
    $BUNDLE exec $COMMON/push_to_zombie.rb "android" "video-tag-discrepancy" "Invalid video tag" "" "" "$DEVICEID" "$SESSION_ID"
  fi
  cls_data="{\"session_id\": $SESSION_ID, \"device_id\": $DEVICEID, \"build_id\": $BUILD_ID, \"from\": "adb-timeout"}"
  $BUNDLE exec $COMMON/push_to_cls.rb "/tmp/duplicate_session_$DEVICEID" "$TEST_FRAMEWORK-teardown-start" "" "$cls_data"
  post_process_espresso_session $((build_stop_time-build_start_time)) "$device_name" "$device_version" "$total_tests_so_far" "$test_framework"
}

function post_process_espresso_session() {
  duration=$1
  device_name=$2
  device_version=$3
  total_tests_ran=$4
  test_framework=$5
  setup_devicelogs_params $(cat "/tmp/devicelogs_params_$DEVICEID")
  setup_video_params $(cat "/tmp/video_params_$DEVICEID")

  if [[ $test_framework == "maestro" ]]; then
    $BUNDLE exec ruby $MAESTRO_HELPER_RUNNER update_session_details "" $SESSION_SUMMARY_FILE "$duration"
  else
    $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER update_session_details "" $SESSION_SUMMARY_FILE "$duration" $MERGED_JUNIT_REPORT_METADATA_FILE $JUNIT_XML_REPORT_FILE "$device_name" "$device_version"
  fi

  if [[ $IS_CUCUMBER_TEST_SUITE == "true" || $IS_SESSION_DATA_ENABLED == "true" ]]; then
    $BUNDLE exec ruby $ASSETS_HELPER_SCRIPT "fetch_assets" $DEVICEID $SESSION_ID "$IS_CUCUMBER_TEST_SUITE" "$IS_SESSION_DATA_ENABLED" "$SESSION_ASSET_MAX_SIZE_LIMIT" "$SESSION_ASSET_FILE_COUNT_LIMIT"
  fi

  # This captures those leaked tests for which upload_test_logs were never captured. We are adjusting/considering them under uploaded key.
  update_logs_stability_metrics $total_tests_ran "$test_framework"
  # Upload report to S3
  upload_junit_report
}

function stop_session() {
  # Stop percy cli
  stop_app_percy
  # Stop device logs
  truncate -s 0 $DEVICELOGS_FILE
  stop_logcat_capture

  # Stop Video recording
  media_projection_fallback_required=`jq -r '.media_projection_fallback_required' $DUPLICATE_SESSION_SUMMARY_FILE`
  stop_video_recording

  # scrcpy_enabled will be true when video recording is done via scrcpy
  scrcpy_enabled="false"
  if [[ ($use_scrcpy_for_video_recording == "true" && $use_rtc_app != "v2") || ($use_scrcpy_for_video_recording == "true" && $media_projection_fallback_required == "true") ]]; then
    scrcpy_enabled="true"
  fi
  # never process video offsets when scrcpy is used or when SRI flow running
  if [[ "$SINGLE_RUNNER_INVOCATION_ENABLED" != "true" && "$scrcpy_enabled" != "true" ]]; then
    if [[ $TEST_FRAMEWORK == "maestro" ]]; then
      $BUNDLE exec ruby $MAESTRO_HELPER_RUNNER process_video_offset "" $SESSION_SUMMARY_FILE "$device_name" "$device_version"
    else
      $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER process_video_offset "" $SESSION_SUMMARY_FILE "$device_name" "$device_version"
    fi
  fi

  # Update final session summary
  backup_and_upload_logs "$BASE_S3URL/$SESSION_ID/$SESSION_ID-summary.json" $SESSION_SUMMARY_FILE "${TEST_FRAMEWORK}_summary"
  # Uploading SESSION Summary File V2
  backup_and_upload_logs "$BASE_S3URL/$SESSION_ID/$SESSION_ID-summary-v2.json" "${SESSION_SUMMARY_FILE}_v2" "${TEST_FRAMEWORK}_summary"

  # Upload merged coverage binary if coverage is enabled
  if [[ $COVERAGE_ENABLED == "true" ]]; then
    merge_and_upload_coverage_binaries
  fi

  if [[ $IS_CUCUMBER_TEST_SUITE == "true" || $IS_SESSION_DATA_ENABLED == "true" ]]; then
    $BUNDLE exec ruby $ASSETS_HELPER_SCRIPT "process_and_upload_assets" $DEVICEID $SESSION_ID $BASE_S3URL "$DEVICELOG_AWS_KEY" "$DEVICELOG_AWS_SECRET"
  fi

  test_status=$(cat "${SESSION_SUMMARY_FILE}_v2" | jq ".test_summary" | tr -d '\n\r\t ')
  failed_count=$(echo $test_status | jq ".failed" | tr -d '\n\r\t ')
  passed_count=$(echo $test_status | jq ".passed" | tr -d '\n\r\t ')
  skipped_count=$(echo $test_status | jq ".skipped" | tr -d '\n\r\t ')
  timedout_count=$(echo $test_status | jq ".timedout" | tr -d '\n\r\t ')
  error_count=$(echo $test_status | jq ".error" | tr -d '\n\r\t ')
  queued_count=$(echo $test_status | jq ".queued" | tr -d '\n\r\t ')
  running_count=$(echo $test_status | jq ".running" | tr -d '\n\r\t ')

  data="{\"test_failed\": $failed_count, \"test_success\": $passed_count, \"test_ignored\": $skipped_count, \"test_timedout\": $timedout_count, \"test_error\": $error_count}"
  $BUNDLE exec $COMMON/push_to_zombie.rb "android" "app_automation_session_stats" "" "" "$data" "$DEVICEID" "$SESSION_ID"

  espresso_callback=`jq -r '.' $CALLBACK_FILE`
  jq ". + { "test_status":{"passed":$passed_count,"failed":$failed_count,"timedout":$timedout_count,"skipped":$skipped_count,"error":$error_count,"running":$running_count,"queued":$queued_count}  }" <<< "$espresso_callback" > $CALLBACK_FILE

  # Cleanup
  cleanup_espresso

  # Inform Rails
  inform_rails_done
}

function reset_espresso_logs() {
  truncate -s 0 $DEVICELOGS_FILE
  truncate -s 0 $INSTRUMENTATION_LOGS_FILE
  truncate -s 0 $PUSHER_MSG_FILE
}

function cleanup_espresso() {
  for device_folder in ${DEVICE_SCREENSHOTS_FOLDER[@]}; do
    safe_adb 5 -s $DEVICEID shell rm -rf $device_folder
  done
  safe_adb 5 -s $DEVICEID shell rm -rf $DEVICE_COVERAGE_FILE_FOLDER
  # temp change to delete file from older path
  safe_adb 5 -s $DEVICEID shell rm -rf "/storage/emulated/0/coverage"
  safe_adb 5 -s $DEVICEID shell rm -rf "/sdcard/Download/assets"
  rm -rf $DEVICELOGS_FILE
  rm -rf $INSTRUMENTATION_LOGS_FILE
  rm -rf $TESTSUITE_FILE
  rm -rf $VIDEO_OFFSET_FILE
  rm -rf $PERCY_LOGS_FILE
  sudo rm -rf "$SCREENSHOTS_FOLDER/"
  sudo rm -rf "$COVERAGE_FILE_FOLDER/"
  sudo rm -rf /tmp/$DEVICEID/assets_"$SESSION_ID"
  # deletes current and previous stale timeout files for the device
  rm -rf /tmp/espresso_timeout_$DEVICEID*
  rm -rf $SESSION_SUMMARY_FILE
  rm -rf $LOGS_STABILITY_FILE
  # Session Summary File v2

  rm -rf $SESSION_SUMMARY_FILE"_v2"
  rm -rf $SESSION_FILE
  stop_logcat_capture
  rm -rf /tmp/devicelogs_params_$DEVICEID
  rm -f /tmp/device_off_adb_*_$DEVICEID
  rm -rf $PUSHER_MSG_FILE
  if [[ $TEST_FRAMEWORK == "maestro" ]]; then
    rm -rf /tmp/$SESSION_ID"_test_suite"
  fi
  ps -ef | grep "privoxy\|logcat\|check_status_bar" | grep $DEVICEID | awk '{print $2}' | sudo xargs kill -9
}

function update_test_details() {
  test_name="$1"
  test_framework=$7
  # Parse Output, update summary
 if [[ $test_framework == "maestro" ]]; then
    logs_s3url=$2
    start_time=$3
    video_url=$4
    session_id=$5
    unique_test_id=$6
    base_url=$8
    $BUNDLE exec ruby $MAESTRO_HELPER_RUNNER "update_test_details" $INSTRUMENTATION_LOGS_FILE $SESSION_SUMMARY_FILE "$test_name" "$logs_s3url" $start_time \"$video_url\" $DEVICEID "$CALLBACK_FILE" "$test_framework" "$session_id" "$unique_test_id" "$base_url"
  else
    $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER update_test_details $INSTRUMENTATION_LOGS_FILE $SESSION_SUMMARY_FILE "$test_name" $2 $3 \"$4\" \"$5\" \"$6\" $MERGED_JUNIT_REPORT_METADATA_FILE $APP_BUNDLEID $DEVICEID $CALLBACK_FILE $7
  fi

}

function parse_device_logs() {
  test_id="$1"
  $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER parse_device_logs "" $SESSION_SUMMARY_FILE "$test_id" $APP_BUNDLEID $DEVICEID
}

function backup_and_upload_logs() {
  S3URL=$1
  FILEPATH=$2
  TAG=$3
  EXT=${S3URL##*.}
  BACKUP_FILE="/tmp/${TEST_FRAMEWORK}_logs_$(date +%s%N)_$TAG.$EXT"

  if [[ ! -f $FILEPATH ]]; then
    $BUNDLE exec $COMMON/push_to_zombie.rb "android" "$TAG-upload-file-missing" "file-not-exists-error" "android" "" "$DEVICEID" "$SESSION_ID"
  fi

  safe_cp $FILEPATH $BACKUP_FILE
  local safe_cp_status=$?

  if [[ "$safe_cp_status" != 0 ]]; then
    callback_data=`jq -r '.' $CALLBACK_FILE`
    jq ". + { "error_reason": \"machine-storage-issue\" }" <<< "$callback_data" > $CALLBACK_FILE
  fi

  if [[ $EXT == "json" || $EXT == "ec" ]]; then
    upload_file_to_s3 $S3URL $BACKUP_FILE $TAG
  else
    upload_file_to_s3 $S3URL $BACKUP_FILE $TAG &
  fi
}

function merge_and_upload_coverage_binaries() {
  MERGED_BINARY_NAME="${TEST_FRAMEWORK}_session_coverage_$SESSION_ID.ec"

  local merge_coverage_start_time=$(date +%s%N)
  # Adding highest timeout currently found in mobile, and pushing time to zombie/eds to find out near actual time based on sessions data.
  timeout 1000 java -jar $MERGE_COVERAGE_JAR merge $COVERAGE_FILE_FOLDER/*.ec --destfile $COVERAGE_FILE_FOLDER/$MERGED_BINARY_NAME
  local merge_command_exit_code=$?

  local merge_coverage_end_time=$(date +%s%N)
  local merge_coverage_total_time=`echo "($merge_coverage_end_time - $merge_coverage_start_time)/1000000" | bc`

  # Pushing coverage merge time to zombie
  if [ $merge_command_exit_code -eq 124 ]
  then
    $BUNDLE exec $COMMON/push_to_zombie.rb "android" "app-${TEST_FRAMEWORK}-coverage-merge-timeout" "timeout-error" "android" "$merge_coverage_total_time" "$DEVICEID" "$SESSION_ID"
  elif [ $merge_command_exit_code -ne 0 ]
  then
    $BUNDLE exec $COMMON/push_to_zombie.rb "android" "app-${TEST_FRAMEWORK}-coverage-merge-error" "exit-code-${merge_command_exit_code}" "android" "$merge_coverage_total_time" "$DEVICEID" "$SESSION_ID"
  else
    $BUNDLE exec $COMMON/push_to_zombie.rb "android" "app-${TEST_FRAMEWORK}-coverage-merge-time" "success" "android" "$merge_coverage_total_time" "$DEVICEID" "$SESSION_ID"
  fi

  echo "Merge time: $merge_coverage_total_time ms"
  backup_and_upload_logs "$BASE_S3URL/$SESSION_ID/$MERGED_BINARY_NAME" "$COVERAGE_FILE_FOLDER/$MERGED_BINARY_NAME" "${TEST_FRAMEWORK}_coveragelogs"

  sudo rm -rf "$COVERAGE_FILE_FOLDER/*"
}

function upload_test_logs() {
  test_or_session_id="$1" # session_id in case of singleRunnerInvocation else test_id
  test_name="$2"
  test_framework=$3

  # Upload Test related Logs(Device, Instrumentation)
  if [[ $DEVICELOGS_ENABLED == "true" ]]; then
    backup_and_upload_logs "$BASE_S3URL/$test_or_session_id/$test_or_session_id-device-logs.txt" $DEVICELOGS_FILE "${test_framework}_devicelogs"
  fi

  if [[ $LAUNCH_MITMPROXY == "true" ]]; then
    bash $SELENIUM_SCRIPT stop_mitm $DEVICEID
    touch_dont_log_netcheck
  fi

  if [[ $SPLIT_LOGS_ENABLED != "true" && $NETWORKLOGS_ENABLED == "true" && -f $NETWORKLOGS_FILE ]]; then
    harlogs="$test_or_session_id-har-logs.txt"
    harlogsfilepath="/var/log/browserstack/$harlogs"

    PYTHONDONTWRITEBYTECODE=1 timeout 600 $MITMDUMP -n -r $TMP_NETWORKLOGS_FILE -s $FLOW_TO_HAR --set hardump=$harlogsfilepath --set captureContent=$NETWORKLOGS_CAPTURE_CONTENT_ENABLED 2>&1 >> /var/log/browserstack/mitm_$DEVICEID.log

    exit_code=$?
    if [ $exit_code -ne 0 ]
    then
      GENERIC_MSG="timeout"
      if [ $exit_code -ne 124 ]
      then
        GENERIC_MSG="error"
        PARSE_ERROR_URL=$(tail -2 /var/log/browserstack/mitm_$DEVICEID.log)
        echo "$SESSIONID: Got parse error for the URL: $PARSE_ERROR_URL"
      fi
      exact_file_size=$(ls -l $TMP_NETWORKLOGS_FILE  | awk '{print $5}')
      backup_and_upload_logs "$BASE_S3URL/$test_or_session_id/$harlogs" $TMP_NETWORKLOGS_FILE "${test_framework}_corruptNetworkLogs"
      $BUNDLE exec $COMMON/push_to_zombie.rb "android" "app-networklogs-parse-""$GENERIC_MSG" "$GENERIC_MSG: $PARSE_ERROR_URL" "android" "$exact_file_size" "$DEVICEID" "$SESSION_ID"
    else
      backup_and_upload_logs "$BASE_S3URL/$test_or_session_id/$harlogs" $harlogsfilepath "${test_framework}_networklogs"
    fi

    rm -rf $harlogsfilepath
    rm -rf $TMP_NETWORKLOGS_FILE
  fi

  # Launch MITM proxy again
  if [[ $LAUNCH_MITMPROXY == "true" && $SINGLE_RUNNER_INVOCATION_ENABLED != "true" ]]; then
    bash $MITM_CMD_FILE
    clean_dont_log_netcheck
  fi

  backup_and_upload_logs "$BASE_S3URL/$test_or_session_id/$test_or_session_id-instrumentation-logs.txt" $INSTRUMENTATION_LOGS_FILE "${test_framework}_instrulogs"

  # In case of singleRunnerInvocation screenshots are uploaded via upload_test_screenshots.sh
  if [[ $SCREENSHOTS_ENABLED == "true" && $SINGLE_RUNNER_INVOCATION_ENABLED != "true" ]]; then
    test_screenshots_folder="$SCREENSHOTS_FOLDER/${test_name##*#}"
    s3_storage_class="$DEVICELOG_AWS_STORAGE_CLASS"
    for file in $(ls $test_screenshots_folder)
    do
      exact_file_size=$(ls -l $test_screenshots_folder/$file  | awk '{print $5}')
      FILE_SIZE_KB=$(expr $exact_file_size / 1024)
      if [ $FILE_SIZE_KB -lt 128 ]; then
        s3_storage_class="STANDARD"
      fi
      screenshots_s3_url="$BASE_S3URL/$test_or_session_id/screenshots/$file"
      timeout 11 $BUNDLE exec ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$DEVICELOG_AWS_KEY" "$DEVICELOG_AWS_SECRET" "image/jpeg" "$test_screenshots_folder/$file" "public-read" "$screenshots_s3_url" "" 10 "" "" "$s3_storage_class"
    done
  fi

}

function upload_junit_report() {
  if [[ -f $JUNIT_XML_REPORT_FILE ]]; then
    report_size=$(wc -c <"$JUNIT_XML_REPORT_FILE")
    minimum_size=100

    if [[ -s $JUNIT_XML_REPORT_FILE ]]; then
      echo "Uploading the Junit XML report file: $JUNIT_XML_REPORT_FILE"

      junit_report_s3_path="$BASE_S3URL/$SESSION_ID/report.xml"
      s3_storage_class="$DEVICELOG_AWS_STORAGE_CLASS"
      exact_file_size=$(ls -l $JUNIT_XML_REPORT_FILE  | awk '{print $5}')
      FILE_SIZE_KB=$(expr $exact_file_size / 1024)
      if [ $FILE_SIZE_KB -lt 128 ]; then
        s3_storage_class="STANDARD"
      fi
      timeout 31  $BUNDLE exec ruby "$BS_DIR/mobile/android/scripts/upload_to_s3.rb" "$DEVICELOG_AWS_KEY" "$DEVICELOG_AWS_SECRET" "text/xml" "$JUNIT_XML_REPORT_FILE" "public-read" "$junit_report_s3_path" "" 30 "" "" "$s3_storage_class"
      echo "Junit XML file succesfully uploaded"

      if [ $report_size -le $minimum_size ]; then
        #TODO: track failure
        echo "JUnit report size was too small, there was some failure or no tests were executed."
      fi
    else
      #TODO: track failure
      echo "JUnit report file generated is empty, no point in uploading."
    fi
  else
    #TODO: track failure
    echo "JUnit report file is not generated, cannot upload."
  fi

  # cleanup files.
  rm $JUNIT_XML_REPORT_FILE
  rm $MERGED_JUNIT_REPORT_METADATA_FILE
}

function setup_devicelogs_params() {
  DEVICELOG_AWS_KEY=$3
  DEVICELOG_AWS_SECRET=$4
  DEVICELOG_AWS_BUCKET=$6
  DEVICELOG_AWS_STORAGE_CLASS=$8
  [[ $DEVICELOG_AWS_BUCKET = "bs-stag" || $5 = "us-east-1" ]] && DEVICELOG_AWS_REGION="" || DEVICELOG_AWS_REGION="-$5"
  BASE_S3URL="https://s3$DEVICELOG_AWS_REGION.amazonaws.com/$DEVICELOG_AWS_BUCKET/$BUILD_ID"
}

function setup_video_params() {
  AWS_KEY=$3
  AWS_SECRET=$4
  AWS_BUCKET=$6
  AWS_STORAGE_CLASS=$13
  [[ $AWS_BUCKET = "bs-stag" || $5 = "us-east-1" ]] && AWS_REGION="" || AWS_REGION=".$5"
  VIDEO_FILE=$7
  VIDEO_S3URL="https://$RAILS_HOST/s3-upload/$AWS_BUCKET/s3$AWS_REGION/$SESSION_ID/$VIDEO_FILE.mp4"
}

function setup_flutter_constants() {
  SESSION_SUMMARY_FILE="/tmp/fluttertest_summary_$DEVICEID"
  CALLBACK_FILE="/tmp/fluttertest_callback_$DEVICEID"
  INSTRUMENTATION_LOGS_FILE="/tmp/fluttertest_instrumentation_$DEVICEID"
  TESTSUITE_FILE="/tmp/fluttertest_testsuite_$DEVICEID"
  MERGED_JUNIT_REPORT_METADATA_FILE="/tmp/temp_fluttertest_junit_report_$SESSION_ID.metadata"
  JUNIT_XML_REPORT_FILE="/tmp/fluttertest_junit_report_$SESSION_ID.xml"
  PUSHER_MSG_FILE="/tmp/fluttertest_pusher_message_${DEVICEID}"
  SESSION_TIMEOUT_FILE="/tmp/fluttertest_timeout_${DEVICEID}_${SESSION_ID}"
  LOGS_STABILITY_FILE="/tmp/fluttertest_logs_stability_$SESSION_ID"
}

function setup_maestro_constants() {
  SESSION_SUMMARY_FILE="/tmp/maestro_summary_$DEVICEID"
  CALLBACK_FILE="/tmp/maestro_callback_$DEVICEID"
  INSTRUMENTATION_LOGS_FILE="/tmp/maestro_instrumentation_$DEVICEID"
  TESTSUITE_FILE="/tmp/maestro_testsuite_$DEVICEID"
  MERGED_JUNIT_REPORT_METADATA_FILE="/tmp/temp_maestro_junit_report_$SESSION_ID.metadata"
  PUSHER_MSG_FILE="/tmp/maestro_pusher_message_${DEVICEID}"
  SESSION_TIMEOUT_FILE="/tmp/maestro_timeout_${DEVICEID}_${SESSION_ID}"
  LOGS_STABILITY_FILE="/tmp/maestro_logs_stability_$SESSION_ID"
}

function setup_app_params() {
  summary_params=$(cat $SESSION_SUMMARY_FILE)
  APP_BUNDLEID=$(echo $summary_params | jq -r '.app_details.bundle_id')
  TEST_BUNDLEID=$(echo $summary_params | jq -r '.test_suite_details.bundle_id')
  DEVICELOGS_ENABLED=$(echo $summary_params | jq -r '.deviceLogs')
  NETWORKLOGS_ENABLED=$(echo $summary_params | jq -r '.networkLogs')
  NETWORKLOGS_CAPTURE_CONTENT_ENABLED=$(echo $summary_params | jq -r '.networkLogsCaptureContent')
  ACCEPT_INSECURE_CERTS_ENABLED=$(echo $summary_params | jq -r '.acceptInsecureCerts')
  VIDEO_ENABLED=$(echo $summary_params | jq -r '.video')
  INSTRUMENTATION_NAME=$(echo $summary_params | jq -r '"\(.test_suite_details.bundle_id)/\(.test_suite_details.instrumentation)"')
  TEST_IDLE_TIMEOUT=$(echo $summary_params | jq -r '.idle_timeout')
  IS_CUCUMBER_TEST_SUITE=$(echo $summary_params | jq -r '.test_suite_details.is_cucumber_test_suite')
  SPLIT_LOGS_ENABLED=$(echo $summary_params | jq -r '.splitLogs')
  if [[ $NETWORKLOGS_ENABLED == "true" || $ACCEPT_INSECURE_CERTS_ENABLED == "true" ]]; then
    LAUNCH_MITMPROXY="true"
  fi
  SCREENSHOTS_ENABLED=$(echo $summary_params | jq -r '.screenshots')
  COVERAGE_ENABLED=$(echo $summary_params | jq -r '.coverage')
  echo "$SESSION_ID: Coverage enabled : $COVERAGE_ENABLED"
  SINGLE_RUNNER_INVOCATION_ENABLED=$(echo $summary_params | jq -r '.singleRunnerInvocation')
  USE_ORCHESTRATOR=$(echo $summary_params | jq -r '.useOrchestrator')
  CLEAR_PACKAGE_DATA=$(echo $summary_params | jq -r '.clearPackageData')

  if [[ $SCREENSHOTS_ENABLED == "true" || $COVERAGE_ENABLED == "true" ]]; then
    DEVICE_SCREENSHOTS_FOLDER+=("/storage/emulated/0/Android/data/$APP_BUNDLEID/files/screenshots")
    safe_adb 5 -s $DEVICEID shell "pm grant $APP_BUNDLEID android.permission.READ_EXTERNAL_STORAGE && pm grant $APP_BUNDLEID android.permission.WRITE_EXTERNAL_STORAGE"
  fi

  if [[ $SCREENSHOTS_ENABLED == "true" ]] || [[ $IS_CUCUMBER_TEST_SUITE == "true" ]]; then
    sudo rm -rf "$SCREENSHOTS_FOLDER"
    mkdir -p "$SCREENSHOTS_FOLDER"
    for device_folder in ${DEVICE_SCREENSHOTS_FOLDER[@]}; do
      timeout 5 adb -s $DEVICEID shell rm -rf $device_folder
    done
  fi

  if [[ $COVERAGE_ENABLED == "true" ]]; then
    timeout 5 adb -s $DEVICEID shell "mkdir -p $DEVICE_COVERAGE_FILE_FOLDER"
    sudo rm -rf "$COVERAGE_FILE_FOLDER"
    mkdir -p "$COVERAGE_FILE_FOLDER"
  fi

  setup_log_stability_file
}


function setup_log_stability_file() {
  default_logs_stability_metrics_data='{"instrumentationLogs": {"uploaded": 0, "failed": 0, "skipped": 0}, "networkLogs": {"uploaded": 0, "failed": 0, "skipped": 0}, "deviceLogs": {"uploaded": 0, "failed": 0, "skipped": 0}}'
  echo $default_logs_stability_metrics_data > $LOGS_STABILITY_FILE
}

function inform_rails_done() {
  echo "$SESSION_ID: Inform Rails"
  curl "http://localhost:45671/inform_${TEST_FRAMEWORK}_done/$DEVICEID"
}

function start_app_percy() {
  params=$1
  $BUNDLE exec ruby $APP_PERCY_HELPER start_app_percy $DEVICEID $SESSION_ID $params
}

function stop_app_percy() {
  $BUNDLE exec ruby $APP_PERCY_HELPER stop_app_percy $DEVICEID $SESSION_ID
}

function store_test_observability() {
  $BUNDLE exec ruby $TEST_OBSERVABILITY_RUNNER "$DEVICEID" "$DEVICE_NAME" "$DEVICE_VERSION" "$BUILD_ID" "$SESSION_ID" "$TEST_HUB_JWT" "$TEST_HUB_BUILD_HASHED_ID" "$TEST_OBSERVABILITY" "$ACCESSIBILITY" "$TESTHUB_ACCESSIBILITY_TOKEN" "$TESTHUB_POLLING_TIMEOUT" "$TESTHUB_SCANNER_VERSION" "$HOSTNAME" "$INCLUDE_TAGS_IN_TESTING_SCOPE" "$EXCLUDE_TAGS_IN_TESTING_SCOPE"
}

# Global variable used in common to determine what was the original function
COMPONENT=$TODO
case $TODO in
  start_session )
    WIDTH=$5
    ORIENTATION=$6
    TIMEZONE=$7
    INSTRUMENTATION_PARAMS=$8
    RES=$9
    RAILS_HOST=${10}
    RAILS_APIHOST=${11}
    DEVICE_NAME=${12}
    DEVICE_VERSION=${13}
    use_scrcpy_for_video_recording=${14}
    TEST_FRAMEWORK=${15}
    TEST_SUITE_DOWNLOAD_PATH=${16}
    app_percy_params=${17}
    USE_RTC_APP=${18}
    TEST_HUB_JWT=${19}
    TEST_HUB_BUILD_HASHED_ID=${20}
    TEST_OBSERVABILITY=${21}
    ACCESSIBILITY=${22}
    TESTHUB_ACCESSIBILITY_TOKEN=${23}
    TESTHUB_POLLING_TIMEOUT=${24}
    TESTHUB_SCANNER_VERSION=${25}
    INCLUDE_TAGS_IN_TESTING_SCOPE=${26}
    EXCLUDE_TAGS_IN_TESTING_SCOPE=${27}
    IS_SESSION_DATA_ENABLED=${28}
    SESSION_ASSET_MAX_SIZE_LIMIT=${29}
    SESSION_ASSET_FILE_COUNT_LIMIT=${30}

    echo "$SESSION_ID: start_session"
    if [[ $TEST_FRAMEWORK == "fluttertest" ]];then
      setup_flutter_constants
    elif [[ $TEST_FRAMEWORK == "maestro" ]];then
      setup_maestro_constants
    fi

    touch $INSTRUMENTATION_LOGS_FILE
    setup_app_params
    setup_devicelogs_params $(cat "/tmp/devicelogs_params_$DEVICEID")
    setup_video_params $(cat "/tmp/video_params_$DEVICEID")
    SESSION_START_TIME=$(date +%s)
    TOTAL_SESSION_TIME=$(cat $SESSION_FILE | jq -r ".session_time" | tr -d "\r\n")
    if [[ $TOTAL_SESSION_TIME = "null" ]]; then
      echo "$SESSION_ID: TOTAL_SESSION_TIME is null, settting it to default 7200"
      $BUNDLE exec $COMMON/push_to_zombie.rb "android" "${TEST_FRAMEWORK}_fallback_session_time" "setting session time as fallback 2 hrs" "" "" "$DEVICEID" "$SESSION_ID"
      TOTAL_SESSION_TIME=7200
    fi
    MAX_VIDEO_TAG_VALUE=7500

    echo "$SESSION_ID: Start request for $TEST_FRAMEWORK"
    safe_adb "" -s $DEVICEID shell "mkdir -p $SESSION_DIR; touch $SESSION_DIR/bs-session-app-automate.txt"
    touch_in_session "$SESSION_ID"
    touch $SESSION_FILE
    DEVICE_ORIENTATION=$ORIENTATION

    if [ "$?" = "0" ]; then
      ensure_screen_is_unlocked
      if [[ $app_percy_params != "null" ]]; then
        APP_PERCY_ENABLED=true
        start_app_percy $app_percy_params
      fi
      set_device_orientation $DEVICE_ORIENTATION
      if [[ $TIMEZONE != "" ]];then
        set_timezone $TIMEZONE
      fi

      disable_chrome_welcome_screen
      add_apps_for_cleanup
      get_device_model
      log_start_time

      if [[ $SINGLE_RUNNER_INVOCATION_ENABLED == "true" ]]; then
        start_logcat_capture
        log_start_time
        store_test_observability
        run_testsuite_single_invocation "$DEVICE_NAME" "$DEVICE_VERSION" "$VIDEO_S3URL" "$use_scrcpy_for_video_recording" "$TEST_FRAMEWORK" "$USE_RTC_APP"
        RUN_TIME=$(log_end_time)
        echo "$SESSION_ID: All Tests ran with singleRunnerInvocation in $RUN_TIME ms"
      else
        if [[ $TEST_FRAMEWORK == "maestro" ]];then
          echo "$SESSION_ID: starting dry run"
          $BUNDLE exec ruby $MAESTRO_RUNNER "dry_run" "$DEVICEID" "$device_model" "$DEVICE_NAME" "$DEVICE_VERSION" "$TEST_FRAMEWORK" "$TEST_SUITE_DOWNLOAD_PATH" "$INSTRUMENTATION_LOGS_FILE" "$SESSION_SUMMARY_FILE" "$CALLBACK_FILE" "$TESTSUITE_FILE"
        else
          $BUNDLE exec ruby $INSTRUMENTATION_RUNNER_SCRIPT "dry_run" "$DEVICEID" "$device_model" "$DEVICE_NAME" "$DEVICE_VERSION" "$TEST_FRAMEWORK" "$INSTRUMENTATION_NAME"
        fi

        backup_and_upload_logs "$BASE_S3URL/$SESSION_ID/instrumentation-parse-logs.txt" $INSTRUMENTATION_LOGS_FILE "${TEST_FRAMEWORK}_parselogs"
        truncate -s 0 $INSTRUMENTATION_LOGS_FILE

        PARSE_TIME=$(log_end_time)
        echo "$SESSION_ID: All Tests parsed in $PARSE_TIME ms, proceeding to run them"

        if [[ $TEST_FRAMEWORK == "maestro" ]];then
          $BUNDLE exec ruby $MAESTRO_HELPER_RUNNER get_build_message "" $SESSION_SUMMARY_FILE $PUSHER_MSG_FILE $TESTSUITE_FILE
        else
          $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER get_build_message "" $SESSION_SUMMARY_FILE $PUSHER_MSG_FILE $TESTSUITE_FILE
        fi

        while read pusher_message; do
            notify_pusher "setup_build" "$pusher_message"
            sleep 0.5
        done < <(cat $PUSHER_MSG_FILE | jq -r ".[]")

        start_logcat_capture
        log_start_time
        store_test_observability
        run_espresso_testsuite "$RES" "$INSTRUMENTATION_NAME" "$VIDEO_S3URL" "$DEVICE_NAME" "$DEVICE_VERSION" "$use_scrcpy_for_video_recording" "$TEST_FRAMEWORK" "$USE_RTC_APP" $TEST_SUITE_DOWNLOAD_PATH
        RUN_TIME=$(log_end_time)
        echo "$SESSION_ID: All Tests ran in $RUN_TIME ms"
      fi
      stop_session
      exit 0
    else
      exit 1
    fi;;
  timeout_session )
    TEST_FRAMEWORK=$7

    if [[ $TEST_FRAMEWORK == "fluttertest" ]];then
      setup_flutter_constants
    elif [[ $TEST_FRAMEWORK == "maestro" ]];then
      setup_maestro_constants
    fi

    echo "$SESSION_ID: Timeout request for ${TEST_FRAMEWORK}"
    device_name=$5
    device_version=$6
    running_sesion_id=$(cat $CALLBACK_FILE | jq -r '.session_id')
    running_build_id=$(cat $CALLBACK_FILE | jq -r '.build_id')
    if [[ $running_sesion_id == $SESSION_ID && $running_build_id == $BUILD_ID ]]
    then
      touch $SESSION_TIMEOUT_FILE
      # if test process is not runnning or killed due to some reason eg: https://browserstack.atlassian.net/browse/AA-4105.
      # Trigger stop_session that will handle the clean up, log uploads and inform rails.
      running_session_pid_count=`ps -ef | grep $DEVICEID | grep -w start_session | grep -v grep | wc -l`
      if [ "$running_session_pid_count" -le 1 ]
      then
        echo "$SESSION_ID: Session process was not running or killed, post processing session"
       if [[ $TEST_FRAMEWORK == "maestro" ]]; then
          $BUNDLE exec ruby $MAESTRO_HELPER_RUNNER timeout_session "" $SESSION_SUMMARY_FILE $TESTSUITE_FILE
        else
          $BUNDLE exec ruby $ESPRESSO_HELPER_RUNNER timeout_session "" $SESSION_SUMMARY_FILE $TESTSUITE_FILE $MERGED_JUNIT_REPORT_METADATA_FILE
        fi
        cls_data="{\"session_id\": $SESSION_ID, \"device_id\": $DEVICEID, \"build_id\": $BUILD_ID, \"from\": "expire-session-worker"}"
        $BUNDLE exec $COMMON/push_to_cls.rb "/tmp/duplicate_session_$DEVICEID" "$TEST_FRAMEWORK-teardown-start" "" "$cls_data"
        post_process_espresso_session "" "$device_name" "$device_version" "" "$TEST_FRAMEWORK"
        stop_session
      fi
      exit 0
    else
      echo "$SESSION_ID: Session ID from params didn't match with current running session. Not stopping session"
      data="{\"running_session_id\": $running_sesion_id, \"running_build_id\": $running_build_id, \"session_id\": $SESSION_ID, \"build_id\": $BUILD_ID}"
      $BUNDLE exec $COMMON/push_to_zombie.rb "android" "${TEST_FRAMEWORK}_timeout_session_fail" "session id mismatch while timeout session" "" "$data" "$DEVICEID" "$SESSION_ID"
      exit 1
    fi
    ;;
  kill_session )
    echo "$SESSION_ID: Kill request for Espresso"
    setup_video_params $(echo "/tmp/video_params_$DEVICEID")
    ADB_PIDS=`ps -ef | grep $DEVICEID | grep -w start_session | grep -v grep | awk '{print $2}'`
    log_start_time
    echo $ADB_PIDS | sudo xargs kill -15
    die_count=0
    while ps -p $ADB_PIDS  > /dev/null
    do
      sleep 0.5
      if [ "$die_count" -eq 3 ]; then
        echo "$SESSION_ID: Sending SIGINT"
        echo $ADB_PIDS | sudo xargs kill -2
      fi
      if [ "$die_count" -eq 5 ]; then
        echo "$SESSION_ID: Sending ForceKill"
        echo $ADB_PIDS | sudo xargs kill -9
        break
      fi
      echo "$SESSION_ID: ADB espresso start_session is still dying, waiting"
      die_count=$((die_count+1))
    done

    KILL_TIME=$(log_end_time)
    echo "$SESSION_ID: ADB espresso start_session was dead in $KILL_TIME ms"

    stop_session
    ;;
esac
