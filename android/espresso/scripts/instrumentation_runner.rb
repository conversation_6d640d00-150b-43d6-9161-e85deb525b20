require "browserstack_logger"
require "json"
require "fileutils"

require_relative "../../helpers/utils"
require_relative "../../exit_file"
require_relative "../../lib/os_utils"
require_relative "./espresso_helper_runner"
require_relative "../../../common/push_to_zombie"
require_relative "../helpers/espresso_helper"
require_relative "../../constants"
require(
  "/usr/local/.browserstack/mobile-common/"\
  "frameworks_timeout_manager/app_automate_frameworks/espresso_timeout_manager"
)
module Espresso
  class InstrumentationRunner # rubocop:todo Metrics/ClassLength
    def self.run_from_bash
      raise StandardError, "Not enough arguments" if ARGV.size < 6

      function_to_call = ARGV[0].to_s.strip
      device_id = ARGV[1].to_s.strip
      device_model = ARGV[2].to_s.strip
      device_name = ARGV[3].to_s.strip
      device_version = ARGV[4].to_s.strip
      test_framework = ARGV[5].to_s.strip || "espresso"
      args = ARGV[6..]

      instrumentation_runner = Espresso::InstrumentationRunner.new(
        device_id, device_model, device_name, device_version, test_framework
      )
      instrumentation_runner.send(function_to_call, *args)
    rescue StandardError => e
      ExitFile.write(e.message[0..200])
      raise e
    end

    def initialize(device, device_model, device_name, device_version, test_framework)
      raise "Device cannot be empty" if device.nil? || device == ""

      @session_summary_file_path = "/tmp/#{test_framework}_summary_#{device}"
      @instrumentation_logs_file_path = "/tmp/#{test_framework}_instrumentation_#{device}"
      @callback_file_path = "/tmp/#{test_framework}_callback_#{device}"
      @device = device
      @device_name = device_name
      @device_model = device_model
      @device_version = device_version
      @test_framework = test_framework
      @session_summary_file = begin
        JSON.parse(File.read(@session_summary_file_path))
      rescue StandardError
        {}
      end
      @session_id = @session_summary_file["session_id"]
      @coverage_enabled = @session_summary_file["coverage"] == "true"
      @is_cucumber_test_suite = @session_summary_file.dig('test_suite_details', 'is_cucumber_test_suite') == "true"
    end

    def dry_run(*args)
      instrumentation_name = args[0].to_s.strip

      test_suite_file_path = "/tmp/#{@test_framework}_testsuite_#{@device}"
      @instrumentation_params = begin
        get_test_params(DRY_RUN_OPTIONS, test_params)
      rescue StandardError
        ""
      end
      BrowserStack.logger.info "Instrumentation_params: #{@instrumentation_params}"

      BrowserStack.logger.info "Running instrumentation #{instrumentation_name} on device #{@device}"
      create_files(test_suite_file_path)
      dry_run_method(instrumentation_name)
      check_for_instrumentation_error
      parse_build(test_suite_file_path)
      check_if_testsuite_parse_empty(test_suite_file_path)
    rescue StandardError => e
      BrowserStack.logger.error "Error in dry run script: #{e.message} #{e.backtrace}"
      ExitFile.write(e.message[0..200])
      raise e
    end

    def invoke_test(*args)
      test_name = args[0].to_s.strip
      test_bundle_id = args[1].to_s.strip
      @unique_test_id = args[2].to_s.strip
      expire_time = args[3].to_s.strip
      retry_attempt = begin
        args[4].to_i
      rescue StandardError
        nil
      end
      max_test_retry_count = begin
        args[5].to_i
      rescue StandardError
        nil
      end
      device_orientation = args[6].to_s.strip

      run_test(test_name, test_bundle_id, expire_time, retry_attempt, max_test_retry_count, device_orientation,
               use_orchestrator: false, clear_package_data: false)
    rescue StandardError => e
      BrowserStack.logger.error "Error initializing test: #{test_name} #{e.message} #{e.backtrace}"
      ExitFile.write(e.message[0..200])
      raise e
    end

    def invoke_test_sri(*args)
      test_bundle_id = args[0].to_s.strip
      expire_time = args[1].to_s.strip
      use_orchestrator = args[2].to_s.strip == "true"
      clear_package_data = args[3].to_s.strip == "true"

      run_test(
        nil, test_bundle_id, expire_time, nil, nil, nil,
        use_orchestrator: use_orchestrator, clear_package_data: clear_package_data
      )
    rescue StandardError => e
      BrowserStack.logger.error "Error initializing test: #{test_name} #{e.message} #{e.backtrace}"
      ExitFile.write(e.message[0..200])
      raise e
    end

    private

    def update_summary_and_callback_file
      File.open(@session_summary_file_path, "w+") do |f|
        f.write(@session_summary_file.to_json)
      end

      File.open(@callback_file_path, "w+") do |f|
        f.write(callback_data.to_json)
      end
    end

    def create_files(test_suite_file_path)
      FileUtils.touch test_suite_file_path
      FileUtils.touch @instrumentation_logs_file_path
    end

    def instrument_dry_run_retry(retry_reason, attempt_number, exit_code, device_id, session_id)
      kind = "espresso-#{retry_reason}"
      data = { attempt: attempt_number, exit_code: exit_code }.to_json
      zombie_push("android", kind, "", "", data, device_id, session_id)
    end

    def dry_run_method(instrumentation_name)
      delay = 3
      try = 1
      max_retries = 3

      # Todo - Refactoring of --dry-run injection. Currently it's being added in two places to handle cases when
      # cucumberOptions are passed and when not passed.
      # We need to refactor it in such a way that it should be added once
      if @is_cucumber_test_suite && !@instrumentation_params.include?("-e cucumberOptions \"--dry-run")
        @instrumentation_params += " -e cucumberOptions \"--dry-run\""
        # When no cucumberOptions are passed and we want to run dry-run for test-suite
      elsif !@is_cucumber_test_suite
        @instrumentation_params = " -e log true#{@instrumentation_params}"      # For espresso dry run
      end

      cmd = "timeout 60 adb -s #{@device} shell $'am instrument -w "\
        "-r#{@instrumentation_params} #{instrumentation_name}' > #{@instrumentation_logs_file_path} 2>&1"
      while try <= max_retries
        output, status = OSUtils.execute(cmd, true)
        dry_run_output = File.read(@instrumentation_logs_file_path)

        instrument_device_off_adb(dry_run_output, @device, @session_id, __method__.to_s, status)

        dry_run_retry_reason = if dry_run_output.strip.empty?
                                 "dry-run-empty"
                               elsif dry_run_output.include?("Process crashed")
                                 "dry-run-crashed"
                               end

        # retry if process crashed or dry run logs output is empty
        break unless dry_run_retry_reason

        instrument_dry_run_retry(dry_run_retry_reason, try, status, @device, @session_id)

        try += 1
        BrowserStack.logger.info "Command failed. Attempt #{try}/#{max_retries}:"
        sleep delay
      end
    rescue StandardError => e
      ExitFile.write(e.message[0..200])
    end

    def check_for_instrumentation_error
      espresso_helper = EspressoHelper.new(@session_summary_file_path)
      espresso_helper.check_instrumentation_error(@instrumentation_logs_file_path, @callback_file_path, @device_model,
                                                  @device, @device_name, @device_version, @test_framework)
    end

    def parse_build(test_suite_file_path)
      espresso_helper = EspressoHelper.new(@session_summary_file_path)
      espresso_helper.parse_junit_build(@instrumentation_logs_file_path, test_suite_file_path)
    end

    def check_if_testsuite_parse_empty(test_suite_file_path)
      return unless callback_data && callback_data["error_reason"].to_s == ""

      if !File.file?(test_suite_file_path) || File.empty?(test_suite_file_path)
        zombie_error_message = @is_cucumber_test_suite ? 'cucumber-testsuite-parse-empty' : 'testsuite-parse-empty'
        zombie_push("android", zombie_error_message, "Testsuite file empty", @device_model, "", @device,
                    @session_id)
        @session_summary_file["error_reason"] = "testsuite-parse-empty"
        callback_data["error_reason"] = "testsuite-parse-empty"
        update_summary_and_callback_file
      end
    end

    def push_to_zombie_and_update_error_reason
      BrowserStack.logger.info "#{@device} #{@session_id} ADB command to start espresso session failed "\
                               "or timeout occurred"
      zombie_error_message = @is_cucumber_test_suite ? 'cucumber-adb-command-start-failed' : 'adb-command-start-failed'
      zombie_push("android", zombie_error_message, "ADB Command failure", @device_model, "", @device,
                  @session_id)
      @session_summary_file["error_reason"] = "testsuite-execution-timedout"
      callback_data["error_reason"] = "testsuite-execution-timedout"
      update_summary_and_callback_file
    end

    def callback_data
      @callback_data ||= begin
        JSON.parse(File.read(@callback_file_path))
      rescue StandardError
        {}
      end
    end

    def session_details
      @session_details ||= begin
        JSON.parse(File.read("/tmp/duplicate_session_#{@device}"))
      rescue StandardError
        {}
      end
    end

    def session_details_param_exists?(param)
      !(session_details[param].nil? || session_details[param] == '{}')
    end

    def test_params
      @test_params ||= (session_details_param_exists?("test_params") ? session_details["test_params"] : {})
    end

    def custom_test_params
      @custom_test_params ||= if session_details_param_exists?("custom_test_params")
                                session_details["custom_test_params"]
                              else
                                {}
                              end

    end

    def test_or_session_id
      @unique_test_id || @session_id
    end

    def coverage_params
      return "" unless @coverage_enabled

      " -e coverageFile #{DEVICE_COVERAGE_FILE_FOLDER}/#{test_or_session_id}-#{COVERAGE_FILE_NAME} -e coverage true"
    end

    def get_test_params(options_list, test_params_json)
      test_params_parsed = ""
      unless test_params_json.empty?
        JSON.parse(test_params_json).each do |k, v|
          value = v.is_a?(Array) ? v.join(",") : v
          BrowserStack.logger.info "key: #{k} value: #{value}"
          test_params_parsed += if k == "cucumberOptions"
                                  get_cucumber_options(value, options_list)
                                else
                                  " -e #{k} #{value}"
                                end
        end
      end

      BrowserStack.logger.info "test_params_parsed: #{test_params_parsed}"
      test_params_parsed
    rescue StandardError => e
      BrowserStack.logger.error "Error parsing test_params: #{e.message} #{e.backtrace}"
      raise e
    end

    def run_test(test_name, test_bundle, expiry_time, retry_attempt, max_test_retry_count, device_orientation,
                 use_orchestrator: false, clear_package_data: false )
      is_single_runner = test_name.nil? && @unique_test_id.nil? && retry_attempt.nil? && max_test_retry_count.nil?
      BrowserStack.logger.info "Started run test #{@unique_test_id} #{test_name} #{test_bundle} #{expiry_time} "\
                                 "#{retry_attempt} #{max_test_retry_count} #{use_orchestrator} "\
                                 "#{clear_package_data} #{is_single_runner}"
      adb_command_status = "failed"
      apply_device_orientation(device_orientation) unless device_orientation.nil?
      start_espresso_monitor_script(@unique_test_id)
      adb_command_status = execute_adb_command(expiry_time, test_name, test_bundle, use_orchestrator,
                                               clear_package_data)
      stop_espresso_monitor_script(@unique_test_id)
      if !is_single_runner && adb_command_status == "failed" &&
        File.empty?(@instrumentation_logs_file_path) && retry_attempt < max_test_retry_count

        BrowserStack.logger.info "Instrumentation file empty, retrying."
        retry_attempt += 1
        adb_command_status = run_test(
          test_name, test_bundle, expiry_time, retry_attempt, max_test_retry_count,
          device_orientation,
          use_orchestrator: use_orchestrator,
          clear_package_data: clear_package_data
        )
      end
      BrowserStack.logger.info "Finished run_test for #{test_name} with status #{adb_command_status}"
      adb_command_status
    rescue StandardError => e
      BrowserStack.logger.info "Error starting run test: #{e.message} #{e.backtrace}"
      ExitFile.write(e.message[0..200])
      nil
    end

    def apply_device_orientation(device_orientation)
      BrowserStack.logger.info "Setting device orientation"
      cmd = "timeout 60 adb -s #{@device} shell am startservice --user 0 "\
        "-n com.android.browserstack/.services.OrientationService "\
        "--es \\'orientation\\' \\'#{device_orientation}\\' 2>&1"
      output, status = OSUtils.execute(cmd, true)
      instrument_device_off_adb(output, @device, @session_id, __method__.to_s, status)
    rescue StandardError => e
      BrowserStack.logger.error "Error parsing test_params: #{e.message} #{e.backtrace}"
      raise e
    end

    def start_espresso_monitor_script(unique_test_id)
      timeout = @session_summary_file["idle_timeout"]
      estest = AppAutomateFrameworks::EspressoTimeoutManager.new(
        @device,
        @instrumentation_logs_file_path,
        framework: 'espresso',
        test_id: unique_test_id,
        logger: Logger.new($stdout)
      )
      estest.start(timeout)
    rescue ArgumentError => e
      BrowserStack.logger.info "Arg error TimeoutManager - retrying by removing extra args: #{e.message}"
      timeout = @session_summary_file["idle_timeout"]
      estest = AppAutomateFrameworks::EspressoTimeoutManager.new(
        @device,
        @instrumentation_logs_file_path,
        test_id: test_or_session_id,
        logger: Logger.new($stdout)
      )
      estest.start(timeout)
    rescue StandardError => e
      BrowserStack.logger.info "Error starting timeout monitor script: #{e.message} #{e.backtrace}"
      ExitFile.write(e.message[0..200])
      nil
    end

    def post_execute_adb_command_actions
      test_id = @unique_test_id || @session_id
      FileUtils.touch("#{ESPRESSO_DEVICE_OFF_ADB_FILE_PREFIX}_#{test_id}_#{@device}") if is_device_off_adb?(@device,
                                                                                                            test_id)
    end

    def execute_adb_command(expire_time, test_name, test_bundle, use_orchestrator, clear_package_data)
      BrowserStack.logger.info "test_name: #{test_name} test_bundle: #{test_bundle} unique_test_id: "\
                                 "#{@unique_test_id} expire_time: #{expire_time} use_orchestrator: "\
                                 "#{use_orchestrator} clear_package_data: #{clear_package_data}"
      instrumentation_params, test_bundle, shell_executor = get_adb_command_inputs(test_name, test_bundle,
                                                                                   use_orchestrator, clear_package_data)

      instrumentation_params = coverage_params + instrumentation_params

      instrumentation_params += get_test_params([], custom_test_params)
      cmd = "timeout #{expire_time} adb -s #{@device} shell $'#{shell_executor}am "\
            "instrument -w -r#{instrumentation_params} #{test_bundle}' > #{@instrumentation_logs_file_path} 2>&1"
      output, status = OSUtils.execute(cmd, true)
      if @test_framework == 'fluttertest'
        instrumentation_logs_content = begin
          File.readlines(@instrumentation_logs_file_path)
        rescue StandardError
          [""]
        end
        first_line_instru_logs = instrumentation_logs_content.first
        is_safe_to_copy = test_process_adb_killed?(first_line_instru_logs,
                                                   status.exitstatus) ||
                                                   first_line_instru_logs.include?("shortMsg=Process crashed")
        if is_safe_to_copy
          BrowserStack.logger.info "Copying dummy instrumentation logs to #{@instrumentation_logs_file_path}"
          FileUtils.cp(FLUTTER_ANDROID_FALLBACK_INSTRUMENTATION_LOGS, @instrumentation_logs_file_path)
        end
      end
      post_execute_adb_command_actions

      return "success" if status.success?

      if test_name.nil? && @unique_test_id.nil?      # If singleRunnerInvocation
        push_to_zombie_and_update_error_reason
      else
        BrowserStack.logger.info "#{@device} #{@unique_test_id} ADB command to start espresso test failed"
        zombie_push("android", "adb-command-start-failed", "ADB Command failure", @device_model, status, @device,
                    @unique_test_id)
      end
      "failed"
    rescue StandardError => e
      ExitFile.write(e.message[0..200])
      nil
    end

    def test_process_adb_killed?(logline, exitstatus)
      exitstatus.to_s == '137' && logline.include?("/bin/adb") && logline.include?("Killed")
    end

    def stop_espresso_monitor_script(unique_test_id)
      estest = AppAutomateFrameworks::EspressoTimeoutManager.new(
        @device,
        @instrumentation_logs_file_path,
        framework: 'espresso',
        test_id: unique_test_id,
        logger: Logger.new($stdout)
      )
      estest.stop("true")
    rescue ArgumentError => e
      BrowserStack.logger.info "Arg error stop TimeoutManager - retrying by removing extra args: #{e.message}"
      estest = AppAutomateFrameworks::EspressoTimeoutManager.new(
        @device,
        @instrumentation_logs_file_path,
        test_id: test_or_session_id,
        logger: Logger.new($stdout)
      )
      estest.stop("true")
    rescue StandardError => e
      ExitFile.write(e.message[0..200])
      nil
    end

    def get_adb_command_inputs(test_name, test_bundle, use_orchestrator, clear_package_data)
      is_single_runner = test_name.nil?
      if use_orchestrator.to_s == "true"
        instrumentation_params = get_test_params(SRI_OPTIONS, test_params)
        if clear_package_data.to_s == "true"
          instrumentation_params = " -e clearPackageData true#{instrumentation_params}"
        end

        test_bundle_parsed = " -e targetInstrumentation #{test_bundle} #{ORCHESTRATOR_RUNNER}"
        [instrumentation_params, test_bundle_parsed, ORCHESTRATOR_SHELL_EXECUTOR]
      elsif is_single_runner
        instrumentation_params = get_test_params(SRI_OPTIONS, test_params)
        [instrumentation_params, test_bundle, ""]
      elsif @is_cucumber_test_suite         # Default flow for cucumber
        cucumber_options = begin
          JSON.parse(test_params)
        rescue StandardError
          {}
        end
        cucumber_params = { "name" => test_name[test_name.index("#") + 1..] }
        if cucumber_options && cucumber_options["cucumberOptions"]
          cucumber_params["plugins"] = cucumber_options["cucumberOptions"]["plugins"]
        end

        instrumentation_params = get_cucumber_options(cucumber_params, CUCUMBER_DEFAULT_FLOW_OPTIONS)
        [instrumentation_params, test_bundle, ""]
      # Default flow for espresso
      else
        test_name = Regexp.escape(test_name) if test_name.to_s.include?(' ')
        instrumentation_params = " -e class #{test_name}"
        [instrumentation_params, test_bundle, ""]
      end
    end

    def option_defined?(value, option, options_list)
      !value.nil? && options_list.include?(option)
    end

    def get_report_paths(plugins)
      command_string = ""

      plugins.each do |plugin_name|
        plugin_object = CUCUMBER_REPORTS_MAP[plugin_name]
        plugin_path = "#{plugin_name}:#{BrowserStack::DEVICE_REPORTS_DIR}/#{plugin_object['dir']}/"\
                      "#{test_or_session_id}#{plugin_object['extension']}"
        command_string += " --plugin #{plugin_path}"
      end

      command_string
    end

    def get_cucumber_options(value, options)
      tags = value["tags"]
      escaped_tags = option_defined?(tags, "tags", options) ? " --tags \\'#{tags}\\'" : ""

      features = begin
        value["features"].join(" ")
      rescue StandardError
        nil
      end
      escaped_features = option_defined?(features, "features", options) ? " #{features}" : ""

      name = value["name"]
      if !name.nil? && options == CUCUMBER_DEFAULT_FLOW_OPTIONS
        name = name.gsub(/[-\[\]{}()&*;:+?.,\\^$|#]/) do |word|
          "\\#{word}"
        end
      end
      name_option = options == CUCUMBER_DEFAULT_FLOW_OPTIONS ? " --name \\'^#{name}$\\'" : " --name \\'#{name}\\'"
      escaped_name = option_defined?(name, "name", options) ? name_option : ""

      plugins = value["plugins"]
      plugins_path = option_defined?(plugins, "plugins", options) ? get_report_paths(plugins) : ""

      dry_run_command = options.include?("dry_run") ? "--dry-run" : ""

      BrowserStack.logger.info "tags: #{escaped_tags} features: #{escaped_features} \
                                name: #{escaped_name} plugins: #{plugins_path} dry_run #{dry_run_command}"

      " -e cucumberOptions \"#{dry_run_command}#{escaped_features}#{escaped_tags}#{escaped_name}#{plugins_path}\""
    end
  end
end

Espresso::InstrumentationRunner.run_from_bash if $PROGRAM_NAME == __FILE__
