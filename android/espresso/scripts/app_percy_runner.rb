#
# Script to interface between bash(espresso_action.sh) and ruby (app_percy_session.rb)
#
require 'dotenv/load'
require_relative '../app_percy/app_percy_session'

class AppPercyRunner
  def initialize
    log "Starting AppPercyRunner..."
  end

  def execute
    task = ARGV[0]
    device_id = ARGV[1]
    session_id = ARGV[2]
    app_percy_session = AppPercySession.new(device_id, session_id)

    case task
    when 'start_app_percy'
      params = ARGV[3]
      app_percy_session.start(params)
    when 'stop_app_percy'
      app_percy_session.stop
    else
      log "Wrong Helper Param"
    end
  rescue StandardError => e
    log "Error encountered #{e}"
  end

  # Helper method for logging
  # @param message [String] Log message
  def log(message)
    puts "#{self.class} ID: #{object_id} message: #{message}"
  end
end

AppPercyRunner.new.execute if __FILE__ == $PROGRAM_NAME
