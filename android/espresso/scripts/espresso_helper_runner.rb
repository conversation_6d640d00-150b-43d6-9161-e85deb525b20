#
# Script to interface between bash(espresso_action.sh) and ruby (espresso_helper.rb)
#
require 'dotenv/load'
require_relative '../helpers/espresso_helper'

class EspressoHelperRunner
  def initialize
    log "Starting EspressoHelperRunner..."
  end

  def execute
    task = ARGV[0]
    input_file = ARGV[1]
    session_file = ARGV[2]
    eh = EspressoHelper.new(session_file)

    case task
    when 'parse_build'
      testsuite_outfile = ARGV[3]
      eh.parse_junit_build(input_file, testsuite_outfile)
    when 'update_test_details' # Called after each test execution
      testname = ARGV[3]
      base_s3url = ARGV[4]
      start_time = ARGV[5]
      video_url = ARGV[6]
      video_url = video_url.gsub(/^"+|"+$/, '')
      screenshots_url = ARGV[7]
      screenshots_string = ARGV[8]
      merged_junit_report_metadata_file = ARGV[9]
      app_bundle_id = ARGV[10]
      device_id = ARGV[11]
      callback_file = ARGV[12]
      test_framework = ARGV[13]
      result = eh.parse_instrumentation_logs(input_file, testname, Time.at(start_time.to_i),
                                             merged_junit_report_metadata_file, video_url,
                                             app_bundle_id, device_id, callback_file, test_framework)
      eh.update_test_summary(testname, result, base_s3url, start_time, video_url, screenshots_url, screenshots_string)
    when 'update_session_details' # Called once all test cases are executed, i.e. when testsuite is finished.
      session_duration = ARGV[3]
      merged_junit_report_metadata_file = ARGV[4]
      junit_xml_report_file = ARGV[5]
      device_name = ARGV[6]
      device_version = ARGV[7]
      eh.update_session_details(session_duration)
      eh.generate_final_xml_report(merged_junit_report_metadata_file, junit_xml_report_file, device_name,
                                   device_version)
    when 'timeout_session'
      testsuite_file = ARGV[3]
      merged_junit_report_metadata_file = ARGV[4]
      eh.update_timeout_tests(testsuite_file, merged_junit_report_metadata_file)
    when 'get_test_message'
      pusher_outfile = ARGV[3]
      testname = ARGV[4]
      eh.get_test_pusher_message(testname, pusher_outfile)
    when 'get_build_message'
      pusher_outfile = ARGV[3]
      testsuite_file = ARGV[4]
      eh.get_build_pusher_message(pusher_outfile, testsuite_file)
    when 'update_test_to_running'
      testname_with_class_name = ARGV[3]
      eh.update_tests_status_to_running(testname_with_class_name)
    when 'session_completed_single_invocation'
      session_duration = ARGV[3]
      callback_file = ARGV[4]
      testsuite_file = ARGV[5]
      base_s3_url = ARGV[6]
      logs_url = ARGV[7]
      video_url = ARGV[8]
      device_id = ARGV[9]
      merged_junit_report_metadata_file = ARGV[10]
      junit_xml_report_file = ARGV[11]
      device_name = ARGV[12]
      device_version = ARGV[13]
      device_model = ARGV[14]
      test_framework = ARGV[15]
      video_start_time = ARGV[16]
      video_stop_time = ARGV[17]
      session_id = ARGV[18]
      logs_stability_file = ARGV[19]
      begin
        eh.generate_summary_file(input_file, callback_file, testsuite_file, base_s3_url, logs_url, video_url,
                                 session_duration, device_id, merged_junit_report_metadata_file, junit_xml_report_file,
                                 device_name, device_version, device_model, test_framework, video_start_time,
                                 video_stop_time, session_id, logs_stability_file)
      rescue StandardError => e
        puts "Exception in generating summary file #{e.message} #{e.backtrace}"
      end

    when 'check_for_instrumentation_error'
      callback_file = ARGV[3]
      device_model = ARGV[4]
      device_id = ARGV[5]
      device_name = ARGV[6]
      device_version = ARGV[7]
      test_framework = ARGV[8]
      eh.check_instrumentation_error(
        input_file, callback_file, device_model, device_id,
        device_name, device_version, test_framework
      )
    when 'process_video_offset'
      eh.update_video_tag_in_summary_files
    when 'handle_dangling_tests'
      eh.handle_dangling_tests
    when 'parse_device_logs'
      test_id = ARGV[3]
      app_bundle_id = ARGV[4]
      device_id = ARGV[5]
      eh.parse_device_logs(test_id, app_bundle_id, device_id)
    else
      puts "Wrong Helper Param"
    end
  end

  # Helper method for logging
  # @param message [String] Log message
  def log(message)
    puts "#{self.class} ID: #{object_id} message: #{message}"
  end
end

EspressoHelperRunner.new.execute if __FILE__ == $PROGRAM_NAME
