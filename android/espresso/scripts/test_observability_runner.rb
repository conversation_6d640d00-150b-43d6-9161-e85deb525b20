#
# Script to interface between bash(espresso_action.sh) and ruby (test_observability_runner.rb)
#
require 'android_toolkit'
require 'json'
require_relative '../../constants'

class TestObservabilityR<PERSON>ner
  def initialize
    log "Starting Test Observability Runner..."
  end

  def execute
    device_id = ARGV[0]
    tmp_file_path = "/tmp/test_observability_#{device_id}.json"
    begin
      generate_observability_data(device_id, tmp_file_path)
      push_to_device(device_id, tmp_file_path)
    rescue StandardError => e
      log "Error encountered during pushing SDK and Observability files to the device: #{e.message}"
    ensure
      File.delete(tmp_file_path) if File.exist?(tmp_file_path)
    end
  end

  def get_s3_params(device_id)
    file_path = "/tmp/devicelogs_params_#{device_id}"
    s3_params = {
      rails_s3_bucket: '',
      rails_s3_region: ''
    }

    if File.exist?(file_path)
      params = File.read(file_path).split
      s3_params[:rails_s3_bucket] = params[5]
      s3_params[:rails_s3_region] =  params[5] == "bs-stag" || params[4] == "us-east-1" ? "" : params[4]
    else
      puts "File not found: #{file_path}"
    end
    s3_params
  end

  def push_to_device(device_id, tmp_file_path)
    adb = AndroidToolkit::ADB.new(udid: device_id)

    adb.push(tmp_file_path, BrowserStack::TEMP_TEST_OBSERVABILITY_FILE)
    log "Pushed test observability data to #{BrowserStack::TEMP_TEST_OBSERVABILITY_FILE}"

    adb.shell("touch #{BrowserStack::TEMP_SDK_LOG_FILE}")
    log "Created SDK log file at #{BrowserStack::TEMP_SDK_LOG_FILE}"
  end

  def generate_observability_data(device_id, tmp_file_path)
    s3_params = get_s3_params(device_id)
    json_string = {
      device_id: device_id,
      device_name: ARGV[1],
      device_version: ARGV[2],
      rails_build_id: ARGV[3],
      rails_session_id: ARGV[4],
      rails_s3_bucket: s3_params[:rails_s3_bucket],
      rails_s3_region: s3_params[:rails_s3_region],
      testhub_jwt: ARGV[5],
      testhub_build_hashed_id: ARGV[6],
      test_observability: ARGV[7],
      accessibility: ARGV[8],
      testhub_accessibility_token: ARGV[9],
      testhub_polling_timeout: ARGV[10],
      testhub_scanner_version: ARGV[11],
      hostname: ARGV[12],
      include_tags_in_testing_scope: ARGV[13],
      exclude_tags_in_testing_scope: ARGV[14]
    }.to_json
    File.write(tmp_file_path, json_string)
  end

  # Helper method for logging
  # @param message [String] Log message
  def log(message)
    puts "#{self.class} ID: #{object_id} message: #{message}"
  end
end

TestObservabilityRunner.new.execute if __FILE__ == $PROGRAM_NAME
