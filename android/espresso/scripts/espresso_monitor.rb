#!/usr/bin/env ruby

require 'dotenv/load'
require 'logger'
require(
  '/usr/local/.browserstack/mobile-common/'\
  'frameworks_timeout_manager/app_automate_frameworks/espresso_timeout_manager'
)
raise 'invalid command line arguments' if ARGV.length < 4

if $PROGRAM_NAME == __FILE__
  task = ARGV[0]
  device_id = ARGV[1]
  target_file = ARGV[2]
  case task
  when 'start'
    timeout = ARGV[3]
    estest = AppAutomateFrameworks::EspressoTimeoutManager.new(
      device_id,
      target_file,
      logger: Logger.new($stdout)
    )
    estest.start(timeout)
  when 'stop'
    delete_flag = ARGV[3]
    estest = AppAutomateFrameworks::EspressoTimeoutManager.new(
      device_id,
      target_file,
      logger: Logger.new($stdout)
    )
    estest.stop(delete_flag.to_s.downcase)
  else
    puts "Wrong Monitor Param"
  end
end
