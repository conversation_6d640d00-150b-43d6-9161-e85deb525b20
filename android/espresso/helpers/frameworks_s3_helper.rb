module FrameworksS3Helper
  def self.get_s3_params(device_id, build_id)
    params = File.read("/tmp/devicelogs_params_#{device_id}").split
    aws_region = params[5] == "bs-stag" || params[4] == "us-east-1" ? "" : params[4]
    aws_bucket = params[5]
    {
      key: params[2],
      secret: params[3],
      bucket: aws_bucket,
      storage_class: params[7],
      region: aws_region,
      base_url: "https://s3#{aws_region}.amazonaws.com/#{aws_bucket}/#{build_id}"
    }
  end
end
