require 'android_toolkit'
require 'logger'
require 'fileutils'
require 'json'
require_relative "../../helpers/utils"

STATE_FILES_DIR = '/usr/local/.browserstack/state_files'.freeze
FORCE_FULL_CLEANUP_FILE_SUFFIX = "_force_full_clean".freeze
BS_HOME = "/usr/local/.browserstack".freeze
DIR_HOME = "#{BS_HOME}/mobile".freeze

require "#{DIR_HOME}/common/push_to_zombie.rb"

require_relative '../../exit_file'

class LogcatBufferManager
  def initialize(device_id, logger, session_id = nil, buffer_data = nil)
    @device_id = device_id
    @logger = logger
    @session_id = session_id

    buffer_data ||= LogcatBufferManager.logcat_buffer_data_from_session_file(@device_id)
    @logcat_buffer_data = buffer_data
    @logcat_buffers = @logcat_buffer_data.keys - ["max", "multiplier"]
    @adb = AndroidToolkit::ADB.new(udid: @device_id, path: BrowserStack::ADB)
    log :info, "init: #{device_id} #{session_id}"
  end

  def set_buffer
    clamped_buffer_data = clamped_logcat_buffer_data
    raise StandardError, "Clamped logcat buffer data is empty" if clamped_buffer_data.empty?

    log :info, "Updating logcat buffers #{@logcat_buffers} with data: #{clamped_buffer_data}"
    @logcat_buffers.each do |key|
      @adb.shell("logcat -b #{key} -G #{clamped_buffer_data[key].to_i}K")
    end
    log :info, "logcat buffers update status: Success"
  rescue AndroidToolkit::ADB::DeviceNotFound => e
    data = { "source" => __method__.to_s }
    zombie_push("android",
                "device-off-adb",
                e,
                "android",
                data.to_json,
                @device_id,
                @session_id)
  rescue AndroidToolkit::ADB::DeviceOffline => e
    data = { "source" => __method__.to_s }
    zombie_push("android",
                "device-off-adb",
                e,
                "android",
                data.to_json,
                @device_id,
                @session_id)
  rescue StandardError => e
    BrowserStack.logger.info(
      "Error while updating logcat buffer size #{e.message}."
    )
    zombie_push(
      'android',
      'logcat-buffer-update-failed',
      'Logcat buffer update failed in start request',
      '',
      e.message.to_s,
      @device_id,
      @session_id,
      ''
    )
  end

  def reset_original_buffer
    return if @logcat_buffer_data.empty?

    log :info, "Resetting original logcat buffers #{@logcat_buffers} with data: #{@logcat_buffer_data}"
    @logcat_buffers.each do |key|
      @adb.shell("logcat -b #{key} -G #{@logcat_buffer_data[key].to_i}K")
    end
    log :info, "logcat buffers reset status: Success"
  rescue AndroidToolkit::ADB::DeviceNotFound => e
    data = { "source" => __method__.to_s }
    zombie_push("android",
                "device-off-adb",
                e,
                "android",
                data.to_json,
                @device_id,
                @session_id)
  rescue AndroidToolkit::ADB::DeviceOffline => e
    data = { "source" => __method__.to_s }
    zombie_push("android",
                "device-off-adb",
                e,
                "android",
                data.to_json,
                @device_id,
                @session_id)
  rescue StandardError => e
    log :info, "Error while resetting logcat buffer size #{e.message}\n"\
    "full cleanup resets the buffer, touching full cleanup state file."

    FileUtils.touch(
      File.join(
        STATE_FILES_DIR.to_s,
        @device_id + FORCE_FULL_CLEANUP_FILE_SUFFIX
      )
    )

    zombie_push(
      'android',
      'logcat-buffer-reset-failed',
      'Logcat buffer reset failed in cleanup',
      '',
      e.message.to_s,
      @device_id,
      @session_id,
      ''
    )
  end

  def self.logcat_buffer_data_from_session_file(device)
    session_data = JSON.parse(File.read("/tmp/duplicate_session_#{device}"))
    if session_data["app_automate_custom_params"] && session_data["app_automate_custom_params"]["logcat_buffer_data"]
      session_data["app_automate_custom_params"]["logcat_buffer_data"]
    else
      {}
    end
  rescue StandardError
    {}
  end

  def self.run_from_bash
    method_to_call = ARGV[0].to_s.strip
    device_id = ARGV[1].to_s.strip
    session_id = ARGV[2].to_s.strip
    logcat_buffer_data = JSON.parse(ARGV[3].to_s.strip) if ARGV.length > 3

    LogcatBufferManager.new(device_id, Logger.new($stdout), session_id, logcat_buffer_data).send(method_to_call)
  rescue StandardError => e
    ExitFile.write(e.message)
    raise e
  end

  private

  def clamped_logcat_buffer_data
    clamped_buffer_data = {}
    @logcat_buffers.each do |key|
      multiplied_value = (@logcat_buffer_data[key].to_i * @logcat_buffer_data["multiplier"].to_i)
      clamped_buffer_data[key] = if multiplied_value <= @logcat_buffer_data["max"].to_i
                                   multiplied_value
                                 else
                                   @logcat_buffer_data["max"].to_i
                                 end
    end
    clamped_buffer_data
  rescue StandardError => e
    log :error, "Error in calculating new logcat buffer size, message : #{e.message}"
    {}
  end

  def log(level, msg)
    params = { component: self.class.to_s, device: @device, session: @session_id }
    if @logger.instance_of?(Logger)
      @logger.send(level.to_sym, msg)
    else
      @logger.send(level.to_sym, msg, params)
    end
  end
end

LogcatBufferManager.run_from_bash if $PROGRAM_NAME == __FILE__
