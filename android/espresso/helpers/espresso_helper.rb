require 'json'
require 'digest'
require 'date'
require 'fileutils'
require 'dotenv/load'

require_relative '../../constants'
require_relative "../../lib/os_utils"

BS_HOME = "/usr/local/.browserstack".freeze
MOBILE_COMMON_HOME = "#{BS_HOME}/mobile-common".freeze
DIR_HOME = "#{BS_HOME}/mobile".freeze

require 'browserstack_logger'
require "#{MOBILE_COMMON_HOME}/frameworks_timeout_manager/app_automate_frameworks/espresso_timeout_manager.rb"

require "#{DIR_HOME}/common/push_to_zombie.rb"
require "#{DIR_HOME}/android/helpers/utils.rb"
require_relative "./junit_reporter"
require_relative "./instrumentation_result_parser"
require_relative "./cucumber_helper"
require_relative '../parsers/test_logs_parser'
require_relative '../parsers/log_split_manager'

include BrowserStack # rubocop:todo Style/MixinUsage

class EspressoHelper # rubocop:todo Metrics/ClassLength
  DEFAULT_TEST_STATUS = 'QUEUED'.freeze
  SERVER_LOG = "/var/log/browserstack/server.log".freeze
  UNNECESSARY_KEYS = ['video', 'screenshots', 'device', 'common_package_name', 'is_duplicate',
                      'is_cucumber_test_suite', 'use_rtc_app'].freeze
  ERROR_KEYS = {
    "user-testsuite-parse-failed" => "Process crashed",
    "testsuite-signature-mismatch" => "does not have a signature matching the target",
    "testsuite-app-mismatch" => "Unable to find instrumentation target package",
    "testsuite-missing-instrumentation" => "Unable to find instrumentation info",
    "testsuite-no-tests-found" => "OK \\(0 tests\\)"
  }.freeze
  JUNIT_INSTRUMENTATION_FAILURE = "INSTRUMENTATION_CODE: -1".freeze

  def initialize(summary_file)
    logger_params = {
      component: "espresso_helper"
    }
    BrowserStack.init_logger(SERVER_LOG, logger_params)

    @summary_file = summary_file
    begin
      @summary_data = JSON.parse(File.read(summary_file))
    rescue StandardError => e
      @summary_data = {}
      BrowserStack.logger.info "[#{summary_file}] Unable to read summary file v1. Setting to {}"
    end

    # Session Summary File v2
    @summary_file_v2 = "#{@summary_file}_v2"
    # To ensure we do not initialize EspressoHelper without session summary v2 file.
    File.open(@summary_file_v2, 'w') { |f| f.write('{}') } unless File.exist?(@summary_file_v2)
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))
    @is_cucumber_test_suite = @summary_data_v2['is_cucumber_test_suite']
  end

  def default_test(id)
    {
      start_time: '',
      status: DEFAULT_TEST_STATUS,
      test_id: id,
      duration: '',
      instrumentation_log: '',
      device_log: '',
      video: ''
    }
  end

  def default_test_v2(id, testname)
    {
      name: testname,
      start_time: '',
      status: DEFAULT_TEST_STATUS.downcase,
      test_id: id,
      duration: '',
      class: "",
      video: ''
    }
  end

  def test_states(count = 0)
    {
      "total" => count,
      'passed' => 0,
      'failed' => 0,
      'skipped' => 0,
      'timedout' => 0,
      'error' => 0,
      'running' => 0,
      'queued' => count
    }

  end

  def parse_junit_build(input, outfile)
    result = {}
    result_v2 = {}
    instrument_classes = []
    instrument_test = ''
    last_class = ''
    only_class_name = ''
    flat_test_list = []
    session_id = @summary_data_v2['session_id']

    #get classnames from instrumentation file
    list_of_classnames = EspressoHelper.get_classnames_from_file(input)

    #check whether duplicate classed are present or not in the list fetched from above
    is_duplicate = EspressoHelper.check_duplicate_classes(list_of_classnames)

    #This logic is to fetch common sub string among classes to differentiate duplicate classes
    # else we set common substring as empty string, because classes are not common
    common_package_name = is_duplicate ? EspressoHelper.get_common_package_name(list_of_classnames) : ""

    File.open(input) do |f|
      f.each_line do |line|
        if line.include?("INSTRUMENTATION_STATUS_CODE")
          test_class_hash = "#{last_class}##{instrument_test}"
          next if flat_test_list.include? test_class_hash
          next if last_class.empty? || instrument_test.empty? || (instrument_test == "null") || only_class_name.empty?

          test_id = session_id + get_test_id(test_class_hash)
          test_obj = default_test(test_id)
          flat_test_list << test_class_hash
          result[last_class][instrument_test] = test_obj

          result_v2[only_class_name][:tests][instrument_test] = default_test_v2(test_id, instrument_test)
          result_v2[only_class_name][:tests][instrument_test][:class] = only_class_name
          result_v2[only_class_name][:tests_summary]['total'] += 1
          result_v2[only_class_name][:tests_summary]['queued'] += 1

          instrument_test = ''
        end

        if line.include?("INSTRUMENTATION_STATUS: class=")
          line = line.gsub("INSTRUMENTATION_STATUS: class=", "").strip
          line = line.sub(/^Feature/, "").strip if @is_cucumber_test_suite
          unless instrument_classes.last == line
            instrument_classes.push(line)
            last_class = line
            only_class_name = EspressoHelper.get_only_class_name(common_package_name, is_duplicate, last_class)
            result[line] = {}

            if result_v2[only_class_name].nil? && !only_class_name.empty?
              result_v2[only_class_name] = {
                name: only_class_name,
                tests_summary: test_states,
                tests: {}
              }
            end
          end
        elsif line.include?("INSTRUMENTATION_STATUS: test=")
          instrument_test = line.gsub("INSTRUMENTATION_STATUS: test=", "").strip
          if @is_cucumber_test_suite
            # Scenario Outline test name example => test name example
            instrument_test = instrument_test.sub(/^Scenario\s(Outline)?/, "").strip
          end
        end
      end
    end

    if @is_cucumber_test_suite
      cucumber_helper = CucumberHelper.new
      flat_test_list, result, result_v2 = cucumber_helper.parse_scenario_outline(flat_test_list, result,
                                                                                 result_v2, session_id,
                                                                                 is_single_runner_invocation: false)
    end

    safe_file_write(outfile, @summary_data_v2['device'], session_id, __method__.to_s) do |f|
      f.puts(flat_test_list)
    end

    result.each { |class_name, class_obj| result.delete(class_name) if class_obj.empty? }
    @summary_data['test_count'] = flat_test_list.count
    @summary_data['test_status'] = {
      'SUCCESS' => 0,
      'FAILED' => 0,
      'IGNORED' => 0,
      'TIMEDOUT' => 0,
      'RUNNING' => 0,
      'QUEUED' => flat_test_list.count,
      'ERROR' => 0
    }
    @summary_data['test_details'] = result

    # Session Summary File V2
    result_v2.each { |class_name, class_obj| result_v2.delete(class_name) if class_obj[:tests].empty? }
    @summary_data_v2['test_summary'] = test_states(flat_test_list.count)
    @summary_data_v2['classes'] = result_v2

    #This key will be used by other functions to check whether a common substring
    # is present in order to split the classnames from their common sub string
    @summary_data_v2["common_package_name"] = common_package_name
    @summary_data_v2["is_duplicate"] = is_duplicate

    update_summary_file
  end

  # Parser to extract the stack trace from the instrumentation log line (in case of failures)
  class StackTraceExtractor
    attr_reader :stacktrace

    def initialize
      @state = :found_failure
      @stacktrace = ""
    end

    def handle_line(line)
      case @state
      when :found_failure
        case line
        when /There was .* failure/
          @state = :waiting_for_list
        end
      when :waiting_for_list
        case line
        when /^\d+\)/
          @state = :proceed_to_collect_errors
        end
      when :proceed_to_collect_errors
        case line
        when /FAILURES/
          @state = :collected_error_message
          return
        end
        # at this state, `line` now contains the stacktrace
        @stacktrace += line
      end
    end
  end

  # Parses a line from instrumentation logs, and checks if that line might be due to BrowserStack error
  # @param line [String] a single line from instrumentation logs file
  #
  # @return [String/nil] if browserstack error is found then the return string will be
  # the corresponding message otherwise nil
  def self.get_browserstack_error(line)
    error_message = nil
    # Array of arrays, here first value of embedded array is pattern and the second one
    # is the corresponding error message
    pattern_message_list = [
      ["device .* not found", ESPRESSO_DEVICE_NOT_FOUND],
      ["error: device offline", ESPRESSO_DEVICE_OFFLINE],
      ["Can't connect to activity manager; is the system running?", ESPRESSO_DEVICE_AM_CRASH],
      ["test.espresso.NoActivityResumedException: No activities in stage RESUMED. "\
       "Did you forget to launch the activity. (test.getActivity() or similar)?", ESPRESSO_DEVICE_NOT_UNLOCKED],
      ["INSTRUMENTATION_ABORTED: System has crashed.", ESPRESSO_INSTRUMENTATION_ABORTED]
    ]
    # Iterate over array and assign the value to error_message, if corresponding pattern matches
    pattern_message_list.each do |pattern_entity|
      if line.match(pattern_entity[0])
        error_message = pattern_entity[1]
        break
      end
    end
    error_message
  end

  def self.is_device_screen_locked?(device_id)
    result, status = OSUtils.execute("sh #{DRIVER_ACTIONS} check_screen_lock #{device_id}", true)
    status.exitstatus == 0 && result.include?("Screen is locked")
  end

  def self.ensure_device_screen_unlocked(device_id)
    _, status = OSUtils.execute("sh #{DRIVER_ACTIONS} ensure_screen_is_unlocked #{device_id}", true)
    status.exitstatus == 0
  end

  def self.is_browserstack_error?(error_message, device_id)
    case error_message
    when nil
      false
    when ESPRESSO_DEVICE_NOT_UNLOCKED
      is_locked = EspressoHelper.is_device_screen_locked?(device_id)
      EspressoHelper.ensure_device_screen_unlocked(device_id) if is_locked

      is_locked
    else
      true
    end
  end

  def update_scenario_outline_examples_status_list(test_status, scenario_outline_examples_status)
    if @is_cucumber_test_suite
      test_status_v2 = InstrumentationResultParser.get_test_status_v2(test_status)
      scenario_outline_examples_status.push(test_status_v2)
    end
  end

  #
  # Parses the instrumentation logs (generated from `adb shell am instrument`) to get
  # a test case's duration and status. Also add the test case to the JUnitReporter for rendering the final
  # JUnit XML report after all test cases are executed.
  #
  # @param input [String] Path to the instrumentation log file
  # @param testname [String] Name of the testname executed (with the classname),
  # example: ensureMultipleInputIsHandled#com.sample.browserstack.samplecalculator.EnsureInputTests
  # @param start_time [Time] Timestamp when the test execution started..
  # @param merged_junit_report_metadata_file [String] Path to file which will be used to seed the JUnitReporter
  #
  # @return [Hash] {status: SUCCESS|FAILED|IGNORED|TIMEDOUT|ERROR, duration: "test_duration_human"}
  # rubocop:todo Metrics/MethodLength
  def parse_instrumentation_logs(input, testname, start_time, merged_junit_report_metadata_file, video_url,
                                 _app_bundle_id, device_id, callback_file, test_framework)
    test_status = nil
    test_duration = ''
    stacktrace = ''
    parser = nil
    video_url ||= ''
    callback_file_data = JSON.parse(File.read(callback_file))
    scenario_outline_examples_status = []
    test_id = (@summary_data_v2['session_id'] + get_test_id(testname)).to_s

    if File.zero?(input)
      test_status = "ERROR"
      stacktrace += ESPRESSO_INSTRUMENT_COMMAND_STALLED
      zombie_data = {
        "line" => ESPRESSO_INSTRUMENT_COMMAND_STALLED,
        "test_id" => test_id
      }
      zombie_push('android', "#{test_framework}-test-marked-error", ESPRESSO_INSTRUMENT_COMMAND_STALLED, '',
                  zombie_data, device_id, @summary_data_v2['session_id'], '')
    else
      File.open(input) do |f|
        failure_declaration_found = false

        f.each_line do |line|
          if line.include?("INSTRUMENTATION_STATUS: stack=") && line.match(ESPRESSO_SOCKET_TIMEOUT)
            zombie_push(
              'android',
              "espresso-socket-timeout-exception",
              test_id,
              '',
              line.to_s[0, 200],
              device_id,
              @summary_data_v2['session_id'],
              ''
            )
          end

          error_message = EspressoHelper.get_browserstack_error(line)
          if line.match(ESPRESSO_ROOT_VIEW_HIERARCHY)
            zombie_data = {
              "line" => line.to_s[0, 200],
              "test_id" => test_id
            }
            zombie_push('android', "espresso-root-view-hierarchy", '', '', zombie_data, device_id,
                        @summary_data_v2['session_id'], '')
          # This means, it can be an error due to BrowserStack
          elsif EspressoHelper.is_browserstack_error?(error_message, device_id)
            test_status = 'ERROR'
            stacktrace += error_message
            zombie_data = {
              "line" => line.to_s[0, 200],
              "test_id" => test_id
            }
            zombie_push('android', "#{test_framework}-test-marked-error", error_message, '', zombie_data, device_id,
                        @summary_data_v2['session_id'], '')
            case error_message
            when ESPRESSO_DEVICE_NOT_FOUND
              callback_file_data["error_reason"] = "device-off-adb"
              write_to_file(callback_file, callback_file_data)
            when ESPRESSO_DEVICE_OFFLINE
              callback_file_data["error_reason"] = "device-adb-offline"
              write_to_file(callback_file, callback_file_data)
            when ESPRESSO_DEVICE_AM_CRASH
              callback_file_data["error_reason"] = "device-am-crash"
              write_to_file(callback_file, callback_file_data)
            end
            update_scenario_outline_examples_status_list(test_status, scenario_outline_examples_status)
          elsif test_duration.empty? && line.match('Time:')
            test_duration = line.split(" ")[1].to_s
            test_duration.sub!(",", ".") # Converting "2,933" to "2.933"
          elsif test_status != 'ERROR' # Incase of Error ignore any other test_status
            case line
            when /INSTRUMENTATION_RESULT: shortMsg=java\.util\.(.*)Exception/
              test_status = 'FAILED'
              update_scenario_outline_examples_status_list(test_status, scenario_outline_examples_status)
            when /INSTRUMENTATION_STATUS_CODE: (.*)$/
              case Regexp.last_match[1].strip
              when '1' # This means test has started and this code can be ignored
                test_status = 'TIMEDOUT' if test_status.nil?
                next
              when '0'
                test_status = 'SUCCESS'
                update_scenario_outline_examples_status_list(test_status, scenario_outline_examples_status)
              when '-1' # Fatal error occurred
                test_status = 'FAILED'
                update_scenario_outline_examples_status_list(test_status, scenario_outline_examples_status)
                stacktrace += 'Fatal Error'
                zombie_push(
                  'android',
                  "#{test_framework}-test-failed",
                  test_id,
                  '',
                  line.to_s[0, 200],
                  device_id,
                  @summary_data_v2['session_id'],
                  ''
                )
                # TODO: Need to add stacktrace over here, needs to be done later, not enough data currently
              when '-2' # Test assertion failure
                test_status = 'FAILED'
                update_scenario_outline_examples_status_list(test_status, scenario_outline_examples_status)
                parser = StackTraceExtractor.new
              when '-3', '-4'
                test_status = 'IGNORED'
                update_scenario_outline_examples_status_list(test_status, scenario_outline_examples_status)
              else
                # Ideally this should never happen
                test_status = 'FAILED'
                update_scenario_outline_examples_status_list(test_status, scenario_outline_examples_status)
                stacktrace += 'Unknown status code'
                zombie_push('android', "#{test_framework}-test-failed-unknown",
                            test_id, '', line.to_s[0, 200],
                            device_id, @summary_data_v2['session_id'], '')
              end
            when /Process crashed/  # App crashes results in this error, marking test failed in such cases.
              test_status = 'FAILED'
              update_scenario_outline_examples_status_list(test_status, scenario_outline_examples_status)
              stacktrace += 'Process Crashed'
              #TODO: Add proper stacktrace in case of process crash.
            when /FAILURES!!!/
              failure_declaration_found = true
            end
          end
          parser&.handle_line(line)
        end

        if failure_declaration_found && test_status == 'SUCCESS'
          BrowserStack.logger.info "[#{test_id}] Test Status Mismatch found in instrumentation logs"
          zombie_push('android',
                      "#{test_framework}-failure-found-in-passed",
                      '',
                      '',
                      test_id,
                      device_id,
                      @summary_data_v2['session_id'])
        end
      end
    end

    test_status = fetch_scenario_outline_status(scenario_outline_examples_status, test_status)

    # Mark test status as error if test timedout due to off-adb.
    if test_status == 'TIMEDOUT' && is_device_off_adb?(device_id, test_id)
      BrowserStack.logger.info "Device went off adb during test execution, marking test status as ERROR"
      test_status = 'ERROR'

      zombie_push('android', "#{test_framework}-test-marked-error", 'timedout test marked error due to off-adb',
                  '',
                  {
                    "test_id" => test_id
                  }.to_json,
                  device_id,
                  @summary_data_v2['session_id'], '')

      # Mark secondary diagnostic reason as `device-off-adb`
      callback_file_data["error_reason"] = "device-off-adb"
      write_to_file(callback_file, callback_file_data)

      device_off_adb_file_path = "#{ESPRESSO_DEVICE_OFF_ADB_FILE_PREFIX}_#{test_id}_#{device_id}"
      File.delete(device_off_adb_file_path) if File.exist?(device_off_adb_file_path)
    end

    if parser
      puts "Checking if stacktrace was collected.."
      if parser.stacktrace && !parser.stacktrace.empty?
        puts "Stacktrace collected of size: #{parser.stacktrace.size}"
        stacktrace += parser.stacktrace
      else
        puts "Error: Stacktrace was not collected from the instrumentation log line."
      end
    else
      puts "No stacktrace to fetch, as the test did not fail."
    end

    # Default value for test status is FAILED
    if test_status.nil?
      test_status = "FAILED"
      stacktrace += 'Not able to detect test status.'
      zombie_push(
        'android',
        "#{test_framework}-test-failed-default",
        test_id,
        '',
        '',
        device_id,
        @summary_data_v2['session_id'],
        ''
      )
    end

    #TODO: Too many args, move these to a dedicated class.
    add_test_to_junit_report(testname,
                             test_status,
                             start_time,
                             test_duration.to_f,
                             stacktrace,
                             merged_junit_report_metadata_file,
                             video_url)

    { status: test_status, duration: test_duration }
  end

  def fetch_scenario_outline_status(scenario_outline_examples_status, test_status)
    # This function returns the status of a scenario outline based on statuses of the examples.
    # For eg:  scenario_outline_examples_status = ["passed", "failed", "skipped"]
    # Output = passed
    # For cases not having scenario outlines, or status TIMEDOUT i.e. INSTRUMENTATION_STATUS_CODE = 1
    if @is_cucumber_test_suite && !scenario_outline_examples_status.empty? && test_status != "TIMEDOUT"
      tests_status_summary = CucumberHelper.default_tests_summary
      scenario_outline_examples_status.each do |scenario_outline_example_status|
        tests_status_summary[scenario_outline_example_status] += 1
        tests_status_summary['total'] += 1
      end
      new_state = CucumberHelper.get_scenario_outline_status(tests_status_summary)
      BrowserStack.logger.info "Updating Scenario Outline status to #{new_state}"
      test_status = CucumberHelper.get_test_status_v1(new_state)
    end

    test_status
  end

  # rubocop:enable Metrics/MethodLength
    # Assumption made:
  # - pid in logline is of app_bundle_id only
  # - logline pair will only appear once, if appears multiple times then first pid will get instrumented.
  def instrument_dynamite_process_killer(app_bundle_id, device_id, device_log_file, test_id, session_id)
    process_regex = nil
    pid = nil
    dynamite_loader_regex = %r{I/DynamiteLoaderV2Impl\((\s*\d+)\):\s*\[(\d+)\]\s*1:measurement.dynamite}

    instrumentation_data = {
      "app_bundle_id" => app_bundle_id,
      "test_id" => test_id
    }

    Timeout.timeout(7) do
      start_time = Time.now
      File.foreach(device_log_file) do |line|
        dynamite_match = line.match(dynamite_loader_regex) if pid.nil?
        if !dynamite_match.nil? && !dynamite_match[1].nil? && !dynamite_match[2].nil?
          instrumentation_data["dynamite_code"] = dynamite_match[2].strip.to_i
          pid = dynamite_match[1].strip.to_i
          process_regex = %r{I/Process \( *#{pid}\): Sending signal. PID: #{pid} SIG: ([0-9]{1,2})}
          next
        end

        unless pid.nil? && process_regex.nil?
          process_match = line.match(process_regex)
          if !process_match.nil? && !process_match[1].nil?
            instrumentation_data["process_killed_signal"] = process_match[1].strip.to_i
            break
          end
        end
      end
      parsing_time = Time.now - start_time
      unless instrumentation_data["process_killed_signal"].nil?
        BrowserStack.logger.info "#{session_id} Dynamite process kill in device log #{parsing_time} seconds."
        zombie_push(
          'android', 'espresso-dynamite-killed-process', "Parsing time: #{parsing_time}", "",
          instrumentation_data.to_json, device_id, session_id
        )
      end
    end
  rescue Timeout::Error => e
    BrowserStack.logger.error "#{session_id} Runtime device logs parsing timed out: #{e.message}"
    zombie_push('android', 'espresso-device-logs-parse-timeout', "", "", "", device_id, session_id)
  rescue StandardError => e
    BrowserStack.logger.error "[#{session_id}] Runtime device logs parsing errored: #{e.message}"
  end

  def parse_device_logs(test_id, app_bundle_id, device_id)
    device_log_file = File.join(LOGGING_DIR, "app_log_#{device_id}.log")
    unless File.exist?(device_log_file)
      BrowserStack.logger.error "[#{@summary_data_v2['session_id']}] Can't parse device logs - file not found"
      return
    end

    instrument_dynamite_process_killer(app_bundle_id, device_id, device_log_file, test_id,
                                       @summary_data_v2['session_id'])
  end

  # Update the JUnitReporter report file
  #
  # @param testname [String] Name of the testname executed (with the classname),
  # example: ensureMultipleInputIsHandled#com.sample.browserstack.samplecalculator.EnsureInputTests
  # @param test_status [String] SUCCESS|FAILED|IGNORED|TIMEDOUT|ERROR
  # @param start_time [Time] Timestamp when the test execution started
  # @param test_duration [Float] Number of seconds the test was executed for
  # @param stacktrace [String] Stacktrace generated from the test execution (exists only for failed tests)
  # @param merged_junit_report_metadata_file [String] Path to file which will be used to seed the JUnitReporter
  def add_test_to_junit_report(testname, test_status, start_time, test_duration, stacktrace,
                               merged_junit_report_metadata_file, video_url)
    classname = testname.split("#")[0]
    test = testname.split("#")[1]

    junit_report_generator = JUnitReporter.new(merged_junit_report_metadata_file, @summary_data_v2['session_id'])

    started_at = start_time
    completed_at = started_at + test_duration

    junit_test_status = case test_status
                        when "SUCCESS"
                          :passed
                        when "FAILED"
                          :failed
                        when "IGNORED"
                          :skipped
                        when "TIMEDOUT"
                          :timedout
                        when "ERROR"
                          :error
                        else
                          :skipped
                        end

    # Add test case to the report
    junit_report_generator.add_result(classname, test, junit_test_status, started_at, completed_at, video_url,
                                      (@summary_data_v2['session_id'] + get_test_id(testname)).to_s, stacktrace)

    # save_state to ensure that merged_junit_report_metadata_file is updated with the
    # current state of junit_report_generator.
    junit_report_generator.save_state
  end

  def get_test_id(test_input)
    md5_id = Digest::MD5.hexdigest test_input
    md5_id[0..7]
  end

  def get_screenshots_hash(s3url, test_id, ss_string, get_screenshot, version = 'v1')
    return {} if ss_string.nil? || ss_string.empty?

    result = { "screenshots" => [] }
    if get_screenshot.to_s.casecmp("true").zero?
      ss_string.to_s.split(";").each do |ss_name|
        ss_name.delete!('\\"')
        ss_name.delete!("\r")
        ss_name.delete!("\n")
        result["screenshots"] << [s3url, test_id, "screenshots", ss_name].join("/") if version == 'v1'
        result["screenshots"] << ss_name if !ss_name.empty? && version == 'v2'
      end
    end
    result
  end

  # Updates the test_status at session and class level
  def update_tests_status(classname, testname, new_state, old_state)
    puts "Updating #{classname}-#{testname} from #{old_state} to #{new_state}"
    unless @summary_data_v2.nil?
      class_result = begin
        @summary_data_v2['classes'][classname]['tests_summary']
      rescue StandardError
        nil
      end
      unless class_result.nil?
        class_result[new_state] += 1
        class_result[old_state] -= 1
      end

      unless @summary_data_v2["test_summary"].nil?
        @summary_data_v2["test_summary"][new_state] += 1
        @summary_data_v2["test_summary"][old_state] -= 1
      end
    end
  end

  def update_test_summary(testname, result, s3url, start_time, video_url, ss_url, ss_string)
    @summary_data = JSON.parse(File.read(@summary_file))
    # Session Summary File V2
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))
    common_package_name = @summary_data_v2["common_package_name"]
    is_duplicate = @summary_data_v2["is_duplicate"]

    classname = testname.split("#")[0]
    classname_v2 = get_class_name(testname, common_package_name, is_duplicate)
    testname = testname.split("#")[1]

    test_object = begin
      @summary_data['test_details'][classname][testname]
    rescue StandardError
      nil
    end
    video_url = "" if @summary_data["video"].to_s == 'false'
    unless test_object.nil?
      test_id = test_object['test_id']
      test_object.merge!({
        'status' => result[:status],
        'duration' => result[:duration],
        'device_log' => @summary_data["deviceLogs"].to_s == 'false' ? "" : "#{s3url}#{test_id}/devicelogs",
        'network_log' => @summary_data["networkLogs"].to_s == 'false' ? "" : "#{s3url}#{test_id}/networklogs",
        'instrumentation_log' => "#{s3url}#{test_id}/instrumentationlogs",
        'video' => video_url,
        'start_time' => Time.at(start_time.to_i)
      })

      test_object.merge!(get_screenshots_hash(ss_url, test_id, ss_string, @summary_data["screenshots"], 'v1'))
    end

    if result[:status] == "TIMEDOUT"
      cls_params = { genre: "app_automate", automate_session_id: @summary_data_v2['session_id'], user_id: nil }
      push_to_cls(cls_params, "test_idle_timeout", "", { "test_id" => test_id, "test_summary" => @summary_data_v2 })
    end

    @summary_data["test_status"][result[:status]] += 1
    @summary_data["test_status"]["RUNNING"] -= 1

    # Session Summary File V2
    test_object_v2 = begin
      @summary_data_v2['classes'][classname_v2]['tests'][testname]
    rescue StandardError
      nil
    end
    video_url = @summary_data_v2["video"].to_s == 'false' ? "" : (video_url.split('#')[1].split('=')[1]).to_s

    new_state = case result[:status].downcase
                when "success"
                  "passed"
                when "ignored"
                  "skipped"
                else
                  result[:status].downcase
                end

    unless test_object_v2.nil?
      test_object_v2.merge!({
        'status' => new_state,
        'duration' => result[:duration],
        'video' => @summary_data_v2["video"].to_s == "false" ? "" : video_url,
        'start_time' => Time.at(start_time.to_i)
      })
      test_object_v2.merge!(get_screenshots_hash(ss_url, test_object_v2['test_id'], ss_string,
                                                 @summary_data_v2["screenshots"], 'v2'))
    end

    update_tests_status(classname_v2, testname, new_state, "running") unless classname_v2.nil?

    update_summary_file
  end

  def update_timeout_tests(testsuite_file, merged_junit_report_metadata_file)
    @summary_data = JSON.parse(File.read(@summary_file))
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))
    common_package_name = @summary_data_v2["common_package_name"]
    is_duplicate = @summary_data_v2["is_duplicate"]

    testlist = File.readlines(testsuite_file).each { |e| e.delete!("\n") }
    testlist.each do |testname_with_classname|
      classname = testname_with_classname.split("#")[0]
      testname = testname_with_classname.split("#")[1]
      test_object = begin
        @summary_data['test_details'][classname][testname]
      rescue StandardError
        nil
      end

      if !test_object.nil? && (test_object['status'] == DEFAULT_TEST_STATUS)
        add_test_to_junit_report(testname_with_classname, "SKIPPED", 0, 0, nil,
                                 merged_junit_report_metadata_file, nil)
        test_object['status'] = "TIMEDOUT"

        @summary_data['test_status']['TIMEDOUT'] += 1
        @summary_data['test_status']['QUEUED'] -= 1
      end

      classname = get_class_name(testname_with_classname, common_package_name, is_duplicate)
      test_object_v2 = begin
        @summary_data_v2['classes'][classname]['tests'][testname]
      rescue StandardError
        nil
      end

      if !test_object_v2.nil? && (test_object_v2['status'] == DEFAULT_TEST_STATUS.downcase)
        test_object_v2['status'] = "skipped"
        update_tests_status(classname, testname, 'skipped', 'queued')
      end
    end
    update_summary_file
  end

  #Only for summary v2
  def get_build_pusher_message(outfile, testsuite_file)
    data = JSON.parse(File.read(@summary_file_v2))
    common_package_name = data["common_package_name"]
    is_duplicate = @summary_data_v2["is_duplicate"]

    testlist = File.readlines(testsuite_file).each { |e| e.delete!("\n") }
    messages = []
    combined_test_list = []
    message = {
      'build_id' => data['build_id'],
      'device' => data['device'],
      'session_id' => data['session_id'],
      'testlist' => []
    }
    testlist.each do |testname|
      test_id = "#{data['session_id']}#{get_test_id(testname)}"
      classname = get_class_name(testname, common_package_name, is_duplicate)
      testname = testname.split("#")[1]
      combined_test_list << {
        'test_id' => test_id,
        'name' => testname,
        'classname' => classname,
        'status' => DEFAULT_TEST_STATUS.downcase
      }
    end
    chunked_test_list = combined_test_list.each_slice(30).to_a
    chunked_test_list.each do |test_list|
      message['testlist'] = test_list
      messages << message.to_json
    end
    File.open(outfile, "w+") { |f| f.write(messages.to_json) if defined? messages }
  end

  #Only for summary v2
  def get_test_pusher_message(testname, outfile)
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))
    common_package_name = @summary_data_v2["common_package_name"]
    is_duplicate = @summary_data_v2["is_duplicate"]
    classname = get_class_name(testname, common_package_name, is_duplicate)
    testname = testname.split("#")[1]
    test_object_v2 = begin
      @summary_data_v2['classes'][classname]['tests'][testname]
    rescue StandardError
      nil
    end
    test_screenshots = (test_object_v2['screenshots'].nil? ? "" : test_object_v2['screenshots'])
    unless test_object_v2.nil?
      message = {
        'build_id' => @summary_data_v2['build_id'],
        'session_id' => @summary_data_v2['session_id'],
        'test_id' => test_object_v2['test_id'],
        'device' => @summary_data_v2['device'],
        'classname' => classname,
        'testname' => testname,
        'name' => "#{classname}.#{testname}",
        'status' => test_object_v2['status'],
        'duration' => test_object_v2['duration'],
        'video' => test_object_v2['video'].to_s,
        'screenshots' => test_screenshots
      }
    end
    File.open(outfile, "w+") { |f| f.write(message.to_json) if defined? message }
  end

  def get_offset_index_for_timestamp(offset_timestamp_array, timestamp, start, stop)

    start += 1 while (start + 1 <= stop) && (timestamp > offset_timestamp_array[start + 1])
    start
  end

  def is_time_missing_in_video?(timestamp, previous_video_end_time, next_available_video_starts_at)
    timestamp >= previous_video_end_time && timestamp <= next_available_video_starts_at
  end

  def update_video_tag

    summary_v1_details = @summary_data["test_details"]
    classes_data = @summary_data_v2["classes"] || []
    total_classes = classes_data.size
    last_element_start_tag = nil
    classes_data.reverse_each.with_index do |data, class_index|
      tests = data["tests"] || []
      total_tests = tests.size
      v1_class_details = summary_v1_details[summary_v1_details.keys[total_classes - class_index - 1]]
      tests.reverse_each.with_index do |test, test_index|
        v1_test_details = v1_class_details[v1_class_details.keys[total_tests - test_index - 1]]
        video_time = test["video"]
        video_start, video_end = video_time.split(",").map(&:to_i)
        next if video_start.nil? || video_end.nil?

        if last_element_start_tag.nil?
          video_end += MEDIA_PROJECTION_LAST_TEST_CONSTANT_INCREASE
        else
          video_end = last_element_start_tag
        end
        last_element_start_tag = video_start
        video_time_with_offset = "#{video_start},#{video_end}"
        test["video"] = video_time_with_offset
        v1_video_url, v1_video_time = v1_test_details["video"].split("#t=")
        v1_test_details["video"] = "#{v1_video_url}#t=#{video_time_with_offset}"
      end
    end
    update_summary_file

  rescue StandardError => e
    BrowserStack.logger.error "Some error happened while updating video tags, error: #{e.message}"
    zombie_push('android', 'video-tag-update-failed', e.message, '', '', @summary_data_v2['device'],
                @summary_data_v2['session_id'])

  end

  def is_video_enabled?
    @summary_data["video"].to_s == 'true' || @summary_data_v2["video"].to_s == 'true'
  end

  def update_video_tag_in_summary_files
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))
    @summary_data = JSON.parse(File.read(@summary_file))
    offset_data = begin
      JSON.parse(File.read("/tmp/video_offset_files/offset_file_session_"\
                               "#{@summary_data_v2['session_id']}"))
    rescue StandardError
      {}
    end

    is_mediaprojection_enabled = (@summary_data["use_rtc_app"] == "v2" || @summary_data_v2["use_rtc_app"] == "v2")
    update_video_tag if is_mediaprojection_enabled && is_video_enabled?

    return if offset_data.empty?

    offset_timestamp_array = offset_data.keys.map(&:to_i)
    offset_index = 0
    stop = offset_timestamp_array.length - 1
    first_offset_time = offset_timestamp_array[0]

    summary_v1_details = @summary_data["test_details"]

    classes_data = @summary_data_v2["classes"] || []
    classes_data.each_with_index do |data, class_index|
      tests = data["tests"] || []
      v1_class_details = summary_v1_details[summary_v1_details.keys[class_index]]
      tests.each_with_index do |test, test_index|
        v1_test_details = v1_class_details[v1_class_details.keys[test_index]]
        video_time = test["video"]
        video_start, video_end = video_time.split(",").map(&:to_i)
        test_start_timestamp = Time.parse(test["start_time"]).to_i

        test_end_timestamp = test_start_timestamp + (video_end - video_start)
        if test_start_timestamp >= first_offset_time
          should_apply_start_offset = true
          test_start_time_offset_index = get_offset_index_for_timestamp(offset_timestamp_array, test_start_timestamp,
                                                                        offset_index, stop)

          #for test start time
          #offset_timestamp_array contains video process end times
          previous_video_end_time = offset_timestamp_array[test_start_time_offset_index]
          video_loss_time = offset_data[offset_timestamp_array[test_start_time_offset_index].to_s].to_i
          #adjusting since we have cumulative video loss in offset_data
          if test_start_time_offset_index > 0
            video_loss_time -= offset_data[
              offset_timestamp_array[test_start_time_offset_index - 1].to_s
            ].to_i
          end
          next_available_video_starts_at  = previous_video_end_time + video_loss_time

          #check if start time is missing in video
          is_test_start_time_missing_in_video = is_time_missing_in_video?(test_start_timestamp, previous_video_end_time,
                                                                          next_available_video_starts_at)
          if is_test_start_time_missing_in_video
            #actual test start time is missing in video
            #so advancing video start till next_available_video_starts_at
            jump_in_start_time = next_available_video_starts_at - test_start_timestamp
            video_start += jump_in_start_time
          end
        end

        if test_end_timestamp >= first_offset_time
          should_apply_end_offset = true
          test_end_time_offset_index = get_offset_index_for_timestamp(offset_timestamp_array, test_end_timestamp,
                                                                      offset_index, stop)
          offset_index = test_end_time_offset_index
          #for test end time
          previous_video_end_time = offset_timestamp_array[test_end_time_offset_index]
          video_loss_time = offset_data[offset_timestamp_array[test_end_time_offset_index].to_s].to_i
          if test_end_time_offset_index > 0
            video_loss_time -= offset_data[
              offset_timestamp_array[test_end_time_offset_index - 1].to_s
            ].to_i
          end
          next_available_video_starts_at  = previous_video_end_time + video_loss_time

          #check if end time is missing in video
          is_test_end_time_missing_in_video = is_time_missing_in_video?(test_end_timestamp, previous_video_end_time,
                                                                        next_available_video_starts_at)
          if is_test_end_time_missing_in_video
            #actual test end time is missing in video
            #so advancing video end till next_available_video_starts_at
            jump_in_end_time = next_available_video_starts_at - test_end_timestamp
            video_end += jump_in_end_time
          end
        end

        if is_test_start_time_missing_in_video && is_test_end_time_missing_in_video &&
           (test_start_time_offset_index == test_end_time_offset_index)
          video_start = 0
          video_end = 0
        else
          if should_apply_start_offset
            video_start -= offset_data[
              offset_timestamp_array[test_start_time_offset_index].to_s
            ].to_i
          end
          if should_apply_end_offset
            video_end -= offset_data[
              offset_timestamp_array[test_end_time_offset_index].to_s
            ].to_i
          end
        end

        video_time_with_offset = "#{video_start},#{video_end}"
        test["video"] = video_time_with_offset
        v1_video_url, v1_video_time = v1_test_details["video"].split("#t=")
        v1_test_details["video"] = "#{v1_video_url}#t=#{video_time_with_offset}"
      end
    end
    update_summary_file
  rescue StandardError => e
    puts "error in update_video_tag_in_summary_files message #{e.message} backtrace: #{e.backtrace.join('\n')}"
  end

  def update_session_details(duration)
    @summary_data["duration"] = duration

    # Session Summary V2
    @summary_data_v2["duration"] = duration
    flatten_session_summary_v2

    update_summary_file
    zombie_push(
      'android', "total-tests-duration", '', '', duration,
      @summary_data_v2['device'], @summary_data_v2['session_id'], ''
    )
  end

  def flatten_session_summary_v2
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))

    # Remove unneccessary keys
    # We are deleting these keys because we don't want this keys to be present in our summary file
    UNNECESSARY_KEYS.each do |key|
      @summary_data_v2.delete(key)
    end

    # Class level flattening
    @summary_data_v2["classes"] = @summary_data_v2["classes"].values

    # Test Level flattening
    @summary_data_v2["classes"].each do |class_object|
      class_object['tests'] = class_object['tests'].values
    end
  end

  # Renders the xml report and saves to disk
  # Refer to #add_test_to_junit_report
  #
  # @param merged_junit_report_metadata_file [String] Path to the metadata file which is used for persistence
  # @param junit_report_generator [String] Path to the xml file which will be rendered.
  def generate_final_xml_report(merged_junit_report_metadata_file, junit_xml_report_file, device_name, device_version)
    junit_report_generator = JUnitReporter.new(merged_junit_report_metadata_file, @summary_data_v2['session_id'])
    junit_report_generator.render_xml(junit_xml_report_file, device_name, device_version,
                                      @summary_data_v2['session_id'])
  end

  def update_summary_file
    safe_file_write(@summary_file, @summary_data['device'], @summary_data['session_id'], __method__.to_s) do |f|
      f.write(@summary_data.to_json)
    end

    # Session Summary File V2
    safe_file_write(@summary_file_v2, @summary_data_v2['device'], @summary_data_v2['session_id'],
                    __method__.to_s) do |f|
      f.write(@summary_data_v2.to_json)
    end
  end

  # This kills the current running test and timeouts remaining tests
  def force_stop_session(device_id, test_framework, session_id)
    # Create timeout file which is checked before running every test, to timeout session
    FileUtils.touch "/tmp/#{test_framework}_timeout_#{device_id}_#{session_id}"
    estest = AppAutomateFrameworks::EspressoTimeoutManager.new(
      device_id,
      nil,
      logger: BrowserStack.logger,
      logger_params: BrowserStack.logger.params
    )
    estest.kill_idle_process
  rescue StandardError => e
    BrowserStack.logger.error("Error message: #{e.message} Backtrace: #{e.backtrace}")
    raise e
  end

  def update_tests_status_to_running(testname_with_classname_combined)
    @summary_data = JSON.parse(File.read(@summary_file))
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))
    common_package_name = @summary_data_v2["common_package_name"]
    is_duplicate = @summary_data_v2["is_duplicate"]

    classname = testname_with_classname_combined.split("#")[0]
    testname = testname_with_classname_combined.split("#")[1]
    test_object = begin
      @summary_data['test_details'][classname][testname]
    rescue StandardError
      nil
    end

    if !test_object.nil? && (test_object['status'] == DEFAULT_TEST_STATUS)
      test_object['status'] = "RUNNING"
      @summary_data['test_status']['RUNNING'] += 1
      @summary_data['test_status']['QUEUED'] -= 1
    end

    classname = get_class_name(testname_with_classname_combined, common_package_name, is_duplicate)
    test_object_v2 = begin
      @summary_data_v2['classes'][classname]['tests'][testname]
    rescue StandardError
      nil
    end

    if !test_object_v2.nil? && (test_object_v2['status'] == DEFAULT_TEST_STATUS.downcase)
      test_object_v2['status'] = "running"
      update_tests_status(classname, testname, 'running', 'queued')
    end
    update_summary_file
  end

  #Fetch all unique classnames from instrumentation file
  def self.get_classnames_from_file(input_file)
    result = []

    if input_file.empty?
      puts "Path of inputFile missing"
      return result
    end

    regex = Regexp.new("INSTRUMENTATION_STATUS: class=")

    begin
      File.open(input_file) do |f|
        f.each do |l|
          next unless regex =~ l

          line = l.gsub("INSTRUMENTATION_STATUS: class=", "")
          line = line.strip
          result << line
        end
      end
    rescue StandardError => e
      puts "Exception in get_classnames_from_file #{e}"
      return []
    end

    result.uniq
  end

  #Check if we have duplicated classes present in the list
  # e.g for "com.example.foo", "com.examp.foo" and "com.example.bar" then output will be true
  def self.check_duplicate_classes(list)
    return false unless list.instance_of?(Array) && list.length > 1

    class_names = []
    list.each do |classname|
      classname = classname.split(".").last
      return true if class_names.include? classname

      class_names << classname
    end

    false
  end

  #This method is used to find common substring tokens among classes.
  # e.g for "com.example.foo" and "com.example.bar" output will be "com.example"
  def self.get_common_package_name(list)
    return "" unless list.instance_of?(Array) && list.length > 1

    token_length = 0
    result = 0
    (0...list.first.length).each do |k|
      all_matched = true
      character = list.first[k]
      all_matched = list.all? { |str| character == str[k] }
      break unless all_matched

      result += 1
      token_length = result if character == '.'
    end

    list.first.slice(0, token_length)
  end

  # If common_package_name is empty classname will be last part of dot seperated string so need for sanitize_name
  # else classname would be last part of split from common_package_name
  # e.g "com.example.foo" is passed with common_package_name empty then ouput will be foo
  # e.g "com.example.foo.bar" is passed with common_package_name com.example. then output will be foo.bar
  # If there is are duplicate classes and no common package name among them then we use fully qualified name
  def get_class_name(classname_testname, common_package_name, is_duplicate)
    classname = classname_testname.split("#")[0]

    return classname if common_package_name && common_package_name.empty? && is_duplicate

    begin
      !common_package_name.empty? ? classname.split(common_package_name).last : classname.split('.').last
    rescue StandardError
      classname
    end

  end

  def self.get_only_class_name(common_package_name, is_duplicate, last_class)
    only_class_name = nil

    #This one condition where even though there were duplicate classes but there was no common substring among them
    if common_package_name.empty? && is_duplicate == true
      last_class
    else
      # If common_package_name is empty and is_duplicate is false that means there are no duplicate classes and we can
      # fetch only classname
      # else we split on the basis of common_package_name from fully qualified class name
      begin
        !common_package_name.empty? ? last_class.split(common_package_name).last : last_class.split('.').last
      rescue StandardError
        ''
      end
    end
  end

  # This method is used for generating the espresso summary file when singleRunnerInvocation is enabled
  def generate_summary_file( # rubocop:todo Metrics/ParameterLists, Metrics/MethodLength
    instrumentation_file, callback_file, test_suite_file, base_s3_url, logs_url, video_url,
    session_duration, device_id, merged_junit_report_metadata_file, junit_xml_report_file,
    device_name, device_version, device_model, test_framework, video_start_time, video_end_time,
    session_id, logs_stability_file
  )
    callback_file_data = JSON.parse(File.read(callback_file))
    data = {
      "device_name" => device_name,
      "device_version" => device_version,
      "session_duration" => session_duration
    }.to_json
    if error_in_instrumentation_file?(
      instrumentation_file, device_id, callback_file_data, device_model, data, test_framework
    )
      File.open(callback_file, "w+") do |f|
        f.write(callback_file_data.to_json)
      end
      return
    end

    parsed_tests = {}
    log_splitting_enabled = is_true?(@summary_data["splitLogs"])
    if log_splitting_enabled
      begin
        BrowserStack.logger.info("Got summary data v2: #{@summary_data_v2}")
        BrowserStack.logger.info("Got summary data v1: #{@summary_data}")
        log_split_manager = LogSplitManager.new(
          device_id, session_id, test_framework,
          base_s3_url: base_s3_url,
          build_id: @summary_data_v2["build_id"],
          video_enabled: is_true?(@summary_data_v2["video"]),
          network_enabled: is_true?(@summary_data["networkLogs"]),
          device_enabled: is_true?(@summary_data["deviceLogs"]),
          time_components: {
            video: {
              start_time: video_start_time,
              stop_time: video_end_time
            }
          },
          callback_file: callback_file,
          callback_data: callback_file_data,
          logs_stability_file: logs_stability_file,
          total_tests_or_session: 1,
          summary_data_v2: @summary_data_v2,
          summary_data: @summary_data
        )
        log_split_manager.process_logs
        parsed_tests = log_split_manager.tests
        BrowserStack.logger.info("LogSplitting Errors: #{log_split_manager.log_split_errors.to_json} \n \
                                  log splitting failures: #{log_split_manager.log_split_failures.to_json}")
        log_split_zombie_data = { "split_issues" => log_split_manager.log_split_errors }
        zombie_push(
          ANDROID,
          "frameworks-logsplit-info",
          nil,
          ANDROID,
          log_split_zombie_data,
          device_id,
          session_id
        )
      rescue StandardError => e
        parsed_tests = {}
        BrowserStack.logger.info("Exception in parsing logs for tests: #{e.message} #{e.backtrace} ")
      end
    end

    BrowserStack.logger.info("Parsed tests: #{parsed_tests}")

    instrumentation_logs_parser = InstrumentationResultParser.new(
      instrumentation_file, @summary_file, base_s3_url,
      logs_url, video_url, session_duration, device_id,
      merged_junit_report_metadata_file, callback_file_data, test_framework, parsed_tests: parsed_tests
    )

    (
      result, result_v2, flat_test_list,
      instrumentation_code, callback_file_data) = instrumentation_logs_parser.parse

    source = __method__.to_s

    if flat_test_list.empty?
      zombie_push(
        'android', "#{test_framework}-testsuite-parse-empty", '', device_model, data,
        device_id, @summary_data_v2['session_id'], ''
      )
      callback_file_data["error_reason"] = "testsuite-parse-empty"
      @summary_data["error_reason"] = "testsuite-parse-empty"

      safe_file_write(callback_file, device_id, @summary_data_v2['session_id'], source) do |f|
        f.write(callback_file_data.to_json)
      end

      return
    end

    # at this point the entire instrumentation logs is parsed
    safe_file_write(test_suite_file, device_id, @summary_data_v2['session_id'], source) do |f|
      f.puts(flat_test_list)
    end

    if instrumentation_code.nil?
      # if instrumentation logs doesn't have the line "INSTRUMENTATION_CODE: "
      # then assuming that instrumentation logs are not complete
      handle_incomplete_instrumentation_logs(
        @summary_file, callback_file, result_v2, result,
        merged_junit_report_metadata_file, video_url, session_duration, device_id, test_framework
      )

      if is_device_off_adb?(device_id, @summary_data_v2['session_id'])
        callback_file_data["error_reason"] = "device-off-adb"
        @summary_data["error_reason"] = "device-off-adb"
      end
    end

    write_to_file(callback_file, callback_file_data) if callback_file_data && callback_file_data["error_reason"]

    total_tests_count = get_count_for_each_status(result_v2)

    result.each { |class_name, class_obj| result.delete(class_name) if class_obj.empty? }
    @summary_data['test_count'] = flat_test_list.count
    @summary_data['test_status'] = {}
    %w[SUCCESS FAILED IGNORED TIMEDOUT RUNNING QUEUED ERROR].each do |status|
      @summary_data['test_status'].merge!({ status => total_tests_count[status] })
    end
    @summary_data['test_details'] = result

    # Session Summary File V2
    result_v2.each { |class_name, class_obj| result_v2.delete(class_name) if class_obj[:tests].empty? }
    @summary_data_v2['test_summary'] = {
      "total" => flat_test_list.count,
      'passed' => total_tests_count["SUCCESS"],
      'failed' => total_tests_count["FAILED"],
      'skipped' => total_tests_count["IGNORED"],
      'timedout' => total_tests_count["TIMEDOUT"],
      'error' => total_tests_count["ERROR"],
      'running' => total_tests_count["RUNNING"],
      'queued' => total_tests_count["QUEUED"]
    }
    @summary_data_v2['classes'] = result_v2

    update_summary_file
    update_session_details(session_duration)
    generate_final_xml_report(merged_junit_report_metadata_file, junit_xml_report_file, device_name, device_version)

    BrowserStack.logger.info("generate_summary_file finished")
  end

  def error_in_instrumentation_file?(
    instrumentation_file, device_id, callback_file_data, device_model, data, test_framework
  )
    error_in_instrumentation_file = false
    if !File.exist?(instrumentation_file) || File.zero?(instrumentation_file)
      zombie_push(
        'android',
        "#{test_framework}-testsuite-parse-empty",
        'instrumentation file does not exist or is empty',
        device_model,
        data,
        device_id,
        @summary_data_v2['session_id'],
        ''
      )
      callback_file_data["error_reason"] = "testsuite-parse-empty"
      @summary_data["error_reason"] = "testsuite-parse-empty"
      return true
    end

    # Excluding values like "Process crashed" in case of singleRunnerInvocation because it leads to false positives
    # as these values can also be returned as the test execution result for an espresso test
    # https://browserstack.atlassian.net/browse/AA-3793?focusedCommentId=151556
    pattern_to_grep = /#{ERROR_KEYS.reject { |k| k == "user-testsuite-parse-failed" }.values.join("|")}/
    check_for_testsuite_parse_failure(instrumentation_file, callback_file_data, device_model,
                                      device_id, data, pattern_to_grep, test_framework)
  end

  def handle_incomplete_instrumentation_logs(
    summary_file, callback_file, result_v2, result,
    merged_junit_report_metadata_file, video_url, session_duration, device_id, test_framework
  )
    callback_file_data = JSON.parse(File.read(callback_file))
    if callback_file_data["error_reason"].to_s.strip.empty? # Don't overwrite the error_reason if it's already present
      `sed -i -e 's/"error_reason": ""/"error_reason":"instrumentation-logs-incomplete"/' "#{callback_file}"`
      `sed -i -e 's/"error_reason":""/"error_reason":"instrumentation-logs-incomplete"/' "#{summary_file}"`
      # update all the tests with status RUNNING to ERROR as exit_code of adb instrument command was 0
      # but still the instrumentation logs are incomplete
      mark_running_tests(result_v2, result, "ERROR", merged_junit_report_metadata_file,
                         "#{video_url}#t=0,#{session_duration}")
    else
      # update all the tests with status RUNNING to TIMEDOUT
      mark_running_tests(result_v2, result, "TIMEDOUT", merged_junit_report_metadata_file,
                         "#{video_url}#t=0,#{session_duration}")
    end
    zombie_push('android', "#{test_framework}-instrumentation-logs-incomplete", '', '', '',
                device_id, @summary_data_v2['session_id'], '')
  end

  def get_count_for_each_status(classes_v2)
    total_tests_count = {
      "QUEUED" => 0,
      "RUNNING" => 0,
      "SUCCESS" => 0,
      "FAILED" => 0,
      "IGNORED" => 0,
      "TIMEDOUT" => 0,
      "ERROR" => 0
    }
    classes_v2.each_value do |class_details|
      total_tests_count["QUEUED"] += class_details[:tests_summary]['queued']
      total_tests_count["RUNNING"] += class_details[:tests_summary]['running']
      total_tests_count["SUCCESS"] += class_details[:tests_summary]['passed']
      total_tests_count["FAILED"] += class_details[:tests_summary]['failed']
      total_tests_count["IGNORED"] += class_details[:tests_summary]['skipped']
      total_tests_count["TIMEDOUT"] += class_details[:tests_summary]['timedout']
      total_tests_count["ERROR"] += class_details[:tests_summary]['error']
    end
    total_tests_count
  end

  # Even after complete parsing of the instrumentation logs
  # if some tests are in running state then mark them as timedout or error in case of BStack issues
  def mark_running_tests(classes_v2, classes_v1, new_status, merged_junit_report_metadata_file, video_url)
    classes_v2.each do |class_name, class_details|
      next if class_details[:tests_summary]['running'] == 0

      class_details[:tests].each do |test_name, test_details|
        next if test_details[:status] != "running"

        classes_v2[class_name][:tests][test_name][:status] = new_status.downcase
        class_details[:tests_summary]['running'] -= 1
        class_details[:tests_summary][new_status.downcase] += 1
      end
    end

    classes_v1.each do |class_name, class_details|
      class_details.each do |test_name, test_details|
        next unless test_details[:status] == "RUNNING"

        test_details[:status] = new_status
        add_test_to_junit_report("#{class_name}##{test_name}", new_status, 0, 0,
                                 nil, merged_junit_report_metadata_file, video_url)
      end
    end
  end

  def check_instrumentation_error(instrumentation_file, callback_file, device_model,
                                  device_id, device_name, device_version, test_framework)
    callback_file_data = JSON.parse(File.read(callback_file))

    data = {
      "device_name" => device_name,
      "device_version" => device_version
    }.to_json

    check_for_testsuite_parse_failure(
      instrumentation_file, callback_file_data, device_model, device_id, data, nil, test_framework
    )

    safe_file_write(@summary_file, device_id, @summary_data['session_id'], __method__.to_s) do |f|
      f.write(@summary_data.to_json)
    end

    safe_file_write(callback_file, device_id, @summary_data_v2['session_id'], __method__.to_s) do |f|
      f.write(callback_file_data.to_json)
    end
  end

  def check_for_testsuite_parse_failure(
    instrumentation_file, callback_file_data, device_model,
    device_id, data, pattern_string = nil, test_framework='espresso'
  )
    pattern = []
    search_space = ERROR_KEYS.values + [JUNIT_INSTRUMENTATION_FAILURE]
    pattern_string = /#{search_space.join("|")}/ if pattern_string.nil?

    File.open(instrumentation_file) do |file|
      pattern = file.grep(pattern_string)
    end
    return false if pattern.empty?

    ERROR_KEYS.each do |error_key, error_pattern|
      sanitised_error_pattern = error_pattern.gsub("\\", "")
      next unless pattern[0].include?(sanitised_error_pattern)

      zombie_push("android", "#{test_framework}-testsuite-parse-failed", sanitised_error_pattern, device_model,
                  data, device_id, @summary_data_v2['session_id'], '')

      # Presence of JUnit Instru Failure + 0 Tests Found is a scenario for no tests found due to auto sharding
      if pattern.any? { |p| p.include?(JUNIT_INSTRUMENTATION_FAILURE) } && error_key == "testsuite-no-tests-found"
        error_key = "testsuite-no-tests-with-junit-failure"
      end

      callback_file_data["error_reason"] = error_key
      @summary_data["error_reason"] = error_key
      return true
    end

    false
  end

  # Iterate over the summary file and update the test status to error.
  # Complexity: O(n). Iterating each test only once.
  def handle_dangling_tests
    session_id = @summary_data_v2['session_id']
    BrowserStack.logger.info "[#{session_id}] Handling left over queued and running tests"

    queued_tests = 0
    running_tests = 0

    @summary_data_v2["classes"].each do |class_name, class_obj|
      next if class_obj.nil? || class_obj["tests"].nil? || class_obj["tests_summary"].nil?
      next if class_obj["tests_summary"]["running"] == 0 && class_obj["tests_summary"]["queued"] == 0

      class_obj["tests"].each do |test_name, test_obj|
        test_status = test_obj["status"]
        next unless ["queued", "running"].include?(test_status)

        queued_tests += 1 if test_status == "queued"
        running_tests += 1 if test_status == "running"

        test_obj["status"] = "error"
        update_tests_status(class_name, test_name, "error", test_status)
      end
    end

    update_summary_file

    zombie_push('android', 'dangling-tests-handling', "", "",
                "Updated status for #{queued_tests} queued tests and #{running_tests} running tests",
                "", session_id)
  rescue StandardError => e
    BrowserStack.logger.error "[#{session_id}] Unable to update dangling tests statuses: #{e.message}"
  end
end
