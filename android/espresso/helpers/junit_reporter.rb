require 'nokogiri' # Used for rendering xml
require 'time' # Used for parsing time from the `adb logcat` output
require 'json'

BS_HOME = "/usr/local/.browserstack".freeze
DIR_HOME = "#{BS_HOME}/mobile".freeze
require "#{DIR_HOME}/common/push_to_zombie.rb"

# A generic class which can be used for generating JUnit XML reports.
# This can persist state across different runs. Therefore, it is not required that
# all the tests execute in the lifetime of a single process.
class JUnitReporter
  # For testing purposes
  attr_reader :datastore

  # Ensure that <merged_junit_report_metadata_file> is same for all the subsequent
  # JUnitReporter objects created for a particular session.
  # It is meant to share state across different runs. This is by storing <datastore> in it as JSON.
  #
  # @param merged_junit_report_metadata_file [String] Path to a file which will be used for persisting state
  # @param session_id [String]
  #
  def initialize(merged_junit_report_metadata_file, session_id)
    @merged_junit_report_metadata_file = merged_junit_report_metadata_file
    @session_id = session_id || ""
    @datastore = initialize_datastore

    log "Initialized with report path #{@merged_junit_report_metadata_file}"
  end

  # Add a test case result to the datastore
  #
  # @param class_name [String] The name of the class this test case belongs to,
  #   example: com.sample.browserstack.samplecalculator.EnsureInputTests
  # @param test_name [String] The name of the test case, example: ensureSingleInputIsHandled
  # @param result [String] one of ["passed", "failed", "skipped"]
  # @param started_at [Time] the timestamp when the test execution started
  # @param completed_at [Time] the timestamp when the test execution finished
  # @param failure_stack_trace [String] stacktrace for failure to be added in the report
  def add_result(class_name, test_name, result, started_at, completed_at, video_url, test_id, failure_stack_trace = nil)
    testcase = {
      classname: class_name,
      name: test_name,
      started_at: started_at,
      completed_at: completed_at,
      duration: completed_at - started_at,
      result: result,
      failure_stack_trace: failure_stack_trace,
      video_url: video_url,
      test_id: test_id
    }
    @datastore << testcase
  end

  # Call this before exiting the process, to ensure that <datastore> is updated for
  # the next call to #initialize
  def save_state
    File.write(@merged_junit_report_metadata_file, Marshal.dump(@datastore).force_encoding("UTF-8"))
  rescue Exception => e
    zombie_push('android', 'aa-junit-exception', 'Unable to save state on disk', '',
                e.message + e.backtrace.join("\n"), '', @session_id, '')
    log "Unable to save state on disk, Session ID: #{@session_id}, "\
          "Exception: #{e.message} #{e.backtrace.join("\n")} Datastore: #{@datastore}"
  end

  # Generates the final JUnit XML report
  # #save_state
  # ENSURE THAT YOU CALL THIS AT THE END, I.E. ONCE ALL THE TESTS HAVE BEEN EXECUTED
  #
  # @param final_junit_report_file_path [String] Path to a xml file to write the report to.
  def render_xml(final_junit_report_file_path, device_name, device_version, session_id)
    testsuites = pre_process

    junit_report = Nokogiri::XML::Builder.new do |xml|
      xml.testsuites do
        testsuites.each_value do |testsuite|
          format_testsuite(xml, testsuite, device_name, device_version, session_id)
        end
      end
    end

    File.write(final_junit_report_file_path, junit_report.to_xml)
  rescue Exception => e
    zombie_push('android', 'aa-junit-exception', 'Unable to render the XML report', '',
                e.message + e.backtrace.join("\n"), '', session_id, '')
    log "Unable to render the XML report, Session ID: #{session_id}, "\
          "Exception: #{e.message} #{e.backtrace.join("\n")}  Datastore: #{@datastore}"
  end

  # Helper method for logging
  # @param message [String] Log message
  def log(message)
    puts "#{self.class} ID: #{object_id} message: #{message}"
  end

  private

  # Intialize the datastore
  #  If there is already a <merged_junit_report_metadata_file> file, use that as the seed.
  #
  # The datastore stores the in-memory representation of the JUnit report.
  # The actual XML report can be rendered by calling #render_xml
  #
  # @return [Array] an Array of test result object (created in #add_result)
  def initialize_datastore
    if File.exist?(@merged_junit_report_metadata_file) && File.size(@merged_junit_report_metadata_file) > 0
      Marshal.load(File.read(@merged_junit_report_metadata_file))
    else
      log "Initializing empty datastore"
      []
    end
  rescue Exception => e
    zombie_push('android', 'aa-junit-exception', 'Unable to initialize datastore properly', '',
                e.message + e.backtrace.join("\n"), '', @session_id, '')
    log "Unable to initialize datastore properly, Session ID: #{@session_id}, "\
          "Exception: #{e.message} #{e.backtrace.join("\n")} Datastore: #{@datastore}"
    []
  end

  #
  #
  # @param xml [Nokogiri::XML::Builder] The instance of the Nokogiri builder to
  # represent the xml format of the JUnit report.
  # @param testsuite [Hash] Is a single test suite, created in #pre_process
  def format_testsuite(xml, testsuite, device_name, device_version, session_id)
    xml.testsuite(name: testsuite[:name],
                  tests: testsuite[:tests],
                  failures: testsuite[:failures],
                  skipped: testsuite[:skipped],
                  timedout: testsuite[:timedout],
                  errors: testsuite[:errors],
                  time: testsuite[:time],
                  timestamp: testsuite[:timestamp]) do
      xml.properties do
        [
            xml.property(session_id: session_id),
            xml.property(devicename: device_name),
            xml.property(os: "Android"),
            xml.property(version: device_version)
        ]
      end

      testsuite[:testcases].each do |testcase|
        if testcase[:result] == :failed && !testcase[:failure_stack_trace].to_s.empty?
          xml.testcase(name: testcase[:name], classname: testcase[:classname], result: testcase[:result],
                       test_id: testcase[:test_id], time: testcase[:duration],
                       video_url: testcase[:video_url]) do
            xml.failure(testcase[:failure_stack_trace])
          end
        elsif testcase[:result] == :error && !testcase[:failure_stack_trace].to_s.empty?
          xml.testcase(name: testcase[:name], classname: testcase[:classname], result: testcase[:result],
                       test_id: testcase[:test_id], time: testcase[:duration],
                       video_url: testcase[:video_url]) do
            xml.error(testcase[:failure_stack_trace])
          end
        else
          xml.testcase(name: testcase[:name], classname: testcase[:classname], result: testcase[:result],
                       test_id: testcase[:test_id], time: testcase[:duration],
                       video_url: testcase[:video_url])
        end
      end
    end
  end

  # Iterates through the datastore(which has individual test cases), and reformats it to a format
  # which allows report generation. It groups the test cases on the basis of the class_name.
  # This way of grouping removes dependency on test execution ordering for report generation.
  #
  # @return [Hash] The keys are the testsuite names (i.e. class_names) and the values
  # are details regarding the test cases within that class.
  def pre_process
    testsuites = {}
    @datastore.each do |test_case|
      classname = test_case[:classname]
      if testsuites[classname].nil?
        # Initialize testsuite
        testsuites[classname] = {
          name: classname,
          tests: 0,
          failures: 0,
          skipped: 0,
          timedout: 0,
          errors: 0,
          time: 0,
          timestamp: test_case[:started_at],
          testcases: []
        }
      end

      testsuites[classname][:testcases] << test_case
      testsuites[classname][:tests] += 1

      case test_case[:result]
      when :failed
        testsuites[classname][:failures] += 1
      when :skipped
        testsuites[classname][:skipped] += 1
      when :timedout
        testsuites[classname][:timedout] += 1
      when :error
        testsuites[classname][:errors] += 1
      end

      if test_case[:completed_at].instance_of?(Time)
        testsuites[classname][:time] = test_case[:completed_at] - testsuites[classname][:timestamp]
      end
    end

    testsuites
  end
end
