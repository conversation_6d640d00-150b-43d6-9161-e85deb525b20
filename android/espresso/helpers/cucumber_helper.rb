require 'browserstack_logger'
require_relative '../../constants'
require_relative '../../../common/push_to_zombie'

class CucumberHelper

  def remove_scenario_outline_example_number(test_name)
    splitted_test_name = test_name.split(" ")
    scenario_outline_example_number = begin
      Integer(splitted_test_name[-1])
    rescue StandardError
      nil
    end
    return splitted_test_name[0..-2].join(" ") if scenario_outline_example_number.is_a?(Integer)

    test_name
  end

  def self.default_tests_summary
    {
      "total" => 0,
      'passed' => 0,
      'failed' => 0,
      'skipped' => 0,
      'timedout' => 0,
      'error' => 0,
      'running' => 0,
      'queued' => 0
    }
  end

  # We parse instrumentation logs and store result in two types of hash: @result and @result_v2
  # Both store the status of tests in different ways. For eg: a passed test in @result is marked as '
  # SUCCESS' whereas 'passed' in result_v2.
  # get_test_status_v1 is used to get the status of tests in @result format from @result_v2 format.
  def self.get_test_status_v1(test_status_v2)
    if test_status_v2.casecmp("passed").zero?
      "SUCCESS"
    elsif test_status_v2.casecmp("skipped").zero?
      "IGNORED"
    else
      test_status_v2.upcase
    end
  end

  def get_test_id(test_input)
    md5_id = Digest::MD5.hexdigest test_input
    md5_id[0..7]
  end

  def get_class_method_names(test_name)
    class_name = test_name[0, test_name.index("#")]
    method_name = test_name[test_name.index("#") + 1..]
    [class_name, method_name]
  end

  def self.get_scenario_outline_status(tests_summary)
    return "skipped" if tests_summary["total"].to_i.zero?

    # do not change the order of the below array
    ["running", "failed", "error", "timedout", "timeout"].select do |status| # timeout is there for older sessions
      return status unless tests_summary[status].to_i.zero?
    end

    # [v2] if all tests are skipped
    return "skipped" if tests_summary["total"].to_i == tests_summary["skipped"].to_i

    # do not change the order of the below array
    ["passed", "queued"].select do |status|
      # Excluding all skipped test cases
      return status if tests_summary[status].to_i == (tests_summary["total"].to_i - tests_summary["skipped"].to_i)
    end
  end

  # Update the status of the scenario outline example based on the status of other scenario outline examples
  def update_scenario_outline_example_status(test_name, scenario_outline_examples, result, result_v2)
    scenario_outline_examples_status = scenario_outline_examples.map do |test|
      class_name, method_name = get_class_method_names(test)
      result_v2[class_name][:tests][method_name][:status]
    end
    tests_status = CucumberHelper.default_tests_summary
    scenario_outline_examples_status.each do |test_status|
      tests_status[test_status] += 1
      tests_status['total'] += 1
    end
    class_name, method_name = get_class_method_names(test_name)
    old_state = result_v2[class_name][:tests][method_name][:status]
    result_v2[class_name][:tests_summary][old_state] -= 1
    new_state = CucumberHelper.get_scenario_outline_status(tests_status)
    result_v2[class_name][:tests_summary][new_state] += 1
    BrowserStack.logger.info "Updating status of Scenario Outline #{test_name} from #{old_state} to #{new_state}"
    result_v2[class_name][:tests][method_name][:status] = new_state
    result[class_name][method_name][:status] = CucumberHelper.get_test_status_v1(new_state)
    [result, result_v2]
  end

  # Identify scenario outlines in the flat_test_list and return the indices of examples
  # which needs to be updated or deleted
  def identify_scenario_outlines(flat_test_list, is_single_runner_invocation, result = {}, result_v2 = {})
    current_index = 0
    remove_scenario_outline_example_index_list = []
    update_scenario_outline_example_index_list = []
    scenario_outline_examples = []
    while current_index < flat_test_list.length
      temp_index = current_index + 1
      streak = false
      current_scenario = remove_scenario_outline_example_number(flat_test_list[current_index])
      while temp_index < flat_test_list.length
        next_scenario = remove_scenario_outline_example_number(flat_test_list[temp_index])
        if current_scenario == next_scenario
          remove_scenario_outline_example_index_list.push(temp_index)
          scenario_outline_examples.push(flat_test_list[temp_index])
          streak = true
        elsif streak
          update_scenario_outline_example_index_list.push(current_index)
          scenario_outline_examples.push(flat_test_list[current_index])
          if is_single_runner_invocation
            update_scenario_outline_example_status(flat_test_list[current_index], scenario_outline_examples, result,
                                                   result_v2)
          end
          scenario_outline_examples = []
          current_index = temp_index - 1
          break
        else
          break
        end
        temp_index += 1
      end
      if streak && (temp_index == (flat_test_list.length))
        update_scenario_outline_example_index_list.push(current_index)
        if is_single_runner_invocation
          update_scenario_outline_example_status(flat_test_list[current_index], scenario_outline_examples, result,
                                                 result_v2)
        end
        break
      end
      break if temp_index == flat_test_list.length     # if no match found, that means no tests remaining for parsing

      current_index += 1
    end
    [remove_scenario_outline_example_index_list, update_scenario_outline_example_index_list]
  end

  # This function will convert a scenario outline with multiple examples into one test
  # For eg:
  # Scenario Outline example 1
  # Scenario Outline example 2
  # Scenario Outline example 3
  # Scenario Outline example 4

  # Will be converted to
  # Scenario Outline
  def parse_scenario_outline(flat_test_list, result, result_v2, session_id, is_single_runner_invocation: false,
                             parsed_tests: {}, log_split_enabled: false)
    remove_scenario_outline_example_index_list, update_scenario_outline_example_index_list = identify_scenario_outlines(
      flat_test_list, is_single_runner_invocation, result, result_v2
    )
    # Test id is the combination of session id and md5 hash created from test name
    # Since we are changing the test name for cucumber, we need to update the test id in result and result_v2
    update_indices(flat_test_list, update_scenario_outline_example_index_list, result, result_v2, session_id,
                   is_single_runner_invocation, parsed_tests, log_split_enabled)
    delete_indices(flat_test_list, remove_scenario_outline_example_index_list, result, result_v2)
    [flat_test_list, result, result_v2]
  rescue StandardError => e
    BrowserStack.logger.info "Exception occured while parsing scenario outline"
    BrowserStack.logger.error("Error message: #{e.message} Backtrace: #{e.backtrace}")
    zombie_push('android', 'cucumber-parsing-scenario-outline-failed', e.message, '', e.backtrace.inspect, '',
                session_id, '')
  end

  # This method deletes the extra scenario outline examples from flat_test_list, result, and result_v2
  # For eg: flat_test_list = ["Scenario Outline example 1", "Scenario Outline example 2",
  #                           "Scenario Outline example 3", "Scenario Outline example 4"]
  # array will be [1, 2, 3]
  # So delete_indices would remove elements with index 1, 2, 3 from flat_test_list
  # So output of flat_test_list would be ["Scenario Outline example 1"] and similar changes in result and result_v2
  def delete_indices(flat_test_list, delete_index_list, result, result_v2)
    delete_index_list = delete_index_list.sort.reverse
    delete_index_list.each do |i|
      class_name, method_name = get_class_method_names(flat_test_list[i])
      result[class_name].delete(method_name)
      test_status = result_v2[class_name][:tests][method_name][:status]
      result_v2[class_name][:tests_summary]["total"] -= 1
      result_v2[class_name][:tests_summary][test_status] -= 1
      result_v2[class_name][:tests].delete(method_name)
      flat_test_list.delete_at(i)
    end
    [flat_test_list, result, result_v2]
  end

  # This method updates the first scenario outline example(delete_indices deletes other examples
  # so that only one example remains) such that example number is removed and test_id is updated accordingly
  # For eg: flat_test_list = ["Class#Scenario Outline 1 example 1", "Class#Scenario Outline 1 example 2",
  #                           "Class#Scenario Outline 2 example 1", "Class#Scenario Outline 2 example 2"]
  # would be updated to ["Class#Scenario Outline 1 example", "Class#Scenario Outline 1 example 2",
  #                     "Class#Scenario Outline 2 example", "Class#Scenario Outline 2 example 2"]
  # update_index_list = [0, 2] for this case
  def update_indices(flat_test_list, update_index_list, result, result_v2, session_id, is_single_runner_invocation,
                     parsed_tests, log_split_enabled)
    update_index_list.each do |update_index|
      # test_name = Class#Scenario Outline 1 example 1
      test_name = flat_test_list[update_index]
      # updated_test_name = Class#Scenario Outline 1 example
      updated_test_name = remove_scenario_outline_example_number(test_name)

      # class_name = Class, method_name = Scenario Outline 1 example 1
      class_name, method_name = get_class_method_names(test_name)
      # updated_method_name = Scenario Outline 1 example
      updated_method_name = remove_scenario_outline_example_number(method_name)

      old_test_id = "#{session_id}#{get_test_id(test_name)}"
      new_test_id = "#{session_id}#{get_test_id(updated_test_name)}"

      result[class_name][updated_method_name] = result[class_name][method_name]
      result[class_name][updated_method_name][:test_id] = new_test_id
      result[class_name][updated_method_name][:is_scenario_outline] = true
      if is_single_runner_invocation
        result[class_name][updated_method_name][:instrumentation_log] =
          result[class_name][method_name][:instrumentation_log].gsub(old_test_id, new_test_id)
        result[class_name][updated_method_name][:device_log] =
          result[class_name][method_name][:device_log].gsub(old_test_id, new_test_id)
      end
      result[class_name].delete(method_name)

      result_v2[class_name][:tests][updated_method_name] = result_v2[class_name][:tests][method_name]
      result_v2[class_name][:tests][updated_method_name][:name] = updated_method_name
      result_v2[class_name][:tests][updated_method_name][:test_id] = new_test_id
      result_v2[class_name][:tests][updated_method_name][:is_scenario_outline] = true
      result_v2[class_name][:tests].delete(method_name)

      add_log_boundaries(class_name, updated_method_name, result_v2, parsed_tests) if log_split_enabled
      flat_test_list[update_index] = updated_test_name
    end
    [flat_test_list, result, result_v2]
  end

  def add_log_boundaries(class_name, updated_method_name, result_v2, parsed_tests)
    BrowserStack.logger.info "add_log_boundaries: #{class_name} #{updated_method_name} "
    key = "#{class_name}.#{updated_method_name}"

    if parsed_tests.key?(key)
      BrowserStack.logger.info "add_log_boundaries: Adding for key #{key} "
      log_boundaries = {
        network_log: parsed_tests[key].network_log_boundary,
        device_log: parsed_tests[key].device_log_boundary
      }

      log_boundaries[:video] = parsed_tests[key].video_log_boundary if parsed_tests[key].video_boundary_valid?

      result_v2[class_name][:tests][updated_method_name] = result_v2[class_name][:tests][updated_method_name].merge(
        log_boundaries
      )

      BrowserStack.logger.info "add_log_boundaries: updated: #{result_v2[class_name][:tests][updated_method_name]} "
    end
  end
end
