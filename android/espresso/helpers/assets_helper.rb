require "fileutils"
require 'browserstack_logger'
require 'android_toolkit'
require_relative '../../lib/os_utils'
require_relative '../../constants'
require_relative '../../exit_file'
require_relative '../../scripts/upload_to_s3'
require_relative '../../../common/push_to_zombie'

module Espresso
  class AssetsHelper
    def self.run_from_bash
      raise StandardError, "Not enough arguments" if ARGV.size < 3

      function_to_call = ARGV[0].to_s.strip
      device_id = ARGV[1].to_s.strip
      session_id = ARGV[2].to_s.strip
      args = ARGV[3..]

      assets_helper = Espresso::AssetsHelper.new(device_id, session_id)
      assets_helper.send(function_to_call, *args)
    rescue StandardError => e
      ExitFile.write(e.message[0..200])
      raise e
    end

    def initialize(device, session_id)
      raise "Device cannot be empty" if device.nil? || device == ""

      logger = Logger.new($stdout)
      @device = device
      @session_id = session_id
      @adb = AndroidToolkit::ADB.new(udid: @device)
      AndroidToolkit::Log.logger = logger
      @error_kind = nil
      @error_message = nil
      @is_cucumber = false
      @is_session_data_enabled = false
      @max_file_count_limit = 100 # default file count limit 100
      @max_size_limit = 500 # default size limit 500 MB
    end

    def fetch_assets(*args)
      arg_mappings = [
        ->(v) { @is_cucumber = %w[true 1].include?(v.to_s.strip.downcase) },
        ->(v) { @is_session_data_enabled = %w[true 1].include?(v.to_s.strip.downcase) },
        ->(v) { @max_size_limit = v.to_i },
        ->(v) { @max_file_count_limit = v.to_i }
      ]

      args.each_with_index do |arg, index|
        break if index >= arg_mappings.size

        arg_mappings[index].call(arg)
      end

      # For cucumber sessions fetch reports
      @is_cucumber && fetch_reports_assets

      # For all sessions, if session data enabled fetch session assets
      @is_session_data_enabled && fetch_session_assets
    end

    def fetch_reports_assets
      assets_on_device = @adb.ls(BrowserStack::DEVICE_REPORTS_DIR)
      assets_on_machine = Dir.exist?(assets_dir)

      return if !assets_on_machine || assets_on_device.empty?

      pull_assets(BrowserStack::DEVICE_REPORTS_DIR)
    rescue AndroidToolkit::ADB::ExecutionError => e
      BrowserStack.logger.info "ADB Execution error: #{e.message}, backtrace: #{e.backtrace.inspect}"
      @error_kind = "assets-reports-adb-error"
      @error_message = e.message[0..200]
      instrument_metrics(push_to_eds: false)
    end

    def fetch_session_assets
      unless Dir.exist?(assets_dir)
        BrowserStack.logger.info "Assets directory does not exist. Skipping session assets fetch."
        return
      end

      device_session_assets_dir = BrowserStack::DEVICE_SESSION_ASSETS_DIR
      begin
        if device_directory_empty?(device_session_assets_dir)
          @error_kind = "device-session-assets-dir-empty"
          @error_message = "[DEVICE_SESSION_ASSETS_DIR_MISSING] No session_data "\
            "directory found at #{device_session_assets_dir} or the directory "\
            "is empty. Skipping session assets fetch."
          BrowserStack.logger.error @error_message
          create_error_file(@error_message)
          instrument_metrics(push_to_eds: false)
          return
        end

        size_mb = get_directory_size_mb(device_session_assets_dir)
        file_count = get_file_count(device_session_assets_dir)

        if size_mb > @max_size_limit ||
          file_count > @max_file_count_limit
          handle_limits_exceeded(size_mb, file_count)
          return
        end

        pull_assets(device_session_assets_dir)
        instrument_metrics(push_to_eds: true, feature_usage: {
          "fetch_session_data_assets": {
            "success": "true",
            "size": size_mb,
            "file_count": file_count,
            "fetch_time": @fetch_time
          }
        })
      rescue AndroidToolkit::ADB::ExecutionError => e
        BrowserStack.logger.info "ADB Execution error: #{e.message}, backtrace: #{e.backtrace.inspect}"
        @error_kind = "assets-session-data-adb-error"
        @error_message = e.message[0..200]
        instrument_metrics(push_to_eds: true, feature_usage: {
          "fetch_session_data_assets": {
            "success": "failed",
            "error_kind": @error_kind,
            "error_message": @error_message
          }
        })
      rescue StandardError => e
        BrowserStack.logger.info "Exception during session data fetch: #{e.message}, backtrace: #{e.backtrace.inspect}"
        @error_kind = "assets-session-data-error"
        @error_message = e.message[0..200]
        instrument_metrics(push_to_eds: true, feature_usage: {
          "fetch_session_data_assets": {
            "success": "failed",
            "error_kind": @error_kind,
            "error_message": @error_message
          }
        })
      end
    end

    def process_and_upload_assets(*args)
      if Dir.empty?(assets_dir)
        BrowserStack.logger.info "No assets found. Returning.."
        return
      end

      process_assets
      upload_assets(*args) if @error_kind.nil?
      instrument_metrics
    ensure
      FileUtils.rm_f(zipped_assets_path)
    end

    private

    def pull_assets(device_dir)
      start_time = Time.now.to_i
      @adb.pull(device_dir, assets_dir)
      @fetch_time = Time.now.to_i - start_time
    end

    def handle_limits_exceeded(size_mb, file_count)
      @error_kind = "assets-session-data-limit-exceeded"
      @error_message = "[SESSION_DATA_LIMIT_EXCEEDED] Original size: #{size_mb}mb of "\
        "session_data directory should be under #{@max_size_limit}mb and "\
        "original number of files: #{file_count} should not be above "\
        "#{@max_file_count_limit}."
      BrowserStack.logger.error @error_message
      create_error_file(@error_message)
      instrument_metrics(push_to_eds: false)
    end

    def create_error_file(message)
      error_file_path = File.join(assets_dir, "bstack_error.txt")
      File.write(error_file_path, message)
      BrowserStack.logger.info "Created bstack_error.txt with message: #{message}"
    rescue StandardError => e
      BrowserStack.logger.error "Failed to create error file: #{e.message}"
    end

    def get_directory_size_mb(dir)
      size_cmd = "du -sm #{dir} | cut -f1"
      @adb.shell(size_cmd, timeout: 30).strip.to_i
    end

    def get_file_count(dir)
      count_cmd = "find #{dir} -type f | wc -l"
      @adb.shell(count_cmd, timeout: 30).strip.to_i
    end

    def device_directory_empty?(dir)
      @adb.ls(dir).empty?
    end

    def devices_dir
      "/tmp/#{@device}"
    end

    def assets_dir
      "#{devices_dir}/assets_#{@session_id}"
    end

    def zipped_file_name
      "assets_#{@session_id}.zip"
    end

    def zipped_assets_path
      "#{devices_dir}/#{zipped_file_name}"
    end

    def file_size(file_path)

      File.size(file_path) / 1024.0
    rescue StandardError
      0

    end

    def process_assets
      zip_cmd = "cd #{devices_dir}; timeout -s KILL 120 zip -rq #{zipped_file_name} * 2>&1"

      output, status = OSUtils.execute(zip_cmd, true)
      BrowserStack.logger.info("[zip_assets] zip_cmd output: #{output} and exit_code: #{status}")

      if status != 0
        @error_kind = "assets-zip-error"
        @error_message = output[0..200]
      end
    end

    def upload_assets(*args)
      base_s3_url = args[0].to_s.strip
      device_logs_aws_key = args[1].to_s.strip
      device_logs_aws_secret = args[2].to_s.strip

      s3_url = "#{base_s3_url}/#{@session_id}/assets_#{@session_id}.zip"
      BrowserStack.logger.info("Uploading Assets to #{s3_url}")

      status, error = UploadToS3.upload_file_to_s3(device_logs_aws_key, device_logs_aws_secret,
                                                   "application/octet-stream", zipped_assets_path,
                                                   "public-read", s3_url, nil, 10)

      if !status || error
        @error_kind = "assets-upload-error"
        @error_message = error[0..200]
      end
    end

    def instrument_metrics(push_to_eds: true, feature_usage: nil)
      zombie_push('android', @error_kind, "", '', @error_message, '', @session_id, '') unless @error_kind.nil?
      if push_to_eds
        feature_success = @error_kind.nil?
        feature_usage ||= {
          "assets": {
            "success": feature_success.to_s,
            "size": file_size(zipped_assets_path).to_s,
            "exception": @error_message
          }
        }
        eds = EDS.new({}, BrowserStack.logger)
        eds_data = {
          "feature_usage": feature_usage,
          "hashed_id": @session_id.to_s,
          "timestamp": Time.now.to_i.to_s
        }
        eds.push_logs("app_automate_test_sessions", eds_data).join
      end
    end
  end
end

Espresso::AssetsHelper.run_from_bash if $PROGRAM_NAME == __FILE__
