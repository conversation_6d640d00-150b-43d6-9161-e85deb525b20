require 'android_toolkit'
require 'browserstack_logger'

require_relative '../../exit_file'
module BrowserStack
  module Espresso
    class ScreenshotHelper
      DEVICE_SCREENSHOT_FOLDERS = %w[
        /storage/emulated/0/app_spoon-screenshots
        /storage/emulated/0/Download/screenshots
      ].freeze

      def self.run_from_bash
        raise StandardError, "Not enough arguments" if ARGV.size < 2

        function_to_call = ARGV[0].to_s.strip
        device_id = ARGV[1].to_s.strip
        args = ARGV[2..]

        screenshot_helper = BrowserStack::Espresso::ScreenshotHelper.new(device_id)
        screenshot_helper.send(function_to_call, *args)
      rescue StandardError => e
        ExitFile.write(e.message[0..200])
        raise e
      end

      def initialize(device)
        raise "Device cannot be empty" if device.nil? || device == ""

        logger = Logger.new($stdout)
        @device = device
        @adb = AndroidToolkit::ADB.new(udid: @device)
        AndroidToolkit::Log.logger = logger
      end

      def list_screenshots_files(class_name, test_name, single_runner_enabled = "false")
        screenshots_list = []
        if single_runner_enabled?(single_runner_enabled)
          machine_screenshot_folder.each do |screenshot_dir|
            screenshots_list.concat(get_screenshot_files("#{screenshot_dir}/#{class_name}/#{test_name}/")).uniq!
          end
        else
          screenshots_list.concat(get_screenshot_files("#{machine_screenshot_base_folder}/#{test_name}/")).uniq!
        end
        screenshots_list.empty? ? "" : screenshots_list
      end

      def fetch_screenshots(*args)
        single_runner_enabled = args[0].to_s.strip
        screenshot_enabled = args[1].to_s.strip
        bundle_id = args[2].to_s.strip
        fully_qualified_test_name = args[3].to_s.strip
        screenshots_list = []

        if screenshot_enabled.to_s.downcase != "true"
          ExitFile.write(";")
          return
        end

        start_time = Time.now.to_i
        class_name, test_name = fully_qualified_test_name.split("#")
        screenshot_folders = DEVICE_SCREENSHOT_FOLDERS + [app_specific_screenshot_dir(bundle_id)]

        screenshot_folders.each do |screenshot_base_dir|
          test_screenshot_dir = if fully_qualified_test_name.empty?
                                  screenshot_base_dir
                                else
                                  "#{screenshot_base_dir}/#{class_name}/#{test_name}"
                                end
          @adb.pull(test_screenshot_dir, machine_screenshot_base_folder)
        rescue AndroidToolkit::ADB::ExecutionError => e
          BrowserStack.logger.info "ADB Execution error: #{e.message}"
        end

        unless single_runner_enabled?(single_runner_enabled)
          screenshots_list = list_screenshots_files(class_name, test_name, single_runner_enabled)
        end
        screenshot_data = screenshots_list.empty? ? ";" : screenshots_list.join(";")
        BrowserStack.logger.info "Screenshots fetch time: #{Time.now.to_i - start_time}. "\
                                   "Screenshot data: #{screenshot_data}"
        ExitFile.write(screenshot_data)
      end

      private

      def get_screenshot_files(screenshots_dir)
        are_screenshots_present_for_test = (Dir.exist?(screenshots_dir) &&
                                          !(Dir.entries(screenshots_dir) - %w[. ..]).empty?)
        screenshot_files = are_screenshots_present_for_test ? (Dir.entries(screenshots_dir) - %w[. ..]) : []

        # This regex is same as the one used in EspressoScreenshot custom processor
        screenshot_files.select { |file_name| file_name.match(/[^A-Za-z0-9._-]/).nil? }
      end

      # This path is not publically documented. If future android versions break current paths,
      # this path will come in handy.
      def app_specific_screenshot_dir(bundle_id)
        "/storage/emulated/0/Android/data/#{bundle_id}/files/screenshots"
      end

      # This constant is similar to SCREENSHOTS_FOLDER in espresso_actions file
      # Note: We are storing androidx screenshots inside a folder named spoon due to legacy reasons.
      def machine_screenshot_base_folder
        "/tmp/#{@device}/spoon-screenshots"
      end

      def machine_screenshot_folder
        ["#{machine_screenshot_base_folder}/app_spoon-screenshots", "#{machine_screenshot_base_folder}/screenshots"]
      end

      def single_runner_enabled?(single_runner_flag)
        single_runner_flag.to_s.downcase == "true"
      end
    end
  end
end

BrowserStack::Espresso::ScreenshotHelper.run_from_bash if $PROGRAM_NAME == __FILE__
