require 'json'
require 'digest'
require 'browserstack_logger'
require_relative '../../../common/push_to_zombie'
require_relative "./junit_reporter"
require_relative "./screenshot_helper"
require_relative "./cucumber_helper"
require_relative "../../helpers/utils"

# This class parses the 'raw output mode' results of an instrumentation test run via adb shell am instrument command
class InstrumentationResultParser  # rubocop:disable Metrics/ClassLength

  DEFAULT_TEST_STATUS = 'RUNNING'.freeze

  # Test result status codes
  STATUS_CODES = {
    ignored: '-3',
    failure: '-2',
    start: '1',
    error: '-1',
    ok: '0',
    assumption_failure: '-4'
  }.freeze
  # Prefixes used to identify output in instrumentation logs
  INSTRUMENTATION_PREFIXES = {
    code: "INSTRUMENTATION_CODE: ",
    status: "INSTRUMENTATION_STATUS: ",
    status_code: "INSTRUMENTATION_STATUS_CODE: ",
    status_failed: "INSTRUMENTATION_FAILED: ",
    time_report: "Time: ",
    class: "INSTRUMENTATION_STATUS: class=",
    test: "INSTRUMENTATION_STATUS: test=",
    stack: "INSTRUMENTATION_STATUS: stack=",
    short_msg: "INSTRUMENTATION_RESULT: shortMsg",
    instrumentation_aborted: "INSTRUMENTATION_ABORTED: System has crashed."
  }.freeze

  def initialize(instrumentation_file, summary_file, base_s3_url, logs_url, video_url, session_duration,
                 device_id, merged_junit_report_metadata_file, callback_file_data, test_framework, parsed_tests: {})
    @summary_file = summary_file
    @summary_file_v2 = "#{@summary_file}_v2"
    @summary_data = JSON.parse(File.read(@summary_file))
    @summary_data_v2 = JSON.parse(File.read(@summary_file_v2))
    @instrumentation_file = instrumentation_file
    @session_duration = session_duration
    @device_id = device_id
    @merged_junit_report_metadata_file = merged_junit_report_metadata_file
    @urls = {
      base_s3_url: base_s3_url,
      logs_url: logs_url,
      video_url: if @summary_data["video"].to_s == 'false'
                   ""
                 else
                   "#{video_url}#t=0,#{session_duration.to_i + increment_video_end_tag}"
                 end
    }
    @callback_file_data = callback_file_data
    @test_framework = test_framework
    @parsed_tests = parsed_tests
  end

  def increment_video_end_tag
    mediaprojection_enabled? ? MEDIA_PROJECTION_LAST_TEST_CONSTANT_INCREASE : 0
  end

  # @return [Array] An array with following elements -
  # 1. test_details as per v1 summary
  # 2. classes as per v2 summary
  # 3. flat_test_list is a list of all tests in the form of com.android.foo.FooClassName#testMethodName
  # 4. INSTRUMENTATION_CODE of the instrumentation logs
  def parse # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
    start_time = Time.now
    log "instrumentation logs parsing started at #{start_time}"
    @result = {}
    @result_v2 = {}
    instrument_classes = []
    instrument_test = ''
    last_class = ''
    only_class_name = ''
    flat_test_list = []
    session_id = @summary_data['session_id']
    app_bundle_id = @summary_data['app_details']['bundle_id']
    test_status = nil
    stacktrace = ''
    collect_stack = false
    instrumentation_code = nil
    scenario_outline_examples_status = []
    is_cucumber_test_suite = @summary_data['test_suite_details']['is_cucumber_test_suite']
    class_names = EspressoHelper.get_classnames_from_file(@instrumentation_file)
    is_duplicate_class_name_present = EspressoHelper.check_duplicate_classes(class_names)
    common_package_name = is_duplicate_class_name_present ? EspressoHelper.get_common_package_name(class_names) : ""

    File.open(@instrumentation_file) do |file|
      file.each_line do |line|
        collect_stack = false if line.include?(INSTRUMENTATION_PREFIXES[:status]) ||
                                 line.include?(INSTRUMENTATION_PREFIXES[:status_code])
        if line.include?(INSTRUMENTATION_PREFIXES[:status_code])
          status_code = line[INSTRUMENTATION_PREFIXES[:status_code].length, line.length].strip
          next if last_class.empty? || instrument_test.empty? || (instrument_test == "null") || only_class_name.empty?

          test_class_hash = "#{last_class}##{instrument_test}"
          if flat_test_list.include? test_class_hash
            test_status, stacktrace = handle_status_code(status_code, test_status, stacktrace)
            next if status_code == STATUS_CODES[:start]

            unless stacktrace.empty?
              kind = if stacktrace == "Fatal Error"
                       "#{@test_framework}-test-failed"
                     else
                       "#{@test_framework}-test-failed-unknown"
                     end
              zombie_push(
                'android', kind,
                (session_id + get_test_id("#{last_class}##{instrument_test}")).to_s,
                '', line.to_s[0, 200], @device_id, session_id, ''
              )
            end

            # Default value for test status is FAILED
            if test_status.nil?
              test_status = "FAILED"
              stacktrace += 'Not able to detect test status.'
              zombie_push('android', "#{@test_framework}-test-failed-default",
                          (session_id + get_test_id("#{last_class}##{instrument_test}")).to_s,
                          '', '', @device_id, session_id, '')
            end

            error_message = EspressoHelper.get_browserstack_error(stacktrace) unless stacktrace.empty?
            if EspressoHelper.is_browserstack_error?(error_message, @device_id)
              test_status = 'ERROR'
              report_espresso_test_marked_error(line, session_id, last_class, instrument_test, error_message)
            end
            if is_cucumber_test_suite
              test_status = get_scenario_outline_example_status_without_example_number(
                test_status, scenario_outline_examples_status
              )
            end
            update_test_status_and_junit_report(
              only_class_name, last_class, instrument_test, session_id, test_status, stacktrace
            )
            # at this point parsing of current instrument_test is complete, reset all the variables
            instrument_test = ''
            test_status = nil
            stacktrace = ''
            collect_stack = false
          else
            # handle the new test
            initialize_new_test(test_class_hash, flat_test_list, only_class_name, last_class, instrument_test)
            scenario_outline_examples_status = []
          end
        elsif line.include?(INSTRUMENTATION_PREFIXES[:short_msg]) && instrument_test != ''
          #INSTRUMENTATION_RESULT: shortMsg indicates that a fatal error occurred when attempted to run the tests
          test_status = 'FAILED'
          #assuming test initialization is done,and class name values are populated because
          # `INSTRUMENTATION_STATUS: class` and `INSTRUMENTATION_STATUS_CODE:`
          # lines always occur before `INSTRUMENTATION_RESULT` line
          update_test_status_and_junit_report(only_class_name, last_class, instrument_test,
                                              session_id, test_status, stacktrace)

          # at this point parsing of current instrument_test is complete, reset all the variables
          instrument_test = ''
          test_status = nil
          stacktrace = ''
          collect_stack = false
        elsif line.include?(INSTRUMENTATION_PREFIXES[:instrumentation_aborted]) && instrument_test != ''
          # INSTRUMENTATION_ABORTED: System has crashed. occurs in case of device errors
          test_status = 'ERROR'
          report_espresso_test_marked_error(line, session_id, last_class, instrument_test, error_message)
          #assuming test initialization is done,and class name values are populated because
          #`INSTRUMENTATION_STATUS: class` and `INSTRUMENTATION_STATUS_CODE:`
          # lines always occur before `INSTRUMENTATION_ABORTED: System has crashed` line
          update_test_status_and_junit_report(only_class_name, last_class, instrument_test,
                                              session_id, test_status, stacktrace)
          #setting instrumentation code to 0, to avoid getting marked timedout due to nil? check later
          #and not resetting variables here as this is always the last line
          instrumentation_code = "0"

        elsif line.include?(INSTRUMENTATION_PREFIXES[:class])
          line = line[INSTRUMENTATION_PREFIXES[:class].length, line.length].strip
          line = line.sub(/^Feature/, "").strip if is_cucumber_test_suite
          unless instrument_classes.include?(line)
            instrument_classes.push(line)
            last_class = line
            only_class_name = EspressoHelper.get_only_class_name(common_package_name, is_duplicate_class_name_present,
                                                                 last_class)
            @result[line] = {}

            if @result_v2[only_class_name].nil? && !only_class_name.empty?
              @result_v2[only_class_name] = default_class_object(only_class_name)
            end
          end
        elsif line.include?(INSTRUMENTATION_PREFIXES[:test])
          instrument_test = line[INSTRUMENTATION_PREFIXES[:test].length, line.length].strip
          if is_cucumber_test_suite
            # Scenario Outline test name example => test name example
            instrument_test = instrument_test.sub(/^Scenario\s(Outline)?/, "").strip
          end
        elsif line.include?(INSTRUMENTATION_PREFIXES[:stack]) || collect_stack
          if collect_stack
            stacktrace.concat("\r")
            stacktrace.concat(line)
          else
            value = line[INSTRUMENTATION_PREFIXES[:stack].length, line.length].strip
            collect_stack = true
            stacktrace.concat(value)
          end

          if line.match(ESPRESSO_SOCKET_TIMEOUT)
            zombie_push(
              'android',
              "espresso-socket-timeout-exception",
              (session_id + get_test_id("#{last_class}##{instrument_test}")).to_s,
              '',
              line.to_s[0, 200],
              @device_id,
              session_id,
              ''
            )
          end
        elsif line.include?(INSTRUMENTATION_PREFIXES[:time_report])
          @session_duration = line[INSTRUMENTATION_PREFIXES[:time_report].length, line.length].strip
          @session_duration.sub!(",", ".") # Converting "2,933" to "2.933"
        elsif line.include?(INSTRUMENTATION_PREFIXES[:code])
          instrumentation_code = line[INSTRUMENTATION_PREFIXES[:code].length, line.length].strip
        end
      end
    end
    if is_cucumber_test_suite
      cucumber_helper = CucumberHelper.new
      flat_test_list, @result, @result_v2 = cucumber_helper.parse_scenario_outline(
        flat_test_list, @result, @result_v2, session_id, is_single_runner_invocation: true, parsed_tests: @parsed_tests,
                                                         log_split_enabled: is_true?(@summary_data["splitLogs"])
      )
    end

    log "instrumentation logs parsing ended, parsing took #{Time.now - start_time} s"

    [@result, @result_v2, flat_test_list, instrumentation_code, @callback_file_data]
  end

  def self.get_test_status_v2(test_status_v1)
    if test_status_v1.casecmp("success").zero?
      "passed"
    elsif test_status_v1.casecmp("ignored").zero?
      "skipped"
    else
      test_status_v1.downcase
    end
  end

  private

  def log(message)
    BrowserStack.logger.info "#{self.class} ID: #{object_id} message: #{message}"
  end

  def get_test_id(test_input)
    md5_id = Digest::MD5.hexdigest test_input
    md5_id[0..7]
  end

  def default_test(id)
    {
      start_time: '',
      status: DEFAULT_TEST_STATUS,
      test_id: id,
      duration: '',
      instrumentation_log: '',
      device_log: '',
      video: ''
    }
  end

  def default_test_v2(id, testname)
    {
      name: testname,
      start_time: '',
      status: DEFAULT_TEST_STATUS.downcase,
      test_id: id,
      duration: '',
      class: "",
      video: ''
    }
  end

  def default_tests_summary
    {
      "total" => 0,
      'passed' => 0,
      'failed' => 0,
      'skipped' => 0,
      'timedout' => 0,
      'error' => 0,
      'running' => 0,
      'queued' => 0
    }
  end

  def get_screenshots_hash(s3url, test_id, screenshots, get_screenshot, version = 'v1')
    return {} if screenshots.nil? || screenshots.empty?

    result = { "screenshots" => [] }
    if get_screenshot.to_s.casecmp("true").zero?
      screenshots.each do |ss_name|
        ss_name.delete!('\\"')
        ss_name.delete!("\r")
        ss_name.delete!("\n")
        result["screenshots"] << [s3url, test_id, "screenshots", ss_name].join("/") if version == 'v1'
        result["screenshots"] << ss_name if !ss_name.empty? && version == 'v2'
      end
    end
    result
  end

  # In case of cucumber we can have multiple results for same classname & testname
  # since each scenario outline can have multiple values.
  # Order of precedence -> FAILED > ERROR > TIMEDOUT > SUCCESS > IGNORED > RUNNING
  def get_test_status_with_higher_precedence(old_status, new_status)
    test_status_precedence = %w[FAILED ERROR TIMEDOUT SUCCESS IGNORED RUNNING]
    test_status_precedence.index(old_status) < test_status_precedence.index(new_status) ? old_status : new_status
  end

  def handle_status_code(status_code, test_status, stacktrace)
    case status_code
    when STATUS_CODES[:start]
      test_status = 'TIMEDOUT' if test_status.nil?
    when STATUS_CODES[:ok]
      test_status = 'SUCCESS'
    when STATUS_CODES[:error] # Fatal error occurred
      test_status = 'FAILED'
      stacktrace.concat('Fatal Error')
    when STATUS_CODES[:failure]
      test_status = 'FAILED'
    when STATUS_CODES[:ignored], STATUS_CODES[:assumption_failure]
      test_status = 'IGNORED'
    else
      test_status = 'FAILED'
      stacktrace.concat('Unknown status code')
    end
    [test_status, stacktrace]
  end

  def default_class_object(only_class_name)
    {
      name: only_class_name,
      tests_summary: default_tests_summary,
      tests: {}
    }
  end

  # Update test status when scenario outline examples do not have example number appended at the end of string.
  def get_scenario_outline_example_status_without_example_number(test_status, scenario_outline_examples_status)
    scenario_outline_examples_status.push(test_status)
    if scenario_outline_examples_status.length > 1
      current_tests_status = CucumberHelper.default_tests_summary
      scenario_outline_examples_status.each do |scenario_test_status|
        scenario_test_status_v2 = InstrumentationResultParser.get_test_status_v2(scenario_test_status)
        current_tests_status[scenario_test_status_v2] += 1
        current_tests_status['total'] += 1
      end
      test_status = CucumberHelper.get_scenario_outline_status(current_tests_status)
      test_status = CucumberHelper.get_test_status_v1(test_status)
    end
    test_status
  end

  def mediaprojection_enabled?
    @summary_data["use_rtc_app"] == "v2"
  end

  # This method is used to initialize a new test in a class
  def initialize_new_test(test_class_hash, flat_test_list, only_class_name, last_class, instrument_test)
    flat_test_list << test_class_hash
    test_id = @summary_data['session_id'] + get_test_id(test_class_hash)
    screenshots = BrowserStack::Espresso::ScreenshotHelper.new(@device_id).list_screenshots_files(last_class,
                                                                                                  instrument_test, true)
    test_obj = default_test(test_id)
    test_obj[:instrumentation_log] = "#{@urls[:logs_url]}#{test_id}/instrumentationlogs"
    test_obj[:device_log] = "#{@urls[:logs_url]}#{test_id}/devicelogs" if @summary_data["deviceLogs"].to_s != 'false'
    test_obj[:network_log] = "#{@urls[:logs_url]}#{test_id}/networklogs" if @summary_data["networkLogs"].to_s != 'false'
    test_obj[:video] = @urls[:video_url]
    test_obj.merge!(get_screenshots_hash(@urls[:base_s3_url], test_id, screenshots, @summary_data["screenshots"], 'v1'))
    @result[last_class][instrument_test] = test_obj

    test_obj_v2 = default_test_v2(test_id, instrument_test)
    test_obj_v2[:video] =
      if @summary_data_v2["video"].to_s == "false"
        ""
      else
        "0,#{@session_duration.to_i + increment_video_end_tag}"
      end
    test_obj_v2.merge!(get_screenshots_hash(@urls[:base_s3_url], test_id, screenshots,
                                            @summary_data_v2["screenshots"], 'v2'))

    if is_true?(@summary_data["splitLogs"])
      add_device_logs_boundaries(test_obj_v2, last_class)
      add_video_logs_boundaries(test_obj_v2, last_class)
      add_network_logs_boundaries(test_obj_v2, last_class)
    end

    @result_v2[only_class_name][:tests][instrument_test] = test_obj_v2
    @result_v2[only_class_name][:tests][instrument_test][:class] = only_class_name
    @result_v2[only_class_name][:tests_summary]['total'] += 1
    @result_v2[only_class_name][:tests_summary]['running'] += 1
  end

  def add_device_logs_boundaries(test_obj, test_class)
    return unless is_true?(@summary_data["deviceLogs"])

    test_key = "#{test_class}.#{test_obj[:name]}"
    test_obj[:device_log] = @parsed_tests[test_key].device_log_boundary if @parsed_tests.key?(test_key)
  rescue StandardError => e
    BrowserStack.logger.info("[Parser] Exception in add_device_logs_boundaries: #{e.message} #{e.backtrace}")

  end

  def add_video_logs_boundaries(test_obj, test_class)
    if @summary_data_v2["video"].to_s == "false"
      test_obj[:video] = ""
    else
      test_key = "#{test_class}.#{test_obj[:name]}"
      if @parsed_tests.key?(test_key) && @parsed_tests[test_key].video_boundary_valid?
        test_obj[:video] =
          @parsed_tests[test_key].video_log_boundary
      end
    end
  rescue StandardError => e
    BrowserStack.logger.info("[Parser] Exception in add_video_logs_boundaries: #{e.message} #{e.backtrace}")

  end

  def add_network_logs_boundaries(test_obj, test_class)

    return unless is_true?(@summary_data["networkLogs"])

    test_key = "#{test_class}.#{test_obj[:name]}"
    test_obj[:network_log] = @parsed_tests[test_key].network_log_boundary if @parsed_tests.key?(test_key)
  rescue StandardError => e
    BrowserStack.logger.info("[Parser] Exception in add_network_logs_boundaries: #{e.message} #{e.backtrace}")

  end

  def update_test_status_and_junit_report(only_class_name, last_class, instrument_test,
                                          session_id, test_status, stacktrace)
    test_class_hash = "#{last_class}##{instrument_test}"
    test_status = get_test_status_with_higher_precedence(@result[last_class][instrument_test][:status], test_status)
    test_status_v2 = InstrumentationResultParser.get_test_status_v2(test_status)
    @result[last_class][instrument_test][:status] = test_status
    old_test_status_v2 = @result_v2[only_class_name][:tests][instrument_test][:status]
    @result_v2[only_class_name][:tests][instrument_test][:status] = test_status_v2
    @result_v2[only_class_name][:tests_summary][old_test_status_v2] -= 1
    @result_v2[only_class_name][:tests_summary][test_status_v2] += 1

    # add test to JUnit report
    junit_report_generator = JUnitReporter.new(@merged_junit_report_metadata_file, session_id)
    junit_report_generator.add_result(last_class, instrument_test, test_status_v2.to_sym, 0, 0,
                                      @urls[:video_url],
                                      (session_id + get_test_id(test_class_hash)).to_s, stacktrace)
    junit_report_generator.save_state
  end

  def report_espresso_test_marked_error(
    line, session_id, last_class, instrument_test, error_message, test_framework='espresso'
  )
    zombie_data = {
      "line" => line.to_s[0, 200],
      "test_id" => (session_id + get_test_id("#{last_class}##{instrument_test}")).to_s
    }
    zombie_push(
      'android', "#{test_framework}-test-marked-error", error_message, '', zombie_data, @device_id,
      session_id, ''
    )
    @callback_file_data["error_reason"] = "device-off-adb" if error_message == ESPRESSO_DEVICE_NOT_FOUND
  end
end
