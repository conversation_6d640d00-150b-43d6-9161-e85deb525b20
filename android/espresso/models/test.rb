require 'time'
require 'browserstack_logger'
require_relative '../../../android/lib/logging/logger'

class Test
  include LoggerModule
  attr_accessor :name, :start_time, :end_time, :status, :duration, :klass, :video, :log_start_pos, :log_end_pos,
                :video_start_offset, :video_end_offset

  def initialize(name:, start_time: nil, end_time: nil, status: TEST_STATUS[:QUEUED], test_id: nil, duration: nil,
                 klass: nil, video: nil, log_start_pos: nil, log_end_pos: nil, video_start_offset: nil,
                 video_end_offset: nil)
    @name = name
    @start_time = start_time
    @end_time = end_time
    @status = status
    @id = test_id
    @duration = duration
    @klass = klass
    @video = video
    @log_start_pos = log_start_pos # TODO: Add array
    @log_end_pos = log_end_pos
    @video_start_offset = video_start_offset
    @video_end_offset = video_end_offset
    @network_start_offset = nil
    @network_end_offset = nil
  end

  def start(timestamp, klass, log_position)
    BrowserStack.logger.info(
      "[Test] started: name: #{@name} " \
        "timestamp: #{timestamp} klass: #{klass} " \
        "log_position: #{log_position}"
    )
    @start_time = Time.parse(timestamp)
    @status = TEST_STATUS[:RUNNING]
    @klass = klass
    @log_start_pos = log_position
  end

  def test_id
    "#{@klass}.#{@name}"
  end

  def pass(timestamp, log_position)
    complete(timestamp, TEST_STATUS[:PASSED], log_position)
  end

  def fail(timestamp, log_position)
    complete(timestamp, TEST_STATUS[:FAILED], log_position)
  end

  def skip(timestamp, log_position)
    @start_time = Time.parse(timestamp) # Set timestamp as started for skipped test
    complete(timestamp, TEST_STATUS[:SKIPPED], log_position)
  end

  def timeout(timestamp, log_position)
    complete(timestamp, TEST_STATUS[:TIMEOUT], log_position)
  end

  def assumption_failed(timestamp, log_position)
    complete(timestamp, TEST_STATUS[:FAILED], log_position)
  end

  def calculate_video_offsets(video_start_time_epoch)
    if @start_time && @end_time
      video_start_time = Time.at(video_start_time_epoch.to_i)
      @video_start_offset = calculate_offset(video_start_time, @start_time)
      @video_end_offset = calculate_offset(video_start_time, @end_time)
    end
  end

  def device_log_boundary
    {
      start: @log_start_pos,
      end: @log_end_pos
    }
  end

  def video_log_boundary
    "#{@video_start_offset&.floor},#{@video_end_offset&.ceil}"
  end

  def network_log_boundary
    {
      start: @network_start_offset,
      end: @network_end_offset
    }
  end

  def video_boundary_valid?
    !(@video_start_offset.nil? || @video_end_offset.nil?)
  end

  def device_log_boundary_valid?
    !(@log_start_pos.nil? || @log_end_pos.nil?)
  end

  def network_log_boundary_valid?
    !(@network_start_offset.nil? || @network_end_offset.nil?)
  end

  def update_network_log_boundary(start, end_range)
    @network_start_offset = start
    @network_end_offset = end_range
  end

  def calculate_duration(start_time, end_time)

    (end_time - start_time).round(3).to_s
  rescue StandardError
    nil

  end

  def update_duration
    @duration = calculate_duration(@start_time, @end_time)
  end

  def merge(other)
    @start_time = [@start_time, other.start_time].compact.min
    @end_time = [@end_time, other.end_time].compact.max
    @log_start_pos = [@log_start_pos, other.log_start_pos].compact.min
    @log_end_pos = [@log_end_pos, other.log_end_pos].compact.max
    @video_start_offset = [@video_start_offset, other.video_start_offset].compact.min
    @video_end_offset = [@video_end_offset, other.video_end_offset].compact.max
  end

  def self.copy(other)
    Test.new(
      name: other.name,
      start_time: other.start_time,
      end_time: other.end_time,
      status: other.status,
      test_id: other.test_id,
      duration: other.duration,
      klass: other.klass,
      video: other.video,
      log_start_pos: other.log_start_pos,
      log_end_pos: other.log_end_pos,
      video_start_offset: other.video_start_offset,
      video_end_offset: other.video_end_offset
    )
  end

  private

  def complete(timestamp, status, log_position)
    @end_time = Time.parse(timestamp)
    @duration = calculate_duration(@start_time, timestamp)
    @status = status
    @log_end_pos = log_position
  end

  def calculate_offset(base_time, event_time)
    log("calculate_offset: base_time: #{base_time} event_time: #{event_time}")
    begin
      log("calculate_offset: parsed event time: #{event_time.to_i}  ")
    # (DateTime.strptime(event_time, "%m-%d %H:%M:%S.%L").to_time - base_time).round(3) rescue nil
      (event_time - base_time).round(3)
    rescue StandardError => e
      log("Error calculating offset: #{e.message} #{e.backtrace}")
      nil
    end

  end
end
