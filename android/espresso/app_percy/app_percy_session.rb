require 'json'

require 'app_percy_utils'
require 'browserstack_logger'

require_relative './cli_manager'

class AppPercySession
  attr_reader :device_id, :cli_manager, :session_id, :device_obj

  FRAMEWORK = 'Espresso'.freeze
  PLAN_LIMIT_ERROR_MSG =
    'This organization has exceeded the limits of the Percy BrowserStack plan'.freeze

  def initialize(device_id, session_id)
    @device_id = device_id
    @session_id = session_id
    @cli_manager = CLIManager.new(@device_id)
    @device_obj = AndroidDevice.new(@device_id, self.class.to_s, BrowserStack.logger)
  end

  def start(params)
    start_percy_cli(params)
    disable_animation
  end

  def stop
    stop_percy_cli
  end

  def start_percy_cli(params)
    AppPercy::Util.logit('start_percy_cli', FRAMEWORK, suppress: true) do |event_data|
      event_data[:session_id] = session_id
      event_data[:device_id] = device_id
      cli_manager.start_percy_cli(JSON.parse(params), session_id)
      output = cli_manager.cli_running?
      unless output
        url = org_limit_reached?
        if url
          event_data[:message] = 'Organization plan limit reached'
          event_data[:organization_slug] = url.split('/')[-2]
        else
          event_data[:message] = 'Percy CLI start failed'
        end
        BrowserStack.logger.info("session=#{session_id}, app_percy_cli not running")
        return false
      end
      BrowserStack.logger.info("session=#{session_id}, Percy CLI started")
      event_data[:success] = true
    end
  end

  def stop_percy_cli
    if cli_manager.cli_running?
      AppPercy::Util.logit('stop_percy_cli', FRAMEWORK, suppress: true) do |event_data|
        event_data[:session_id] = session_id
        event_data[:device_id] = device_id
        output = cli_manager.stop_percy_cli
        unless output
          event_data[:message] = 'CLI stop failed'
          BrowserStack.logger.info("session=#{session_id}, Percy CLI stop event_data #{event_data}")
          return false
        end
        BrowserStack.logger.info("session=#{session_id}, Percy CLI stopped")
        event_data[:success] = true
      end
    end
    true
  end

  def org_limit_reached?
    file_content = File.read(cli_manager.cli_log_file_path(session_id))
    return false unless file_content.include?(PLAN_LIMIT_ERROR_MSG)

    url_regex = %r{https?://\S+}
    file_content[url_regex]
  rescue StandardError => e
    BrowserStack.logger.info("session=#{session_id}, Failed to read file: #{e.message}")
    false
  end

  def disable_animation
    AppPercy::Util.logit('disable_animation', 'Appium', suppress: true) do |event_data|
      event_data[:session_id] = session_id
      event_data[:device_id] = device_id
      unless device_obj.toggle_animation(1)
        event_data[:message] = 'Disable animation failed'
        return false
      end
      BrowserStack.logger.info("session=#{session_id}, Disable animation success")
      event_data[:success] = true
    end
  end
end
