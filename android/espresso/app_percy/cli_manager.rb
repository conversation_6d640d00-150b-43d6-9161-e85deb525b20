require 'json'
require 'browserstack_logger'
require 'timeout'

# This is defined because this file will be called via bash script standalone
DIR_HOME = "/usr/local/.browserstack/mobile".freeze

require "#{DIR_HOME}/common/helpers"
require "#{DIR_HOME}/android/helpers/utils"

class CLIManager

  CONFIG_FILE = "/usr/local/.browserstack/config/config.json".freeze
  LOGGING_DIR = '/var/log/browserstack'.freeze
  SERVER_LOG = "/var/log/browserstack/server.log".freeze

  attr_reader :device

  def initialize(device_id)
    config = JSON.parse(read_with_lock(CONFIG_FILE))
    @device = config["devices"][device_id]
    logger_params = { component: 'cli_manager.rb' }
    BrowserStack.init_logger(SERVER_LOG, logger_params)
  end

  def start_percy_cli(app_percy_params, session_id)
    cli_start_command =
      "#{cli_env(app_percy_params['env'])}" \
      "percy app exec:start --port #{CLIManager.cli_port(@device['port'])} " \
      " > #{cli_log_file_path(session_id)} 2>&1"

    pid = Process.spawn(cli_start_command)
    Process.detach(pid)
    cli_check(session_id)
  end

  def stop_percy_cli
    if cli_running?
      system("percy app exec:stop --port #{CLIManager.cli_port(@device['port'])}")
      begin
        attempts ||= 1
        raise 'Percy CLI still running' if cli_running?
      rescue StandardError => e
        if (attempts += 1) < 5
          sleep 1
          retry
        end
        CLIManager.force_stop(CLIManager.cli_port(@device['port']))
      end
    end
    !cli_running?
  end

  def cli_check(session_id)
    Timeout.timeout(5) do
      loop do
        if cli_running?
          BrowserStack.logger.info "session=#{session_id}, Percy CLI started successfully"
          break
        end
        BrowserStack.logger.info "session=#{session_id}, Percy CLI still starting ..."
        sleep(1)
      end
    end
  rescue Timeout::Error
    BrowserStack.logger.info "session=#{session_id}, App Percy Timed out waiting CLI to start"
  end

  def cli_running?
    is_port_open?('127.0.0.1', CLIManager.cli_port(@device['port']))
  end

  def self.cli_port(device_port)
    "4#{device_port}"
  end

  def self.force_stop(cli_port)
    cmd = "percy app exec:start"
    pid = `lsof -t -i :#{cli_port}`.strip
    cli_process = `ps aux | grep #{pid} | grep -v grep | grep "#{cmd}"`.strip
    if !pid.nil? && pid != "" && !cli_process.nil? && cli_process != ""
      BrowserStack.logger.info(
        "force stopping app_percy_cli #{pid}"
      )
      # KILL the process
      Process.kill("KILL", pid.to_i)
    end
  end

  def cli_log_file_path(session_id)
    "#{LOGGING_DIR}/percy_cli.#{session_id}_#{CLIManager.cli_port(@device['port'])}.log"
  end

  private

  def cli_env(params)
    env = ""
    params.each do |key, value|
      env += "#{key}='#{value}' "
    end
    env
  end
end
