require 'dotenv/load'
require 'static_conf'
require 'fileutils'
require 'json'
require 'logger'
require_relative '../constants'
require_relative '../helpers/http_utils'
require_relative '../helpers/systemd_helper'
require_relative '../../common/helpers'
require_relative "../lib/os_utils"

class DeviceLogger
  include SystemDHelper

  attr_reader :device, :port

  DEVICE_LOGGER_SERVICE_TEMPLATE = "#{PATH}/device_logger/device_logger_service.erb".freeze

  def initialize(device, should_join_threads: false)
    @device = device
    config = JSON.parse(read_with_lock(BrowserStack::CONFIG_JSON_FILE))
    @devices_json = config["devices"]
    @port = @devices_json[device]["device_logger_port"]
    @device_logger_state_file = File.join(BrowserStack::STATE_FILES_DIR, "device_logger_#{device}")
    @dl_session_end_pid_file = File.join(BrowserStack::STATE_FILES_DIR, "device_logger_session_end_pid_#{device}")
    @logger = Logger.new($stdout)
    @should_join_threads = should_join_threads
  end

  def create_service
    data = { device: device, port: port, node_path: ENV['NODE_8_PATH'] }
    systemd_create_service("device_logger", DEVICE_LOGGER_SERVICE_TEMPLATE , device: device, binding_data: data)
  end

  def init
    init_thread = Thread.bs_run do
      FileUtils.touch(@device_logger_state_file)
      create_service unless systemd_check_if_service_in_list("device_logger", device: device)
      systemd_start_service("device_logger", device: @device)
    rescue StandardError => e
      log(:error, "Error in Device logger init: #{e.message}")
    end
    init_thread.join if @should_join_threads
  end

  def start
    start_thread = Thread.bs_run do
      if File.exist?(@device_logger_state_file)
        log(:info, "Sending start request")
        wait_until_server_is_healthy
        BrowserStack::HttpUtils.send_get("http://localhost:#{port}/session/start")
      end
    rescue StandardError => e
      log(:error, "Error in Device logger start: #{e.message}")
    end
    start_thread.join if @should_join_threads
  end

  def update
    update_thread = Thread.bs_run do
      if File.exist?(@device_logger_state_file)
        BrowserStack::HttpUtils.send_get("http://localhost:#{port}/session/update")
      end
    rescue StandardError => e
      log(:error, "Error in Device logger update: #{e.message}")
    end
    update_thread.join if @should_join_threads
  end

  def stop
    if File.exist?(@device_logger_state_file)
      session_end_pid = OSUtils.execute("ps -ef | grep '[d]evice-logger' | grep #{@device} | awk '{print $2}'")
      log(:info, "Write Device Logger Pid #{session_end_pid} to #{@dl_session_end_pid_file}")
      File.write(@dl_session_end_pid_file, session_end_pid)
      BrowserStack::HttpUtils.send_get("http://localhost:#{port}/session/stop")
      File.delete(@device_logger_state_file)
      systemd_stop_service("device_logger", device: @device)
    end
  rescue StandardError => e
    log(:error, "Error in Device logger init: #{e.message}")
  end

  private

  def log(level, msg)
    @logger.send(level.to_sym, "#{self.class} #{msg}")
  end

  def server_healthy?
    BrowserStack::HttpUtils.send_get("http://localhost:#{port}/health")
    log(:info, "Healthyyyyy")
    true
  rescue Errno::ECONNREFUSED, SocketError
    false
  end

  def wait_until_server_is_healthy
    retries = 30

    retries.times do
      break if server_healthy?

      log(:info, "Still unhealthy")
      sleep 1
    end
  end
end

if $PROGRAM_NAME == __FILE__
  device = ARGV[0].to_s.strip
  command = ARGV[1].to_s.strip.to_sym

  device_logger_helper = DeviceLogger.new(device, should_join_threads: true)
  device_logger_helper.send(command)
end
