require 'fileutils'
require 'timeout'
require 'browserstack_logger'

BS_HOME = "/usr/local/.browserstack".freeze
DIR_HOME = "#{BS_HOME}/mobile".freeze
require "#{DIR_HOME}/common/push_to_zombie.rb"

class Utils

  class << self

    def read_file_in_array(filename)
      File.read(filename).strip.split("\n")
    end

    def write_array_to_file!(filename, array)
      # Overwrites the file
      File.open(filename, 'w') { |f| f.write(array.join("\n")) }
    end

    def write_to_file(filename, data, params = {})

      File.open(filename, 'w+') { |file| file.write(data) }
      raise "file write failed" unless File.exist?(filename)
    rescue StandardError => e
      BrowserStack::Zombie.push_logs("file-write-failed", "file write failed", { "data" => data }, nil, params)
      BrowserStack.logger.info "Failed to write the file : #{filename} , #{e.message} #{e.backtrace}"

    end

    def touch_file_and_create_parents(filepath)
      dir = File.dirname(filepath)
      FileUtils.mkdir_p(dir) unless File.exist?(dir)
      FileUtils.touch(filepath)

      raise "Failed to touch file at: #{filepath}" unless File.exist?(filepath)
    end

    def with_lock(file, lock_timeout = 5, &_block)
      File.open("#{file}.lock", "w+") do |f|
        Timeout.timeout(lock_timeout) { f.flock(File::LOCK_EX) }
        yield
      rescue Timeout::Error => e
        raise LockfileTimeoutError, "Timeout of #{lock_timeout} reached trying to acquire lock for file #{f}"
      ensure
        f.flock(File::LOCK_UN)
      end
    end

    def write_to_file_with_lock(file_name, data, _lock_timeout: 5)
      with_lock(file_name) do
        write_to_file(file_name, data, data)
      end
    end
  end
end
