{"disable_permission_monitoring": {"rules": [{"os_version": "^(1[5-9]|[2-9]\\d+)(\\.[0-9])?$", "via": "intent_automation", "intent": "-a android.settings.APPLICATION_DEVELOPMENT_SETTINGS"}], "default_via": "normal_automation", "default_intent": "-a android.settings.APPLICATION_DEVELOPMENT_SETTINGS"}, "disable_popups_for_system_ui": {"rules": [{"os_version": "^(1[5-9]|[2-9]\\d+)(\\.[0-9])?$", "via": "intent_automation", "intent": "-a android.settings.APP_NOTIFICATION_SETTINGS --es android.provider.extra.APP_PACKAGE com.android.systemui"}], "default_via": "normal_automation", "default_intent": "-a android.settings.APP_NOTIFICATION_SETTINGS --es android.provider.extra.APP_PACKAGE com.android.systemui"}, "connect_to_internet_network": {"rules": [{"os_version": "^(1[1-9]|[2-9]\\d+)(\\.[0-9])?$", "via": "intent_automation", "intent": "-a android.settings.WIFI_SETTINGS"}], "default_via": "normal_automation", "default_intent": "-a android.settings.WIFI_SETTINGS"}, "revoke_android_intelligence_permissions_rule": {"rules": [{"os_version": "^(1[5-9]|[2-9]\\d+)(\\.[0-9])?$", "via": "intent_automation", "intent": "-a android.settings.APPLICATION_DETAILS_SETTINGS -d package:com.google.android.as"}], "default_via": "normal_automation", "default_intent": "-a android.settings.APPLICATION_DETAILS_SETTINGS -d package:com.google.android.as"}, "disable_mac_randomization_rule": {"rules": [{"os_version": "^(1[5-9]|[2-9]\\d+)(\\.[0-9])?$", "via": "intent_automation", "intent": "-a android.settings.WIFI_SETTINGS"}], "default_via": "normal_automation", "default_intent": "-a android.settings.WIFI_SETTINGS"}}