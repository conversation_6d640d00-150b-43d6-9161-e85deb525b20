require_relative '../../../android/helpers/cleanup_function_runner'
require 'rspec'

describe CleanupFunctionRunner do
  let(:mock_logger) { double('Logger', info: nil) }
  let(:mock_device) { 'some_device' }

  let(:mock_android_device) do
    double(
      'BrowserStack::AndroidDevice',
      model: 'Pixel 5',
      manufacturer: 'Google',
      os_version: '12.0'
    )
  end

  let(:mock_rules) do
    double('RulesRelation', where: double('WhereResult', all: rules_array))
  end

  let(:rules_array) { [] }

  before do
    allow(BrowserStack::AndroidDevice).to receive(:new).and_return(mock_android_device)
    allow(BrowserStack::CleanupRules).to receive(:cleanup_rules).and_return(mock_rules)
    allow(AndroidToolkit::Log).to receive(:logger=)
  end

  subject { described_class.new(mock_device, mock_logger) }

  describe '#csv_to_array' do
    it 'returns an empty array for nil input' do
      expect(subject.csv_to_array(nil)).to eq([])
    end

    it 'returns an empty array for empty string' do
      expect(subject.csv_to_array("  ")).to eq([])
    end

    it 'splits and strips comma-separated values' do
      expect(subject.csv_to_array(" a, b ,c ")).to eq(['a', 'b', 'c'])
    end

    it 'removes empty elements' do
      expect(subject.csv_to_array("a,, ,b")).to eq(['a', 'b'])
    end
  end

  describe '#should_run?' do
    context 'when rules are empty' do
      let(:rules_array) { [] }

      it 'returns false' do
        expect(subject.should_run?('some_function')).to eq(false)
      end
    end

    context 'when rule matches device info' do
      let(:rules_array) do
        [{
          'include_models' => 'Pixel 5',
          'exclude_models' => '',
          'include_manufacturers' => '',
          'exclude_manufacturers' => '',
          'version_gt' => '11.0',
          'version_lt' => '13.0'
        }]
      end

      it 'returns true' do
        expect(subject.should_run?('some_function')).to eq(true)
      end
    end

    context 'when rule does not match version' do
      let(:rules_array) do
        [{
          'include_models' => 'Pixel 5',
          'exclude_models' => '',
          'include_manufacturers' => '',
          'exclude_manufacturers' => '',
          'version_gt' => '12.0',
          'version_lt' => '13.0'
        }]
      end

      it 'returns false due to version_gt check' do
        expect(subject.should_run?('some_function')).to eq(false)
      end
    end
  end
end
