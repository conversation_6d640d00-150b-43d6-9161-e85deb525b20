require_relative "../../../android/helpers/metadata_extraction"

require 'json'
require 'ostruct'

describe MetadataExtraction do
  let(:app_path) { '/path/to/app.apk' }
  let(:device_id) { 'test_device_01' }
  let(:session_id) { 'session_123' }
  let(:state_file_path) { "#{STATE_FILES_DIR}/al_session_#{session_id}" }

  let(:mock_logger) { double('Logger', info: nil, error: nil) }
  let(:mock_device) { instance_double(BrowserStack::AndroidDevice) }

  let(:params) do
    {
      "app_bs_auth" => "auth_token",
      "app_check_endpoint" => "https://api.example.com/verify",
      "app_filename" => "test_app.apk",
      "device_api_level" => "22.0",
      "user_id" => "user_123"
    }
  end

  before do
    allow(BrowserStack).to receive(:logger).and_return(mock_logger)
    allow(BrowserStack::AndroidDevice).to receive(:new).and_return(mock_device)
    allow(File).to receive(:read).with(state_file_path).and_return(params.to_json)
    allow(mock_device).to receive(:os_version).and_return(10.0)
  end

  describe '#validate_app_path' do
    it 'returns false for nil path' do
      extractor = described_class.new(nil, device_id, session_id)
      expect(extractor.validate_app_path).to be_falsey
    end

    it 'returns false for empty path' do
      extractor = described_class.new('', device_id, session_id)
      expect(extractor.validate_app_path).to be_falsey
    end

    it 'returns true for valid path' do
      extractor = described_class.new(app_path, device_id, session_id)
      expect(extractor.validate_app_path).to be_truthy
    end
  end

  describe '#extract_app_metadata' do
    let(:extractor) { described_class.new(app_path, device_id, session_id) }

    before do
      allow(Open3).to receive(:capture3).and_return([
        <<~OUTPUT, '', instance_double(Process::Status, success?: true)
          package: name='com.example.app'
          sdkVersion:'21'
          launchable-activity: name='com.example.app.MainActivity'
        OUTPUT
      ])
    end

    it 'extracts metadata successfully' do
      extractor.extract_app_metadata

      expect(extractor.instance_variable_get(:@bundle_id)).to eq('com.example.app')
      expect(extractor.instance_variable_get(:@min_sdk_version)).to eq(21.0)
      expect(extractor.instance_variable_get(:@launcher_activity)).to eq([["com.example.app.MainActivity",
                                                                           0]])
    end
  end

  describe '#validate_extracted_metadata' do
    let(:extractor) { described_class.new(app_path, device_id, session_id) }

    it 'returns true when all metadata is valid' do
      extractor.instance_variable_set(:@bundle_id, 'com.example.app')
      extractor.instance_variable_set(:@min_sdk_version, 21.0)
      extractor.instance_variable_set(:@launcher_activity, '[["com.example.Main", 0]]')

      expect(extractor.validate_extracted_metadata).to be true
    end

    it 'returns true when launcher_activity is missing' do
      extractor.instance_variable_set(:@bundle_id, 'com.example.app')
      extractor.instance_variable_set(:@min_sdk_version, 21.0)
      extractor.instance_variable_set(:@launcher_activity, nil)

      expect(extractor.validate_extracted_metadata).to be true
    end

    it 'returns false when bundle_id is missing' do
      extractor.instance_variable_set(:@bundle_id, nil)
      extractor.instance_variable_set(:@min_sdk_version, 21.0)
      extractor.instance_variable_set(:@launcher_activity, [["com.example.Main", 0]])

      expect(extractor.validate_extracted_metadata).to be false
    end

    it 'returns false when min_sdk_version is missing' do
      extractor.instance_variable_set(:@bundle_id, 'com.example.app')
      extractor.instance_variable_set(:@min_sdk_version, nil)
      extractor.instance_variable_set(:@launcher_activity, [["com.example.Main", 0]])

      expect(extractor.validate_extracted_metadata).to be false
    end
  end

  describe '#verify_app' do
    let(:extractor) { described_class.new(app_path, device_id, session_id) }
    let(:response_double) do
      double('response',
             code: '200',
             body: { app_params: { name: 'TestApp' } }.to_json)
    end

    before(:each) do
      extractor.instance_variable_set(:@bundle_id, 'com.example.app')
      extractor.instance_variable_set(:@min_sdk_version, 21.0)
      extractor.instance_variable_set(:@launcher_activity, [["com.example.Main", 0]])
      allow(HttpUtils).to receive(:send_post).and_return(response_double)
    end

    it 'returns success with app_data' do
      result = extractor.verify_app
      expect(result['success']).to be true
      expect(result['app_data']['name']).to eq('TestApp')
      expect(result['app_data']['package']).to eq('com.example.app')
      expect(result['app_data']['launcher_activity']).to eq('com.example.Main')
    end

    it 'returns false when sdk version of the app is not supported by the device' do
      extractor.instance_variable_set(:@min_sdk_version, 23.0)
      expect(extractor).to receive(:notify_min_os_version).and_return(true)
      result = extractor.verify_app
      expect(result['success']).to be false
    end

    it 'returns failure if API returns non-200' do
      allow(response_double).to receive(:code).and_return('400')
      expect(extractor).to receive(:notify_close_session).and_return(true)
      result = extractor.verify_app
      expect(result['success']).to be false
    end

    it 'returns failure on API error' do
      allow(HttpUtils).to receive(:send_post).and_raise(StandardError.new("api error"))
      result = extractor.verify_app
      expect(result['success']).to be false
    end
  end

  describe '#extract_validate_and_get_app_params' do
    let(:extractor) { described_class.new(app_path, device_id, session_id) }

    before do
      allow(extractor).to receive(:validate_app_path).and_return(true)
      allow(extractor).to receive(:extract_app_metadata)
      allow(extractor).to receive(:validate_extracted_metadata).and_return(true)
      allow(extractor).to receive(:verify_app).and_return({ "success" => true, "app_data" => { key: "value" } })
    end

    it 'returns success response when all steps pass' do
      result = extractor.extract_validate_and_get_app_params
      expect(result['success']).to be true
      expect(result['app_data']).to eq({ key: 'value' })
    end

    it 'returns failure if validation fails' do
      allow(extractor).to receive(:validate_extracted_metadata).and_return(false)
      result = extractor.extract_validate_and_get_app_params
      expect(result['success']).to be false
    end
  end
end
