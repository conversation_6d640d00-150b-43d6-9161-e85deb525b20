require_relative '../../spec_helper'

RSpec.describe SessionSavePolling do
  let(:session_id) { "session123" }
  let(:device_id) { "device123" }
  let(:debugger_port) { 9222 }
  let(:genre) { "test_genre" }
  let(:setting_cookies) { false }
  let(:ssp) { described_class.new(session_id, device_id, debugger_port, genre, setting_cookies) }
  let(:start_file_path) { described_class.start_file(device_id) }
  let(:ws_mock) { double(WebSocket::Client::Simple::Client) }
  let(:cookie_file_path) { "/usr/local/.browserstack/state_files/#{device_id}_cookie_data_from_s3.json" }

  before do
    allow(EDS).to receive(:new).and_return(double(push_logs: true))
    allow(OSUtils).to receive(:execute).and_return('[{"type":"page","id":"page1","url":"http://example.com"}]')
    allow(WebSocket::Client::Simple).to receive(:connect).and_yield(ws_mock)
    allow(ws_mock).to receive(:on)
    allow(ws_mock).to receive(:send)
    allow(ws_mock).to receive(:open?).and_return(true)

    # Prevent any real sleeps
    allow_any_instance_of(described_class).to receive(:sleep)
  end

  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect(ssp.instance_variable_get(:@session_id)).to eq(session_id)
      expect(ssp.instance_variable_get(:@device_id)).to eq(device_id)
      expect(ssp.instance_variable_get(:@debugger_port)).to eq(debugger_port)
      expect(ssp.instance_variable_get(:@genre)).to eq(genre)
      expect(ssp.instance_variable_get(:@history)).to eq({})
      expect(ssp.instance_variable_get(:@setting_cookies)).to eq(setting_cookies)
    end
  end

  describe '#setup_ws_connection' do
    before do
      allow(ws_mock).to receive(:on).with(:open).and_yield(double)
      allow(ws_mock).to receive(:on).with(:error)
      allow(ws_mock).to receive(:on).with(:close)
      allow(ws_mock).to receive(:on).with(:message)
    end

    it 'establishes a WebSocket connection' do
      expect(WebSocket::Client::Simple).to receive(:connect)
      expect(ws_mock).to receive(:on).with(:open)

      ssp.setup_ws_connection

      expect(ssp.instance_variable_get(:@ws)).to eq(ws_mock)
    end
  end

  describe '#start' do
    before do
      # Replace the actual implementation of start to avoid any loops
      allow(ssp).to receive(:start) do
        FileUtils.touch(start_file_path)
        ssp.setup_ws_connection

        ssp.send(:set_cookies) if ssp.instance_variable_get(:@setting_cookies) && ssp.instance_variable_get(:@ws)&.open?

        # Just call fetch_cookies once instead of looping
        ssp.send(:fetch_cookies)

        File.delete(start_file_path) if File.exist?(start_file_path)
      end

      allow(FileUtils).to receive(:touch)
      allow(File).to receive(:exist?).with(start_file_path).and_return(true, false)
      allow(File).to receive(:delete)
      allow(ssp).to receive(:setup_ws_connection)
      allow(ssp).to receive(:fetch_cookies)
      allow(ssp).to receive(:set_cookies)
      allow(Timeout).to receive(:timeout).and_yield
    end

    it 'creates the start file and sets up WebSocket connection' do
      expect(FileUtils).to receive(:touch).with(start_file_path)
      expect(ssp).to receive(:setup_ws_connection)

      ssp.start
    end

    it 'sets cookies when setting_cookies is true' do
      ssp.instance_variable_set(:@setting_cookies, true)
      ssp.instance_variable_set(:@ws, ws_mock)
      allow(ws_mock).to receive(:open?).and_return(true)

      expect(ssp).to receive(:set_cookies)

      ssp.start
    end

    it 'does not set cookies when setting_cookies is false' do
      expect(ssp).not_to receive(:set_cookies)

      ssp.start
    end

    it 'fetches cookies periodically while the start file exists' do
      # Just verify fetch_cookies is called at least once
      expect(ssp).to receive(:fetch_cookies).at_least(:once)

      ssp.start
    end

    it 'deletes the start file when done' do
      allow(File).to receive(:exist?).with(start_file_path).and_return(true)
      expect(File).to receive(:delete).with(start_file_path)

      ssp.start
    end
  end

  describe '.start_file' do
    it 'returns the correct start file path' do
      start_file = "#{BrowserStack::STATE_FILES_DIR}/start_save_session_#{device_id}"
      expect(described_class.start_file(device_id)).to eq(start_file)
    end
  end

  describe '.running?' do
    before do
      allow(File).to receive(:exist?).and_return(true)
    end

    it 'returns true if start file exists' do
      expect(described_class.running?(device_id)).to be true
    end

    it 'returns false if start file does not exist' do
      allow(File).to receive(:exist?).and_return(false)
      expect(described_class.running?(device_id)).to be false
    end
  end

  describe '#fetch_cookies' do
    let(:ws_mock) { double(WebSocket::Client::Simple::Client, open?: true) }
    let(:message_handler) { proc {} }

    before do
      ssp.instance_variable_set(:@ws, ws_mock)
      frame_tree_data = {
        id: 4999,
        result: {
          frameTree: {
            frame: {
              url: "http://example.com"
            }
          }
        }
      }.to_json
      allow(ws_mock).to receive(:on).with(:message).and_yield(double(data: frame_tree_data))
      allow(ws_mock).to receive(:send)
      allow(FileUtils).to receive(:mkdir_p)
      allow(File).to receive(:write)
      allow(File).to receive(:exist?).and_return(false)
      allow(File).to receive(:read)
    end

    it 'sends requests to get URL and cookies' do
      expect(ws_mock).to receive(:send).with(match(/"method":"Page.getFrameTree"/))
      expect(ws_mock).to receive(:send).with(match(/"method":"Network.getCookies"/))

      ssp.send(:fetch_cookies)
    end

    it 'saves cookies to file when received' do
      allow(ws_mock).to receive(:on).with(:message).and_yield(
        double(data: '{"id":4999,"result":{"frameTree":{"frame":{"url":"http://example.com"}}}}')
      ).and_yield(
        double(data: '{"id":5000,"result":{"cookies":[{"name":"test","value":"123"}]}}')
      )

      expect(FileUtils).to receive(:mkdir_p)
      expect(File).to receive(:write).with(cookie_file_path, anything)

      ssp.send(:fetch_cookies)
    end
  end

  describe '#set_cookies' do
    before do
      ssp.instance_variable_set(:@ws, ws_mock)
      allow(File).to receive(:exist?).with(cookie_file_path).and_return(true)
      allow(File).to receive(:read).with(cookie_file_path).and_return('{"http://example.com":[{"name":"test","value":"123","domain":"example.com"}]}')
      allow(ws_mock).to receive(:on).with(:message).and_yield(double(data: '{"id":6000,"result":{"success":true}}'))
    end

    it 'loads cookies from file and sends them to browser' do
      expect(ws_mock).to receive(:send).with(match(/"method":"Network.setCookie"/))
      expect(ws_mock).to receive(:send).with(match(/"method":"Page.reload"/))

      ssp.send(:set_cookies)
    end

    it 'does nothing if cookie file does not exist' do
      allow(File).to receive(:exist?).with(cookie_file_path).and_return(false)

      expect(ws_mock).not_to receive(:send)

      ssp.send(:set_cookies)
    end
  end

  describe '#strip_url_to_domain' do
    it 'extracts domain from URL' do
      expect(ssp.send(:strip_url_to_domain, 'https://example.com/path?query=1')).to eq('https://example.com')
    end

    it 'returns original URL if parsing fails' do
      invalid_url = 'not-a-valid-url'
      allow(URI).to receive(:parse).with(invalid_url).and_raise(URI::InvalidURIError)

      expect(ssp.send(:strip_url_to_domain, invalid_url)).to eq(invalid_url)
    end
  end
end