{"build_id": "44f4d5ca581c53cef84efc7f0fc498e7b14b2322", "session_id": "5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd", "device": "Samsung Galaxy S22-12.0", "deviceLogs": "true", "networkLogs": "false", "networkLogsCaptureContent": false, "acceptInsecureCerts": "false", "video": "true", "start_time": "2025-04-02 08:58:14 +0000", "duration": "141", "idle_timeout": "900", "error_reason": "", "screenshots": "true", "coverage": null, "singleRunnerInvocation": null, "useOrchestrator": null, "clearPackageData": null, "use_rtc_app": "", "app_details": {"url": "bs://8fed33d334aa165958c849ecd62aca5ceef4fb0a", "bundle_id": "org.wikipedia.alpha", "custom_id": null, "version": "2.5.194-alpha-2017-05-30", "name": "WikipediaSample.apk"}, "test_suite_details": {"url": "bs://a2a947e45ba8c5635ef1168a80f234b36de414e0", "bundle_id": "", "custom_id": null, "name": "TYPE_3.zip", "instrumentation": null, "is_cucumber_test_suite": null}, "test_count": 1, "test_status": {"SUCCESS": 0, "FAILED": 0, "IGNORED": 0, "TIMEDOUT": 0, "RUNNING": 0, "QUEUED": 1, "ERROR": 0}, "test_details": {"TYPE_3/master": {"TYPE_3/master": {"start_time": "2025-04-02 08:58:49 +0000", "status": "QUEUED", "test_id": "5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd0e2de3d8", "duration": "115", "instrumentation_log": "https://apik8s-devdas.bsstag.com/app-automate/maestro/builds/44f4d5ca581c53cef84efc7f0fc498e7b14b2322/sessions/tests/5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd0e2de3d8/instrumentationlogs", "device_log": "https://apik8s-devdas.bsstag.com/app-automate/maestro/builds/44f4d5ca581c53cef84efc7f0fc498e7b14b2322/sessions/tests/5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd0e2de3d8/devicelogs", "video": "https://app-automate-k8s-devdas.bsstag.com/s3-upload/bs-stag-video-logs-euw/s3.eu-west-1/5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd/video-5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd.mp4#t=4,134", "network_log": "", "commands_log": "https://apik8s-devdas.bsstag.com/app-automate/maestro/builds/44f4d5ca581c53cef84efc7f0fc498e7b14b2322/sessions/tests/5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd0e2de3d8/commandslogs", "maestro_log": "https://apik8s-devdas.bsstag.com/app-automate/maestro/builds/44f4d5ca581c53cef84efc7f0fc498e7b14b2322/sessions/tests/5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd0e2de3d8/maestrologs", "screenshots": ["https://s3-eu-west-1.amazonaws.com/bs-stag-selenium-logs-euw/44f4d5ca581c53cef84efc7f0fc498e7b14b2322/5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd0e2de3d8/5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd0e2de3d8_screenshots.zip"], "metadata": {"completed_flows": 1, "total_flows": 1, "canceled_flows": 0, "status": "QUEUED", "maestro_status": "Passed", "duration": "115", "subflows": [{"status": "QUEUED", "maestro_status": "Passed", "flow_name": "flow 0", "duration": "115", "details": "", "logs": "\n\n"}]}}}}, "media_projection_fallback_required": true}