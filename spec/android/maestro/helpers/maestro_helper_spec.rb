require_relative "../../../../android/maestro/helpers/maestro_helper"
require_relative "../../../../spec/spec_helper"
require 'pry'
require 'os'
require 'fileutils'
require_relative '../../../../android/constants'

describe MaestroHelper do
  let(:helper) { MaestroHelper.new('./spec/android/maestro/helpers/fixtures/summary_file') }
  let(:build_id) { '44f4d5ca581c53cef84efc7f0fc498e7b14b2322' }
  let(:session_id) { '5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd' }
  let(:screenshots_zip_file) { '/tmp/screenshots.zip' }
  let(:base_s3url) { 'https://s3.amazonaws.com/test-bucket' }
  let(:baseurl) { 'https://s3.amazonaws.com/test-bucket' }
  let(:aws_key) { 'test-aws-key' }
  let(:aws_secret) { 'test-aws-secret' }
  let(:unique_test_id) { '5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd0e2de3d8' }
  let(:output_folder_path) { "/tmp/#{session_id}_test_suite/logs/#{unique_test_id}/screenshots" }
  let(:zip_file_path) { File.join(output_folder_path, 'screenshots.zip') }
  let(:s3_url) { "#{baseurl}/#{session_id}#{unique_test_id}/screenshots.zip" }

  before do
    allow(helper).to receive(:log) # Stub the log method to avoid actual logging
    allow(helper).to receive(:zombie_push) # Stub the zombie_push method
    allow(helper).to receive(:upload_log_to_s3) # Stub the upload_log_to_s3 method
  end

  describe "fetch_screenshots" do
    context 'when the directory does not exist' do
      it 'returns nil' do
        allow(File).to receive(:directory?).with(output_folder_path).and_return(false)
        expect(helper.fetch_screenshots(output_folder_path)).to be_nil
      end
    end

    context 'when the directory exists and compression succeeds' do
      it 'creates a zip file and returns its path' do
        allow(File).to receive(:directory?).with(output_folder_path).and_return(true)
        allow(Dir).to receive(:chdir).with(output_folder_path).and_yield
        allow(OSUtils).to receive(:execute).with("zip -r #{zip_file_path} .",
                                                 true).and_return(['', double(success?: true)])

        result = helper.fetch_screenshots(output_folder_path)
        expect(result).to eq(zip_file_path)
      end
    end

    context 'when the directory exists but compression fails' do
      it 'logs an error and pushes a failure to Zombie' do
        allow(File).to receive(:directory?).with(output_folder_path).and_return(true)
        allow(Dir).to receive(:chdir).with(output_folder_path).and_yield
        allow(OSUtils).to receive(:execute).with("zip -r #{zip_file_path} .",
                                                 true).and_return(['Error message', double(success?: false)])

        result = helper.fetch_screenshots(output_folder_path)
        expect(result).to be_nil
        expect(helper).to have_received(:log).with(/Zipping SS failed/, severity: 'ERROR')
        expect(helper).to have_received(:zombie_push).with(
          'android',
          'maestro-zip-screenshots-failed',
          'Error message',
          nil,
          anything,
          nil
        )
      end
    end
  end

  describe "upload_screenshots_to_s3" do
    context 'when the upload is successful' do
      it 'uploads the screenshots to S3 and logs the upload time' do
        # Mock the upload_log_to_s3 method to simulate a successful upload
        allow(helper).to receive(:upload_log_to_s3).and_return(true)

        # Call the method
        helper.upload_screenshots_to_s3(session_id, screenshots_zip_file, base_s3url, aws_key, aws_secret)

        # Verify that the upload_log_to_s3 method was called with the correct arguments
        expect(helper).to have_received(:upload_log_to_s3).with(
          base_s3url, aws_key, aws_secret, screenshots_zip_file, session_id
        )

        # Verify that the log method was called to log the upload time
        expect(helper).to have_received(:log).with(/Screenshots upload time: \d+ ms/)
      end
    end

    context 'when an error occurs during the upload' do
      it 'logs the error' do
        # Mock the upload_log_to_s3 method to raise an error
        expect(helper).to receive(:upload_log_to_s3).and_raise(StandardError.new('Upload failed'))

        # Call the method and handle the error
        expect do
          helper.upload_screenshots_to_s3(session_id, screenshots_zip_file, base_s3url, aws_key, aws_secret)
        end.to raise_error(StandardError, 'Upload failed')

        # Verify that the log method was called to log the error
        # expect(helper).to have_received(:log).with(/Upload failed/, severity: 'ERROR')
      end
    end
  end

  describe "fetch_and_push_screenshots" do
    before do
      allow(helper).to receive(:fetch_screenshots).and_return(zip_file_path) # Stub fetch_screenshots
      allow(helper).to receive(:upload_screenshots_to_s3).and_return(s3_url) # Stub upload_screenshots_to_s3
    end

    context 'when screenshots are successfully fetched and uploaded' do
      it 'returns the S3 URL of the uploaded screenshots' do
        result = helper.fetch_and_push_screenshots(baseurl, aws_key, aws_secret, unique_test_id)
        expect(result).to eq(s3_url)

        # Verify that fetch_screenshots and upload_screenshots_to_s3 were called
        expect(helper).to have_received(:fetch_screenshots).with(output_folder_path)
        expect(helper).to have_received(:upload_screenshots_to_s3).with(
          "#{session_id}#{unique_test_id}", zip_file_path, baseurl, aws_key, aws_secret
        )
      end
    end

    context 'when no screenshots are found' do
      it 'returns an empty string' do
        allow(helper).to receive(:fetch_screenshots).and_return(nil)

        result = helper.fetch_and_push_screenshots(baseurl, aws_key, aws_secret, unique_test_id)
        expect(result).to eq('')

        # Verify that fetch_screenshots was called but upload_screenshots_to_s3 was not
        expect(helper).to have_received(:fetch_screenshots).with(output_folder_path)
        expect(helper).not_to have_received(:upload_screenshots_to_s3)
      end
    end

    context 'when an error occurs during execution' do
      it 'logs the error and returns an empty string' do
        allow(helper).to receive(:fetch_screenshots).and_raise(StandardError.new('Unexpected error'))

        result = helper.fetch_and_push_screenshots(baseurl, aws_key, aws_secret, unique_test_id)
        expect(result).to eq('')

        # Verify that the error was logged
        expect(helper).to have_received(:log).with(/Error fetching screenshots: Unexpected error/, severity: 'ERROR')
      end
    end
  end

  describe '#update_summary_file' do
    let(:summary_file) { './spec/android/maestro/helpers/fixtures/summary_file' }
    let(:summary_file_v2) { "#{summary_file}_v2" }
    let(:summary_data) { { 'device' => 'test-device', 'session_id' => 'test-session-id' } }
    let(:summary_data_v2) { { 'device' => 'test-device-v2', 'session_id' => 'test-session-id-v2' } }

    before do
      # Stub the log method to avoid actual logging
      allow(helper).to receive(:log)

      # Set up initial data for the helper instance
      helper.instance_variable_set(:@summary_file, summary_file)
      helper.instance_variable_set(:@summary_file_v2, summary_file_v2)
      helper.instance_variable_set(:@summary_data, summary_data)
      helper.instance_variable_set(:@summary_data_v2, summary_data_v2)
    end

    context 'when the file update is successful' do
      it 'writes the summary data to the summary files' do
        # Mock the safe_file_write method to simulate successful file writes
        allow(helper).to receive(:safe_file_write).and_yield(double('file', write: true))

        # Call the method
        helper.update_summary_file

        # Verify that safe_file_write was called for both summary files
        expect(helper).to have_received(:safe_file_write).with(
          summary_file, 'test-device', 'test-session-id', 'update_summary_file'
        )
        expect(helper).to have_received(:safe_file_write).with(
          summary_file_v2, 'test-device-v2', 'test-session-id-v2', 'update_summary_file'
        )
      end
    end

    context 'when an error occurs during file write' do
      it 'logs the error' do
        # Mock the safe_file_write method to raise an error
        allow(helper).to receive(:safe_file_write).and_raise(StandardError.new('File write failed'))

        # Call the method and handle the error
        expect { helper.update_summary_file }.to raise_error(StandardError, 'File write failed')
      end
    end
  end

  describe '#error_in_instrumentation_file?' do
    let(:instrumentation_file) { './spec/android/maestro/helpers/fixtures/instrumentation.log' }
    let(:device_id) { 'test-device-id' }
    let(:callback_file_data) { {} }
    let(:device_model) { 'test-device-model' }
    let(:data) { {} }
    let(:test_framework) { 'maestro' }

    before do
      allow(helper).to receive(:check_for_testsuite_parse_failure).and_return(false) # Stub parse failure check
    end
    context 'when the instrumentation file does not exist' do
      it 'returns true and pushes an error to Zombie' do
        allow(File).to receive(:exist?).with(instrumentation_file).and_return(false)

        result = helper.error_in_instrumentation_file?(
          instrumentation_file, device_id, callback_file_data, device_model, data, test_framework
        )

        expect(result).to be true
        expect(callback_file_data['error_reason']).to eq('testsuite-parse-empty')
        expect(helper).to have_received(:zombie_push).with(
          'android',
          "#{test_framework}-testsuite-parse-empty",
          'instrumentation file does not exist or is empty',
          device_model,
          data,
          device_id,
          helper.instance_variable_get(:@summary_data_v2)['session_id'],
          ''
        )
      end
    end

    context 'when the instrumentation file is empty' do
      it 'returns true and pushes an error to Zombie' do
        allow(File).to receive(:exist?).with(instrumentation_file).and_return(true)
        allow(File).to receive(:zero?).with(instrumentation_file).and_return(true)

        result = helper.error_in_instrumentation_file?(
          instrumentation_file, device_id, callback_file_data, device_model, data, test_framework
        )

        expect(result).to be true
        expect(callback_file_data['error_reason']).to eq('testsuite-parse-empty')
        expect(helper).to have_received(:zombie_push).with(
          'android',
          "#{test_framework}-testsuite-parse-empty",
          'instrumentation file does not exist or is empty',
          device_model,
          data,
          device_id,
          helper.instance_variable_get(:@summary_data_v2)['session_id'],
          ''
        )
      end
    end

    context 'when the instrumentation file contains parse failures' do
      it 'calls check_for_testsuite_parse_failure and returns its result' do
        allow(File).to receive(:exist?).with(instrumentation_file).and_return(true)
        allow(File).to receive(:zero?).with(instrumentation_file).and_return(false)
        allow(helper).to receive(:check_for_testsuite_parse_failure).and_return(true)

        result = helper.error_in_instrumentation_file?(
          instrumentation_file, device_id, callback_file_data, device_model, data, test_framework
        )

        expect(result).to be true
        expect(helper).to have_received(:check_for_testsuite_parse_failure).with(
          instrumentation_file, callback_file_data, device_model, device_id, data, test_framework
        )
      end
    end

    context 'when there are no errors in the instrumentation file' do
      it 'returns false' do
        allow(File).to receive(:exist?).with(instrumentation_file).and_return(true)
        allow(File).to receive(:zero?).with(instrumentation_file).and_return(false)
        allow(helper).to receive(:check_for_testsuite_parse_failure).and_return(false)

        result = helper.error_in_instrumentation_file?(
          instrumentation_file, device_id, callback_file_data, device_model, data, test_framework
        )

        expect(result).to be false
        expect(helper).to have_received(:check_for_testsuite_parse_failure).with(
          instrumentation_file, callback_file_data, device_model, device_id, data, test_framework
        )
      end
    end
  end

  describe '#process_and_upload_logs' do
    let(:base_s3_url) { 'https://s3.amazonaws.com/test-bucket' }
    let(:debug_op_path) { "/tmp/#{session_id}_test_suite/logs/#{unique_test_id}" }
    let(:log_files) do
      ["/tmp/#{session_id}_test_suite/logs/#{unique_test_id}/file1.log",
       "/tmp/#{session_id}_test_suite/logs/#{unique_test_id}/file2.log"]
    end

    before do
      allow(helper).to receive(:log) # Stub the log method to avoid actual logging
      allow(helper).to receive(:upload_log_to_s3) # Stub the upload_log_to_s3 method
      allow(helper).to receive(:fetch_all_files).and_return(log_files) # Stub fetch_all_files
    end
    context 'when no logs are found' do
      it 'logs a message and returns early' do
        allow(Dir).to receive(:exist?).with(debug_op_path).and_return(false)

        helper.process_and_upload_logs(session_id, unique_test_id, base_s3_url, aws_key, aws_secret)

        expect(helper).to have_received(:log).with('No logs found. Returning..')
        expect(helper).not_to have_received(:upload_log_to_s3)
      end
    end

    context 'when logs are found and successfully uploaded' do
      it 'uploads all log files to S3' do
        allow(Dir).to receive(:exist?).with(debug_op_path).and_return(true)
        allow(Dir).to receive(:empty?).with(debug_op_path).and_return(false)

        helper.process_and_upload_logs(session_id, unique_test_id, base_s3_url, aws_key, aws_secret)

        log_files.each do |file|
          expect(helper).to have_received(:upload_log_to_s3).with(base_s3_url, aws_key, aws_secret, file,
                                                                  unique_test_id)
        end
      end
    end

    context 'when an error occurs during file upload' do
      it 'logs the error and continues with the next file' do
        allow(Dir).to receive(:exist?).with(debug_op_path).and_return(true)
        allow(Dir).to receive(:empty?).with(debug_op_path).and_return(false)
        allow(helper).to receive(:upload_log_to_s3).and_raise(StandardError.new('Upload failed'))

        expect do
          helper.process_and_upload_logs(session_id, unique_test_id, base_s3_url, aws_key, aws_secret)
        end.to raise_error(StandardError, /Upload failed/)
      end
    end
  end

  describe '#generate_s3_url' do
    let(:helper) { MaestroHelper.new('./spec/android/maestro/helpers/fixtures/summary_file') }
    let(:base_s3url) { 'https://s3.amazonaws.com/test-bucket' }
    context 'when the file matches the special commands file pattern' do
      it 'returns the specific S3 URL for commands files' do
        file_path = '/tmp/commands-test-nested.json'

        result = helper.generate_s3_url(base_s3url, session_id, file_path)

        expect(result).to eq("#{base_s3url}/#{session_id}/#{session_id}-commands.json")
      end
    end

    context 'when the file does not match the special pattern' do
      it 'returns the default S3 URL format' do
        file_path = '/tmp/other-file.log'

        result = helper.generate_s3_url(base_s3url, session_id, file_path)

        expect(result).to eq("#{base_s3url}/#{session_id}/#{session_id}-other-file.log")
      end
    end
  end

  describe '#test_suite_unzip_path' do
    it 'returns the correct path for the test suite unzip directory' do
      result = helper.test_suite_unzip_path(session_id)
      expect(result).to eq("/tmp/#{session_id}_test_suite")
    end
  end

  describe '#debug_output_path' do
    it 'returns the correct path for the debug output directory' do
      result = helper.debug_output_path(session_id)
      expect(result).to eq("/tmp/#{session_id}_test_suite/logs")
    end
  end

  describe '#parse_execution_plan' do
    let(:execution_plan_dir) { './spec/android/maestro/helpers/fixtures/execution_plan' }
    let(:final_execution_plan_dir) { 'spec/android/maestro/helpers/fixtures/execution_plan' }
    let(:outfile) { './spec/android/maestro/helpers/fixtures/output_execution_plan.json' }
    let(:execution_plan_path) { "#{execution_plan_dir}/execution_plan.json" }

    before do
      allow(helper).to receive(:log) # Stub the log method to avoid actual logging
      allow(helper).to receive(:update_summary_file) # Stub the update_summary_file method
      helper.instance_variable_set(:@summary_data_v2, { 'session_id' => session_id })
      helper.instance_variable_set(:@summary_data, {})
    end
    context 'when the execution plan file does not exist' do
      it 'handles the missing file gracefully' do
        allow(File).to receive(:read).with(execution_plan_path).and_raise(Errno::ENOENT)

        expect do
          helper.parse_execution_plan(execution_plan_dir, outfile)
        end.not_to raise_error

        expect(helper.instance_variable_get(:@summary_data)['test_count']).to eq(0)
        expect(helper.instance_variable_get(:@summary_data)['test_status']).to eq({
          'SUCCESS' => 0,
          'FAILED' => 0,
          'IGNORED' => 0,
          'TIMEDOUT' => 0,
          'RUNNING' => 0,
          'QUEUED' => 0,
          'ERROR' => 0
        })
      end
    end

    context 'when the execution plan file is empty' do
      it 'handles an empty file gracefully' do
        allow(File).to receive(:read).with(execution_plan_path).and_return('')

        expect do
          helper.parse_execution_plan(execution_plan_dir, outfile)
        end.not_to raise_error

        expect(helper.instance_variable_get(:@summary_data)['test_count']).to eq(0)
        expect(helper.instance_variable_get(:@summary_data)['test_status']).to eq({
          'SUCCESS' => 0,
          'FAILED' => 0,
          'IGNORED' => 0,
          'TIMEDOUT' => 0,
          'RUNNING' => 0,
          'QUEUED' => 0,
          'ERROR' => 0
        })
      end
    end

    context 'when the execution plan file contains valid data' do
      it 'parses the execution plan and updates the summary data' do
        execution_plan_content = [
          "#{execution_plan_dir}/flow1",
          "#{execution_plan_dir}/flow2"
        ].to_json

        classes = [
          "#{final_execution_plan_dir}/flow1",
          "#{final_execution_plan_dir}/flow2"
        ].to_json

        allow(File).to receive(:read).with(execution_plan_path).and_return(execution_plan_content)
        allow(File).to receive(:write).with(outfile, anything)

        helper.parse_execution_plan(execution_plan_dir, outfile)

        summary_data = helper.instance_variable_get(:@summary_data)
        summary_data_v2 = helper.instance_variable_get(:@summary_data_v2)

        expect(summary_data['test_count']).to eq(2)
        expect(summary_data['test_status']['QUEUED']).to eq(2)
        expect(summary_data_v2['test_summary']['total']).to eq(2)
        expect(summary_data_v2['classes'].keys.to_json).to eq(classes)
      end
    end

    context 'when there is an error during parsing' do
      it 'handles JSON parsing errors gracefully' do
        allow(File).to receive(:read).with(execution_plan_path).and_return('invalid-json')

        expect do
          helper.parse_execution_plan(execution_plan_dir, outfile)
        end.not_to raise_error

        expect(helper.instance_variable_get(:@summary_data)['test_count']).to eq(0)
        expect(helper.instance_variable_get(:@summary_data)['test_status']).to eq({
          'SUCCESS' => 0,
          'FAILED' => 0,
          'IGNORED' => 0,
          'TIMEDOUT' => 0,
          'RUNNING' => 0,
          'QUEUED' => 0,
          'ERROR' => 0
        })
      end
    end
  end

  describe '#get_build_pusher_message' do
    let(:outfile) { './spec/android/maestro/helpers/fixtures/output_build_message' }
    let(:testsuite_file) { './spec/android/maestro/helpers/fixtures/testsuite_file.txt' }
    let(:device) { 'test-device' }
    let(:test_dir) { "/tmp/#{session_id}_test_suite" }

    before do
      allow(helper).to receive(:log) # Stub the log method to avoid actual logging
      helper.instance_variable_set(:@summary_file_v2, './spec/android/maestro/helpers/fixtures/summary_file_v2')
      helper.instance_variable_set(:@summary_data_v2, {
        'build_id' => build_id,
        'device' => device,
        'session_id' => session_id
      })
    end
    context 'when the test suite file does not exist' do
      it 'handles the missing file gracefully' do
        allow(File).to receive(:readlines).with(testsuite_file).and_raise(Errno::ENOENT)

        expect do
          helper.get_build_pusher_message(outfile, testsuite_file)
        end.not_to raise_error

        expect(File.read(outfile)).to eq("[]")
      end
    end

    context 'when the test suite file contains valid data' do
      it 'generates a valid build pusher message and writes it to the output file' do
        allow(File).to receive(:readlines).with(testsuite_file).and_return(["flow", "flow2"])

        helper.get_build_pusher_message(outfile, testsuite_file)

        expect(File).to exist(outfile)
        expect(File.read(outfile)).to eq(
          '["{\"build_id\":\"44f4d5ca581c53cef84efc7f0fc498e7b14b2322\",\"device\":null,'\
          '\"session_id\":\"5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd\",' \
          '\"testlist\":[{\"test_id\":\"5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd3b212781\",'\
          '\"name\":\"flow\",\"classname\":\"flow\",\"status\":\"queued\"},'\
          '{\"test_id\":\"5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd4caf26dc\",\"name\":\"flow2\",'\
          '\"classname\":\"flow2\",\"status\":\"queued\"}]}"]'
        )
      end
    end

    context 'when an error occurs during file read' do
      it 'handles the error gracefully and logs the error' do
        allow(File).to receive(:readlines).with(testsuite_file).and_raise(StandardError.new('Unexpected error'))

        expect do
          helper.get_build_pusher_message(outfile, testsuite_file)
        end.not_to raise_error

        expect(File.read(outfile)).to eq('[]')
      end
    end
  end
end