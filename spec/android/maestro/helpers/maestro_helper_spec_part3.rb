require_relative "../../../../android/maestro/helpers/maestro_helper"
require_relative "../../../../spec/spec_helper"
require 'pry'
require 'os'
require 'fileutils'
require_relative '../../../../android/constants'

describe MaestroHelper do
  let(:helper) { MaestroHelper.new('./spec/android/maestro/helpers/fixtures/summary_file') }
  let(:build_id) { '44f4d5ca581c53cef84efc7f0fc498e7b14b2322' }
  let(:session_id) { '5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd' }
  let(:screenshots_zip_file) { '/tmp/screenshots.zip' }
  let(:base_s3url) { 'https://s3.amazonaws.com/test-bucket' }
  let(:baseurl) { 'https://s3.amazonaws.com/test-bucket' }
  let(:aws_key) { 'test-aws-key' }
  let(:aws_secret) { 'test-aws-secret' }
  let(:unique_test_id) { '5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd0e2de3d8' }
  let(:output_folder_path) { "/tmp/#{session_id}_test_suite/logs/#{unique_test_id}/screenshots" }
  let(:zip_file_path) { File.join(output_folder_path, 'screenshots.zip') }
  let(:s3_url) { "#{baseurl}/#{session_id}#{unique_test_id}/screenshots.zip" }
  let(:testname) { '/TYPE_3/master.yml' }
  let(:flowname) { 'TYPE_3/master' }
  let(:input_file) { './spec/android/maestro/helpers/fixtures/instrumentation.log' }
  let(:callback_file) { './spec/android/maestro/helpers/fixtures/callback_file' }
  let(:device_id) { 'test-device-id' }
  let(:test_framework) { 'maestro' }
  let(:original_summary_file_content) { File.read(helper.instance_variable_get(:@summary_file)) }
  let(:original_summary_file_v2_content) { File.read(helper.instance_variable_get(:@summary_file_v2)) }

  before do
    allow(helper).to receive(:log) # Stub the log method to avoid actual logging
    allow(helper).to receive(:zombie_push) # Stub the zombie_push method
    allow(helper).to receive(:write_to_file) # Stub the write_to_file method
  end

  describe '#parse_instrumentation_logs' do
    context 'when the instrumentation file is empty' do
      it 'logs an error and returns a status of ERROR' do
        allow(File).to receive(:zero?).with(input_file).and_return(true)

        result = helper.parse_instrumentation_logs(input_file, testname, device_id, callback_file, test_framework)
        expect(result[:status]).to eq('ERROR')
        expect(helper).to have_received(:log).with(/Instrumentation file is empty/, severity: 'ERROR')
        expect(helper).to have_received(:zombie_push).with(
          "android", "maestro-test-marked-error",
          "Instrumentation stalled", "",
          { "line" => "Instrumentation stalled",
            "test_id" => "5f2a8c0420e0679917a7fea533e2b36ae5b5e3ddf9e4698d" },
          "test-device-id", "5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd", ""
        )
      end
    end

    context 'when the instrumentation file contains BrowserStack errors' do
      it 'logs the error and returns a status of ERROR' do
        allow(File).to receive(:zero?).with(input_file).and_return(false)
        allow(File).to receive(:open).with(input_file).and_yield(StringIO.new('Device disconnected'))

        allow(helper).to receive(:get_browserstack_error).and_return('Device disconnected')

        result = helper.parse_instrumentation_logs(input_file, testname, device_id, callback_file, test_framework)

        expect(result[:status]).to eq('ERROR')
        expect(helper).to have_received(:log).with(/BrowserStack error detected/, severity: 'ERROR')
        expect(helper).to have_received(:zombie_push).with(
          "android",
          "maestro-test-marked-error",
          "Device disconnected", "",
          { "line" => "Device disconnected", "test_id" => "5f2a8c0420e0679917a7fea533e2b36ae5b5e3ddf9e4698d" },
          "test-device-id", "5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd", ""
        )
      end
    end

    context 'when the instrumentation file contains valid data' do
      it 'parses the file and returns the correct test status' do
        log_content = <<~LOG
          Waiting for flows to complete...
          [RUNNING] flow1 (10s)
          [SUCCESS] flow1 (10s)
          1/1 Flows SUCCESS in 10s
        LOG

        allow(File).to receive(:zero?).with(input_file).and_return(false)
        allow(File).to receive(:open).with(input_file).and_yield(StringIO.new(log_content))

        result = helper.parse_instrumentation_logs(input_file, testname, device_id, callback_file, test_framework)

        expect(result[:status]).to eq('SUCCESS')
        expect(result[:completed_flows]).to eq(1)
        expect(result[:total_flows]).to eq(1)
        expect(result[:duration]).to eq('10s')
        expect(result[:subflows].size).to eq(1)
        expect(result[:subflows].first[:flow_name]).to eq('flow1')
        expect(result[:subflows].first[:status]).to eq('SUCCESS')
      end
    end

    context 'when an error occurs during parsing' do
      it 'logs the error and returns an empty hash' do
        allow(File).to receive(:zero?).with(input_file).and_return(false)
        allow(File).to receive(:open).with(input_file).and_raise(StandardError.new('Unexpected error'))

        result = helper.parse_instrumentation_logs(input_file, testname, device_id, callback_file, test_framework)

        expect(result).to eq({})
        expect(helper).to have_received(:log).twice
      end
    end
  end

  describe '#update_video_tag_in_summary_files' do
    let(:offset_file_path) { "/tmp/video_offset_files/offset_file_session_#{session_id}" }
    let(:offset_data) do
      {
        "1000" => 5,
        "2000" => 10
      }
    end
    let(:original_summary_file_content) { File.read(helper.instance_variable_get(:@summary_file)) }
    let(:original_summary_file_v2_content) { File.read(helper.instance_variable_get(:@summary_file_v2)) }

    before do
      allow(helper).to receive(:log) # Stub the log method to avoid actual logging
      allow(helper).to receive(:update_summary_file) # Stub the update_summary_file method
      helper.instance_variable_set(:@summary_file,
                                   './spec/android/maestro/helpers/fixtures/summary_file_multiple_flows')
      helper.instance_variable_set(:@summary_file_v2,
                                   './spec/android/maestro/helpers/fixtures/summary_file_multiple_flows_v2')
    end
    context 'when no offset data is available' do
      it 'returns early without making changes' do
        helper.update_video_tag_in_summary_files

        expect(helper).not_to have_received(:update_summary_file)
      end
    end

    context 'when MediaProjection is disabled' do
      it 'does not update video tags' do
        helper.instance_variable_set(:@summary_data, { 'use_rtc_app' => 'v1' })
        helper.instance_variable_set(:@summary_data_v2, { 'use_rtc_app' => 'v1' })

        helper.update_video_tag_in_summary_files

        expect(helper).not_to have_received(:update_summary_file)
      end
    end

    after do
      File.write(helper.instance_variable_get(:@summary_file), original_summary_file_content)
      File.write(helper.instance_variable_get(:@summary_file_v2), original_summary_file_v2_content)
    end
  end

  describe '#generate_flow_name' do
    context 'when flow path contains the test suite unzip path' do
      it 'removes the test suite path and file extension' do
        flow = "/tmp/#{session_id}_test_suite/flow1/test.yaml"

        result = helper.generate_flow_name(flow, session_id)

        expect(result).to eq('test')
      end
    end

    context 'when flow path has multiple directory levels' do
      it 'preserves the directory structure after test suite path' do
        flow = "/tmp/#{session_id}_test_suite/dir1/dir2/test.yaml"

        result = helper.generate_flow_name(flow, session_id)

        expect(result).to eq('dir2/test')
      end
    end

    context 'when flow path has no directory structure after test suite path' do
      it 'returns just the filename without extension' do
        flow = "/tmp/#{session_id}_test_suite/test/test.yaml"

        result = helper.generate_flow_name(flow, session_id)

        expect(result).to eq('test')
      end
    end

    context 'when flow path does not contain the test suite path' do
      it 'returns the original path without extension' do
        flow = "/tmp/#{session_id}_test_suite/other/path/test.yaml"

        result = helper.generate_flow_name(flow, session_id)

        expect(result).to eq('path/test')
      end
    end

    context 'when flow path has no file extension' do
      it 'returns the path without modifications' do
        flow = "/tmp/#{session_id}_test_suite/flow1/test"

        result = helper.generate_flow_name(flow, session_id)

        expect(result).to eq('test')
      end
    end
  end

  after do
    File.write(helper.instance_variable_get(:@summary_file), original_summary_file_content)
    File.write(helper.instance_variable_get(:@summary_file_v2), original_summary_file_v2_content)
  end
end
