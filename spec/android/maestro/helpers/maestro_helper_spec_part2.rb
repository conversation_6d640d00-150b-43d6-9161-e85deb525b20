require_relative "../../../../android/maestro/helpers/maestro_helper"
require_relative "../../../../spec/spec_helper"
require 'json'
require 'pry'
require 'os'
require 'fileutils'
require_relative '../../../../android/constants'

describe MaestroHelper do
  let(:helper) { MaestroHelper.new('./spec/android/maestro/helpers/fixtures/summary_file_queued') }
  let(:build_id) { '44f4d5ca581c53cef84efc7f0fc498e7b14b2322' }
  let(:session_id) { '5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd' }
  let(:screenshots_zip_file) { '/tmp/screenshots.zip' }
  let(:base_s3url) { 'https://s3.amazonaws.com/test-bucket' }
  let(:baseurl) { 'https://s3.amazonaws.com/test-bucket' }
  let(:aws_key) { 'test-aws-key' }
  let(:aws_secret) { 'test-aws-secret' }
  let(:unique_test_id) { '5f2a8c0420e0679917a7fea533e2b36ae5b5e3dd0e2de3d8' }
  let(:output_folder_path) { "/tmp/#{session_id}_test_suite/logs/#{unique_test_id}/screenshots" }
  let(:zip_file_path) { File.join(output_folder_path, 'screenshots.zip') }
  let(:ss_url) { "#{baseurl}/#{session_id}/" }
  let(:s3_url) { "#{baseurl}/#{unique_test_id}/#{unique_test_id}-screenshots.zip" }
  let(:testname) { '/TYPE_3/master.yml' }
  let(:flowname) { 'TYPE_3/master' }

  let(:original_summary_file_content) { File.read(helper.instance_variable_get(:@summary_file)) }
  let(:original_summary_file_v2_content) { File.read(helper.instance_variable_get(:@summary_file_v2)) }

  before do
    allow(helper).to receive(:log) # Stub the log method to avoid actual logging
    allow(helper).to receive(:zombie_push) # Stub the zombie_push method
    allow(helper).to receive(:upload_log_to_s3) # Stub the upload_log_to_s3 method
  end

  describe '#update_tests_status_to_running' do
    before do
      # Stub the log method to avoid actual logging
      # Set up initial summary data
      helper.instance_variable_set(:@summary_file, './spec/android/maestro/helpers/fixtures/summary_file_queued')
      helper.instance_variable_set(:@summary_file_v2, './spec/android/maestro/helpers/fixtures/summary_file_v2_queued')
    end
    context 'when the test exists and status is QUEUED' do
      it 'updates the test status to RUNNING in both summary files' do
        # Read the original file content before the test
        original_summary_file_content = File.read(helper.instance_variable_get(:@summary_file))
        original_summary_file_v2_content = File.read(helper.instance_variable_get(:@summary_file_v2))

        begin
          # Perform the test
          helper.update_tests_status_to_running(testname, session_id)

          # Check @summary_data
          summary_data = helper.instance_variable_get(:@summary_data)
          puts summary_data
          expect(summary_data['test_details'][flowname][flowname]['status']).to eq('RUNNING')
          expect(summary_data['test_status']['QUEUED']).to eq(0)
          expect(summary_data['test_status']['RUNNING']).to eq(1)

          # Check @summary_data_v2
          summary_data_v2 = helper.instance_variable_get(:@summary_data_v2)
          expect(summary_data_v2['classes'][flowname]['tests'][flowname]['status']).to eq('running')
          expect(summary_data_v2['classes'][flowname]['tests_summary']['queued']).to eq(0)
          expect(summary_data_v2['classes'][flowname]['tests_summary']['running']).to eq(1)
          expect(summary_data_v2['test_summary']['queued']).to eq(0)
          expect(summary_data_v2['test_summary']['running']).to eq(1)
        ensure
          # This block will run regardless of whether the test passes or fails
          # Restore the original file content after the test
          File.write(helper.instance_variable_get(:@summary_file), original_summary_file_content)
          File.write(helper.instance_variable_get(:@summary_file_v2), original_summary_file_v2_content)
        end
      end
    end

    context 'when the test does not exist' do
      it 'does not make any changes' do
        original_summary_file_content = File.read(helper.instance_variable_get(:@summary_file))
        original_summary_file_v2_content = File.read(helper.instance_variable_get(:@summary_file_v2))
        begin
          non_existent_testname = '/tmp/test-session-id_test_suite/non_existent_flow'
          helper.update_tests_status_to_running(non_existent_testname, session_id)

          # Check @summary_data
          summary_data = helper.instance_variable_get(:@summary_data)
          expect(summary_data['test_status']['QUEUED']).to eq(1)
          expect(summary_data['test_status']['RUNNING']).to eq(0)

          # Check @summary_data_v2
          summary_data_v2 = helper.instance_variable_get(:@summary_data_v2)
          expect(summary_data_v2['test_summary']['queued']).to eq(1)
          expect(summary_data_v2['test_summary']['running']).to eq(0)
        ensure
          File.write(helper.instance_variable_get(:@summary_file), original_summary_file_content)
          File.write(helper.instance_variable_get(:@summary_file_v2), original_summary_file_v2_content)
        end
      end
    end
  end

  describe '#update_test_summary' do
    let(:start_time) { Time.now.to_i }
    let(:ss_url) { "#{baseurl}/#{session_id}/#{unique_test_id}/" }
    let(:video_url) { 'https://s3.amazonaws.com/test-bucket/video.mp4#t=4,134' }
    let(:result) do
      {
        status: 'SUCCESS',
        duration: '10s',
        subflows: [],
        stacktrace: ''
      }
    end

    before do
      allow(helper).to receive(:update_summary_file) # Stub the update_summary_file method
    end

    context 'when the test exists in summary data' do
      it 'updates the test status, duration, and logs' do
        helper.update_test_summary(session_id, unique_test_id, testname, result, baseurl, start_time, video_url,
                                   baseurl)

        # Check @summary_data
        summary_data = helper.instance_variable_get(:@summary_data)
        test_object = summary_data['test_details'][flowname][flowname]
        expect(test_object['status']).to eq('SUCCESS')
        expect(test_object['duration']).to eq('10s')
        expect(test_object['video']).to eq(video_url)
        expect(test_object['screenshots']).to include(s3_url)
        expect(test_object['start_time']).to eq(Time.at(start_time))

        # Check @summary_data_v2
        summary_data_v2 = helper.instance_variable_get(:@summary_data_v2)
        test_object_v2 = summary_data_v2['classes'][flowname]['tests'][flowname]
        expect(test_object_v2['status']).to eq('passed')
        expect(test_object_v2['duration']).to eq('10s')
        expect(test_object_v2['video']).to eq(video_url.split('#t=').last)
        expect(test_object_v2['screenshots']).to include(s3_url)
        expect(test_object_v2['start_time']).to eq(Time.at(start_time))
      end
    end

    context 'when the test does not exist in summary data' do
      it 'logs an error and does not update the summary' do
        non_existent_testname = '/tmp/test-session-id_test_suite/non_existent_flow'
        helper.update_test_summary(session_id, unique_test_id, non_existent_testname, result, ss_url, start_time,
                                   video_url, ss_url)

        old_data = JSON.parse(original_summary_file_content)
        # Verify that no changes were made
        summary_data = helper.instance_variable_get(:@summary_data)
        expect(summary_data['test_status']['SUCCESS']).to eq(old_data['test_status']['SUCCESS'])
        expect(summary_data['test_status']['RUNNING']).to eq(old_data['test_status']['RUNNING'])
        expect(summary_data['test_status']['TIMEDOUT']).to eq(old_data['test_status']['TIMEDOUT'])
      end
    end

    context 'when the test times out' do
      it 'updates the test status to TIMEDOUT' do
        timeout_result = result.merge(status: 'TIMEDOUT')
        helper.update_test_summary(session_id, unique_test_id, testname, timeout_result, ss_url, start_time, video_url,
                                   ss_url)

        # Check @summary_data
        summary_data = helper.instance_variable_get(:@summary_data)
        test_object = summary_data['test_details'][flowname][flowname]
        expect(test_object['status']).to eq('TIMEDOUT')

        # Check @summary_data_v2
        summary_data_v2 = helper.instance_variable_get(:@summary_data_v2)
        test_object_v2 = summary_data_v2['classes'][flowname]['tests'][flowname]
        expect(test_object_v2['status']).to eq('timedout')
      end
    end

    context 'when an error occurs during execution' do
      it 'logs the error and does not raise an exception' do
        allow(helper).to receive(:update_tests_status).and_raise(StandardError.new('Unexpected error'))

        expect do
          helper.update_test_summary(session_id, unique_test_id, testname, result, ss_url, start_time, video_url,
                                     ss_url)
        end.not_to raise_error

        expect(helper).to have_received(:log).with(/Unexpected error/, severity: 'ERROR')
      end
    end
  end

  after do
    File.write(helper.instance_variable_get(:@summary_file), original_summary_file_content)
    File.write(helper.instance_variable_get(:@summary_file_v2), original_summary_file_v2_content)
  end
end
