require_relative "../../../../android/maestro/scripts/maestro_runner"
require_relative "../../../../spec/spec_helper"
require_relative "../../../../common/push_to_zombie"
require 'pry'
require 'os'
require 'fileutils'
require_relative '../../../../android/constants'

describe Maestro::SessionRunner do
  let(:session_runner) do
    described_class.new(
      'test-device-id', 'test-device-model',
      'test-device-name', 'test-device-version',
      'maestro', './spec/android/maestro/scripts/fixtures/test_suite.zip',
      './spec/android/maestro/scripts/fixtures/instrumentation',
      './spec/android/maestro/scripts/fixtures/session_summary',
      './spec/android/maestro/scripts/fixtures/callback_file'
    )
  end

  let(:test_suite_filename) { './spec/android/maestro/scripts/fixtures/test_suite_file' }
  let(:test_suite_file_path) { '/tmp/maestro_testsuite_test-device-id' }
  let(:debug_output_path) { './spec/android/maestro/scripts/fixtures/test_session_id_test_suite/logs' }
  let(:instrumentation_logs_file_path) { './spec/android/maestro/scripts/fixtures/instrumentation' }

  before do
    allow(session_runner).to receive(:log) # Stub the log method to avoid actual logging
    allow(session_runner).to receive(:unzip_test_suite_folder) # Stub the unzip method
    allow(session_runner).to receive(:create_files) # Stub the create_files method
    # Stub the update_summary_and_callback_file method
    allow(session_runner).to receive(:update_summary_and_callback_file)
    allow(session_runner).to receive(:check_if_testsuite_parse_empty) # Stub the check_if_testsuite_parse_empty method
    allow(File).to receive(:read).and_return('{}') # Stub file reads
  end

  describe '#dry_run' do
    before do
      allow(session_runner).to receive(:execute_dry_run) # Stub the execute_dry_run method
    end
    context 'when the dry run is successful' do
      it 'unzips the test suite, creates files, executes the dry run, and updates the summary' do
        session_runner.dry_run(test_suite_filename)

        expect(session_runner).to have_received(:unzip_test_suite_folder).with(
          './spec/android/maestro/scripts/fixtures/test_suite.zip'
        )
        expect(session_runner).to have_received(:create_files).with(test_suite_file_path)
        expect(session_runner).to have_received(:execute_dry_run)
        expect(session_runner).to have_received(:update_summary_and_callback_file)
        expect(session_runner).to have_received(:check_if_testsuite_parse_empty).with(test_suite_filename)
      end
    end

    context 'when the dry run fails due to missing execution plan' do
      it 'retries the dry run and logs the retry reason' do
        allow(session_runner).to receive(:execute_dry_run).and_raise(StandardError.new('Execution Plan Missing'))

        expect do
          session_runner.dry_run(test_suite_filename)
        end.to raise_error(StandardError, 'Execution Plan Missing')
        expect(session_runner).to have_received(:log).with(/Executing Dry Run/)
      end
    end

    context 'when an error occurs during the dry run' do
      it 'logs the error and writes it to the exit file' do
        allow(session_runner).to receive(:execute_dry_run).and_raise(StandardError.new('Unexpected error'))

        expect do
          session_runner.dry_run(test_suite_filename)
        end.to raise_error(StandardError, 'Unexpected error')
      end
    end
  end

  describe '#execute_dry_run' do
    before do
      allow(OSUtils).to receive(:execute).and_return(['', double(success?: true)]) # Stub OSUtils.execute
      allow(File).to receive(:read).and_return('') # Stub file reads
      allow(File).to receive(:write) # Stub file writes
      allow(File).to receive(:basename).and_return("abc") # Stub file writes
      allow(session_runner).to receive(:start_maestro_monitor_script)
      allow(session_runner).to receive(:stop_maestro_monitor_script)
    end
    context 'when the dry run is successful' do
      it 'executes the dry run command and logs the output' do
        allow(File).to receive(:read).and_return('Execution Plan')
        session_runner.execute_dry_run
        expect(session_runner).to have_received(:log).with(/dry_run_output/)
      end
    end

    context 'when the dry run fails due to empty output' do
      it 'retries the dry run and logs the retry reason' do
        allow(File).to receive(:read).and_return('')

        session_runner.execute_dry_run

        expect(session_runner).to have_received(:log).with(/dry-run-empty/).thrice
      end
    end

    context 'when the dry run fails due to missing execution plan' do
      it 'retries the dry run and logs the retry reason' do
        allow(File).to receive(:read).and_return('Some other output')

        session_runner.execute_dry_run
        expect(session_runner).to have_received(:log).with(/dry-run-crashed/).thrice
      end
    end

    context 'when an error occurs during execution' do
      it 'logs the error and does not raise an exception' do
        allow(OSUtils).to receive(:execute).and_raise(StandardError.new('Unexpected error'))

        expect { session_runner.execute_dry_run }.not_to raise_error
        expect(session_runner).to have_received(:log).with(/Unexpected error/, severity: 'ERROR')
      end
    end
  end

  describe '#invoke_test' do
    context 'when the test invocation is successful' do
      it 'parses arguments and calls run_test with the correct parameters' do
        allow(session_runner).to receive(:run_test)

        session_runner.invoke_test('test_name', 'unique_test_id', '300', '1', '3')

        expect(session_runner).to have_received(:run_test).with(
          '300',
          'test_name',
          retry_attempt: 1,
          max_test_retry_count: 3
        )
      end
    end

    context 'when an error occurs during test invocation' do
      it 'logs the error and does not raise an exception' do
        allow(session_runner).to receive(:run_test).and_raise(StandardError.new('Unexpected error'))

        expect { session_runner.invoke_test('test_name', 'unique_test_id', '300') }.to raise_error(
          StandardError, 'Unexpected error'
        )
      end
    end
  end

  describe '#run_test' do
    before do
      Maestro::SessionRunner.send(:public, :run_test)
    end
    context 'when the test execution is successful' do
      it 'executes the test command and returns success' do
        allow(session_runner).to receive(:execute_maestro_command).and_return('success')

        result = session_runner.run_test('300', 'test_name')

        expect(result).to eq('success')
        expect(session_runner).to have_received(:execute_maestro_command).with('300', 'test_name')
      end
    end

    context 'when the test fails and retries' do
      it 'retries the test if it fails and the instrumentation logs are empty' do
        allow(session_runner).to receive(:execute_maestro_command).and_return('failed')
        allow(File).to receive(:empty?).and_return(true)

        result = session_runner.run_test('300', 'test_name', retry_attempt: 0, max_test_retry_count: 2)

        expect(result).to eq('failed')
        expect(session_runner).to have_received(:execute_maestro_command).exactly(3).times
      end
    end

    context 'when an error occurs during test execution' do
      it 'logs the error and does not raise an exception' do
        allow(session_runner).to receive(:execute_maestro_command).and_raise(StandardError.new('Unexpected error'))

        result = session_runner.run_test('300', 'test_name')

        expect(result).to be_nil
        expect(session_runner).to have_received(:log).with(/Unexpected error/, severity: 'ERROR')
      end
    end

    after do
      Maestro::SessionRunner.send(:private, :run_test)
    end
  end
end