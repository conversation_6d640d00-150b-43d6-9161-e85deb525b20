<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.android.browserstack">
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.UPDATE_DEVICE_STATE"/>
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.GET_TASKS"/>
    <uses-permission android:name="android.permission.SET_WALLPAPER"/>
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <application
        android:allowBackup="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:usesCleartextTraffic="true"
        android:theme="@style/AppTheme">
    <!--tools:replace="android:appComponentFactory">android.support.v4.app.CoreComponentFactory-->
        <uses-library android:name="org.apache.http.legacy" android:required="false" />
        <!--<activity android:name=".main.BrowserStack" android:exported="true"/>-->
        <activity android:name=".main.BrowserStack"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".main.ProxyActivity" android:exported="true"/>
        <activity android:name=".main.UnlockScreen" android:exported="true" />
        <activity android:name=".main.CleanupActivity" android:exported="true" />
        <activity android:name=".main.RootChecker" android:exported="true" />
        <activity android:name=".main.RootCheckerNonRooted" android:exported="true"
            tools:ignore="DuplicateActivity" />
        <activity android:name=".main.PreloadContactsActivity" android:exported="true" />

        <service android:name=".services.GraphitePushService" >
        </service>
        <service android:name=".services.NetworkService" android:exported="true" >
        </service>
        <!-- <service android:name=".services.AppLockService" android:exported="true" >
         </service>-->
        <service android:name=".services.OrientationService" android:exported="true">
        </service>
        <service android:name=".services.ClipboardService" android:exported="true">
        </service>
        <service android:name=".services.LocationMockService" android:exported="true">
        </service>
        <service android:name=".services.WifiHandlerService" android:exported="true">
        </service>
        <service android:name=".services.BluetoothHandlerService" android:exported="true">
        </service>
        <service android:name=".services.TelephonyHandlerService" android:exported="true">
        </service>
        <service android:name=".services.ClearCallLogsService" android:exported="true">
        </service>
        <service android:name=".services.TelephonyCleanupService" android:exported="true">
        </service>

        <receiver android:name="com.android.browserstack.receiver.BrowserStackReceiver" android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.android.browserstack.receiver.AlarmReceiver">
        </receiver>

        <receiver android:name=".receiver.LocaleReceiver"
            android:exported="true"
            android:permission="android.permission.CHANGE_CONFIGURATION" />

        <receiver android:name="com.android.browserstack.receiver.WifiBroadcastReceiver" android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.android.browserstack.receiver.InstrumentOutgoingCalls"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.intent.action.NEW_OUTGOING_CALL" />
            </intent-filter>
        </receiver>

        <receiver  android:name="com.android.browserstack.receiver.IncomingCallsReceiver" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.PHONE_STATE" />
            </intent-filter>
        </receiver>

    </application>

</manifest>
