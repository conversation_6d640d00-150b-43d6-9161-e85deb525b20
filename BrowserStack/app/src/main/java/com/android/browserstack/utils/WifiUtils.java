package com.android.browserstack.utils;

import android.net.wifi.WifiManager;
import android.content.Context;
import android.util.Log;

public class WifiUtils {
    private static final String TAG = "BrowserStack-" + WifiUtils.class.getCanonicalName();

    public static void setWifiState(Context context, boolean state) {
        Log.i(TAG, "Setting Wifi state to: " + state);
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        wifiManager.setWifiEnabled(state);
    }
}
