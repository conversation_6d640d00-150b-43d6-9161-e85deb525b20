package com.android.browserstack.services;

import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.provider.Settings;
import android.util.Log;

import com.android.browserstack.utils.OrientationUtil;

public class OrientationService extends Service {

    private static final String TAG = "BrowserStack-" + OrientationService.class.getCanonicalName();

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String orientation;

            Log.i(TAG, "Request for Orientation service");
            if (intent.getStringExtra("orientation") != null)
                orientation = intent.getStringExtra("orientation");
            else
                orientation = "portrait";

            /* Since API 23, the apks don't have default access to write settings.
            * We now have a UIAutomation in place to enable write settings for this app
            * upon installation */

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ) {
                boolean retVal = false;
                retVal = Settings.System.canWrite(this);
                if(retVal)
                    OrientationUtil.setOrientation(this, orientation);
                else
                    Log.e(TAG, "Write settings not enabled for this app");
            }
            else
                OrientationUtil.setOrientation(this, orientation);
        } else {
            stopForeground(true);
            stopSelf();
        }
        stopSelf();
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}
