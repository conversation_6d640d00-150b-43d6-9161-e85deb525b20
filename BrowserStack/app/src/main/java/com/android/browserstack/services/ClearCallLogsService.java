package com.android.browserstack.services;

import android.app.Service;
import android.content.Intent;
import android.util.Log;
import android.os.IBinder;
import android.content.ContentResolver;
import android.provider.CallLog;


public class ClearCallLogsService extends Service {
    private static final String TAG = "BrowserStack-" + ClearCallLogsService.class.getCanonicalName();

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "Clearing Call Logs");
        ContentResolver contentResolver = getApplicationContext().getContentResolver();
        contentResolver.delete(CallLog.Calls.CONTENT_URI, null, null);
        stopSelf();
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}
