package com.android.browserstack.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.android.browserstack.services.InventoryAPIService;

/* This isn't in use now, because the inventory service has been deprecated. At some point, we will move this to the new inventory service by hosting team */
public class AlarmReceiver extends BroadcastReceiver {
    private InventoryAPIService inventory;

    @Override
    public void onReceive(Context context, Intent intent) {
        inventory = new InventoryAPIService();
        inventory.checkInventory();
    }
}
