package com.android.browserstack.services;

import java.io.IOException;
import java.io.InputStream;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;

public class ScreenshotService extends Service {

	private final String TAG = "BrowserStack-Screenshot";

	public static Intent intent;

	@Override
	public IBinder onBind(Intent intent) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public int onStartCommand(Intent intent, int flags, int startId) {
		this.intent = intent;
		// TODO This is where the action happens
//		new ScreenshotThread().start();
		Log.i(TAG, "State changed");
		stopSelf();
		return Service.START_STICKY;
	}

	@Override
	public void onDestroy() {
		Log.i(TAG, "Terminating hook");
	}

	private class ScreenshotThread extends Thread {
		private int counter = 0;
		@Override
		public void run() {
			while (true) {
				takeScreenshot();
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}

		private void takeScreenshot() {
			try {
				Log.i(TAG, "Taking Screenshot");
				Process process = Runtime.getRuntime().exec("sh /sdcard/shell.sh "+ (counter++));
				
				String str = "";
				
				int i = -1;
				InputStream in = process.getInputStream();
				while((i = in.read()) != -1) {
					str += (char) i;
				}
				process.waitFor();
				Log.i(TAG, str);
			} catch (IOException e) {
				Log.i(TAG, e.getMessage());
				e.printStackTrace();
			} catch (InterruptedException e) {
				Log.i(TAG, e.getMessage());
				e.printStackTrace();
			}
		}
	}
}
