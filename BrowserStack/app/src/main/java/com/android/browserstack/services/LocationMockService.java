package com.android.browserstack.services;

import android.app.Service;
import android.content.Intent;
import android.content.Context;
import android.app.AppOpsManager;
import android.os.Bundle;
import android.os.IBinder;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.location.Criteria;
import android.location.Location;
import android.location.LocationManager;
import android.location.LocationProvider;
import android.os.SystemClock;
import android.provider.Settings;
import android.util.Log;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

/**
 * Created by umair on 02/08/16.
 */
public class LocationMockService extends Service {

    private static LocationMockService mInstance;
    private boolean stopThread;
    private Thread currentThread;
    private static final String GPS_PROVIDER = "gps";
    private static final String NETWORK_PROVIDER = "network";
    private static final String TAG = LocationMockService.class.getCanonicalName();

    private synchronized boolean isStopping() {
        return stopThread;
    }

    private synchronized void setStopping(boolean val) {
        stopThread = val;
        if (val){
            if(currentThread != null){
                currentThread.interrupt();
            }
        }
    }


    @Override
    public IBinder onBind(Intent intent) {
        // TODO Auto-generated method stub
        return null;
    }

     @Override
     public void onDestroy(){
         setStopping(true);
         super.onDestroy();
     }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            double lat = Double.parseDouble(intent.getStringExtra("lat"));
            double lon = Double.parseDouble(intent.getStringExtra("lon"));
            Log.i(TAG,"Mocking geolocation to cooridnates: "+ lat + ", "+ lon);

            if( android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.KITKAT){
                setStopping(true);
                try{
                    Thread.sleep(100);
                }
                catch(InterruptedException ex){
                    Log.i(TAG, ex.getMessage());
                }
            }
            setStopping(false);
            mock(this, lat, lon);
        }
        stopSelf();
        return Service.START_STICKY;
    }

    public static LocationMockService getInstance() {
        if (mInstance == null) {
            mInstance = new LocationMockService();
        }
        return mInstance;
    }

    public void mock(final Context context, final double lat, final double lon) {
        currentThread = new Thread(new Runnable() {
            @Override
            public void run() {
                while (!isStopping()) {
                    mockUtil(context, GPS_PROVIDER, lat, lon);
                    mockUtil(context, NETWORK_PROVIDER, lat, lon);
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        Log.i(TAG,"Thread for coordinates " + lat + ", " + lon + " has been interrupted. Exiting the loop.");
                        break;
                    }
                }
            }
        });
        currentThread.start();
    }

    public void mockUtil(Context context, String provider, double lat, double lon) {

        //Commented out because I found an easier way
        /*
        if (android.os.Build.VERSION.SDK_INT >= 23) {
            try {
                enableMockPermission(context, context.getPackageName());
            } catch (PackageManager.NameNotFoundException e) {
                e.printStackTrace();
            }
        }
        */
        LocationManager mLocationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        try {
          try {
            mLocationManager.removeTestProvider(provider);
          }
          catch(IllegalArgumentException e) {
            e.printStackTrace();
          }

          if (android.os.Build.VERSION.SDK_INT >= 28) {
              mLocationManager.addTestProvider(provider, false, false,
                                               false, false, true, true, true, 1, 1);
          } else {
              mLocationManager.addTestProvider(provider, false, false,
                                               false, false, true, true, true, 0, 5);
          }

          Location newLocation = new Location(provider);
          newLocation.setLatitude(lat);
          newLocation.setLongitude(lon);
          newLocation.setAccuracy(5);
          newLocation.setTime(System.currentTimeMillis());
          newLocation.setElapsedRealtimeNanos(SystemClock.elapsedRealtimeNanos());

          mLocationManager.setTestProviderEnabled(provider, true);
          mLocationManager.setTestProviderStatus(provider, LocationProvider.AVAILABLE,null, System.currentTimeMillis());
          mLocationManager.setTestProviderLocation(provider, newLocation);
          Log.i(TAG,"Updated " + provider + " provider to cooridnates: "+ lat + ", "+ lon);
        } catch (SecurityException e) {
            Log.e(TAG,"Mock Location failed: insufficient permissions.");
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void enableMockPermission(Context context, String packageName) throws PackageManager.NameNotFoundException {
        AppOpsManager appOpsManager = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
        ApplicationInfo ai = context.getPackageManager().getApplicationInfo(
                context.getPackageName(), PackageManager.GET_DISABLED_COMPONENTS);

        try {
            Method setMode = appOpsManager.getClass().getMethod("setMode", new Class[] {Integer.TYPE, Integer.TYPE, String.class, Integer.TYPE});
            setMode.invoke(appOpsManager, new Object[] {58, ai.uid, packageName, AppOpsManager.MODE_ALLOWED});
            return;
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }
}
