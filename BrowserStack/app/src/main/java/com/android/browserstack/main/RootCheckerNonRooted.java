package com.android.browserstack.main;
import com.scottyab.rootbeer.RootBeer;

public class RootCheckerNonRooted extends RootChecker {
    @Override
    public String isRooted() {
        RootBeer rootBeer = new RootBeer(this);
        rootBeer.setLogging(true);
        String result =  super.isRooted();
        if (result == null) {
            result = "";
        }
        // performing this check separately as unroot is not needed in non-rooted devices and we can scan thoroughly
        if (rootBeer.checkForRWPaths()) {
            result += "rwPaths ";
        }

        if (result.isEmpty()) {
            return null;
        } else {
            return result.trim();
        }
    }
}
