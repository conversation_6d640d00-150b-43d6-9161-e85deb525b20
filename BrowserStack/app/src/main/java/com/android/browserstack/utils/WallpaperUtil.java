package com.android.browserstack.utils;

import java.io.File;
import java.io.FileOutputStream;

import android.app.WallpaperManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.os.Environment;
import android.util.Log;

public class WallpaperUtil{
	private static String wpath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/wall.png";
	private static WallpaperManager wm;
	
	public static void setWall(Context context){
		wm = WallpaperManager.getInstance(context);
		
        try {
            wm.setBitmap(BitmapFactory.decodeFile(wpath));
        } catch (Exception e) {
            e.printStackTrace();
            Log.e("File", e.getMessage());
        }
	}
	
	public static void getWall(Context context){
		wm = WallpaperManager.getInstance(context);

		final Drawable drawable = wm.getDrawable();
		Bitmap bitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight(), Config.ARGB_8888);
	    Canvas canvas = new Canvas(bitmap); 
	    drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
	    drawable.draw(canvas);
	    
	    FileOutputStream out;
		try {
			out = new FileOutputStream(wpath);
			bitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
		} catch (Exception e) {
            e.printStackTrace();
            Log.e("File", e.getMessage());
        }
	}
}