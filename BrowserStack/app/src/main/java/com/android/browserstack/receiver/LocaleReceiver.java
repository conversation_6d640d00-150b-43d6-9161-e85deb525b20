package com.android.browserstack.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import java.util.Locale;

public class LocaleReceiver extends BroadcastReceiver {

    // TODO: Need to remove this file completely. Receivers take time.
    // Android system does not deliver broadcasts immediately to receivers.
    // Moved this code to NetworkService. Use that this will be removed once APK version 4.8 is deployed to production.
    private final String TAG = "BrowserStack-Locale";

    @Override
    public void onReceive(Context context, Intent intent) {
        try {
            String locale = intent.getStringExtra("l");
            Log.i(TAG, "Locale=" + locale);
            Class.forName("com.android.internal.app.LocalePicker")
                .getMethod("updateLocale", Locale.class)
                .invoke(null, parseLocale(locale));
        } catch (Exception e) {
            Log.wtf(TAG, e);
        }
    }

    private static Locale parseLocale(String locale) {
        String[] parts = locale.replace('-', '_').split("_");
        switch (parts.length) {
        case 0:
            return Locale.US;
        case 1:
            return new Locale(parts[0]);
        default:
            return new Locale(parts[0], parts[1]);
        }
    }
}
