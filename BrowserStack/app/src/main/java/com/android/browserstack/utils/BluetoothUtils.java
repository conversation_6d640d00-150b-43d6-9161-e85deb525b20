package com.android.browserstack.utils;

import android.bluetooth.BluetoothAdapter;
import android.util.Log;

public class BluetoothUtils {
    private static final String TAG = "BrowserStack-" + BluetoothUtils.class.getCanonicalName();

    public static void setBluetoothState(boolean state) {
        Log.i(TAG, "Setting Bluetooth state to: " + state);
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (bluetoothAdapter != null) {
            if (state) {
                bluetoothAdapter.enable();
            }
            else {
                bluetoothAdapter.disable();
            }
            return;
        }
        Log.w(TAG, "Bluetooth not supported on this device");
    }
}
