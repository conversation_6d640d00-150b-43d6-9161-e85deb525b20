package com.android.browserstack.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;
import com.android.browserstack.utils.NetworkServiceUtils;

import java.util.Arrays;
import java.util.List;

public class WifiBroadcastReceiver extends BroadcastReceiver {

    private final String TAG = "BrowserStack-Wifi";
    private final List<String> WHITELISTED_WIFI_SSIDS = Arrays.asList("BLT", "BLT iOS", "BrowserStack Android", "BrowserStack", "BrowserStackGuest", "BLT_PVT_NETWORK", "WeWork", "WWGuest");

    @Override
    public void onReceive(Context context, Intent intent) {
        ConnectivityManager conMan = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo netInfo = conMan.getActiveNetworkInfo();
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);

        if (netInfo != null && netInfo.getType() == ConnectivityManager.TYPE_WIFI) {
            log("Already enabled");
        }
        else if (!NetworkServiceUtils.wifiDisabledByUser()){
            log("Wifi was disabled, running setWifiEnabled...");
            wifiManager.setWifiEnabled(true);
        }

        if (netInfo != null && netInfo.getType() == ConnectivityManager.TYPE_WIFI && netInfo.isConnected()) {
            WifiInfo info = wifiManager.getConnectionInfo();
            String ssid = info.getSSID();
            for (int i = 0; i < 5; i++) {
                if (ssid != null && !ssid.equals("<unknown ssid>")) {
                    break;
                }
                try {
                    log("Waiting for ssid...");
                    Thread.sleep(1000);
                    info = wifiManager.getConnectionInfo();
                    ssid = info.getSSID();
                }
                catch (InterruptedException e) {
                }
            }

            if (!check_whitelisted(ssid)) {
                log(ssid + " Not whitelisted network saved. Disconnecting");
                int current_networkId = info.getNetworkId();
                if (!(wifiManager.removeNetwork(current_networkId) && wifiManager.saveConfiguration())) {
                    List<WifiConfiguration> list = wifiManager.getConfiguredNetworks();
                    for (WifiConfiguration wifinetwork : list) {
                        if (check_whitelisted(wifinetwork.SSID)) {
                            log("Connecting to whitelisted ssid - " + wifinetwork.SSID);
                            int networkId = wifinetwork.networkId;
                            wifiManager.disconnect();
                            wifiManager.enableNetwork(networkId, true);
                            break;
                        }
                    }
                }
            }
        }
    }

    private void log(String logLine) {
        Log.i(TAG, logLine);
    }

    private Boolean check_whitelisted(String ssid) {
        String trimmed_ssid = ssid.replaceAll("^\"|\"$", "");
        Boolean is_whitelisted = WHITELISTED_WIFI_SSIDS.contains(trimmed_ssid);
        return is_whitelisted;
    }
}
