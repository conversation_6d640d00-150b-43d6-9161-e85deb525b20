package com.android.browserstack.main;

import java.io.FileOutputStream;

import com.scottyab.rootbeer.RootBeer;

import android.app.Activity;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;

/*
 * RootBeer library is based off https://github.com/scottyab/rootbeer.
 * Library is of .aar format (Android Library project) which does not work with ANT/ADT-Eclipse.
 * To integrate this into our project
 *  1. Download .aar
 *  2. Rename it to .zip
 *  3. Unzip and copy classes.jar to project libs folder.
 *  4. Copy the jni abi folders to libs folder (as this project also includes .so files)
 *  5. This project did not have any relevant resource files and hence no copying of resources.
 */
public class RootChecker extends Activity {
	private static final String TAG = "BrowserStack-" + RootChecker.class.getName();
	private static final String rootStatusFile = Environment.getExternalStorageDirectory().getAbsolutePath() + "/is_rooted";
	private static final String ZOMBIE = "zombie";
	
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		Log.i(TAG, "Starting root checker");
		new WriteToFile().execute(isRooted());
	}
	
	// if not rooted returns null
	// if rooted returns space separated reason list
	public String isRooted() {
		String result = "";
		// Not using isRooted() api of RootBeer Library, as our devices always fail the isDangerousProps check (eg: ro.debuggable=1)
		RootBeer rootBeer = new RootBeer(this);
		rootBeer.setLogging(true);
		if (rootBeer.detectRootManagementApps()) {
			result += "rootManagementApps ";
		}
		if (rootBeer.detectPotentiallyDangerousApps()) {
			result += "dangerousApps ";
		}
		if (rootBeer.checkForBinary("su")) {
			result += "su ";
		}
		if (rootBeer.checkForBinary("busybox")) {
			result += "busybox ";
		}
		if (rootBeer.detectTestKeys()) {
			result += "testKeys ";
		}
		if (rootBeer.checkSuExists()) {
			result += "suExists ";
		}
		if (rootBeer.checkForRootNative()) {
			result += "rootNative ";
		}

		if (result.isEmpty()) {
			return null;
		} else {
			return result.trim();
		}
	}
	
	private class WriteToFile extends AsyncTask<String, Void, Void> {
	 
		@Override
		protected Void doInBackground(String... params) {
			Log.i(TAG, "Writing root status to file");
			String rootReason = params[0];
			try {
				FileOutputStream outputStream = new FileOutputStream(rootStatusFile);
				if (rootReason != null) {
					outputStream.write("true\n".getBytes());
					outputStream.write(rootReason.getBytes());
				} else {
					outputStream.write("false\n".getBytes());
				}
				outputStream.close();
			} catch (Exception ex) {
				Log.i(TAG, "Exception: " + ex.getMessage());
			}
			return null;
		}

		@Override
		protected void onPostExecute(Void result) {
			Log.i(TAG, "Finish Activity");
			RootChecker.this.finish();
		}
	}
}
