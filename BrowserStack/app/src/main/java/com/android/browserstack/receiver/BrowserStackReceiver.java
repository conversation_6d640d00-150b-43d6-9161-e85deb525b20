package com.android.browserstack.receiver;

import com.android.browserstack.services.GraphitePushService;
import com.android.browserstack.services.ScreenshotService;
import com.android.browserstack.utils.NetworkServiceUtils;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.wifi.WifiManager;
import android.util.Log;

public class BrowserStackReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i("BrowserStack", "Booted");
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        if (!NetworkServiceUtils.wifiDisabledByUser()) {
            wifiManager.setWifiEnabled(true);
        }
        Intent serviceIntent = new Intent(context, GraphitePushService.class);
        context.startService(serviceIntent);
    }
}
