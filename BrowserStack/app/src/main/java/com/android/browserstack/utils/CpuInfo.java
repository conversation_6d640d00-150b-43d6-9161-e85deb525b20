package com.android.browserstack.utils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;

import android.content.Context;
import android.os.Build;
import android.util.Log;


/**
 * operate CPU information
 * 
 * <AUTHOR>
 */
public class CpuInfo {

	private static final String LOG_TAG = "BrowserStack-"
			+ CpuInfo.class.getSimpleName();

	private Context context;
	private long processCpu;
	private long idleCpu;
	private long totalCpu;
	private boolean isInitialStatics = true;
	private SimpleDateFormat formatterFile;
	private MemoryInfo mi;
	private long totalMemorySize;
	private long initialTraffic;
	private long lastestTraffic;
	private long traffic;
	private ArrayList<String> cpuUsedRatio;
	private long totalCpu2;
	private long processCpu2;
	private long idleCpu2;
	private String processCpuRatio = "";
	private String totalCpuRatio = "";
	private int pid;

	public CpuInfo(Context context, int pid, String uid) {
		this.pid = pid;
		this.context = context;
		mi = new MemoryInfo();
		totalMemorySize = mi.getTotalMemory();
		cpuUsedRatio = new ArrayList<String>();
	}
	
	public CpuInfo()
	{
		mi = new MemoryInfo();
	}

	/**
	 * read the status of CPU.
	 * 
	 * @throws FileNotFoundException
	 */
	public void readCpuStat() {
		String processPid = Integer.toString(pid);
		String cpuStatPath = "/proc/" + processPid + "/stat";
		try {
			// monitor cpu stat of certain process
			RandomAccessFile processCpuInfo = new RandomAccessFile(cpuStatPath,
					"r");
			String line = "";
			StringBuffer stringBuffer = new StringBuffer();
			stringBuffer.setLength(0);
			while ((line = processCpuInfo.readLine()) != null) {
				stringBuffer.append(line + "\n");
			}
			String[] tok = stringBuffer.toString().split(" ");
			processCpu = Long.parseLong(tok[13]) + Long.parseLong(tok[14]);
			processCpuInfo.close();
		} catch (FileNotFoundException e) {
			Log.e(LOG_TAG, "FileNotFoundException: " + e.getMessage());
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}

	}

	public float getCpuUsage()
	{
		long idleCpu1 = 0;
		long totalCpu1 = 0 ;
		long idleCpu2 = 0;
		long totalCpu2 = 0;
		float cpuUsage = 0;
		try {
			// monitor total and idle cpu stat of certain process
			RandomAccessFile cpuInfo = new RandomAccessFile("/proc/stat", "r");
			String[] toks = cpuInfo.readLine().split("\\s+");
			idleCpu1 = Long.parseLong(toks[4]);
			totalCpu1 = Long.parseLong(toks[1]) + Long.parseLong(toks[2])
					+ Long.parseLong(toks[3]) + Long.parseLong(toks[4])
					+ Long.parseLong(toks[6]) + Long.parseLong(toks[5])
					+ Long.parseLong(toks[7]);
			
			try {
				Thread.sleep(360);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			cpuInfo.seek(0);
			
			toks = cpuInfo.readLine().split("\\s+");
			idleCpu2 = Long.parseLong(toks[4]);
			totalCpu2 = Long.parseLong(toks[1]) + Long.parseLong(toks[2])
					+ Long.parseLong(toks[3]) + Long.parseLong(toks[4])
					+ Long.parseLong(toks[6]) + Long.parseLong(toks[5])
					+ Long.parseLong(toks[7]);
			cpuInfo.close();
			
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		System.out.println(totalCpu1 + " "+totalCpu2+" "+idleCpu1+" "+idleCpu2);
		cpuUsage =  (float)(totalCpu2 - totalCpu1) / ((totalCpu2 + idleCpu2) - (totalCpu1 + idleCpu1));
		return cpuUsage;
	}

	/**
	 * get CPU name.
	 * 
	 * @return CPU name
	 */
	public String getCpuName() {
		try {
			RandomAccessFile cpuStat = new RandomAccessFile("/proc/cpuinfo",
					"r");
			String[] cpu = cpuStat.readLine().split(":"); // cpu信息的前一段是含有processor字符串，此处替换为不显示
			if (cpu[1].trim().equals("0")){
				cpu = cpuStat.readLine().split(":");
			}
			cpuStat.close();
			return cpu[1];
		} catch (IOException e) {
			Log.e(LOG_TAG, "IOException: " + e.getMessage());
		}
		return "";
	}

	/**
	 * reserve used ratio of process CPU and total CPU, meanwhile collect
	 * network traffic.
	 * 
	 * @return network traffic ,used ratio of process CPU and total CPU in
	 *         certain interval
	 */
	public ArrayList<String> getCpuRatioInfo(String totalBatt,
			String currentBatt, String temperature, String voltage) {

		DecimalFormat fomart = new DecimalFormat();
		fomart.setMaximumFractionDigits(2);
		fomart.setMinimumFractionDigits(2);

		readCpuStat();
		cpuUsedRatio.clear();

		String mDateTime2;
		Calendar cal = Calendar.getInstance();
		if ((Build.MODEL.equals("sdk"))
				|| (Build.MODEL.equals("google_sdk"))) {
			mDateTime2 = formatterFile.format(cal.getTime().getTime() + 8
					* 60 * 60 * 1000);
			totalBatt = "N/A";
			currentBatt = "N/A";
			temperature = "N/A";
			voltage = "N/A";
		} else
			mDateTime2 = formatterFile.format(cal.getTime().getTime());

			totalCpu2 = totalCpu;
		processCpu2 = processCpu;
		idleCpu2 = idleCpu;
		cpuUsedRatio.add(processCpuRatio);
		cpuUsedRatio.add(totalCpuRatio);
		cpuUsedRatio.add(String.valueOf(traffic));
		return cpuUsedRatio;
	}

	/**
	 * get CPU max frequency.
	 * 
	 * @return CPU frequency
	 */
	public double getCpuFreq()
  {
    try
    {
      String freqFilePath = "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq";
      RandomAccessFile reader = new RandomAccessFile(freqFilePath, "r");
      Double freq = Double.parseDouble(reader.readLine());
      return freq/1000;
    }
    catch (IOException e) {
      Log.e(LOG_TAG, "IOException: " + e.getMessage());
    }
    return 0.0;
  }
}
