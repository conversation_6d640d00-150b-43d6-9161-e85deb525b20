/***
 *
 * Author: <PERSON><PERSON><PERSON>
 * Email: <EMAIL>
 *
 ***/
package com.android.browserstack.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.FileNotFoundException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import android.util.Log;


public final class StartParamsUtil {
    private static final String JSON_PATH = "/sdcard/rtc_service";
    private static JSONObject startParams = null;
    private static final String TAG = "BrowserStack-StartParamsUtil";

    private static String readParamsFromFile() throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(new File(JSON_PATH))));
        StringBuilder sb = new StringBuilder();
        String line = null;
        while ((line = reader.readLine()) != null) {
            sb.append(line).append("\n");
        }
        reader.close();
        return sb.toString();
    }

    public static JSONObject getStartParams(boolean refresh) {
        try {
            if (startParams != null && !refresh) return startParams;
            startParams = new JSONObject(readParamsFromFile());
        } catch (JSONException e) {
            Log.i(TAG, "Unable to parse JSON from file", e);
        } catch (FileNotFoundException e) {
            Log.e(TAG, "File not found at " + JSON_PATH, e);
        } catch (IOException e) {
            Log.i(TAG, "Unable to read the file ", e);
        }
        return startParams;
    }
}
