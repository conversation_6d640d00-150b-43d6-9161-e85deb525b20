package com.android.browserstack.services;

import android.annotation.SuppressLint;
import android.app.AlarmManager;
import android.app.KeyguardManager;
import android.app.KeyguardManager.KeyguardLock;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.BatteryManager;
import android.os.Build;
import android.os.IBinder;
import android.os.PowerManager;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;

import com.amazonaws.org.apache.http.client.params.ClientPNames;
import com.android.browserstack.R;
import com.android.browserstack.main.UnlockScreen;
import com.android.browserstack.receiver.AlarmReceiver;
import com.android.browserstack.utils.CpuInfo;
import com.android.browserstack.utils.MemoryInfo;
import com.android.browserstack.utils.NetworkServiceUtils;
import com.android.browserstack.utils.ProxyUtils;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.util.InetAddressUtils;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.params.HttpParams;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.TimeZone;

@SuppressLint("NewApi")
public class GraphitePushService extends Service {

	private final String TAG = "BrowserStack-Service";

	private PostingThread graphitePostingThread;
	private String GRAPHITE_THREAD_NAME = "GraphitePostingThread";
	private PowerManager.WakeLock wl;
	private PowerManager pm;
	private KeyguardLock keyLock;
	private String NET_CHECK_FILE = "/sdcard/internetCheck";
	private String NET_CHECK_URL = "http://mobile-internet-check.browserstack.com/";
  private String NET_CHECK_URL_BKP = "http://www.browserstack.com/robots.txt";
	public static Intent intent;

	@Override
	public IBinder onBind(Intent intent) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public int onStartCommand(Intent intent, int flags, int startId) {
    // onStartCommand always gets called when you run 'am startservice...'
    log("Trying to start Graphite Thread");
		if (!isGraphiteThreadRunning()) {
      //Add Notification so that the foreground service is allowed to keep running
      startForeground(1, getForegroundNotification());

			log("Starting Graphite Thread");
			graphitePostingThread = new PostingThread(this.getApplicationContext());
			graphitePostingThread.setName(GRAPHITE_THREAD_NAME); // Thread name is used to check if this thread is already running the next time startservice is called
			graphitePostingThread.start();
		}

		updateIP();
		return Service.START_STICKY;
	}

	@Override
	public void onDestroy() {
    log("Destroying Graphite Thread");
		graphitePostingThread.closeSocket();
    graphitePostingThread.interrupt();
	}

	private void log(String logLine) {
		try {
			Log.i(TAG, logLine);
		} catch(Exception e) {
			Log.i(TAG, "Error logging: " + e.getMessage());
			e.getStackTrace();
		}
	}

	public void acquireLock() {
		pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
		wl = pm.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK, "BrowserStack Screenlock");
		wl.acquire();
	}

	private void updateIP() {
		String ipAddr = getIPAddress();
		log("IP address of the device " + ipAddr);
		try {
			FileOutputStream outputStream = new FileOutputStream("/sdcard/ipaddr");
			outputStream.write(ipAddr.getBytes());
			outputStream.close();
		} catch (Exception ex) {
			log("Exception: " + ex.getMessage());
		}
	}

	private boolean isGraphiteThreadRunning() {
		Set<Thread> threadSet = Thread.getAllStackTraces().keySet();

		for (Thread t: threadSet) {
			String name = t.getName();
			Boolean running = t.isAlive();

			if (name.equals(GRAPHITE_THREAD_NAME) && running) {
				log("Threadset already contains " + GRAPHITE_THREAD_NAME);
				return true;
			}
		}

		log("Threadset does not contain " + GRAPHITE_THREAD_NAME);
		return false;
	}

	/* This isn't in use now, because the inventory service has been deprecated. At some point, we will move this to the new inventory service by hosting team */
	private void callAlarmInventory() {
		AlarmManager am = (AlarmManager)getSystemService(Context.ALARM_SERVICE);
		Intent alarmIntent = new Intent(GraphitePushService.this, AlarmReceiver.class);
		AlarmManager alarmManager = (AlarmManager)getSystemService(Context.ALARM_SERVICE);
		PendingIntent pendingIntent = PendingIntent.getBroadcast(GraphitePushService.this, 0, alarmIntent, 0);
		alarmManager.cancel(pendingIntent);
		long interval =  5 * 60 * 1000;
		alarmManager.setRepeating(AlarmManager.ELAPSED_REALTIME_WAKEUP, interval, AlarmManager.INTERVAL_HOUR, pendingIntent);
	}

	public String getIPAddress() {
    try {
      List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
      for (NetworkInterface intf : interfaces) {
        String interface_name = intf.getDisplayName();
        if (interface_name.contains("tun")) {
          continue;
        }
        List<InetAddress> addrs = Collections.list(intf.getInetAddresses());
        for (InetAddress addr : addrs) {
          if (!addr.isLoopbackAddress()) {
            String sAddr = addr.getHostAddress().toUpperCase();
            boolean isIPv4 = InetAddressUtils.isIPv4Address(sAddr);
            if (isIPv4) return sAddr;
          }
        }
      }
    } catch (Exception ex) { Log.e(TAG, "Could not get IP address");} // for now eat exceptions
    return "";
  }

  private Notification getForegroundNotification() {
    Intent intent = new Intent(this, GraphitePushService.class);
    PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE);

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      NotificationChannel serviceChannel = new NotificationChannel(
              "NotifyStatus",
              "Foreground Service Channel",
              NotificationManager.IMPORTANCE_DEFAULT
      );
      NotificationManager manager = getSystemService(NotificationManager.class);
      manager.createNotificationChannel(serviceChannel);

      Notification builder = new Notification.Builder(this, "NotifyStatus")
              .setContentIntent(pendingIntent)
              .setContentTitle("Browserstack Service")
              .setContentText("Browserstack app foreground service is running...")
              .setSmallIcon(R.drawable.ic_launcher)
              .build();

      return builder;
    }
  else
    {
      /*  setLatestEventInfo is deprecated and using the
          below method of invocation to avoid compiler errors*/
      Notification notification = new Notification(R.drawable.ic_launcher, "Browserstack Service Running...", System.currentTimeMillis());
      try {
        Method deprecatedMethod = notification.getClass().getMethod("setLatestEventInfo", Context.class, CharSequence.class, CharSequence.class, PendingIntent.class);
        deprecatedMethod.invoke(notification, this, "Browserstack Service", "Browserstack GRADLE foreground service is running...", pendingIntent);
      }
      catch (NoSuchMethodException | IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
        Log.w(TAG, "Method not found", e);
      }
      return notification;
    }
  }

  private class PostingThread extends Thread {
    private static final long POST_TIME = 10000;

    private final ProcessBuilder touchCommand = new ProcessBuilder("touch", "/sdcard/status");
    private final int serverPort = 2003;
    private final MemoryInfo meminfo = new MemoryInfo();
    private final CpuInfo cpuinfo = new CpuInfo();

    private final String TAG = "BrowserStack-" + PostingThread.class;

    private Context context;
    private DatagramSocket socket;
    private String serverName = "graphite.browserstack.com";
    private String status;
    private String internetCheckFailures;
    private int deviceBatteryLevel = 0;
    private int deviceBatteryTemp = 0;

    public void closeSocket() {
      socket.close();
      socket = null;
    }

    public PostingThread(Context context) {
      this.context = context;
      try {
        socket = new DatagramSocket();
      } catch (Exception ex) {
        log(ex.getMessage());
      }
    }

    private void setWifi() {
      WifiManager wifiManager = null;
      
      try {
        wifiManager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        if (!NetworkServiceUtils.wifiDisabledByUser()) {
          wifiManager.setWifiEnabled(true);
        }
      } catch (Exception ex) {
        Log.e(TAG, "Error turning on Wifi", ex);
      }
      if(GraphitePushService.intent != null && GraphitePushService.intent.getStringExtra("setWifi") != null) {
        String ssid = GraphitePushService.intent.getStringExtra("ssid");
        String compareSsid = "\"" + ssid + "\"";
        String password = GraphitePushService.intent.getStringExtra("pass");
        if(ssid == null || password == null) {
          return;
        }

  			// Check wifi is turned on.
        int i = 0;
        while (wifiManager.getWifiState() != WifiManager.WIFI_STATE_ENABLED && i < 5) {
          try {
            Thread.sleep(5000);
          } catch (Exception ex) {
          Log.e(TAG, "Something wrong happened while wifi was being turned on.");
            return;
          }
          i++;
        }

        ProxyUtils pu = new ProxyUtils(getApplicationContext(), ssid, password);
        pu.setConfig();

  			// Check we're connected to wifi.
        for (i = 0; i < 5; i ++) {
          try {
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            log(wifiInfo.getSSID() + " -- " + compareSsid);
            if(compareSsid.equalsIgnoreCase(wifiInfo.getSSID())) {
              Thread.sleep(5000);
              break;
            }
          } catch (Exception ex) {
            Log.e(TAG, "Error trying to set Wifi");
          }
        }
        if (i == 5) {
          Log.e(TAG, "Could not connect to " + ssid + " after trying for a while.");
        }

        // Check internet is working
        for (i = 0; i < 5; i ++) {
          updateInternetStatus();
          if (status.contains("internet down:")) {
            Log.e(TAG, "yes internet is down");
            try {
              Thread.sleep(5000);
            } catch (Exception ex) {
              Log.e(TAG, "Something wrong happened while wifi was being turned on.");
              return;
            }
          } else {
            break;
          }
          status = "";
        }
      }
      log("Wifi should be up and running");
    }
    private Boolean isInSession(){
      File inSessionFile = new File("/sdcard/in_session");
      return inSessionFile.exists();
    }

    @Override
    public void run() {
      initBatteryStatus();
      log("Ensuring Wifi is on and ssid is set");
      status = "";
      setWifi();
      log("Graphite thread running");
      touchStatusFile();

      while (!isInterrupted()) {
        status = "";
        try {
          updateInternetStatus();
        } catch (Exception e) {
          updateInternetStatus();
          log(e.getMessage());
        }

        // This one finally flushes the status created by updateDeviceStatus()
        try {
          flushStatus();
        } catch(Exception e) {
          log(e.getMessage());
          Log.e(TAG, "Flush to the device", e);
        } finally {
          try {
            Thread.sleep(POST_TIME);
          } catch (Exception e) {
            log(e.getMessage());
          }
        }
      }
    }

    public void initBatteryStatus() {
      BroadcastReceiver mBatInfoReceiver = new BroadcastReceiver(){
        @Override
        public void onReceive(Context ctxt, Intent intent) {
          deviceBatteryLevel = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
          deviceBatteryTemp = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0);
        }
      };
      context.registerReceiver(mBatInfoReceiver, new IntentFilter(Intent.ACTION_BATTERY_CHANGED));
    }

    private void updateInternetStatus() {
      log("Checking internet status now.");
      internetCheckFailures = "";
      String status = "Success";
      String url = "";
      String sessionId = getSessionId();
      try {
        String[] urls = getNetCheckUrls(NET_CHECK_FILE);
        String netCheckURL = urls[0];
        String netCheckURLBackup = urls[1];
        Pair<Boolean, String> response = httpCheck(netCheckURL);
        url = netCheckURL;
        String error = "";
        touchStatusFile();
        if(!response.first){
          log("Internet check failed on " + netCheckURL + " Trying on " + netCheckURLBackup);
          logConnectionStatus();
          error = response.second;
          url = netCheckURLBackup;
          response = httpCheck(netCheckURLBackup);
        }

        touchStatusFile();
        if (!response.first) {
          log("Internet check also failed on " + netCheckURLBackup + ".  Failed on both the URL");
          logConnectionStatus();
          error = error +" | "+ response.second;
          status = "Failed";
          updateDeviceStatus("internet down: " + response.second);
          log("Internet check failed on " + netCheckURLBackup);
          updateInternetHealthCheck(sessionId, netCheckURL +" | "+netCheckURLBackup, error, status);
        } else {
          updateDeviceStatus("");
          updateInternetHealthCheck(sessionId, url, error, status);
        }
      } catch (Exception ex) {
        log(ex.getMessage());
        logConnectionStatus();
        updateDeviceStatus("internet down");
        status = "Failed";
        updateInternetHealthCheck(sessionId, url, ex.getMessage(), status);
      }

      logInternetCheckFailure(sessionId);
    }

    private Pair<Boolean, String> httpCheck(String url){
      boolean response = false;
      String error = "";
      try{
        HttpClient client = new DefaultHttpClient();

        HttpParams httpParams = client.getParams();
        httpParams.setParameter(ClientPNames.CONN_MANAGER_TIMEOUT, new Long(10 * 1000)); //Time to receive a connection from the connection manager/pool
        httpParams.setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 10 * 1000); //Time to establish a connection to the remote host
        httpParams.setParameter(CoreConnectionPNames.SO_TIMEOUT, 5 * 1000); //Time waiting for data / between recieving two packets

        HttpGet get = new HttpGet(url);
        HttpResponse responseGet = client.execute(get);
        HttpEntity resEntityGet = responseGet.getEntity();
        if (resEntityGet != null) {
          response = true;
        }
        resEntityGet.consumeContent();
      } catch (Exception ex) {
        log(ex.getMessage());
        response = false;
        error = ex.getMessage();
      }
      return new Pair(response, error);
    }

    private String[] getNetCheckUrls(String filename){
      String[] netCheckUrlArray = { NET_CHECK_URL, NET_CHECK_URL_BKP };
      try{
        File internetCheckFile = new File(filename);
        if(internetCheckFile.exists()){
          String[] urls = readLines(filename);
          netCheckUrlArray[0] = urls[0];
          if(urls.length > 1) {
            netCheckUrlArray[1] = urls[1];
          }
        }
      } catch(Exception ex){
        log(ex.getMessage());
      }
      finally{
        log("Internet check Url: " + netCheckUrlArray[0] + "\n" + "Internet check backup Url: " + netCheckUrlArray[1]);
        return netCheckUrlArray;
      }
    }

    private String[] readLines(String filename) throws IOException {
      FileReader fileReader = new FileReader(filename);
      BufferedReader bufferedReader = new BufferedReader(fileReader);
      List<String> lines = new ArrayList<String>();
      String line = null;
      while ((line = bufferedReader.readLine()) != null) {
        lines.add(line);
      }
      bufferedReader.close();
      return lines.toArray(new String[lines.size()]);
    }

    private void updateScreenService() {
      log("Checking if the screen lock is acquired and the screen is on");

      if(pm == null || wl == null) {
        acquireLock();
      }

      if(!pm.isScreenOn()) {
        fireUnlockScreenIntent();
        return;
      }

      if(!wl.isHeld()) {
        wl.acquire();
      }

      try {
        KeyguardManager keygaurdManager = (KeyguardManager) getSystemService(Context.KEYGUARD_SERVICE);
        keyLock = keygaurdManager.newKeyguardLock("BrowserStack Key unlock");
        keyLock.disableKeyguard();
      } catch (SecurityException e) {
        updateDeviceStatus("unlock failed");
        Log.e(TAG, "Cannot get screen unlock", e);
      }
    }

    private void fireUnlockScreenIntent() {
      try {
        log("Starting unlock service");
        Intent intent = new Intent(context, UnlockScreen.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        this.context.startActivity(intent);
        wl.acquire();
        log("Screen should be awesome now");
        Thread.sleep(5000);
      } catch (Exception e) {
        log("Error calling UnlockScreen: " + e.getMessage());
      }
    }

    private void updateDeviceStatus(String message) {
      // Hard-coded because poller will always read from /sdcard
      if(message == null || message.equals("")) {
        return;
      }
      status += message + ":";
    }

    private void touchStatusFile() {
      try {
        touchCommand.start();
      }
      catch (IOException e)
      {
        log("Error touching status file: " + e.getMessage());
      }
    }

    private void flushStatus() {
      String filename = "/sdcard/status";
      FileOutputStream outputStream;
      try {
        outputStream = new FileOutputStream(filename);
        outputStream.write(status.getBytes());
        outputStream.close();
      } catch (Exception ex) {
        log("Exception: " + ex.getMessage());
      }

      // This is a copy of /sdcard/status, but instead of overwritting we just
      // append lines, like in a log.
      // Do only if we are in a session, as we want to instrument if the
      // internet went down in the middle of them.
      try {
        log("checking if we are in a session...");
        File in_session_file = new File("/sdcard/in_session");
        if(in_session_file.exists()) {
          log("I am in a session");
          String filenameLog = "/sdcard/browserstack_app_status";
          FileOutputStream outputStreamLog;
          outputStreamLog = new FileOutputStream(filenameLog, true); // true for append
          outputStreamLog.write(status.getBytes());
          outputStreamLog.close();
        } else {
            log("Not writting browserstack_app_status, I'm not in a session");
        }
      } catch (Exception ex) {
        log("Exception while writting browserstack_app_status: " + ex.getMessage());
      }


      if (status == null || status.equals("")) {
        return;
      }

      try {
        WifiManager wifiManager = (WifiManager) context
          .getSystemService(Context.WIFI_SERVICE);
        if (!NetworkServiceUtils.wifiDisabledByUser()) {
          wifiManager.setWifiEnabled(true);
        }
      } catch (Exception ex) {
        log("Exception: " + ex.getMessage());
      }
    }

    private void logConnectionStatus() {
      ConnectivityManager conMan = (ConnectivityManager) getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
      NetworkInfo netInfo = conMan.getActiveNetworkInfo();
      WifiManager wifiManager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);
      log("Wifi Status: ssid-" + wifiManager.getConnectionInfo().getSSID() + ", IsWifiOn-" + wifiManager.isWifiEnabled() + ", NetworkID-" + wifiManager.getConnectionInfo().getNetworkId());
      if(netInfo != null) {
        log("Connection Status: connection type-" + netInfo.getType() + ", IsConnected-" + netInfo.isConnected());
      }
    }

    private String getSessionId() {
      File inSessionFile = new File("/sdcard/in_session");
      if (!inSessionFile.exists()) return null;

      try {
        StringBuilder sessionId = new StringBuilder();
        BufferedReader br = new BufferedReader(new FileReader(inSessionFile));
        String line;
        while ((line = br.readLine()) != null) {
          sessionId.append(line);
          sessionId.append(" ");
        }
        br.close();

        if (sessionId != null && !sessionId.toString().trim().isEmpty()) {
          return sessionId.toString().trim();
        }
      } catch (IOException e) {
        log(e.getMessage());
      }
      return null;
    }

    private void updateInternetHealthCheck(String sessionId, String url, String error, String status) {
      if (sessionId == null) return;

      String internetConnectionType = isInternetOverUSB() ? "USB" : "WiFi";
      String[] internetStatus = new String[] { getUTCTime().toString(), status, sessionId, url, internetConnectionType, error };
      internetCheckFailures += TextUtils.join(",", internetStatus) + "\n";
    }

    private void logInternetCheckFailure(String sessionId){
      if (sessionId == null) {
        log("Not writing internet status, I'm not in a session");
        return;
      }

      if (internetCheckFailures == null || internetCheckFailures.trim().isEmpty()) {
        log("Not writing internet status, I got nothing to write.");
        return;
      }

      File dontLogInternetStatus = new File("/sdcard/stop_net_check_logging");
      if (dontLogInternetStatus.exists()) {
        log("Not writing internet status, stop_net_check_logging file touched.");
        return;
      }

      String logFileName = "/sdcard/internet_status";
      try {
        FileOutputStream outputStreamLog;
        outputStreamLog = new FileOutputStream(logFileName, true); // true for append
        outputStreamLog.write(internetCheckFailures.getBytes());
        outputStreamLog.close();
      } catch (IOException e) {
        log(e.getMessage());
      }
    }

    private String getUTCTime(){
      TimeZone tz = TimeZone.getTimeZone("UTC");
      SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
      df.setTimeZone(tz);
      return df.format(new Date());
    }

    private boolean isInternetOverUSB() {
      try {
        for (NetworkInterface networkInterface : Collections.list(NetworkInterface.getNetworkInterfaces())) {
          if (networkInterface.isUp() && networkInterface.getName().contains("tun")) {
            return true;
          }
        }
      } catch (Exception e) {
        log(e.getMessage());
      }
      return false;
    }
  }
}
