package com.android.browserstack.utils;

import android.os.Environment;

import java.io.File;

public class DeviceInfo {
    public static boolean isInPrivateCloud() {
        File extStore = Environment.getExternalStorageDirectory();
        File privateCloudFile = new File(extStore.getAbsolutePath() + "/private_cloud");
        return privateCloudFile.exists();
    }

    public static boolean isPublicSimSession() {
        File extStore = Environment.getExternalStorageDirectory();
        File publicSimSessionFile = new File(extStore.getAbsolutePath() + "/public_sim_session");
        return publicSimSessionFile.exists();
    }
}
