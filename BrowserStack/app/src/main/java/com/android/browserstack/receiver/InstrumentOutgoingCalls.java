package com.android.browserstack.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.widget.Toast;

import com.android.browserstack.utils.DeviceInfo;

public class InstrumentOutgoingCalls extends BroadcastReceiver {
    public final static String TAG = "BrowserStack-" + InstrumentOutgoingCalls.class.getCanonicalName();

    String number;
   @Override
   public void onReceive(Context context, Intent intent) {
       if (null == intent || null == context || DeviceInfo.isInPrivateCloud()) {
           return;
       }

       number = intent.getStringExtra(Intent.EXTRA_PHONE_NUMBER);
       Log.i(TAG, "Call attempted on number:" + number);
       setResultData(null);
       Toast.makeText(context, "Outgoing Call Attempted" , Toast.LENGTH_SHORT).show();
   }
}

