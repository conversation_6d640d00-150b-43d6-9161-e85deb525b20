package com.android.browserstack.services;

import android.content.Context;
import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;

import com.android.browserstack.utils.WifiUtils;

public class WifiHandlerService extends Service {
    private static final String TAG = "BrowserStack-" + WifiHandlerService.class.getCanonicalName();

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            Log.i(TAG, "Request for WifiHandler service");
            if (intent.getStringExtra("action") != null) {  
                String action = intent.getStringExtra("action");
                if (action.equals("disable")) {
                    WifiUtils.setWifiState(this.getApplicationContext(), false);
                }
                else if (action.equals("enable")) {
                    WifiUtils.setWifiState(this.getApplicationContext(), true);
                }
                else {
                    Log.w(TAG, "Invalid action passed to WifiHandler service");
                }
            }
        }
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}
