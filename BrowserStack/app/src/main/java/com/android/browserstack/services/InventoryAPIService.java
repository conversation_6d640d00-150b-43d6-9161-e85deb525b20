package com.android.browserstack.services;

import org.json.JSONObject;
import org.json.JSONException;

import org.apache.http.HttpResponse;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.apache.http.message.BasicHeader;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.impl.client.DefaultHttpClient;

import com.android.browserstack.utils.CpuInfo;
import com.android.browserstack.utils.MemoryInfo;

import java.util.List;
import java.net.InetAddress;
import java.util.Collections;
import java.net.NetworkInterface;
import org.apache.http.conn.util.InetAddressUtils;

import android.util.Log;
import android.os.Build;
import android.os.StatFs;
import java.io.IOException;
import android.os.AsyncTask;
import android.os.Environment;
import java.io.BufferedReader;
import org.apache.http.entity.StringEntity;
import java.util.Date;
import java.io.File;
import java.io.FileReader;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Calendar;
import java.text.SimpleDateFormat;


public class InventoryAPIService {
  private static final String TAG = "BrowserStack-InventoryService";
  private final MemoryInfo meminfo = new MemoryInfo();
  private final CpuInfo cpuinfo = new CpuInfo();
  private String url =  "http://inventory.bsstag.com/pushdata";
  private String filePath = "/sdcard/InventoryStatus";
  private String regionFilePath = "/sdcard/region";

  public void checkInventory()
  {
    log("Starting Inventory API Service");
    String data = prepareData();
    postInventoryData(data);
  }

  private void postInventoryData(String data)
  {
    try{
      File file = new File(filePath);
      if(file.exists()) {
        String lastModified,oldResponse;
        Date lastModDate,currentDate;
        long diff=0;
        SimpleDateFormat df = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
        String line = new BufferedReader(new FileReader(filePath)).readLine();
        String[] parts = line.split("-");
        try{
          lastModified = parts[0];
          oldResponse = parts[1];
          lastModDate = df.parse(lastModified);
          currentDate = new Date();
          diff = (currentDate.getTime() - lastModDate.getTime()) / (60 * 60 * 1000);
        }catch (Exception e) {
          oldResponse = null;
        }
        log( filePath + " was modified " + diff + " hours ago");
        Calendar calendar = Calendar.getInstance();
        int currHour = calendar.get(Calendar.HOUR_OF_DAY);

        if ( oldResponse == null || !oldResponse.equals("{OK}") || diff > 24  || currHour == 0){
          new HttpPostData().execute(url, data); 
        }
        else{
          log("Data already posted for the day");
        }
      }
      else{
        log("File not found:" + filePath);
        new HttpPostData().execute(url, data);
      }
    }
    catch (Exception e) {
      log(e.getMessage());
    }
  }

  private void log(String logLine) {
    try {
      Log.i(TAG, logLine);
    }
    catch(Exception e) {
      Log.i(TAG, "Error logging: " + e.getMessage());
      e.getStackTrace();
    }
  }

  private String getIPAddress() {
    try {
      List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
      for (NetworkInterface intf : interfaces) {
        List<InetAddress> addrs = Collections.list(intf.getInetAddresses());
        for (InetAddress addr : addrs) {
          if (!addr.isLoopbackAddress()) {
            String sAddr = addr.getHostAddress().toUpperCase();
            boolean isIPv4 = InetAddressUtils.isIPv4Address(sAddr);
            if (isIPv4) return sAddr;
          }
        }
      }
    } 
    catch (Exception ex) { 
      log("Could not get IP address");
    }
    return "";
  }

  private long getStorage(){
    try
    {
      StatFs stat = new StatFs(Environment.getExternalStorageDirectory().getPath());
      long bytesAvailable = (long)stat.getBlockSize()*(long)stat.getBlockCount();
      long megAvailable = bytesAvailable / 1048576;
      return megAvailable;
    }
    catch (Exception e) {
      log(e.getMessage());
    }
    return 0;
  }

  private String getRegion(){
    String region = "eu-west-1";
    try {
      File regionFile = new File(regionFilePath);
      if (regionFile.exists()) {
        region = new BufferedReader(new FileReader(regionFilePath)).readLine();
      }
    } catch (Exception ex) {
      log(ex.getMessage());
    } finally {
      log("device region: " + region);
      return region;
    }
  }

  private String prepareData()
  {    
    try {
      JSONObject data = new JSONObject();
      String ipAddr = getIPAddress();
      long ram = meminfo.getTotalMemory();
      String cpuName = cpuinfo.getCpuName();
      String cpuFreq = Double.toString(cpuinfo.getCpuFreq())+"MHz";
      long disk = getStorage();

      data.put("ip", ipAddr);
      data.put("category", "mobile");
      data.put("owner", "BrowserStack");
      data.put("region", getRegion());
      data.put("memory", ram+"M");
      data.put("cpu", cpuName+"-"+cpuFreq);
      data.put("serial_number", Build.SERIAL);
      data.put("model", Build.MODEL);
      data.put("os_name", "android-"+Build.VERSION.RELEASE);
      data.put("child_ip", "N/A");
      data.put("disk", disk+"M");
      return data.toString();
    } 
    catch(JSONException e) {
      log(e.getMessage());
    }
    return "";
  }

  public class HttpPostData extends AsyncTask<String, Void, String> 
  {
    public String doInBackground(String... params)   
    {
      String url=params[0];
      String data=params[1];
      String result = "fail";
      log("Posting Inventory Data - " + data.toString());
      DefaultHttpClient httpclient = new DefaultHttpClient();
      HttpPost httppost = new HttpPost(url);
      try 
      {
        StringEntity se = new StringEntity(data); 
        se.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE, "application/json;charset=UTF-8"));
        httppost.setEntity(se);

        HttpResponse response = httpclient.execute(httppost);
        result = EntityUtils.toString(response.getEntity());
        log("Inventory API response - " + result);
      }
      catch (ClientProtocolException e) {
        log(e.getMessage());
      } 
      catch (IOException e) {
        log(e.getMessage());
      }
      updateFile(result);
      return result;
    }

    private void updateFile(String response){
      FileOutputStream outputStream;
      try {
        SimpleDateFormat df = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
        Date today = Calendar.getInstance().getTime();
        String reportDate = df.format(today);
        outputStream = new FileOutputStream(filePath);
        String line = reportDate + "-" + response;
        outputStream.write(line.getBytes());
        outputStream.close();
      }
      catch (Exception ex) {
        log("Exception: " + ex.getMessage());
      }
    }
  }
}
