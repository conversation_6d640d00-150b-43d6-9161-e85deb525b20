package com.android.browserstack.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.telephony.SubscriptionInfo;
import android.telephony.SubscriptionManager;
import android.telephony.TelephonyManager;
import android.util.Log;

import java.io.FileOutputStream;
import java.util.List;

public class TelephonyUtils {
    private static final String TAG = "BrowserStack-" + TelephonyUtils.class.getCanonicalName();

    @SuppressLint({"MissingPermission", "NewApi"})
    public static void writeMsisdnFile(Context context) {
        SubscriptionManager subscriptionManager = null;
        String msisdn0 = "";
        String msisdn1 = "";
        subscriptionManager = SubscriptionManager.from(context);
        List<SubscriptionInfo> subscriptionList = subscriptionManager.getActiveSubscriptionInfoList();

        if (subscriptionList != null && subscriptionList.size() >= 1) {
            Log.d(TAG, "Cellular subscription present on device");
            // Handle storing information for multiple SIM or eSIM subscriptions
            for (int i = 0; i < subscriptionList.size(); i++) {
                Log.d(TAG, "Storing phone number details for the cellular subscription " + i + " to prop files");

                String msisdn = "";
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                    msisdn = subscriptionList.get(i).getNumber();
                } else if (i == 0 && msisdn.equals("")) {
                    // Using TelephonyManager's getLine1Number() primarily as fallback
                    // It can return garbage like "Unknown" or "?????" or a variety of other things
                    // ref - https://stackoverflow.com/questions/2480288/programmatically-obtain-the-phone-number-of-the-android-phone/25131061#25131061

                    // Sometimes we will get some garbage like "Unknown" or "?????" or a variety of other things
                    // As a quick possible check, we can check  if a "number" is  at least 25% digits
                    TelephonyManager tMgr;
                    tMgr = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                    @SuppressLint("HardwareIds") String maybeNumber = tMgr.getLine1Number();
                    int digitCount = 0;
                    for (char digit : "0123456789".toCharArray()) {
                        // https://stackoverflow.com/a/8910767/3723163
                        // The number of occurrences of a particular character can be counted by looking at the
                        // total length of the string and subtracting the length of the string without the
                        // target digit
                        int count = maybeNumber.length() - maybeNumber.replace("" + digit, "").length();
                        digitCount += count;
                    }
                    if (maybeNumber.length() > digitCount * 4) {
                        Log.d(TAG, "Discarding " + maybeNumber + " because it does not contain a high enough digit ratio to be a real phone number");
                    } else {
                        msisdn = maybeNumber;
                    }
                }
                writePropToFile("msisdn-" + i, msisdn);
            }
        }
    }


    public static void writeImsiFile(Context context) {
        Log.i(TAG, "Getting imsi");
        try {
            TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(context.TELEPHONY_SERVICE);
            String imsi = telephonyManager.getSubscriberId();
            writePropToFile("imsi", imsi);
        } catch(Exception e){
            Log.e(TAG, "Exception: " + e.getMessage());
            writePropToFile("imsi", ""); // writing empty string in case getSubsriberId throws an exception
        }
    }
    @SuppressLint({"MissingPermission", "NewApi"})
    public static boolean checkIfEsim(Context context) {
        Log.i(TAG, "Checking if device has esim");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            SubscriptionManager subscriptionManager = (SubscriptionManager) context.getSystemService(context.TELEPHONY_SUBSCRIPTION_SERVICE);
            List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();
            for(SubscriptionInfo subscriptionInfo : subscriptionInfos){
                if(subscriptionInfo.isEmbedded()){ // Returns true if subscription is from Esim
                    return true;
                }
            }
        }
        return false;
    }

    public static void writePropToFile(String propName, String propValue) {
        if (propValue != null) {
            String fileName = "/sdcard/" + propName + ".prop";
            Log.i(TAG, "Writing " + propName + " : " + propValue + " to " + fileName);
            try {
                FileOutputStream outputStream = new FileOutputStream(fileName);
                outputStream.write(propValue.getBytes());
                outputStream.close();
                return;
            } catch (Exception ex) {
                Log.e(TAG, "Exception: " + ex.getMessage());
            }
        }
        Log.w(TAG, propName + " was null");
    }
}
