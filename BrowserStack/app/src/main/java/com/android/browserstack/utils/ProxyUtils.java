package com.android.browserstack.utils;

import android.content.Context;
import android.content.Intent;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;
import java.util.List;

import be.shouldit.proxy.lib.APL;
import be.shouldit.proxy.lib.WiFiApConfig;
import be.shouldit.proxy.lib.reflection.android.ProxySetting;

public class ProxyUtils {

    private String ssid;
    private String password;
    private String host;
    private int port;
    private WifiManager wifi = null;
    private Context context;

    private static final String TAG = "BrowserStack-" + ProxyUtils.class;

    public ProxyUtils(Context cont, String sssid, String spass) {
        initialize(cont, sssid, spass, null, -1);
    }

    public ProxyUtils(Context cont, String sssid, String spass, String shost, int nport) {
        initialize(cont, sssid, spass, shost, nport);
    }

    private void initialize(Context cont, String sssid, String spass, String shost, int nport) {
        wifi = (WifiManager) cont.getSystemService(Context.WIFI_SERVICE);
        APL.setup(cont);
        ssid = sssid;
        password = spass;
        host = shost;
        port = nport;
    }

    public void setConfig() {
        setWifi();

        // Try to check thrice whether we are connected.
        WifiConfiguration conf = null;
        for (int count = 0; count < 3; count++) {
            try {
                Thread.sleep(500);
            } catch(Exception ex) {
                log(ex.getMessage());
            }

            conf = getWifi();
            if (conf != null) {
                break;
            }
        }

        if (conf != null) {
            try {
                log("Setting proxy for: " + host + ":" + port);
                setProxy(conf);
            } catch (Exception e) {
                Log.e(TAG, e.getMessage(), e);
            }
        } else {
            log("Wifi was not connected, so not setting proxy.");
        }
    }

    public void setProxy(WifiConfiguration conf) throws Exception {
    	WiFiApConfig proxyConf = new WiFiApConfig(conf, ProxySetting.STATIC, null, null, null);
    	if (host != null || port != -1) {
    	proxyConf = new WiFiApConfig(conf, ProxySetting.STATIC, host, port, null);
    	}
    	proxyConf.writeConfigurationToDevice();
    }

    public void setWifi() {
        if (ssid == null || password == null) {
            return;
        }

        WifiConfiguration wc = new WifiConfiguration();
        wc.SSID = "\"" + ssid + "\"";
        wc.preSharedKey = "\"" + password + "\"";
        wc.hiddenSSID = true;
        wc.status = WifiConfiguration.Status.ENABLED;
        wc.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP);
        wc.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP);
        wc.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK);
        wc.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP);
        wc.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP);
        wc.allowedProtocols.set(WifiConfiguration.Protocol.RSN);
        int res = wifi.addNetwork(wc);
        log("add Network returned " + res);
        boolean b = wifi.enableNetwork(res, true);
        log("enableNetwork returned " + b);
    }

    public WifiConfiguration getWifi() {
        String connectedSSID = getCurrentSSID();
        if(connectedSSID == null || connectedSSID.equals("<unknown ssid>")) {
            log("Not connected to any WiFi. Dial 911 for help!");
            return null;
        }
        connectedSSID = connectedSSID.replaceAll("\"", "");
        log("Connected to: " + connectedSSID);
        List<WifiConfiguration> configurations = wifi.getConfiguredNetworks();
        if (configurations == null) {
            log("getConfiguredNetworks returned null!");
            return null;
        }
        for (WifiConfiguration conn : configurations) {
            String compareSSID = conn.SSID.replaceAll("\"", "");
            log("Compare to: " + compareSSID);
            if (compareSSID.equals(connectedSSID)) {
                return conn;
            }
        }
        log("Returning null");
        return null;
    }

    public String getCurrentSSID() {
        WifiInfo currentWifi = wifi.getConnectionInfo();
        String connectedSSID = null;
        for (int i = 0; i < 5; i++) {
            connectedSSID = currentWifi.getSSID();
            if(connectedSSID != null && !connectedSSID.equals("<unknown ssid>")) {
                break;
            }
            try {
                log("Waiting for ssid...");
                Thread.sleep(1000);
            } 
            catch(InterruptedException e) {}
        }
        return connectedSSID;
    }

    private void log(String log) {
        Log.i(TAG, log);
    }
}
