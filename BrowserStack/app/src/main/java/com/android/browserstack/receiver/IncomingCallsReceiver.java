package com.android.browserstack.receiver;

import static android.telephony.TelephonyManager.EXTRA_STATE;
import static android.telephony.TelephonyManager.EXTRA_STATE_RINGING;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.telecom.TelecomManager;
import android.telephony.TelephonyManager;
import android.util.Log;

import com.android.browserstack.utils.DeviceInfo;
import com.android.internal.telecom.ITelecomService;

import org.lsposed.hiddenapibypass.HiddenApiBypass;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class IncomingCallsReceiver extends BroadcastReceiver {
    public final static String TAG = "BrowserStack-" + IncomingCallsReceiver.class.getCanonicalName();

    @Override
    public void onReceive(Context context, Intent intent) {
        if (null == intent || null == context || !DeviceInfo.isPublicSimSession() || DeviceInfo.isInPrivateCloud() || !TelephonyManager.ACTION_PHONE_STATE_CHANGED.equals(intent.getAction())) {
            return;
        }

        final String stringExtra = intent.getStringExtra(EXTRA_STATE);
        ITelecomService telecomService;

        if ((stringExtra.equalsIgnoreCase(EXTRA_STATE_RINGING)) && (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P)) {

            Log.i(TAG, "Incoming call detected. Attempting to Ending call...");
            TelecomManager tm = (TelecomManager) context.getSystemService(Context.TELECOM_SERVICE);
            try {
                Method m = HiddenApiBypass.getDeclaredMethod(tm.getClass(), "getTelecomService" /*, args */);
                m.setAccessible(true);
                telecomService = (ITelecomService) m.invoke(tm);
                boolean result = telecomService.endCall("com.android.browserstack");
                if (result) {
                    Log.i(TAG, "Ended Incoming call");
                } else {
                    Log.i(TAG, "Failed to end incoming call");
                }
            } catch (Exception e) {
                Log.e(TAG, "Failed to end call -  " + e.getMessage() + " trying alternate method", e);
                Method m = HiddenApiBypass.getDeclaredMethod(tm.getClass(), "endCall" /*, args */);
                m.setAccessible(true);
                boolean result = false;
                try {
                    result = (Boolean) m.invoke(tm);
                } catch (Exception ex) {
                    Log.e(TAG, ex.getMessage(), ex);
                }
                Log.e(TAG, "Alternate method to end call response -" + result);
            }
        } else {
            Log.i(TAG, "No Incoming call detected or unsupported android version");
        }
    }
}
