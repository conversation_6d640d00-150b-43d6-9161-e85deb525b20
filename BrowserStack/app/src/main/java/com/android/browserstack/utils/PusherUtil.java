/***
 *
 * Author: <PERSON><PERSON><PERSON>
 * Email: <EMAIL>
 *
 ***/
package com.android.browserstack.utils;


import java.util.concurrent.Executors;
import java.util.concurrent.ExecutorService;

import com.android.browserstack.utils.Constants;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.http.entity.StringEntity;

import android.util.Log;


import com.android.browserstack.utils.StartParamsUtil;


public enum PusherUtil {
    pusherUtilInstance;
    private static final String PUSHER_AUTH_TOKEN_KEY = "pusher_auth";
    private static final String PUSHER_CHANNEL_KEY = "pusher_channel";
    private static final String PUSHER_URL_KEY = "pusher_url";


    private String pusher_auth;
    private String pusher_channel;
    private String pusher_url;
    private String live_session_id;
    private final String TAG = "BrowserStack-PusherUtil";
    ExecutorService executorService = Executors.newFixedThreadPool(5);

    PusherUtil() {
        try {
          JSONObject startParams = StartParamsUtil.getStartParams(true);
          pusher_auth = startParams.optString(PUSHER_AUTH_TOKEN_KEY);
          pusher_channel = startParams.optString(PUSHER_CHANNEL_KEY);
          pusher_url = startParams.optString(PUSHER_URL_KEY);
          live_session_id = startParams.optString(Constants.LIVE_SESSION_ID_KEY);
        } catch (Exception e) {
            Log.e(TAG, "PusherUtil Init Exception", e);
        }
    }

    private int makeRequest(String postParams) {
        try {
            HttpClient httpClient = new DefaultHttpClient();
            HttpPost post = new HttpPost(pusher_url + "/sendMessage");
            StringEntity entity = new StringEntity(postParams);
            post.setEntity(entity);

            HttpResponse response = httpClient.execute(post);
            int statusCode = response.getStatusLine().getStatusCode();

            Log.i(TAG, String.format("Post request to %s/sendMessage responsed with status code: %d", pusher_url, statusCode));
            return statusCode;
        } catch (Exception e) {
            Log.i(TAG, "Unknown Exception occured", e);
        }
        return -1;
    }

    public void notifyPusher(String type, String event, String message) {
        final String postParams = String.format("type=%s&token=%s&channel=%s&live_session_id=%s&event=%s&message=%s", type, pusher_auth, pusher_channel, live_session_id, event, message);

        Log.i(TAG, "Final pusher params: " + postParams);
        executorService.execute(new Runnable() {
            public void run() {
                makeRequest(postParams);
            }
        });
    }

}
