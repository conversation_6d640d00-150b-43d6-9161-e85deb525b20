package com.android.browserstack.main;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.net.wifi.WifiManager;

import com.android.browserstack.utils.ProxyUtils;
import com.android.browserstack.utils.NetworkServiceUtils;

/**
 * Created by dhimil on 30/05/14.
 */
public class ProxyActivity extends Activity {
	private static final String TAG = "BrowserStack-" + ProxyActivity.class;

	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		Intent intent = this.getIntent();
		Log.i(TAG, "Starting ProxyActivity");
		WifiManager wifiManager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);

		if (!NetworkServiceUtils.wifiDisabledByUser()) {
			wifiManager.setWifiEnabled(true);
		}

		ProxyUtils pu = new ProxyUtils(getApplicationContext(),
			intent.getStringExtra("ssid"),
			intent.getStringExtra("pass"),
			intent.getStringExtra("host"),
			intent.getIntExtra("port", -1));
		pu.setConfig();
		finish();
	}

	private void log(String log) {
		Log.i(TAG, log);
	}
}
