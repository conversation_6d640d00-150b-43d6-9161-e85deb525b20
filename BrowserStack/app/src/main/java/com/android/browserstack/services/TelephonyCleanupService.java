package com.android.browserstack.services;

import android.app.Service;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.util.Log;
import android.os.IBinder;
import android.content.ContentResolver;
import android.provider.CallLog;


public class TelephonyCleanupService extends Service {
    private static final String TAG = "BrowserStack-" + TelephonyCleanupService.class.getCanonicalName();
    private static final Uri SMS_CONTENT_URI = Uri.parse("content://sms");

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "Clearing SMS");
        ContentResolver contentResolver = getApplicationContext().getContentResolver();
        contentResolver.delete(CallLog.Calls.CONTENT_URI, null, null);
        deleteSms(SMS_CONTENT_URI, null);
        stopSelf();
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private int deleteSms(Uri deleteUri, String where) {
        int result = 0;

        ContentResolver cr = getApplicationContext().getContentResolver();
        Cursor c = cr.query(deleteUri, new String[]{"_id"}, where, null, null);
        try {
            while (c.moveToNext()) {
                // Delete the SMS
                String uri = "content://sms/" + c.getString(0);
                result += cr.delete(Uri.parse(uri), null, null);
            }
        } catch (Exception e) {
            Log.e("exception in deleteSms:", String.valueOf(e));
            if (result == 0) {
                result = -1;
            }
        }

        return result;
    }
}
