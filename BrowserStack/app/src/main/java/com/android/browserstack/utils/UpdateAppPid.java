package com.android.browserstack.utils;
import java.util.HashMap;
import java.io.IOException;
import android.util.Log;
import com.android.browserstack.utils.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.HttpResponse;
import android.os.AsyncTask;
import java.text.SimpleDateFormat;
import java.util.Date;

public class UpdateAppPid extends AsyncTask<String, Void, Void> {
            private static final String TAG = "BrowserStack-" + UpdateAppPid.class;
            private ZombiePushUtil zombie;
            protected Void doInBackground(String... getURL) {
                //Get url is an array of params
                String url="";
                String errorMessage=null;
                String errorData=null;
                for(int i = 0; i< getURL.length; i++){
                            try {
                                    url = getURL[i];
                                    int respCode = fireRequest(url);
                                    if(respCode != 200){
                                            sendErrorToZombie("Got response with status code "+ Integer.toString(respCode)+ " for url "+url, null); 
                                            continue;
                                    }
                                    Log.i(TAG, "request for url "+url+ " was executed successfully");
                                    break;
                            } catch (Exception ex) {
                                    Log.e(TAG, "error for http request for url "+ url+" is", ex);
                                    errorMessage = ex.getMessage();
                                    errorData = Log.getStackTraceString(ex);
                                    sendErrorToZombie(errorMessage, errorData);
                            }
                          }
                return null;
        }

        private static int fireRequest(String url) throws IOException, ClientProtocolException {
                HttpClient client = new DefaultHttpClient();
                HttpGet get = new HttpGet(url);
                HttpResponse responseGet = client.execute(get);
                int statusCode =responseGet.getStatusLine().getStatusCode();
                return statusCode;
        } 
        
        private void sendErrorToZombie(String errorMessage, String errorData) {
                HashMap<String, String> zombieData = new HashMap<String, String>(5);
                zombieData.put("category", "device-to-machine-failed");
                zombieData.put("kind", "app-update-pid-failed");
                zombieData.put("error", errorMessage);
                zombieData.put("data", errorData);
                zombieData.put("app_timestamp", NetworkServiceUtils.getUTCTime());
                try {
                        zombie = ZombiePushUtil.getInstance();
                        zombie.put(zombieData, ZombiePushUtil.Target.valueOf("ZOMBIE"));
                } catch (InterruptedException e) {
                        Log.e(TAG, "zombie request failed", e);
                }
        }
}
