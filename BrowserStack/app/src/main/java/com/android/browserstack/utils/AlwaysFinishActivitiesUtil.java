package com.android.browserstack.utils;

import android.bluetooth.BluetoothAdapter;
import android.util.Log;

import java.lang.reflect.Method;

public class AlwaysFinishActivitiesUtil {
    private static final String TAG = "BrowserStack-" + AlwaysFinishActivitiesUtil.class.getCanonicalName();

    public static void setAlwaysFinishActivitiesState(boolean state) {
        try {
            // Step 1: Access ActivityManager class
            Class<?> activityManagerClass = Class.forName("android.app.ActivityManager");

            // Step 2: Get the `getService()` method and invoke it to retrieve IActivityManager instance
            Method getServiceMethod = activityManagerClass.getDeclaredMethod("getService");
            getServiceMethod.setAccessible(true);  // Make it accessible if it's private
            Object iActivityManager = getServiceMethod.invoke(null);  // Call `getService()`, no instance needed

            // Step 3: Access the `IActivityManager` interface (internal)
            Class<?> iActivityManagerClass = Class.forName("android.app.IActivityManager");

            // Step 4: Get the `setAlwaysFinish(boolean)` method from the `IActivityManager` interface
            Method setAlwaysFinishMethod = iActivityManagerClass.getDeclaredMethod("setAlwaysFinish", boolean.class);

            // Step 5: Invoke `setAlwaysFinish` on the IActivityManager instance
            setAlwaysFinishMethod.invoke(iActivityManager, state);

            Log.i(TAG, "Setting AlwaysFinishActivities state to: " + state);
        } catch (Exception e) {
            Log.w(TAG, "Setting AlwaysFinishActivities state failed");
        }
    }
}
