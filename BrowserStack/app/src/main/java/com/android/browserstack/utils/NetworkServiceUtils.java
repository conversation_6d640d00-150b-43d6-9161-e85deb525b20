package com.android.browserstack.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;
import java.util.LinkedList;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.android.browserstack.services.NetworkService;
import com.android.browserstack.utils.StartParamsUtil;

import android.content.Intent;
import android.os.Build;
import android.util.Log;

public class NetworkServiceUtils{
	private static final String TAG = "BrowserStack-" + NetworkService.class;
	private static NetworkServiceUtils networkServiceUtils;
	private static JSONObject jsonRTC;
	private static LinkedList<JSONObject> logsQueue;
	private static boolean isAppLiveSession;
	private static boolean isAutomateSession;
	private static boolean isAppAutomateSession;
	private static boolean isEDSPush = false;
	private ZombiePushUtil zombie;
	private static final String disableWifiFilePath = "/sdcard/disable_wifi"; // File created when network simulation API is used to disable wifi
	private NetworkServiceUtils(){}

	public static NetworkServiceUtils getInstance(ZombiePushUtil zombie){
		if(networkServiceUtils == null){
			networkServiceUtils = new NetworkServiceUtils();
			networkServiceUtils.zombie = zombie;
		}
		logsQueue = new LinkedList<JSONObject>();
		return networkServiceUtils;
	}

	public static boolean wifiDisabledByUser() {
		// If wifi is disabled via network simulation, we don't want any receiver to call setWifiEnabled(true)
		File disableWifiFile = new File(disableWifiFilePath);
		Boolean isDisabled = disableWifiFile.exists();
		Log.i(TAG, "Disable wifi file present? " + isDisabled);
		return isDisabled;
	}

	private JSONObject getSessionData(Boolean readFile){
		JSONObject clone = new JSONObject();

		if (readFile || jsonRTC == null){
			try {
				jsonRTC = new JSONObject();
				JSONObject json = StartParamsUtil.getStartParams(true);
				if (isEDSPush) {
					jsonRTC.put("api_key", json.getString("edsKey"));
					isEDSPush = false;
				}
				if (json.has("genre") && "app_live_testing".equals(json.getString("genre"))) {
					isAppLiveSession = true;
					isAppAutomateSession = false;
					isAutomateSession = false;
					jsonRTC.put("app_live_session_id", json.getString("app_live_session_id"));
				} else if(json.has("genre") && Constants.AUTOMATE.equals(json.getString("genre"))) {
					isAutomateSession = true;
					isAppAutomateSession = false;
					isAppLiveSession = false;
					jsonRTC.put("automate_session_id", json.getString("automate_session_id"));
				} else if(json.has("genre") && Constants.APP_AUTOMATE.equals(json.getString("genre"))) {
					isAppAutomateSession = true;
					isAutomateSession = false;
					isAppLiveSession = false;
					jsonRTC.put("app_automate_session_id", json.getString("automate_session_id"));
				} else {
					isAppLiveSession = false;
					isAppAutomateSession = false;
					isAutomateSession = false;
					jsonRTC.put("live_session_id", json.getString("live_session_id"));
				}
				jsonRTC.put("user_id", json.optString("user_id"));
				setClsServers(new JSONArray(json.getString("cls_servers")));
				setEDSServers(json);
			} catch (Exception e) {
				Log.e(TAG, e.getMessage(), e);
			}
		}

		try {
			clone = new JSONObject(jsonRTC.toString());
		} catch (JSONException e) {
			Log.e(TAG, e.getMessage(), e);
		}
		return clone;
	}

	private void setClsServers(JSONArray clsServers){
		try{
			for(int i = 0; i < clsServers.length(); i++) {
				JSONObject cls = clsServers.getJSONObject(i);
				String type = cls.getString("type");
				String host = cls.getString("host");
				int port = cls.getInt("port");
				zombie.setEndPoints(ZombiePushUtil.Target.valueOf(type.toUpperCase()), host, port);
			}
		} catch (Exception e){
			e.printStackTrace();
		}
	}

	private void setEDSServers(JSONObject jsonObject) {
		try {
			String host = jsonObject.getString("edsHost");
			int port = Integer.parseInt(jsonObject.getString("edsPort"));
			String key = jsonObject.getString("edsKey");
			Log.d(TAG, "Setting eds endpoint to: " + host + ":" + port);
			zombie.setEndPoints(ZombiePushUtil.Target.EDS, host, port);
		} catch (Exception e) {
			Log.e(TAG, e.getMessage(), e);
		}
	}

	protected static String getUTCTime(){
		TimeZone tz = TimeZone.getTimeZone("UTC");
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		df.setTimeZone(tz);
		return df.format(new Date());
	}

	public void processIntent(Intent intent, String task){
		Log.d(TAG, "Task received: " + task);
		if (task.equals("eds")) {
			isEDSPush = true;
			String jsonStr = (String)intent.getSerializableExtra("data");

			try {
				JSONObject toEDS = new JSONObject();
				JSONObject eventJson = new JSONObject(jsonStr);
				JSONObject data = getSessionData(true);
				String event_name = eventJson.getString("message");
				putDeviceDetails(eventJson);

				if (isAppLiveSession) {
					toEDS.put("event_type", "app_live_web_events");
					data.put("product", "app_live");
					eventJson.put("session_id", data.getString("app_live_session_id"));
				} else if(isAutomateSession) {
					toEDS.put("event_type", "automate_web_events");
					data.put("product", Constants.AUTOMATE);
					eventJson.put("session_id", data.getString("automate_session_id"));
				} else if(isAppAutomateSession) {
					toEDS.put("event_type", "app_automate_web_events");
					data.put("product", Constants.APP_AUTOMATE);
					eventJson.put("session_id", data.getString("app_automate_session_id"));
				} else {
					toEDS.put("event_type", "live_web_events");
					data.put("product", "live");
					eventJson.put("session_id", data.getString("live_session_id"));
				}

				data.put("event_name", event_name);
				data.put("event_json", eventJson);
				toEDS.put("data", data);
				toEDS.put("api_key", data.getString("api_key"));

				Log.d(TAG, "Data for EDS curated: " + toEDS.toString());
				addToZombieQueue(toEDS.toString(), ZombiePushUtil.Target.valueOf(task.toUpperCase()));
			} catch (JSONException e) {
				Log.e(TAG, e.getMessage(), e);
			}
		} else if (task.equals("cls")) {
			String jsonStr = (String)intent.getSerializableExtra("data");
			JSONObject json = new JSONObject();
			try {
				json = new JSONObject(jsonStr);

				if(json.has("json_data")){
					JSONObject json_data = (JSONObject)json.get("json_data");
					putDeviceDetails(json_data);
				}
				else{
					JSONObject json_data = new JSONObject();
					putDeviceDetails(json_data);
					json.put("json_data", json_data);
				}
				
				if(json.has("live_session_id") && "".equals(json.get("live_session_id"))) {
					logsQueue.add(json);
				}
				else {
					if(isAppLiveSession) {
						if(json.has("live_session_id")) {
							json.put("app_live_session_id", json.getString("live_session_id")); 
							json.remove("live_session_id");
						}
						if(json.has("update") && "live_session_id".equals(json.getString("update"))) {
							json.put("update", "app_live_session_id");
						}
					} else if(isAutomateSession) {
						if(json.has("live_session_id")) {
							json.put("automate_session_id", json.getString("live_session_id")); 
							json.remove("live_session_id");
						}
						json.put("product", "Automate");
						if(json.has("update") && "live_session_id".equals(json.getString("update"))) {
							json.put("update", "automate_session_id");
						}
					} else if(isAppAutomateSession) {
						if(json.has("live_session_id")) {
							json.put("app_automate_session_id", json.getString("live_session_id")); 
							json.remove("live_session_id");
						}
						json.put("product", "App Automate");
						if(json.has("update") && "live_session_id".equals(json.getString("update"))) {
							json.put("update", "app_automate_session_id");
						}
					}
				}
			} catch (JSONException e) {
				Log.e(TAG, e.getMessage(), e);
			}
			
			Log.i(TAG, "Data from " + task +": " + json.toString());
			addToZombieQueue(json.toString(), ZombiePushUtil.Target.valueOf(task.toUpperCase()));
			
		} else if (task.equals("cls_binary")) {
			JSONObject json = getSessionData((isAppAutomateSession || isAutomateSession));
			try {
				json.put("app", (String)intent.getSerializableExtra("app"));
				json.put("message", (String)intent.getSerializableExtra("message"));
				json.put("error", (String)intent.getSerializableExtra("error"));
				json.put("product", "Live");
				
				JSONObject json_data = new JSONObject();
				json_data.put("txtdata", (String)intent.getSerializableExtra("data"));
				putDeviceDetails(json_data);
				
				json.put("json_data", json_data);
				json.put("app_timestamp", getUTCTime());
				if(json.has("live_session_id") && "".equals(json.get("live_session_id"))) {
					logsQueue.add(json);
				}
				else {
					if(isAppLiveSession) {
						if(json.has("live_session_id")) {
							json.put("app_live_session_id", json.getString("live_session_id")); 
							json.remove("live_session_id");
							json.remove("product");
							json.put("product", "App Live");
						}
						if(json.has("update") && "live_session_id".equals(json.getString("update"))) {
							json.put("update", "app_live_session_id");
						}
					} else if(isAutomateSession) {
						if(json.has("live_session_id")) {
							json.put("automate_session_id", json.getString("live_session_id")); 
							json.remove("live_session_id");
						}
						json.put("product", "Automate");
						if(json.has("update") && "live_session_id".equals(json.getString("update"))) {
							json.put("update", "automate_session_id");
						}
					} else if(isAppAutomateSession) {
						if(json.has("live_session_id")) {
							json.put("app_automate_session_id", json.getString("live_session_id")); 
							json.remove("live_session_id");
						}
						json.put("product", "App Automate");
						if(json.has("update") && "live_session_id".equals(json.getString("update"))) {
							json.put("update", "app_automate_session_id");
						}
					}
				}
			} catch (JSONException e) {
				Log.e(TAG, e.getMessage(), e);
			}
			Log.i(TAG, "Data from " + task +": " + json.toString());
			addToZombieQueue(json.toString(), ZombiePushUtil.Target.valueOf(task.split("_")[0].toUpperCase()));
		} else if (task.equals("rtc_cls_init")){
			getSessionData(true);

			try {
				if(isAppLiveSession) {
					if ((jsonRTC.getString("app_live_session_id") != null) && !(jsonRTC.getString("app_live_session_id").equals(""))) {
						flushLogsQueue(jsonRTC.getString("app_live_session_id"), jsonRTC.getString("user_id"));
					}
				} else if (isAutomateSession) {
					if ((jsonRTC.getString("automate_session_id") != null) && !(jsonRTC.getString("automate_session_id").equals(""))) {
						flushLogsQueue(jsonRTC.getString("automate_session_id"), jsonRTC.getString("user_id"));
					}
				} else if (isAppAutomateSession) {
					if ((jsonRTC.getString("app_automate_session_id") != null) && !(jsonRTC.getString("app_automate_session_id").equals(""))) {
						flushLogsQueue(jsonRTC.getString("app_automate_session_id"), jsonRTC.getString("user_id"));
					}
				} else {
					if ((jsonRTC.getString("live_session_id") != null) && !(jsonRTC.getString("live_session_id").equals(""))) {
						flushLogsQueue(jsonRTC.getString("live_session_id"), jsonRTC.getString("user_id"));
					}
				}
			}  catch (JSONException e) {
				Log.e(TAG, e.getMessage(), e);
			}
		} else if (task.equals("rtc_reset")) {
			try {
				if (jsonRTC != null){
					if(isAppLiveSession) jsonRTC.put("app_live_session_id", "");
					else if (isAutomateSession) { 
						jsonRTC.put("automate_session_id", "");
					} else if (isAppAutomateSession) { 
						jsonRTC.put("app_automate_session_id", "");
					} else jsonRTC.put("live_session_id", "");
				}
			} catch (JSONException e) {
				Log.e(TAG, e.getMessage(), e);
			}
		}
	}

	private void flushLogsQueue(String live_session_id, String user_id) {
		if (logsQueue == null) return;

		while(!logsQueue.isEmpty()) {
			Log.i(TAG, "screwed...man!");
			JSONObject json = logsQueue.poll();
			try {
				if(isAppLiveSession) {
					json.put("app_live_session_id", live_session_id);
					if(json.has("update") && "live_session_id".equals(json.getString("update"))) {
						json.put("update", "app_live_session_id");
					}
				} else if(isAutomateSession) {
					json.put("automate_session_id", live_session_id);
					if(json.has("update") && "live_session_id".equals(json.getString("update"))) {
						json.put("update", "automate_session_id");
					}
				} else if(isAppAutomateSession) {
					json.put("app_automate_session_id", live_session_id);
					if(json.has("update") && "live_session_id".equals(json.getString("update"))) {
						json.put("update", "app_automate_session_id");
					}
				} else json.put("live_session_id", live_session_id);
				json.put("user_id", user_id);
			} catch (JSONException e) {
				Log.e(TAG, e.getMessage(), e);
			}
			addToZombieQueue(json.toString(), ZombiePushUtil.Target.valueOf("CLS"));
		}
	}

	private static void putDeviceDetails(JSONObject json_data) throws JSONException{
		json_data.put("device_id", Build.SERIAL);
		json_data.put("model", Build.MODEL);
	}

	private boolean addToZombieQueue(String jsonString, ZombiePushUtil.Target target) {
		for (int i = 0; i < 5; i++) {
			try {
				zombie.put(jsonString, target);
				return true;
			} catch (InterruptedException e) {
				continue;
			}
		}
		return false;
	}
}