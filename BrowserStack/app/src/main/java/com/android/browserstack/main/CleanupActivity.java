package com.android.browserstack.main;

import android.app.Activity;
import android.accounts.AccountManager;
import android.accounts.Account;
import android.content.Context;
import android.content.Intent;
import android.provider.Settings;
import android.os.Bundle;
import android.util.Log;

import com.android.browserstack.utils.ProxyUtils;
import com.android.browserstack.utils.OrientationUtil;
import com.android.browserstack.utils.WallpaperUtil;


public class CleanupActivity extends Activity {
    private final String TAG = "BrowserStack-" + CleanupActivity.class.toString();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = this.getIntent();
        String action = intent.getStringExtra("action");
        Context context = this.getApplicationContext();

        if(action != null) {
            Log.i(TAG, "received startservice command with action: " + action);
            if(action.equalsIgnoreCase("restore_settings")){
                removeAccounts();
                resetBrightness();
                WallpaperUtil.setWall(context);
                restoreWifi(intent);
                resetOrientation();
            }
            else if(action.equalsIgnoreCase("getwall")){
                WallpaperUtil.getWall(context);
            }
            else if(action.equalsIgnoreCase("resetwall")){
                WallpaperUtil.setWall(context);
            }
        }
        else
            Log.i(TAG, "give specific action");
        finish();
    }

    public void restoreWifi(Intent intent) {
        ProxyUtils pu = new ProxyUtils(this.getApplicationContext(),
            intent.getStringExtra("ssid"),
            intent.getStringExtra("pass"),
            intent.getStringExtra("host"),
            intent.getIntExtra("port", -1));
        pu.setConfig();
    }

    public void resetOrientation() {
        OrientationUtil.setOrientation(this, "portrait");
    }

    public void removeAccounts() {
        AccountManager am = AccountManager.get(this);
        Account[] accounts = am.getAccounts();
        for (int i = 0; i < accounts.length; i++) {
            Account accountToRemove = accounts[i];
            am.removeAccount(accountToRemove, null, null);
        }
    }

    public void resetBrightness() {
        Settings.System.putInt(getContentResolver(), Settings.System.SCREEN_BRIGHTNESS, 20);
    }
}
