package com.android.browserstack.services;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;

import com.android.browserstack.utils.TelephonyUtils;

public class TelephonyHandlerService extends Service {
    private static final String TAG = "BrowserStack-" + TelephonyHandlerService.class.getCanonicalName();

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            Log.i(TAG, "Request for TelephonyHandler service");
            if (intent.getStringExtra("action") != null) {  
                String action = intent.getStringExtra("action");
                if (action.equals("write-out-imsi")) {
                    TelephonyUtils.writeImsiFile(this.getApplicationContext());
                }
                else if(action.equals("check-if-esim")){
                    if(TelephonyUtils.checkIfEsim(this.getApplicationContext())){
                        TelephonyUtils.writePropToFile("esim", "true"); //Create /sdcard/esim.prop if esim is present
                    }
                }
                else {
                    Log.w(TAG, "Invalid action passed to TelephonyHandler service");
                }
            }
        }
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}
