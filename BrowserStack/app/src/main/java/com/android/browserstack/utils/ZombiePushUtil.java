package com.android.browserstack.utils;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.util.Calendar;
import java.util.HashMap;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.PriorityBlockingQueue;

import org.json.JSONException;
import org.json.JSONObject;

import android.os.Build;
import android.util.Log;
import android.util.Pair;

public class ZombiePushUtil extends Thread {

	class CLSData implements Comparable {
		public Target target;
		public String textData;

		public CLSData(Target target, String textData) {
			this.target = target;
			this.textData = textData;
		}

		@Override
		public int compareTo(Object arg0) {
			// TODO Auto-generated method stub
			return 0;
		}
	}

	private HashMap<Target, Pair<String,Integer>> endPoints = new HashMap<Target, Pair<String,Integer>>(3);

	public void setEndPoints(Target type, String host, Integer Port){
		endPoints.put(type, new Pair<String, Integer>(host, Port));
	}

	private static final String TAG = "BrowserStack" + ZombiePushUtil.class;

	private static volatile DatagramSocket socket;

	private static volatile BlockingQueue<CLSData> data;
	private static ZombiePushUtil zombiePushUtil;

	public enum Target {
		ZOMBIE, CLS, EDS
	}

	private ZombiePushUtil() {
		data = new PriorityBlockingQueue<CLSData>(5);
		this.setEndPoints(Target.ZOMBIE, "pager.browserstack.com", 8000);
		this.setEndPoints(Target.CLS, "https://zombie.bsstag.com", 41234);
		this.setEndPoints(Target.EDS, "edsstaging.bsstag.com", 8553);
	}

	public void put(HashMap<String, String> zombieData, Target target)
			throws InterruptedException {
		if (zombieData == null) {
			Log.i(TAG, "Data is null. Fuck you");
			return;
		}
		if(target.equals(Target.ZOMBIE))
			addStatic(zombieData);
		try {
			String dataToSend = convertHashMapToJSONString(zombieData);
			data.put(new CLSData(target, dataToSend));
		} catch (JSONException e) {
			Log.i(TAG, "Not a json data: " + zombieData.toString());
			return;
		}
	}

	public void put(String json, Target target)
			throws InterruptedException {
		if (json == null) {
			Log.i(TAG, "Data is null. Fuck you");
			return;
		}
		data.put(new CLSData(target, json));
	}

	private void addStatic(HashMap<String, String> zombieData) {
		zombieData.put("timestamp", ""
				+ (Calendar.getInstance().getTimeInMillis() / 1000));
		zombieData.put("os", "android");
		// If no team given put "mobile"
		if (zombieData.get("team") == null) {
   		  zombieData.put("team", "mobile");
		}
		zombieData.put("job_id", Build.SERIAL);
	}

	private String convertHashMapToJSONString(HashMap<String, String> map)
			throws JSONException {
		JSONObject json = new JSONObject();
		for (String key : map.keySet()) {
			json.put(key, map.get(key));
		}
		return json.toString();
	}

	private void sendDataToZombie(byte[] dataToSend, Target target)
			throws IOException {
		Log.i(TAG, "Sending data to Zombie. Length: " + dataToSend.length);
		if (socket == null) {
			socket = new DatagramSocket();
		}
		DatagramPacket packet = new DatagramPacket(dataToSend,
				dataToSend.length, InetAddress.getByName(endPoints.get(target).first), endPoints.get(target).second);
		socket.send(packet);
	}

	public static ZombiePushUtil getInstance() {
		if (zombiePushUtil == null) {
			zombiePushUtil = new ZombiePushUtil();
		}
		return zombiePushUtil;
	}

	@Override
	public void run() {
		Log.i(TAG, "Shit has started");
		while (true) {
			try {
				CLSData clsData = data.take();
				Target target = clsData.target;
				String d = clsData.textData;
				Log.i(TAG, "Shit head! Target: " + target.toString()
						+ " data: " + d);
				sendDataToZombie(d.getBytes(), clsData.target);
			} catch (Exception e) {
				Log.e(TAG, "Error Posting data to zombie", e);
			}
		}
	}
}
