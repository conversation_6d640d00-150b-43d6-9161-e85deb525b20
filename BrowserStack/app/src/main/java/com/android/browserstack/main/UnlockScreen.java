package com.android.browserstack.main;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;
import android.view.Window;
import android.view.WindowManager;

public class UnlockScreen extends Activity {
	private static final String TAG = "BrowserStack-" + UnlockScreen.class;

	private void getScreenUp() {
		try {
			log("Trying to unlock the screen");
			final Window win = getWindow();
			if (android.os.Build.VERSION.SDK_INT >= 21) {
				win.addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);
			} else {
				win.addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD
					| WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
			}
	    win.addFlags(WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
	    log("Screen should be unlocked");
		} catch (Exception e) {
			log("Unable to unlock screen", e);
		}
	}

	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		getScreenUp();
	}

	public void log(String message, Exception... exceptions) {
		if(exceptions == null) {
			Log.i(TAG, message);
		} else {
			for(Exception exception : exceptions) {
				Log.e(TAG, message, exception);
			}
		}
	}

	@Override
	public void onResume() {
		super.onResume();
		getScreenUp();
	}
}
