package com.android.browserstack.services;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;
import android.content.Context;
import android.os.Build;

import android.content.ClipboardManager;
import android.content.ClipData;


public class ClipboardService extends Service {
  private static final String TAG = "BrowserStack-" + ClipboardService.class.getCanonicalName();

  @Override
  public void onCreate() {
      super.onCreate();
  }

  @Override
  public int onStartCommand(Intent intent, int flags, int startId) {
    if (intent != null) {
      Log.i(TAG, "Request for ClipboardService service");
      ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
      String space=" ";
      ClipData clipData = ClipData.newPlainText("text", space);
      Log.i(TAG, "cleaning clipboard");
      if(Build.BRAND.toLowerCase().matches("(.*)samsung(.*)")){
        for(int i=0; i<20; i++){
          clipboard.setPrimaryClip(clipData);
          space += " ";
          clipData = ClipData.newPlainText("text", space);
        }
      } else{
        clipboard.setPrimaryClip(clipData);
      }
    }
    stopSelf();
    return START_REDELIVER_INTENT;
  }

  @Override
  public IBinder onBind(Intent intent) {
    return null;
  }

  public void onDestroy() {
    super.onDestroy();
  }
}
