package com.android.browserstack.utils;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

public class BatteryInfo {
    private final String TAG = "BrowserStack-" + "Battery";
    private final String batteryInfoSdcardPath = "/sdcard/battery.json";
    private Context context;
    private String batteryJson = "";
    private BatteryManager mBatteryManager = null;

    public void setContext(Context context) {
        this.context = context;
        mBatteryManager = (BatteryManager) this.context.getSystemService(Context.BATTERY_SERVICE);
    }

    private String getUTCTime(){
        TimeZone utcTimeZone = TimeZone.getTimeZone("UTC");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormat.setTimeZone(utcTimeZone);
        String currentFormattedTime = dateFormat.format(new Date());
        return currentFormattedTime;
    }

    private int getTemperature(Intent batteryStatus){
        return batteryStatus.getIntExtra(BatteryManager.EXTRA_TEMPERATURE,0);
    }

    private int getVoltage(Intent batteryStatus) {
        return batteryStatus.getIntExtra(BatteryManager.EXTRA_VOLTAGE,0);
    }

    private int getCharge(Intent batteryStatus) {
        int level = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
        int scale = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1);

        int charge = (level * 100 /scale);
        return charge;
    }

    private String getStatus(Intent batteryStatus){
        int chargeStatus = batteryStatus.getIntExtra(BatteryManager.EXTRA_STATUS, -1);
        String chargeStatusString = "unknown";
        switch (chargeStatus) {
            case BatteryManager.BATTERY_STATUS_CHARGING:
                chargeStatusString = "charging";
                break;
            case BatteryManager.BATTERY_STATUS_DISCHARGING:
                chargeStatusString = "discharging";
                break;
            case BatteryManager.BATTERY_STATUS_FULL:
                chargeStatusString = "full";
                break;
            case BatteryManager.BATTERY_STATUS_NOT_CHARGING:
                chargeStatusString = "not_charging";
                break;
        }
        return chargeStatusString;
    }

    private String getHealth(Intent batteryStatus) {
        int health = batteryStatus.getIntExtra(BatteryManager.EXTRA_HEALTH, -1);
        String healthString = "unknown";
        switch (health) {
            case BatteryManager.BATTERY_HEALTH_COLD:
                healthString = "cold";
                break;
            case BatteryManager.BATTERY_HEALTH_DEAD:
                healthString = "dead";
                break;
            case BatteryManager.BATTERY_HEALTH_GOOD:
                healthString = "good";
                break;
            case BatteryManager.BATTERY_HEALTH_OVER_VOLTAGE:
                healthString = "over_voltage";
                break;
            case BatteryManager.BATTERY_HEALTH_OVERHEAT:
                healthString = "over_heat";
                break;
            case BatteryManager.BATTERY_HEALTH_UNSPECIFIED_FAILURE:
                healthString = "unspecified_failure";
                break;
        }
        return healthString;
    }

    private int getCapacity(){
        return  mBatteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
    }
    private int getChargeCounter(){
        return  mBatteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CHARGE_COUNTER);
    }
    private int getCurrentNow(){
        return  mBatteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW);
    }

    private long getEnergyCounter(){
        return  mBatteryManager.getLongProperty(BatteryManager.BATTERY_PROPERTY_ENERGY_COUNTER);
    }

    public void generateBatteryMetric(){

        //Refer: https://developer.android.google.cn/reference/android/os/BatteryManager
        try{
            IntentFilter ifilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
            Intent batteryStatus = context.registerReceiver(null, ifilter);

            String time = getUTCTime(); //time
            int temperature = getTemperature(batteryStatus); //temp in (10*C)
            int charge = getCharge(batteryStatus); //battery charge (%)
            String status = getStatus(batteryStatus); //charge status (charging, not charging, full, etc)
            String health = getHealth(batteryStatus); // overall battery health
            int voltage = getVoltage(batteryStatus); //voltage in (mV)
            int capacity = getCapacity(); // remaining battery capacity as an integer percentage of total capacity
            int chargeCounter = getChargeCounter(); // battery capacity in microampere-hours
            int currentNow = getCurrentNow(); // Instantaneous battery current in microamperes (+ve => into battery, -ve => out of battery)
            long energyCounter = getEnergyCounter(); // Battery remaining energy in nanowatt-hours

            JSONObject data = new JSONObject();
            data.put("time",time);
            data.put("temperature",temperature);
            data.put("charge",charge);
            data.put("status",status);
            data.put("health",health);
            data.put("voltage",voltage);
            data.put("capacity",capacity);
            data.put("charge_counter",chargeCounter);
            data.put("current_now",currentNow);
            data.put("energy_counter",energyCounter);
            batteryJson = data.toString() + "\n";
            Log.i(TAG, "battery data: " + batteryJson);
        } catch (Exception e){
            Log.e(TAG,e.getMessage());
        }
    }
    private void appendMetricsToFile(){
        try {
            FileOutputStream outputStreamLog;
            outputStreamLog = new FileOutputStream(batteryInfoSdcardPath, true); // true for append
            outputStreamLog.write(batteryJson.getBytes());
            outputStreamLog.close();
        } catch (IOException e) {
            Log.e(TAG,e.getMessage());
        }
    }


    public void flushBatteryMetric(){
        appendMetricsToFile();
    }


}
