package com.android.browserstack.services;

import java.io.File;
import java.util.HashMap;
import java.util.Locale;

import com.android.browserstack.utils.*;

import android.app.Service;
import android.content.Intent;
import android.os.Environment;
import android.os.IBinder;
import android.util.Log;

import static com.android.browserstack.utils.PusherUtil.pusherUtilInstance;

public class NetworkService extends Service {
    private static final String TAG = "BrowserStack-" + NetworkService.class;
    private ZombiePushUtil zombie;
    private NetworkServiceUtils networkServiceUtils;

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent == null) {
            return Service.START_NOT_STICKY;
        }
        String task = intent.getStringExtra("task");
        if (task == null) {
            return Service.START_STICKY;
        }
        Log.i(TAG, "Here at the wall: " + task);
        if (task.equals("change_language")) {
            try {
                String locale = intent.getStringExtra("l");
                Log.i(TAG, "Locale=" + locale);
                Class.forName("com.android.internal.app.LocalePicker")
                        .getMethod("updateLocale", Locale.class)
                        .invoke(null, parseLocale(locale));
            } catch (Exception e) {
                Log.e(TAG, "Exception in setting language", e);
            }
        } else if (task.equals("zombie")) {
            try {
                HashMap<String, String> map = (HashMap<String, String>) intent.getSerializableExtra("data");
                Log.i(TAG, "Data from " + task + ": " + map.toString());
                addToZombieQueue(map, ZombiePushUtil.Target.valueOf(task.toUpperCase()));
            } catch (ClassCastException e) {
                Log.e(TAG, "Not a valid zombie data push", e);
            }
        } else if (task.equals("app_update_pid")) {
            String deviceId = intent.getStringExtra("deviceId");
            String appPid = intent.getStringExtra("appPid");
            String macIp = intent.getStringExtra("macIp");
            String machineUrl = "http://" + macIp + ":45671/app_update_pid?device=" + deviceId + "&pid=" + appPid;
            new UpdateAppPid().execute(machineUrl);
        } else if ("notifyPusher".equals(task)) {
            String type = intent.getStringExtra(Constants.PUSHER_EVENT_TYPE);
            String event = intent.getStringExtra(Constants.PUSHER_EVENT);
            String message = intent.getStringExtra(Constants.PUSHER_MESSAGE);

            pusherUtilInstance.notifyPusher(type, event, message);
        } else {
            networkServiceUtils.processIntent(intent, task);
        }
        stopSelf();
        return Service.START_STICKY;
    }

    private static Locale parseLocale(String locale) {
        String[] parts = locale.replace('-', '_').split("_");
        switch (parts.length) {
            case 0:
                return Locale.US;
            case 1:
                return new Locale(parts[0]);
            default:
                return new Locale(parts[0], parts[1]);
        }
    }

    private boolean addToZombieQueue(HashMap<String, String> data, ZombiePushUtil.Target target) {
        for (int i = 0; i < 5; i++) {
            try {
                zombie.put(data, target);
                return true;
            } catch (InterruptedException e) {
                continue;
            }
        }
        return false;
    }

    @Override
    public void onCreate() {
        startZombieService();
    }

    private void startZombieService() {
        try {
            zombie = ZombiePushUtil.getInstance();
            if (!zombie.isAlive()) {
                Log.i(TAG, "Starting zombie thread");
                zombie.start();
            }
            networkServiceUtils = NetworkServiceUtils.getInstance(zombie);
        } catch (Exception e) {
            Log.e(TAG, e.getMessage(), e);
        }
    }

    @Override
    public void onDestroy() {
        zombie.yield();
    }

}
