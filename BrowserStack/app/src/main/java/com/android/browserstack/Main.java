package com.android.browserstack;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.android.browserstack.utils.AlwaysFinishActivitiesUtil;
import com.android.browserstack.utils.TelephonyUtils;

import org.lsposed.hiddenapibypass.HiddenApiBypass;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

@SuppressLint({"DiscouragedPrivateApi", "PrivateApi"})

/**
 * Used to invoke methods using app_process.
 * @param args use the first three space separated arguments. Uses the first argument to identify
 *             method to invoke and the following arguments are used by the methods.
 *        Eg: adb shell CLASSPATH=#{browserstack_app_package_path} app_process / com.android.browserstack.Main #{arg0} #{arg1} #{arg2}...
 *        where arg0 = String representing the method to call such as "setSimPowerStateForSlot"
 *             arg1 ... argn  = method arguments.
 *             For setSimPowerStateForSlot arg1 =  sim slot index
 *             and arg2 = state value (1 for enable 0 for disable).
 */
public class Main {
    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void main(String[] args) throws ClassNotFoundException, InvocationTargetException, NoSuchMethodException, IllegalAccessException {
        switch(args[0]) {
            case "setSimPowerStateForSlot":
                setSimPowerStateForSlot(Integer.parseInt(args[1]), Integer.parseInt(args[2]));
                break;
            case "setUIModeForApplication":
                setUIModeForApplication(args[1], Integer.parseInt(args[2]));
                break;
            case "alwaysFinishActivities":
                boolean value = args[1].equals("true");
                AlwaysFinishActivitiesUtil.setAlwaysFinishActivitiesState(value);
            // Any other method(s) requiring app process run can be added here as cases
        }
    }
    /**
     * Set SIM card power state. Request is equivalent to inserting or removing the card.
     * @param slotIndex slotIndex SIM slot id.
     * @param state
     * State of SIM (power down, power up, pass through)
     * - {@link <a href="https://android.googlesource.com/platform/frameworks/base/+/refs/tags/android-9.0.0_r45/telephony/java/android/telephony/TelephonyManager.java#6814">...</a>}
     * - {@link <a href="https://android.googlesource.com/platform/frameworks/base/+/refs/tags/android-9.0.0_r45/telephony/java/android/telephony/TelephonyManager.java#6817">...</a>}
     * - {@link <a href="https://android.googlesource.com/platform/frameworks/base/+/refs/tags/android-9.0.0_r45/telephony/java/android/telephony/TelephonyManager.java#6820">...</a>}
     * @throws ClassNotFoundException  if the class cannot be located
     * @throws NoSuchMethodException if a method with the specified name is not found.
     * @throws InvocationTargetException if the underlying method throws an exception.
     * @throws IllegalAccessException if the method invoked using reflection is private or protected.
     */
    public static void setSimPowerStateForSlot(int slotIndex, int state) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        // call via reflection. Reference - https://stackoverflow.com/questions/55119839/iwindowmanager-call-setoverscan-via-reflection/55174537#55174537
        Class<?> serviceManagerClassObj = Class.forName("android.os.ServiceManager");
        Method getServiceMethod =
                serviceManagerClassObj.getDeclaredMethod("getService", String.class);
        getServiceMethod.setAccessible(true);

        Object telephonyServiceBinderObj = getServiceMethod.invoke(serviceManagerClassObj, Context.TELEPHONY_SERVICE);

        Class<?> telephonyManagerStubObject = Class.forName("com.android.internal.telephony.ITelephony").getClasses()[0];
        Method asInterfaceMethod =
                telephonyManagerStubObject.getDeclaredMethod("asInterface", IBinder.class);
        asInterfaceMethod.setAccessible(true);

        Object tmInstance = asInterfaceMethod.invoke(telephonyManagerStubObject, telephonyServiceBinderObj);

        Method method = null;

        // makes use of https://github.com/LSPosed/AndroidHiddenApiBypass to bypass hidden API restrictions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            for (Object object : HiddenApiBypass.getDeclaredMethods(tmInstance.getClass())) {
                if (object.toString().contains("setSimPowerStateForSlot")) {
                    method = (Method) object;
                    break;
                }
            }
        }

        // Below state files are created for sim state validation on mobile server
        if (method != null) {
            method.invoke(tmInstance, slotIndex, state);
            TelephonyUtils.writePropToFile("sim_state", String.valueOf(state));
        } else {
            TelephonyUtils.writePropToFile("sim_state", "1");
        }
    }

    /*
        Used for disabling dark mode for specific apps (eg - youtube app)
        S24 family comes with a new feature which allows dark mode to stay activated for specific apps, this method calls the setPackageNightMode of the UIModeManager service to toggle this setting for an app
    */
    public static void setUIModeForApplication(String packageName, int state) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        // call via reflection. Reference - https://stackoverflow.com/questions/55119839/iwindowmanager-call-setoverscan-via-reflection/55174537#55174537
        Class<?> serviceManagerClassObj = Class.forName("android.os.ServiceManager");
        Method getServiceMethod =
                serviceManagerClassObj.getDeclaredMethod("getService", String.class);
        getServiceMethod.setAccessible(true);

        Object UIModeServiceBinderObj = getServiceMethod.invoke(serviceManagerClassObj, Context.UI_MODE_SERVICE);

        Class<?> UIModeManagerStubObject = Class.forName("android.app.IUiModeManager").getClasses()[0];
        Method asInterfaceMethod =
                UIModeManagerStubObject.getDeclaredMethod("asInterface", IBinder.class);
        asInterfaceMethod.setAccessible(true);

        Object uiInstance = asInterfaceMethod.invoke(UIModeManagerStubObject, UIModeServiceBinderObj);

        Method method = null;

        // makes use of https://github.com/LSPosed/AndroidHiddenApiBypass to bypass hidden API restrictions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            for (Object object : HiddenApiBypass.getDeclaredMethods(uiInstance.getClass())) {
                if (object.toString().contains("setPackageNightMode")) {
                    Log.i("BrowserStack", "Night method: " + object.toString());
                    method = (Method) object;
                    break;
                }
            }
        }

        if (method != null) {
            method.invoke(uiInstance, packageName, 0, state);
        }
    }
}
