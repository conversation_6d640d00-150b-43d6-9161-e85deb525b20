package com.android.browserstack.services;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;

import com.android.browserstack.utils.BluetoothUtils;

public class BluetoothHandlerService extends Service {
    private static final String TAG = "BrowserStack-" + BluetoothHandlerService.class.getCanonicalName();

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            Log.i(TAG, "Request for BluetoothHandler service");
            if (intent.getStringExtra("action") != null) {  
                String action = intent.getStringExtra("action");
                if (action.equals("disable")) {
                    BluetoothUtils.setBluetoothState(false);
                }
                else if (action.equals("enable")) {
                    BluetoothUtils.setBluetoothState(true);
                }
                else {
                    Log.w(TAG, "Invalid action passed to BluetoothHandler service");
                }
            }
        }
        stopSelf();
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}
