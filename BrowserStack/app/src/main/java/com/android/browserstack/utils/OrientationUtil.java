package com.android.browserstack.utils;

import java.util.HashMap;

import android.app.Activity;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.PixelFormat;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.Surface;
import android.view.View;
import android.view.WindowManager;
import android.view.WindowManager.LayoutParams;


public class OrientationUtil {
    
    private static final String TAG = "BrowserStack-" + OrientationUtil.class.getCanonicalName();

    private static boolean isViewAdded;
    private static View view;
    private static WindowManager wm;
    private static LayoutParams lp;

    static HashMap<String, Integer> actionMap;
    static {
        actionMap = new HashMap<String, Integer>();
        actionMap.put("landscape", Surface.ROTATION_90);
        actionMap.put("portrait", Surface.ROTATION_0);
        actionMap.put("landscape_clockwise", Surface.ROTATION_270);
    }

    public static void setOrientation(Context context, String orientation) {
        Log.i(TAG, "Orientation Change Request" + orientation);
        
        if (actionMap.containsKey(orientation)) {
        	Settings.System.putInt(
        			context.getApplicationContext().getContentResolver(),
        		    Settings.System.ACCELEROMETER_ROTATION,
        		    0 //0 means off, 1 means on
        		);

        		Settings.System.putInt(
        				context.getApplicationContext().getContentResolver(),
        		    Settings.System.USER_ROTATION,
        		    actionMap.get(orientation)
        		);
        }
    }
}
