package com.android.browserstack.main;

import android.app.Activity;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;

import com.android.browserstack.receiver.WifiBroadcastReceiver;
import com.android.browserstack.services.GraphitePushService;
import com.android.browserstack.utils.TelephonyUtils;

public class BrowserStack extends Activity {

    private static final String TAG = "BrowserStack-" + BrowserStack.class.getSimpleName();
    IntentFilter intentFilter;
    WifiBroadcastReceiver receiver;

    public void onCreate(Bundle savedInstanceState) {
        Log.i(TAG, "Starting app");
        super.onCreate(savedInstanceState);

        Intent intent = new Intent(this, GraphitePushService.class);
        GraphitePushService.intent = this.getIntent();
        this.startService(intent);
        Log.i(TAG, "Started GraphitePush service");
        Log.i(TAG, "Registering receiver for Wifi connectivity");

        /* Wifi connectivity receiver works iff the receiver is registered programmatically
         and the app is running. More info in README.md*/

        intentFilter = new IntentFilter();
        intentFilter.addAction(android.net.ConnectivityManager.CONNECTIVITY_ACTION);
        receiver = new WifiBroadcastReceiver();
        registerReceiver(receiver, intentFilter);

        try {
            TelephonyUtils.writeMsisdnFile(getApplicationContext());
        } catch (Exception e) {
            Log.i(TAG, "Error while writing phone number to state file " + e.getMessage());
        }
    }
}
