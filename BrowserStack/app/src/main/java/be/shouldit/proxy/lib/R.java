/* AUTO-GENERATED FILE.  DO NOT MODIFY.
 *
 * This class was automatically generated by the
 * aapt tool from the resource data it found.  It
 * should not be modified by hand.
 */

package be.shouldit.proxy.lib;

public final class R {
    public static final class array {
        public static int feedback_ignored_packages=0x7f020000;
        public static int wifi_ap_security=0x7f020001;
        public static int wifi_eap_method=0x7f020002;
        public static int wifi_p2p_status=0x7f020003;
        public static int wifi_p2p_wps_setup=0x7f020004;
        public static int wifi_security=0x7f020005;
        public static int wifi_security_no_eap=0x7f020006;
        public static int wifi_status=0x7f020007;
        public static int wifi_status_with_ssid=0x7f020008;
    }
    public static final class attr {
        /** <p>Must be a boolean value, either "<code>true</code>" or "<code>false</code>".
<p>This may also be a reference to a resource (in the form
"<code>@[<i>package</i>:]<i>type</i>:<i>name</i></code>") or
theme attribute (in the form
"<code>?[<i>package</i>:][<i>type</i>:]<i>name</i></code>")
containing a value of this type.
         */
        public static int state_encrypted=0x7f010000;
    }
    public static final class string {
        public static int available=0x7f030000;
        public static int connected=0x7f030001;
        public static int direct_connection=0x7f030002;
        public static int not_available=0x7f030003;
        public static int not_set=0x7f030004;
        public static int proxy=0x7f030005;
        public static int status_description_checking=0x7f030006;
        public static int status_description_enabled=0x7f030007;
        public static int status_description_invalid_host=0x7f030008;
        public static int status_description_invalid_port=0x7f030009;
        public static int status_description_not_enabled=0x7f03000a;
        public static int status_description_not_reachable=0x7f03000b;
        public static int status_description_web_not_reachable=0x7f03000c;
        public static int status_exclusion_item_empty=0x7f03000d;
        public static int status_exclusion_item_notvalid=0x7f03000e;
        public static int status_exclusion_item_valid=0x7f03000f;
        public static int status_exclusion_list_notvalid=0x7f030010;
        public static int status_exclusion_list_valid=0x7f030011;
        public static int status_hostname_empty=0x7f030012;
        public static int status_hostname_notvalid=0x7f030013;
        public static int status_hostname_valid=0x7f030014;
        public static int status_port_empty=0x7f030015;
        public static int status_port_notvalid=0x7f030016;
        public static int status_port_valid=0x7f030017;
        public static int status_proxy_disabled=0x7f030018;
        public static int status_proxy_enabled=0x7f030019;
        public static int status_proxy_mobile_disabled=0x7f03001a;
        public static int status_proxy_not_reachable=0x7f03001b;
        public static int status_proxy_not_valid_informations=0x7f03001c;
        public static int status_proxy_reachable=0x7f03001d;
        public static int status_title_checking=0x7f03001e;
        public static int status_title_enabled=0x7f03001f;
        public static int status_title_invalid_host=0x7f030020;
        public static int status_title_invalid_port=0x7f030021;
        public static int status_title_not_enabled=0x7f030022;
        public static int status_title_not_reachable=0x7f030023;
        public static int status_title_web_not_reachable=0x7f030024;
        public static int status_web_not_reachable=0x7f030025;
        public static int status_web_reachable=0x7f030026;
        public static int status_wifi_enabled=0x7f030027;
        public static int status_wifi_enabled_disconnected=0x7f030028;
        public static int status_wifi_not_enabled=0x7f030029;
        public static int status_wifi_not_selected=0x7f03002a;
        public static int status_wifi_selected=0x7f03002b;
        public static int wifi_security_eap=0x7f03002c;
        public static int wifi_security_none=0x7f03002d;
        public static int wifi_security_psk_generic=0x7f03002e;
        public static int wifi_security_short_eap=0x7f03002f;
        public static int wifi_security_short_psk_generic=0x7f030030;
        public static int wifi_security_short_wep=0x7f030031;
        public static int wifi_security_short_wpa=0x7f030032;
        public static int wifi_security_short_wpa2=0x7f030033;
        public static int wifi_security_short_wpa_wpa2=0x7f030034;
        public static int wifi_security_wep=0x7f030035;
        public static int wifi_security_wpa=0x7f030036;
        public static int wifi_security_wpa2=0x7f030037;
        public static int wifi_security_wpa_wpa2=0x7f030038;
    }
    public static final class styleable {
        /** Attributes that can be used with a WifiEncryptionState.
           <p>Includes the following attributes:</p>
           <table>
           <colgroup align="left" />
           <colgroup align="left" />
           <tr><th>Attribute</th><th>Description</th></tr>
           <tr><td><code>{@link #WifiEncryptionState_state_encrypted be.shouldit.proxy.lib:state_encrypted}</code></td><td></td></tr>
           </table>
           @see #WifiEncryptionState_state_encrypted
         */
        public static final int[] WifiEncryptionState = {
            0x7f010000
        };
        /**
          <p>This symbol is the offset where the {@link be.shouldit.proxy.lib.R.attr#state_encrypted}
          attribute's value can be found in the {@link #WifiEncryptionState} array.


          <p>Must be a boolean value, either "<code>true</code>" or "<code>false</code>".
<p>This may also be a reference to a resource (in the form
"<code>@[<i>package</i>:]<i>type</i>:<i>name</i></code>") or
theme attribute (in the form
"<code>?[<i>package</i>:][<i>type</i>:]<i>name</i></code>")
containing a value of this type.
          @attr name be.shouldit.proxy.lib:state_encrypted
        */
        public static int WifiEncryptionState_state_encrypted = 0;
    };
}
