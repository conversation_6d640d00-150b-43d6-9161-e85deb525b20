# docker build -t apk-builder build/
# docker run  -v $PWD:/usr/local/.browserstack/mobile --entrypoint "/usr/local/.browserstack/mobile/android/live/scripts/build_browserstack_apk.sh" -it apk-builder build

FROM ubuntu:18.04

ENV ANDROID_SDK_VERSION r22
ENV ANDROID_BUILD_TOOLS_VERSION 19.1.0
ENV ANDROID_VERSION 19
ENV GRADLE_VERSION 2.5

RUN apt-get update && apt-get install -y \
        openjdk-8-jdk \
        wget \
        unzip \
        git \
        lib32stdc++6 \
        lib32z1 \
    && rm -rf /var/lib/apt/lists/*

# Install Android SDK
RUN cd /opt \
    && android_sdk_url=http://dl.google.com/android/android-sdk_${ANDROID_SDK_VERSION}-linux.tgz \
    && wget --output-document=android-sdk.tgz --quiet ${android_sdk_url} \
    && tar zxf android-sdk.tgz \
    && rm -f android-sdk.tgz

# Install Gradle
RUN cd /opt \
    && gradle_url=https://services.gradle.org/distributions/gradle-2.5-bin.zip \
    && wget --output-document=gradle.zip --quiet ${gradle_url} \
    && unzip gradle.zip \
    && rm -f gradle.zip


# Setup environment
ENV ANDROID_HOME /opt/android-sdk-linux
ENV GRADLE_HOME /opt/gradle-${GRADLE_VERSION}
ENV PATH ${PATH}:${ANDROID_HOME}/tools:${ANDROID_HOME}/platform-tools
ENV PATH ${PATH}:${GRADLE_HOME}/bin

# Install sdk elements
RUN echo y | android update sdk --all --no-ui --filter "platform-tools,tools,build-tools-${ANDROID_BUILD_TOOLS_VERSION},android-${ANDROID_VERSION}"
