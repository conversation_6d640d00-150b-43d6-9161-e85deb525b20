# This file can be used to build BS apk locally(on App Patcher Machine) on machine with Minikube setup without mounting the repo.
#Instructions
#   1. eval $(minikube docker-env)
#   2. docker build -t apk-builder --build-arg ssh_prv_key="$(cat ~/.ssh/id_rsa)" --build-arg branch="<branch_name>" bsapkbuild/
#   3. docker run -itd apk-builder bash (returns container_id)
#   4. docker cp <container_id>:/usr/local/.browserstack/mobile/BrowserStack/bin/BrowserStack-release.apk <any_path_on_host>
#   5. docker stop <container_id>
#   6. docker rm <container_id>

FROM ubuntu:18.04

ENV ANDROID_SDK_VERSION r22
ENV ANDROID_BUILD_TOOLS_VERSION 19.1.0
ENV ANDROID_VERSION 19
ENV GRADLE_VERSION 2.5

RUN apt-get update && apt-get install -y \
        openjdk-8-jdk \
        wget \
        unzip \
        git \
        lib32stdc++6 \
        lib32z1 \
    && rm -rf /var/lib/apt/lists/*

# Install Android SDK
RUN cd /opt \
    && android_sdk_url=http://dl.google.com/android/android-sdk_${ANDROID_SDK_VERSION}-linux.tgz \
    && wget --output-document=android-sdk.tgz --quiet ${android_sdk_url} \
    && tar zxf android-sdk.tgz \
    && rm -f android-sdk.tgz

# Install Gradle
RUN cd /opt \
    && gradle_url=https://services.gradle.org/distributions/gradle-2.5-bin.zip \
    && wget --output-document=gradle.zip --quiet ${gradle_url} \
    && unzip gradle.zip \
    && rm -f gradle.zip


# Setup environment
ENV ANDROID_HOME /opt/android-sdk-linux
ENV GRADLE_HOME /opt/gradle-${GRADLE_VERSION}
ENV PATH ${PATH}:${ANDROID_HOME}/tools:${ANDROID_HOME}/platform-tools
ENV PATH ${PATH}:${GRADLE_HOME}/bin

# Install sdk elements
RUN echo y | android update sdk --all --no-ui --filter "platform-tools,tools,build-tools-${ANDROID_BUILD_TOOLS_VERSION},android-${ANDROID_VERSION}"

#SSH Key and Branch on which the repository will be cloned. Default branch is master
ARG ssh_prv_key
ARG branch=master

# Authorize SSH Host
RUN mkdir -p /root/.ssh && \
    chmod 0700 /root/.ssh && \
    ssh-keyscan github.com > /root/.ssh/known_hosts

# Add the keys and set permissions
RUN echo "$ssh_prv_key" > /root/.ssh/id_rsa_without_passphrase && \
    chmod 600 /root/.ssh/id_rsa_without_passphrase

RUN echo "Host github.com\n\tStrictHostKeyChecking no\n\nIdentityFile ~/.ssh/id_rsa_without_passphrase\n" >> /root/.ssh/config

ARG CACHEBUST=1

WORKDIR /usr/local/.browserstack

RUN git clone -b $branch --single-branch **************:browserstack/mobile.git

WORKDIR /usr/local/.browserstack/mobile

RUN android/live/scripts/build_browserstack_apk.sh build

RUN md5sum /usr/local/.browserstack/mobile/BrowserStack/bin/BrowserStack-release.apk