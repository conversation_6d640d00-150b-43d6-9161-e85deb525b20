### Building BrowserStack App locally

Build image first running from root of mobile repo:

    docker build -t apk-builder Browserstack/

After this done you can pass code you want to build in a volume. For example for BrowserStack App you can run:

    docker run  --volume $PWD:/usr/local/.browserstack/mobile \
                --entrypoint "/usr/local/.browserstack/mobile/android/live/scripts/build_browserstack_apk.sh" \
                -it apk-builder build

After that results will appear in `./BrowserStack/bin/...`.

### Building other apps

Potentially, you can re-use the same image for building other apps too.

### Building BrowserStack App completely during image build process

Build image first running from root of mobile repo:

    docker build -t apk-builder --build-arg ssh_prv_key="$(cat ~/.ssh/id_rsa)" --build-arg branch="<branch_name>" -f BrowserStack/Dockerfile.x86_full_build .

This clones the code of the branch specified as build argument during image build step itself. This method can be used when mounting doesn't work.

After this is done, you can run the container which has the app and copy it to host using Docker copy. For example for BrowserStack App you can run:

    docker run -itd apk-builder bash #(returns container_id)
    docker cp <container_id>:/usr/local/.browserstack/mobile/BrowserStack/bin/BrowserStack-release.apk <any_path_on_host>

Refer https://browserstack.atlassian.net/wiki/spaces/ENG/pages/3391848463/Minikube+Hyperkit+Rancher+Desktop+as+Docker+Desktop+alternative+POC for detailed instructions


### Gradle build

BrowserStack.apk can be built with gradle build system now using command line or android studio.
	More info on this: https://browserstack.atlassian.net/wiki/spaces/ENG/pages/3697999908/BrowserStack.apk+-+overview+issues+with+the+current+build+system+and+migration+to+gradle 


Changes made for the gradle build setting:

Wifi receiver: Can be registered only when the app is running
	Reference: https://stackoverflow.com/questions/36421930/connectivitymanager-connectivity-action-deprecated

Starting foreground notification in GraphitePushService
	References: https://developer.android.com/sdk/api_diff/23/changes/android.app.Notification.html
		https://stackoverflow.com/questions/32345768/cannot-resolve-method-setlatesteventinfo

Enabling write settings for the app
	Reference: https://developer.android.com/reference/android/Manifest.permission#WRITE_SETTINGS

Enabling changing configuration - for change language
	Reference: https://stackoverflow.com/questions/13440045/android-4-2-filters-out-change-configuration-permission
